# TV (TradingView) Input Format Documentation

## Overview
The TV (TradingView) input format is used for backtesting strategies based on TradingView signals. It consists of two main components:
1. **TV Settings Excel File** - Contains strategy configuration
2. **Signal Files** - Contains actual trading signals

## 1. TV Settings Excel File Structure

### File: INPUT TV.xlsx
The main TV input file contains a single sheet named "Setting" with the following columns:

### Column Details:

| Column Name | Type | Description | Example Values |
|------------|------|-------------|----------------|
| **StartDate** | String | Strategy start date | "19_01_2024", "01_01_2018" |
| **EndDate** | String | Strategy end date | "01_01_2026" |
| **SignalDateFormat** | String | Date format in signal file | "%Y-%m-%d %H:%M", "%d-%m-%Y %H:%M" |
| **Enabled** | String | Whether strategy is enabled | "yes", "no" |
| **TvExitApplicable** | String | Whether TV exit signals are used | "yes", "no" |
| **ManualTradeEntryTime** | Number | Manual entry time (HHMMSS format) | 91800.0 (9:18:00 AM), 0.0 |
| **ManualTradeLots** | Number | Number of lots for manual trades | 1.0 |
| **IncreaseEntrySignalTimeBy** | Number | Seconds to add to entry signal time | 0.0 |
| **IncreaseExitSignalTimeBy** | Number | Seconds to add to exit signal time | 0.0 |
| **IntradaySqOffApplicable** | String | Square off all positions at day end | "yes", "no" |
| **FirstTradeEntryTime** | Number | Earliest time to take first trade | 0.0 |
| **IntradayExitTime** | Number | Time to exit all intraday positions | 231500.0 (3:15:00 PM) |
| **ExpiryDayExitTime** | Number | Exit time on expiry day | 231500.0 |
| **DoRollover** | String | Enable position rollover | "yes", "no" |
| **RolloverTime** | Number | Time to rollover positions | 151500.0 (3:15:00 PM) |
| **Name** | String | Strategy name | "NF-NDSTR-D 24-25", "ST142" |
| **SignalFilePath** | String | Path to signal file | "C:\Users\<USER>\signal.xlsx" |
| **LongPortfolioFilePath** | String | Path to long portfolio | "D:\Ajay\BT PORTFOLIO\..." |
| **ShortPortfolioFilePath** | String | Path to short portfolio | "D:\Ajay\BT PORTFOLIO\..." |
| **ManualPortfolioFilePath** | String | Path to manual portfolio (optional) | Usually empty |

### Time Format
All time fields use HHMMSS format as numbers:
- 91500 = 9:15:00 AM
- 150000 = 3:00:00 PM
- 231500 = 3:15:00 PM

## 2. Signal File Structure

### File Format: Excel with sheet "List of trades"
The signal file must contain a sheet named "List of trades" with the following columns:

| Column Name | Description |
|------------|-------------|
| **Trade #** | Trade number/ID |
| **Type** | Signal type (e.g., "Entry Long", "Entry Short", "Exit Long", "Exit Short") |
| **Date/Time** | Signal datetime (format specified in SignalDateFormat) |
| **Contracts** | Number of contracts/lots |

### Signal Types
- **Entry Long** - Enter a long position
- **Entry Short** - Enter a short position  
- **Exit Long** - Exit a long position
- **Exit Short** - Exit a short position

## 3. Validation Rules

1. **TvExitApplicable = NO**:
   - Cannot have DoRollover = YES
   - Must have IntradaySqOffApplicable = YES
   - ManualTradeEntryTime must be 0

2. **Time Conflicts**:
   - Cannot have both FirstTradeEntryTime and ManualTradeEntryTime set (one must be 0)

3. **Rollover Restrictions**:
   - DoRollover requires only one index in signals
   - Cannot have both DoRollover and IntradaySqOffApplicable as YES

## 4. Portfolio File Structure

Portfolio files contain two sheets:

### Sheet 1: PortfolioSetting
Configuration for the overall portfolio:

| Column Name | Description | Example |
|------------|-------------|---------|
| **StartDate** | Portfolio start date | "01_01_2018" |
| **EndDate** | Portfolio end date | "1_1_2025" |
| **IsTickBT** | Use tick-by-tick backtesting | "Yes" |
| **Enabled** | Portfolio enabled | "Yes" |
| **PortfolioName** | Name of portfolio | "SHORT TV PORT" |
| **PortfolioTarget** | Target profit (0 = no target) | 0 |
| **PortfolioStoploss** | Stop loss amount (0 = no SL) | 0 |
| **PortfolioTrailingType** | Trailing stop type | "Lock Minimum Profit" |
| **ProfitReaches** | Profit level to trigger trailing | 0 |
| **LockMinProfitAt** | Minimum profit to lock | 0 |
| **IncreaseInProfit** | Profit increment for trailing | 0 |
| **TrailMinProfitBy** | Trail amount | 0 |
| **Multiplier** | Position size multiplier | 1 |
| **SlippagePercent** | Slippage percentage | 0 |

### Sheet 2: StrategySetting
Links strategies to the portfolio:

| Column Name | Description | Example |
|------------|-------------|---------|
| **Enabled** | Strategy enabled | "YES" |
| **PortfolioName** | Must match PortfolioSetting name | "SHORT TV PORT" |
| **StrategyType** | Type of strategy | "TBS" |
| **StrategyExcelFilePath** | Path to strategy file | "D:\...\INPUT TBS SHORT1.xlsx" |

## 5. Creating ML Indicator Test Sheets

To create ML Indicator test sheets, you need to:

1. **Create the TV Settings Excel** with:
   - Strategy configuration in "Setting" sheet
   - Enable/disable appropriate flags
   - Set proper file paths for signals and portfolios

2. **Create Signal Files** with:
   - Excel file with sheet named "List of trades"
   - Columns: Trade #, Type, Date/Time, Contracts
   - Entry/Exit signals with proper datetime format
   - Contract quantities for each signal

3. **Create Portfolio Files** (Long and/or Short):
   - PortfolioSetting sheet with overall configuration
   - StrategySetting sheet linking to strategy files
   - Portfolio risk management parameters

4. **Link Everything Together**:
   - TV Setting references signal file path
   - TV Setting references portfolio file paths
   - Portfolio references strategy files

## Example TV Setting Row
```
StartDate: 01_01_2024
EndDate: 31_12_2024
SignalDateFormat: %Y-%m-%d %H:%M
Enabled: yes
TvExitApplicable: yes
ManualTradeEntryTime: 0
ManualTradeLots: 1
IncreaseEntrySignalTimeBy: 0
IncreaseExitSignalTimeBy: 0
IntradaySqOffApplicable: yes
FirstTradeEntryTime: 91500
IntradayExitTime: 151500
ExpiryDayExitTime: 151500
DoRollover: no
RolloverTime: 151500
Name: ML_Indicator_Strategy_1
SignalFilePath: C:\Signals\ml_signals.xlsx
LongPortfolioFilePath: C:\Portfolio\long_portfolio.xlsx
ShortPortfolioFilePath: C:\Portfolio\short_portfolio.xlsx
ManualPortfolioFilePath: 
```

This format allows you to configure multiple strategies in a single Excel file, each with its own signal file and portfolio configurations.