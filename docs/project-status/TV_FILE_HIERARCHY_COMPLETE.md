# Complete TV File Hierarchy and Column Mapping Documentation

## File Hierarchy Overview

```
input_tv.xlsx (Master Configuration)
    ├── SignalFilePath → sample_nifty_list_of_trades.xlsx (Trade Signals)
    │   └── Contains sheets: Performance, Trades analysis, Risk performance ratios, List of trades, Properties
    │
    ├── LongPortfolioFilePath → input_portfolio_long.xlsx
    │   ├── Sheet: PortfolioSetting (Portfolio configuration)
    │   └── Sheet: StrategySetting → References input_tbs_long.xlsx
    │       └── input_tbs_long.xlsx
    │           ├── Sheet: GeneralParameter (Strategy settings)
    │           └── Sheet: LegParameter (Up to 4 option legs)
    │
    ├── ShortPortfolioFilePath → input_portfolio_short.xlsx  
    │   ├── Sheet: PortfolioSetting (Portfolio configuration)
    │   └── Sheet: StrategySetting → References input_tbs_short.xlsx
    │       └── input_tbs_short.xlsx
    │           ├── Sheet: GeneralParameter (Strategy settings)
    │           └── Sheet: LegParameter (Up to 4 option legs)
    │
    └── ManualPortfolioFilePath → (optional portfolio file, not present in current setup)
```

## Detailed File Analysis

### 1. input_tv.xlsx (Master Configuration)

**Sheet: Setting**
- **Shape**: 1 row x 24 columns
- **Purpose**: Main configuration for TV backtesting

| Column | Sample Value | Description |
|--------|--------------|-------------|
| StartDate | 01_01_2022 | Backtest start date |
| EndDate | 01_01_2027 | Backtest end date |
| SignalDateFormat | %Y-%m-%d %H:%M | Format for parsing signal timestamps |
| Enabled | yes | Enable this configuration row |
| TvExitApplicable | yes | Use TV exit signals |
| ManualTradeEntryTime | 0 | Time for synthetic manual entries |
| ManualTradeLots | 1 | Lots for manual trades |
| IncreaseEntrySignalTimeBy | 0 | Entry time offset (seconds) |
| IncreaseExitSignalTimeBy | 0 | Exit time offset (seconds) |
| IntradaySqOffApplicable | no | Force intraday square off |
| FirstTradeEntryTime | 0 | Override first trade time |
| IntradayExitTime | 151500 | Intraday exit time |
| ExpiryDayExitTime | 151500 | Expiry day exit time |
| DoRollover | yes | Enable position rollover |
| RolloverTime | 151500 | Time to roll positions |
| Name | LUXPOS BB 1HR | Output file identifier |
| SignalFilePath | C:\Users\<USER>\Downloads\sample_nifty_list_of_trades.xlsx | Path to signals |
| LongPortfolioFilePath | C:\Users\<USER>\Downloads\input_portfolio_long.xlsx | Long strategy portfolio |
| ShortPortfolioFilePath | D:\Ajay\BT PORTFOLIO\INPUT SHEETS\INPUT PORTFOLIO SHORT.xlsx | Short strategy portfolio |
| ManualPortfolioFilePath | NaN | Manual strategy portfolio |
| UseDbExitTiming | YES | Use database for precise exit timing |
| ExitSearchInterval | 5 | Search window in minutes |
| ExitPriceSource | SPOT | Price source (SPOT/FUTURE) |
| SlippagePercent | 0.1 | Slippage percentage |

### 2. sample_nifty_list_of_trades.xlsx (Signal File)

**Key Sheet: List of trades**
- **Shape**: 6886 rows x 14 columns
- **Purpose**: Contains trading signals from external source

| Column | Sample Value | Description |
|--------|--------------|-------------|
| Trade # | 1 | Trade identifier for pairing entry/exit |
| Type | Exit long | Signal type (Entry long/Exit long/Entry short/Exit short) |
| Signal | Sell_Entry | Signal name from trading system |
| Date/Time | 2022-05-02 10:55:00 | Signal timestamp |
| Price INR | 16,977.70 | Execution price |
| Contracts | 1 | Number of lots |
| Profit INR | -36.65 | Trade profit/loss |
| Profit % | -0.0022 | Trade profit/loss percentage |
| Cumulative profit INR | -36.65 | Running total P&L |
| Cumulative profit % | 0.0 | Running total P&L percentage |
| Run-up INR | 9.90 | Maximum favorable excursion |
| Run-up % | 0.0006 | MFE percentage |
| Drawdown INR | 54.85 | Maximum adverse excursion |
| Drawdown % | 0.0032 | MAE percentage |

### 3. input_portfolio_long.xlsx (Long Portfolio)

**Sheet: PortfolioSetting**
- **Shape**: 1 row x 20 columns
- **Purpose**: Portfolio-level risk management

| Column | Sample Value | Description |
|--------|--------------|-------------|
| StartDate | 01_01_2018 | Portfolio start date |
| EndDate | 1_1_2027 | Portfolio end date |
| IsTickBT | No | Tick-level backtesting |
| Enabled | Yes | Enable this portfolio |
| PortfolioName | LONG TV PORT | Portfolio identifier |
| PortfolioTarget | 0 | Portfolio target profit |
| PortfolioStoploss | 0 | Portfolio stop loss |
| PortfolioTrailingType | Lock Minimum Profit | Trailing method |
| PnLCalTime | 0 | P&L calculation time |
| LockPercent | 0 | Lock profit percentage |
| TrailPercent | 0 | Trail profit percentage |
| SqOff1Time | 0 | First square off time |
| SqOff1Percent | 0 | First square off percentage |
| SqOff2Time | 0 | Second square off time |
| SqOff2Percent | 0 | Second square off percentage |
| ProfitReaches | 0 | Profit level trigger |
| LockMinProfitAt | 0 | Lock profit level |
| IncreaseInProfit | 0 | Profit increase trigger |
| TrailMinProfitBy | 0 | Trail profit amount |
| Multiplier | 1 | Position multiplier |

**Sheet: StrategySetting**
- **Shape**: 1 row x 4 columns
- **Purpose**: Link to strategy file

| Column | Sample Value | Description |
|--------|--------------|-------------|
| Enabled | YES | Enable this strategy |
| PortfolioName | LONG TV PORT | Must match PortfolioSetting |
| StrategyType | TBS | Strategy type identifier |
| StrategyExcelFilePath | C:\Users\<USER>\Downloads\input_tbs_long.xlsx | Path to strategy |

### 4. input_portfolio_short.xlsx (Short Portfolio)

Structure identical to input_portfolio_long.xlsx but with:
- PortfolioName: SHORT TV PORT
- StrategyExcelFilePath: Points to input_tbs_short.xlsx

### 5. input_tbs_long.xlsx (Long Strategy)

**Sheet: GeneralParameter**
- **Shape**: 1 row x 36 columns
- **Purpose**: Strategy-level settings

Key columns:
| Column | Sample Value | Description |
|--------|--------------|-------------|
| StrategyName | L | Strategy identifier |
| MoveSlToCost | no | Move SL to cost basis |
| Underlying | SPOT | Underlying type |
| Index | NIFTY | Index to trade |
| Weekdays | 1,2,3,4,5 | Trading days |
| DTE | 10 | Days to expiry |
| StrikeSelectionTime | 90000 | Strike selection time |
| StartTime | 90000 | Entry time |
| LastEntryTime | 233000 | Last entry allowed |
| EndTime | 233000 | Exit time |
| StrategyProfit | 0 | Strategy target profit |
| StrategyLoss | 0 | Strategy stop loss |

**Sheet: LegParameter**
- **Shape**: 4 rows x 28 columns (4 legs)
- **Purpose**: Individual option leg configuration

Key columns:
| Column | Sample Values | Description |
|--------|---------------|-------------|
| StrategyName | L | Must match GeneralParameter |
| LegID | 1,2,3,4 | Leg identifier |
| Instrument | call/put | Option type |
| Transaction | buy/sell | Trade direction |
| Expiry | current | Expiry selection |
| MatchPremium | high | Premium matching |
| StrikeMethod | atm | Strike selection method |
| StrikeValue | 0,5 | Strike offset/parameter |
| Lots | 1 | Number of lots per leg |

### 6. input_tbs_short.xlsx (Short Strategy)

Structure identical to input_tbs_long.xlsx but with:
- StrategyName: S
- Same leg structure with potentially different strike values

## Data Flow Process

1. **Entry Point**: System reads `input_tv.xlsx` Setting sheet
2. **Signal Loading**: Loads signals from `SignalFilePath` (sample_nifty_list_of_trades.xlsx)
3. **Signal Type Mapping**:
   - "Entry long"/"Exit long" → Uses `LongPortfolioFilePath`
   - "Entry short"/"Exit short" → Uses `ShortPortfolioFilePath`
   - "Manual Entry"/"Manual Exit" → Uses `ManualPortfolioFilePath`
4. **Portfolio Loading**: Loads the appropriate portfolio file based on signal type
5. **Strategy Loading**: From portfolio's `StrategyExcelFilePath`, loads the TBS strategy
6. **Execution**: Combines signal timing with strategy parameters to execute trades

## Key Relationships

1. **Signal Pairing**: Trade # in signal file pairs entry and exit signals
2. **Time Override**: Signal times override portfolio/strategy times
3. **Lot Override**: Signal Contracts override strategy lots
4. **Strike Selection**: Uses strategy's strike selection logic at signal time
5. **Risk Management**: Applies portfolio and strategy level stops/targets

## File Path References

Current setup uses Windows paths that need updating for Linux:
- Signal: `C:\Users\<USER>\Downloads\sample_nifty_list_of_trades.xlsx`
- Long Portfolio: `C:\Users\<USER>\Downloads\input_portfolio_long.xlsx`
- Short Portfolio: `D:\Ajay\BT PORTFOLIO\INPUT SHEETS\INPUT PORTFOLIO SHORT.xlsx`
- Long Strategy: `C:\Users\<USER>\Downloads\input_tbs_long.xlsx`
- Short Strategy: `C:\Users\<USER>\Downloads\input_tbs_short.xlsx`

These paths should be updated to Linux paths like:
- `/srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/tv/...`

## Column Data Types

### Numeric Columns
- Integer: ManualTradeLots, IncreaseEntrySignalTimeBy, IncreaseExitSignalTimeBy, Lots, LegID, DTE
- Float: SlippagePercent, StrikeValue, various percentages
- Time (HHMMSS): IntradayExitTime, ExpiryDayExitTime, RolloverTime, StartTime, EndTime

### String Columns
- Paths: SignalFilePath, LongPortfolioFilePath, ShortPortfolioFilePath, StrategyExcelFilePath
- Identifiers: Name, StrategyName, PortfolioName, Trade #
- Enums: Enabled (yes/no/YES/NO), Instrument (call/put), Transaction (buy/sell)

### Date/Time Columns
- Date format: StartDate, EndDate (DD_MM_YYYY)
- DateTime: Date/Time in signals (parsed using SignalDateFormat)

## Validation Requirements

1. All file paths must exist and be accessible
2. Portfolio names must match between sheets
3. Strategy names must match between GeneralParameter and LegParameter
4. Signal Trade # must have matching entry/exit pairs
5. Date ranges must be valid (start < end)
6. Time values must be in HHMMSS format
7. Enabled fields must be yes/no/YES/NO