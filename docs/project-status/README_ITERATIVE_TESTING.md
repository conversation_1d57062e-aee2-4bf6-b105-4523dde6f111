# Iterative GPU Backtester Test Framework

## Overview

The Iterative Test Framework for the Enhanced GPU Backtester provides a robust, self-correcting test automation system that runs tests repeatedly until consistent results are achieved. This ensures reliability and identifies intermittent issues that might be missed in single-run tests.

## Key Features

### 1. **Iterative Execution**
- Tests run multiple times (up to 5 iterations by default)
- Consistency threshold of 3 consecutive successful runs
- Automatic detection of flaky tests
- Detailed tracking of variations between iterations

### 2. **Auto-Correction Mechanisms**
- Automatic retry on failures with corrections
- Self-healing capabilities for common issues:
  - Element not found → Increased wait times
  - Timeouts → Browser restart
  - Login failures → Session clearing
  - Upload failures → File verification
- Maximum 3 correction attempts per test

### 3. **Consistency Validation**
- Tracks results across iterations
- Identifies patterns in failures
- Calculates confidence scores
- Reports on test stability

### 4. **Comprehensive Reporting**
- Per-iteration detailed results
- Consistency analysis
- Performance metrics aggregation
- Visual stability charts

## Test Framework Architecture

```
test_gpu_backtester_iterative.py
├── IterativeTestFramework
│   ├── run_with_iterations()      # Core iteration logic
│   ├── _results_consistent()      # Consistency checking
│   └── _get_difference()          # Variation tracking
│
├── IterativeGPUBacktesterTestSuite
│   ├── Test Methods (iterative versions)
│   │   ├── test_authentication_iterative()
│   │   ├── test_navigation_iterative()
│   │   ├── test_new_backtest_iterative()
│   │   ├── test_logs_ui_iterative()
│   │   ├── test_gpu_performance_iterative()
│   │   ├── test_backtesting_systems_iterative()
│   │   ├── test_output_validation_iterative()
│   │   └── test_performance_scalability_iterative()
│   │
│   └── Helper Methods
│       └── MCP browser operations
│
└── Data Classes
    ├── TestIteration      # Single iteration result
    └── ConsistencyResult  # Overall consistency analysis
```

## Running Iterative Tests

### Basic Execution

```bash
# Run with default configuration
python3 /srv/samba/shared/run_iterative_gpu_tests.py
```

### Custom Configuration

```python
# Edit ITERATION_CONFIG in run_iterative_gpu_tests.py
ITERATION_CONFIG = {
    "max_iterations": 5,        # Maximum attempts per test
    "consistency_threshold": 3,  # Required consistent passes
    "retry_delay": 2,           # Seconds between retries
    "auto_correct": True,       # Enable self-correction
    "detailed_logging": True    # Verbose logging
}
```

### Environment Variables

```bash
# Override configuration via environment
export MAX_ITERATIONS=10
export CONSISTENCY_THRESHOLD=5
export AUTO_CORRECT=true

python3 /srv/samba/shared/run_iterative_gpu_tests.py
```

## Test Iteration Process

### 1. Initial Execution
```
Test: Authentication Page
├── Iteration 1: Navigate → Check Elements → Test Login → PASS
├── Iteration 2: Navigate → Check Elements → Test Login → PASS
├── Iteration 3: Navigate → Check Elements → Test Login → PASS
└── Result: ✓ Consistency achieved (3/3 passes)
```

### 2. With Failures and Corrections
```
Test: New Backtest Page
├── Iteration 1: Navigate → Upload Files → FAIL (element not found)
│   └── Correction: Increase wait time
├── Iteration 2: Navigate → Upload Files → PASS
├── Iteration 3: Navigate → Upload Files → PASS
├── Iteration 4: Navigate → Upload Files → PASS
└── Result: ✓ Consistency achieved (3 consecutive passes)
```

### 3. Inconsistent Test
```
Test: GPU Performance
├── Iteration 1: Start Backtest → Monitor → PASS
├── Iteration 2: Start Backtest → Monitor → FAIL (low GPU usage)
├── Iteration 3: Start Backtest → Monitor → PASS
├── Iteration 4: Start Backtest → Monitor → FAIL (timeout)
├── Iteration 5: Start Backtest → Monitor → PASS
└── Result: ✗ Inconsistent (no 3 consecutive passes)
```

## Auto-Correction Examples

### Element Not Found
```python
# Error: "phone_input element not found"
# Correction Applied:
- Increase wait time from 2s to 5s
- Verify page fully loaded
- Retry element search with alternative selectors
```

### Login Failure
```python
# Error: "login failed"
# Correction Applied:
- Clear browser cookies
- Navigate to logout URL
- Clear local storage
- Restart login flow
```

### File Upload Issues
```python
# Error: "file upload failed"
# Correction Applied:
- Verify file paths exist
- Check file permissions
- Create test files if missing
- Retry upload with delay
```

## Output Files

### 1. Iteration History
```
/srv/samba/shared/iterations_YYYYMMDD_HHMMSS/
├── iterative_test_report_*.json     # Detailed results
├── iterative_test_summary_*.txt     # Human-readable summary
└── test_screenshots/                 # Screenshots per iteration
```

### 2. Execution Report
```json
{
  "execution_summary": {
    "timestamp": "2025-01-06T10:30:00",
    "total_tests": 8,
    "passed": 7,
    "failed": 1,
    "success_rate": 87.5,
    "configuration": {...}
  },
  "test_details": {
    "Authentication Page": {
      "status": "PASS",
      "iterations": 3,
      "consistency_achieved": true
    },
    ...
  },
  "correction_attempts": {
    "New Backtest Page": 1,
    "GPU Performance": 2
  }
}
```

### 3. Consistency Analysis
```json
{
  "test_name": "Navigation",
  "total_iterations": 3,
  "consistent_iterations": 3,
  "is_consistent": true,
  "variations": [],
  "final_status": "PASS",
  "confidence_score": 1.0
}
```

## CI/CD Integration

### GitHub Actions Workflow

The `iterative_test_ci.yml` provides:

1. **Scheduled Runs**: Daily iterative testing
2. **Matrix Testing**: Multiple browsers and test modes
3. **Aggregate Analysis**: Cross-run stability metrics
4. **Visual Reports**: Stability charts and trends

### Test Modes

- **Standard Mode**: Default 5 iterations, 3 consistency threshold
- **Stress Mode**: 10 iterations, 5 consistency threshold for thorough validation

## Interpreting Results

### Consistency Metrics

| Metric | Good | Warning | Poor |
|--------|------|---------|------|
| Consistency Rate | > 90% | 70-90% | < 70% |
| Confidence Score | > 0.8 | 0.6-0.8 | < 0.6 |
| Avg Iterations | < 3.5 | 3.5-4.5 | > 4.5 |
| Stability Score | > 0.8 | 0.6-0.8 | < 0.6 |

### Common Patterns

1. **Consistently Passing**: Test is stable and reliable
2. **Eventually Consistent**: Test needs warm-up, consider increasing initial wait
3. **Intermittently Failing**: Indicates flaky test or environmental issues
4. **Consistently Failing**: Test or application has genuine issues

## Best Practices

### 1. **Test Design**
- Make tests deterministic
- Avoid time-dependent assertions
- Use explicit waits over sleep
- Clear state between iterations

### 2. **Iteration Configuration**
- Start with default settings
- Increase iterations for critical tests
- Adjust consistency threshold based on test importance
- Enable auto-correction for CI/CD

### 3. **Debugging Failures**
- Check iteration history for patterns
- Review correction attempts
- Analyze screenshots across iterations
- Compare successful vs failed iterations

### 4. **Performance Optimization**
- Run critical tests more frequently
- Parallelize independent test groups
- Cache static resources
- Optimize wait strategies

## Troubleshooting

### High Iteration Count
```bash
# If tests require many iterations:
1. Check for race conditions
2. Verify element wait strategies
3. Review auto-correction logic
4. Consider environmental factors
```

### Inconsistent Results
```bash
# For tests that never achieve consistency:
1. Enable detailed logging
2. Capture network traffic
3. Check for external dependencies
4. Review timing-sensitive operations
```

### Auto-Correction Failures
```bash
# If corrections don't help:
1. Check correction attempt limits
2. Verify correction logic matches errors
3. Consider manual intervention points
4. Review browser state management
```

## Advanced Features

### Custom Correction Handlers
```python
async def custom_correction_handler(test_name: str, error: str):
    """Implement custom correction logic"""
    if "specific_error" in error:
        # Apply specific fix
        await perform_custom_fix()
        return True
    return False
```

### Iteration Hooks
```python
# Pre-iteration setup
async def before_iteration(iteration_number: int):
    logger.info(f"Preparing iteration {iteration_number}")
    await clear_test_data()

# Post-iteration cleanup
async def after_iteration(result: TestIteration):
    logger.info(f"Iteration complete: {result.status}")
    await save_iteration_state()
```

### Parallel Iteration Execution
```python
# Run multiple tests concurrently
async def run_parallel_iterations():
    tasks = [
        run_with_iterations(test1, "Test1"),
        run_with_iterations(test2, "Test2")
    ]
    results = await asyncio.gather(*tasks)
```

## Maintenance

### Regular Updates
1. Review and update correction logic monthly
2. Adjust iteration thresholds based on historical data
3. Update selectors as UI changes
4. Optimize based on consistency reports

### Monitoring
- Track consistency trends over time
- Alert on degrading stability scores
- Review correction attempt patterns
- Monitor iteration count trends

---

**Version**: 1.0.0  
**Last Updated**: January 2025  
**Maintainer**: MarvelQuant Test Automation Team