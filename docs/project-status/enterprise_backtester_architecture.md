# Enterprise Backtester Architecture

## Overview

This document outlines the enterprise-grade architecture for running both legacy and HeavyDB GPU-optimized backtesters with external access, scalability, and production-ready features.

## Current State

### Legacy System (Windows Server)
- **Location**: ************ (Windows Server)
- **Internal Service**: ***************:5000
- **Technology**: Python, Flask, CPU-based processing
- **Database**: MySQL at ************:3306

### New System (Linux Server)  
- **Location**: Current Linux environment
- **Technology**: Python, FastAPI, HeavyDB GPU
- **Database**: HeavyDB at 127.0.0.1:6274

## Immediate Solution (Quick Implementation)

### 1. Legacy System API Wrapper

Deploy the Flask API wrapper on Windows Server:

```powershell
# Install dependencies
pip install flask redis

# Set up as Windows Service
sc create "LegacyBacktesterAPI" binPath= "C:\Python\python.exe C:\backtester\legacy_backtester_api.py"

# Configure firewall
netsh advfirewall firewall add rule name="Backtester API" dir=in action=allow protocol=TCP localport=5000

# Enable port forwarding from external IP
netsh interface portproxy add v4tov4 listenport=5000 listenaddress=0.0.0.0 connectport=5000 connectaddress=***************
```

### 2. New System API Service

Deploy FastAPI service with systemd on Linux:

```bash
# Install dependencies
pip install fastapi uvicorn redis celery

# Create systemd service
sudo tee /etc/systemd/system/heavydb-backtester.service << EOF
[Unit]
Description=HeavyDB GPU Backtester API
After=network.target

[Service]
Type=simple
User=backtester
WorkingDirectory=/srv/samba/shared/bt
Environment="PYTHONPATH=/srv/samba/shared/bt"
ExecStart=/usr/bin/python3 /srv/samba/shared/bt/heavydb_backtester_api.py
Restart=always

[Install]
WantedBy=multi-user.target
EOF

sudo systemctl enable heavydb-backtester
sudo systemctl start heavydb-backtester
```

## Enterprise Architecture (Production-Ready)

### Architecture Components

```mermaid
graph TB
    subgraph "Client Layer"
        WEB[Web UI]
        API_CLIENT[API Clients]
        JUPYTER[Jupyter Notebooks]
    end

    subgraph "API Gateway"
        NGINX[NGINX/Kong API Gateway]
        AUTH[Auth Service]
    end

    subgraph "Application Layer"
        subgraph "Legacy Cluster"
            LB1[Load Balancer]
            LEG1[Legacy API 1]
            LEG2[Legacy API 2]
            LEGN[Legacy API N]
        end
        
        subgraph "GPU Cluster"
            LB2[Load Balancer]
            GPU1[GPU API 1]
            GPU2[GPU API 2]
            GPUN[GPU API N]
        end
    end

    subgraph "Queue Layer"
        REDIS[Redis Cluster]
        RABBIT[RabbitMQ]
    end

    subgraph "Worker Layer"
        CELERY1[Celery Worker 1]
        CELERY2[Celery Worker 2]
        CELERYN[Celery Worker N]
    end

    subgraph "Data Layer"
        MYSQL[(MySQL)]
        HEAVYDB[(HeavyDB)]
        S3[S3/MinIO Storage]
    end

    subgraph "Monitoring"
        PROM[Prometheus]
        GRAFANA[Grafana]
        ELK[ELK Stack]
    end

    WEB --> NGINX
    API_CLIENT --> NGINX
    JUPYTER --> NGINX
    
    NGINX --> AUTH
    NGINX --> LB1
    NGINX --> LB2
    
    LB1 --> LEG1
    LB1 --> LEG2
    LB1 --> LEGN
    
    LB2 --> GPU1
    LB2 --> GPU2
    LB2 --> GPUN
    
    GPU1 --> REDIS
    GPU2 --> REDIS
    GPUN --> REDIS
    
    REDIS --> CELERY1
    REDIS --> CELERY2
    REDIS --> CELERYN
    
    CELERY1 --> HEAVYDB
    CELERY2 --> HEAVYDB
    CELERYN --> HEAVYDB
    
    LEG1 --> MYSQL
    LEG2 --> MYSQL
    LEGN --> MYSQL
    
    GPU1 --> S3
    GPU2 --> S3
    GPUN --> S3
    
    PROM --> LB1
    PROM --> LB2
    GRAFANA --> PROM
    ELK --> NGINX
```

### Key Features

#### 1. API Gateway Layer
- **NGINX/Kong**: Central entry point with rate limiting, authentication
- **Auth Service**: OAuth2/JWT authentication
- **SSL Termination**: HTTPS for all external communication

#### 2. Load Balancing
- **HAProxy/NGINX**: Distribute load across multiple instances
- **Health Checks**: Automatic failover for unhealthy instances
- **Session Affinity**: Sticky sessions for stateful operations

#### 3. Queue Management
- **Redis Cluster**: High-availability job queue
- **RabbitMQ**: Alternative for complex routing needs
- **Priority Queues**: Different priorities for different client tiers

#### 4. Worker Scaling
- **Celery Workers**: Horizontally scalable processing
- **GPU Workers**: Dedicated nodes with GPU access
- **Auto-scaling**: Based on queue depth and resource usage

#### 5. Data Storage
- **MySQL**: Legacy data compatibility
- **HeavyDB**: GPU-accelerated analytics
- **S3/MinIO**: Object storage for results and uploads

#### 6. Monitoring & Logging
- **Prometheus**: Metrics collection
- **Grafana**: Visualization dashboards
- **ELK Stack**: Centralized logging

## Deployment Options

### 1. Docker Compose (Development/Testing)

```yaml
version: '3.8'

services:
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data

  legacy-api:
    build:
      context: ./legacy
      dockerfile: Dockerfile
    ports:
      - "5000:5000"
    environment:
      - MYSQL_HOST=************
      - MYSQL_USER=mahesh
      - MYSQL_PASSWORD=mahesh_123
    volumes:
      - ./data:/data

  gpu-api:
    build:
      context: ./gpu
      dockerfile: Dockerfile
    ports:
      - "8000:8000"
    environment:
      - HEAVYDB_HOST=heavydb
      - REDIS_URL=redis://redis:6379
    depends_on:
      - redis
      - heavydb
    volumes:
      - ./data:/data

  heavydb:
    image: heavyai/core-os-cpu:latest
    ports:
      - "6274:6274"
    volumes:
      - heavydb_data:/heavyai-storage

  celery-worker:
    build:
      context: ./gpu
      dockerfile: Dockerfile.worker
    environment:
      - CELERY_BROKER_URL=redis://redis:6379
      - HEAVYDB_HOST=heavydb
    depends_on:
      - redis
      - heavydb
    deploy:
      replicas: 3

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./certs:/etc/nginx/certs
    depends_on:
      - legacy-api
      - gpu-api

volumes:
  redis_data:
  heavydb_data:
```

### 2. Kubernetes (Production)

```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: gpu-backtester-api
spec:
  replicas: 3
  selector:
    matchLabels:
      app: gpu-backtester-api
  template:
    metadata:
      labels:
        app: gpu-backtester-api
    spec:
      containers:
      - name: api
        image: backtester/gpu-api:latest
        ports:
        - containerPort: 8000
        env:
        - name: HEAVYDB_HOST
          valueFrom:
            configMapKeyRef:
              name: backtester-config
              key: heavydb.host
        - name: REDIS_URL
          valueFrom:
            secretKeyRef:
              name: backtester-secrets
              key: redis.url
        resources:
          requests:
            memory: "2Gi"
            cpu: "1"
          limits:
            memory: "4Gi"
            cpu: "2"
            nvidia.com/gpu: 1  # GPU access

---
apiVersion: v1
kind: Service
metadata:
  name: gpu-backtester-service
spec:
  selector:
    app: gpu-backtester-api
  ports:
  - port: 80
    targetPort: 8000
  type: LoadBalancer

---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: gpu-backtester-hpa
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: gpu-backtester-api
  minReplicas: 2
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
```

### 3. AWS/Cloud Deployment

```terraform
# Terraform configuration for AWS deployment

resource "aws_ecs_cluster" "backtester" {
  name = "backtester-cluster"
}

resource "aws_ecs_service" "gpu_api" {
  name            = "gpu-backtester-api"
  cluster         = aws_ecs_cluster.backtester.id
  task_definition = aws_ecs_task_definition.gpu_api.arn
  desired_count   = 3

  deployment_configuration {
    maximum_percent         = 200
    minimum_healthy_percent = 100
  }

  load_balancer {
    target_group_arn = aws_alb_target_group.gpu_api.arn
    container_name   = "gpu-api"
    container_port   = 8000
  }
}

resource "aws_ecs_task_definition" "gpu_api" {
  family                   = "gpu-backtester-api"
  requires_compatibilities = ["EC2"]
  network_mode            = "bridge"
  cpu                     = "2048"
  memory                  = "4096"

  container_definitions = jsonencode([
    {
      name  = "gpu-api"
      image = "backtester/gpu-api:latest"
      
      portMappings = [
        {
          containerPort = 8000
          protocol      = "tcp"
        }
      ]

      environment = [
        {
          name  = "HEAVYDB_HOST"
          value = aws_db_instance.heavydb.endpoint
        }
      ]

      resourceRequirements = [
        {
          type  = "GPU"
          value = "1"
        }
      ]
    }
  ])
}
```

## Security Considerations

### 1. Authentication & Authorization
```python
# JWT authentication middleware
from fastapi import Depends, HTTPException, Security
from fastapi.security import HTTPBearer, HTTPAuthorizationCredentials
import jwt

security = HTTPBearer()

async def verify_token(credentials: HTTPAuthorizationCredentials = Security(security)):
    token = credentials.credentials
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=["HS256"])
        return payload
    except jwt.JWTError:
        raise HTTPException(status_code=403, detail="Invalid token")

# Apply to routes
@app.post("/api/v2/backtest", dependencies=[Depends(verify_token)])
async def submit_backtest(...):
    ...
```

### 2. API Rate Limiting
```python
from slowapi import Limiter
from slowapi.util import get_remote_address

limiter = Limiter(key_func=get_remote_address)

@app.post("/api/v2/backtest")
@limiter.limit("10/minute")
async def submit_backtest(...):
    ...
```

### 3. Input Validation
```python
class BacktestRequest(BaseModel):
    portfolio_excel: str = Field(..., regex="^[a-zA-Z0-9_/-]+\\.xlsx$")
    start_date: str = Field(..., regex="^\\d{8}$")
    end_date: str = Field(..., regex="^\\d{8}$")
    
    @validator('end_date')
    def validate_dates(cls, v, values):
        if 'start_date' in values and v < values['start_date']:
            raise ValueError('end_date must be after start_date')
        return v
```

## Performance Optimization

### 1. Caching Layer
```python
import aiocache

cache = aiocache.Cache()

@app.get("/api/v2/backtest/{job_id}")
@cache.cached(ttl=60)
async def get_job_status(job_id: str):
    ...
```

### 2. Connection Pooling
```python
# HeavyDB connection pool
from concurrent.futures import ThreadPoolExecutor

class HeavyDBPool:
    def __init__(self, size=10):
        self.executor = ThreadPoolExecutor(max_workers=size)
        self.connections = []
        
    def get_connection(self):
        # Implement connection pooling logic
        pass
```

### 3. Batch Processing
```python
@app.post("/api/v2/backtest/batch")
async def submit_batch_backtest(requests: List[BacktestRequest]):
    job_ids = []
    for request in requests:
        job_id = await submit_single_backtest(request)
        job_ids.append(job_id)
    return {"job_ids": job_ids}
```

## Monitoring & Alerting

### 1. Metrics Collection
```python
from prometheus_client import Counter, Histogram, generate_latest

backtest_requests = Counter('backtest_requests_total', 'Total backtest requests')
backtest_duration = Histogram('backtest_duration_seconds', 'Backtest duration')

@app.get("/metrics")
async def metrics():
    return Response(generate_latest(), media_type="text/plain")
```

### 2. Health Checks
```python
@app.get("/health/live")
async def liveness():
    return {"status": "alive"}

@app.get("/health/ready")
async def readiness():
    # Check all dependencies
    checks = await check_all_services()
    if all(checks.values()):
        return {"status": "ready", "checks": checks}
    else:
        return JSONResponse(
            status_code=503,
            content={"status": "not ready", "checks": checks}
        )
```

## Client SDKs

### Python Client
```python
class BacktesterClient:
    def __init__(self, base_url, api_key):
        self.base_url = base_url
        self.headers = {"Authorization": f"Bearer {api_key}"}
    
    async def submit_backtest(self, portfolio_excel, start_date, end_date):
        async with aiohttp.ClientSession() as session:
            async with session.post(
                f"{self.base_url}/api/v2/backtest",
                json={
                    "portfolio_excel": portfolio_excel,
                    "start_date": start_date,
                    "end_date": end_date
                },
                headers=self.headers
            ) as response:
                return await response.json()
    
    async def get_status(self, job_id):
        async with aiohttp.ClientSession() as session:
            async with session.get(
                f"{self.base_url}/api/v2/backtest/{job_id}",
                headers=self.headers
            ) as response:
                return await response.json()
```

### JavaScript/TypeScript Client
```typescript
class BacktesterClient {
    constructor(private baseUrl: string, private apiKey: string) {}
    
    async submitBacktest(params: BacktestParams): Promise<BacktestResponse> {
        const response = await fetch(`${this.baseUrl}/api/v2/backtest`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${this.apiKey}`
            },
            body: JSON.stringify(params)
        });
        return response.json();
    }
    
    async getStatus(jobId: string): Promise<JobStatus> {
        const response = await fetch(`${this.baseUrl}/api/v2/backtest/${jobId}`, {
            headers: {
                'Authorization': `Bearer ${this.apiKey}`
            }
        });
        return response.json();
    }
}
```

## Migration Strategy

### Phase 1: API Wrappers (Week 1-2)
1. Deploy Flask API for legacy system
2. Deploy FastAPI for GPU system
3. Basic authentication and monitoring

### Phase 2: Infrastructure (Week 3-4)
1. Set up Redis cluster
2. Deploy Celery workers
3. Configure load balancers

### Phase 3: Security & Monitoring (Week 5-6)
1. Implement JWT authentication
2. Set up Prometheus/Grafana
3. Configure alerts

### Phase 4: Optimization (Week 7-8)
1. Implement caching
2. Optimize database queries
3. Performance testing

### Phase 5: Production (Week 9-10)
1. Deploy to production environment
2. Client SDK development
3. Documentation and training

## Cost Optimization

### 1. GPU Instance Management
- Use spot instances for non-critical workloads
- Implement auto-scaling based on queue depth
- Schedule GPU instances for business hours only

### 2. Storage Optimization
- Compress result files before storage
- Implement lifecycle policies for old results
- Use tiered storage (hot/cold)

### 3. Network Optimization
- Use CDN for result delivery
- Implement edge caching
- Compress API responses

## Disaster Recovery

### 1. Backup Strategy
- Daily database backups
- Replicate to multiple regions
- Test restore procedures monthly

### 2. Failover Plan
- Active-passive setup for critical services
- Automated failover with health checks
- DNS-based traffic routing

### 3. Business Continuity
- Document all procedures
- Regular disaster recovery drills
- Maintain runbooks for common issues 