# Golden Output Format Specification

Generated from: Nifty_Golden_Ouput.xlsx
Analysis Date: 2025-06-09T17:36:17.928787

## Sheet Structure

### PortfolioParameter

- **Rows**: 21
- **Columns**: 2

#### Column Specification

| # | Column Name | Data Type | Non-Null | Unique | Sample Values |
|---|-------------|-----------|----------|---------|---------------|
| 1 | Head | object | 21 | 21 | StartDate, EndDate, IsTickBT |
| 2 | Value | object | 21 | 10 | 01_04_2025, 11_04_2025, no |

### GeneralParameter

- **Rows**: 1
- **Columns**: 36

#### Column Specification

| # | Column Name | Data Type | Non-Null | Unique | Sample Values |
|---|-------------|-----------|----------|---------|---------------|
| 1 | StrategyName | object | 1 | 1 | RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL |
| 2 | MoveSlToCost | object | 1 | 1 | no |
| 3 | Underlying | object | 1 | 1 | SPOT |
| 4 | Index | object | 1 | 1 | NIFTY |
| 5 | Weekdays | object | 1 | 1 | 1,2,3,4,5 |
| 6 | DTE | int64 | 1 | 1 | 0.0 |
| 7 | StrikeSelectionTime | int64 | 1 | 1 | 91600.0 |
| 8 | StartTime | int64 | 1 | 1 | 91600.0 |
| 9 | LastEntryTime | int64 | 1 | 1 | 120000.0 |
| 10 | EndTime | int64 | 1 | 1 | 120000.0 |
| 11 | StrategyProfit | int64 | 1 | 1 | 0.0 |
| 12 | StrategyLoss | int64 | 1 | 1 | 0.0 |
| 13 | StrategyProfitReExecuteNo | int64 | 1 | 1 | 0.0 |
| 14 | StrategyLossReExecuteNo | int64 | 1 | 1 | 0.0 |
| 15 | StrategyTrailingType | object | 1 | 1 | Lock & Trail Profits |
| 16 | PnLCalTime | int64 | 1 | 1 | 230000.0 |
| 17 | LockPercent | int64 | 1 | 1 | 0.0 |
| 18 | TrailPercent | int64 | 1 | 1 | 0.0 |
| 19 | SqOff1Time | int64 | 1 | 1 | 230000.0 |
| 20 | SqOff1Percent | int64 | 1 | 1 | 0.0 |
| 21 | SqOff2Time | int64 | 1 | 1 | 230000.0 |
| 22 | SqOff2Percent | int64 | 1 | 1 | 0.0 |
| 23 | ProfitReaches | int64 | 1 | 1 | 0.0 |
| 24 | LockMinProfitAt | int64 | 1 | 1 | 0.0 |
| 25 | IncreaseInProfit | int64 | 1 | 1 | 0.0 |
| 26 | TrailMinProfitBy | int64 | 1 | 1 | 0.0 |
| 27 | TgtTrackingFrom | object | 1 | 1 | high/low |
| 28 | TgtRegisterPriceFrom | object | 1 | 1 | tracking |
| 29 | SlTrackingFrom | object | 1 | 1 | high/low |
| 30 | SlRegisterPriceFrom | object | 1 | 1 | tracking |
| 31 | PnLCalculationFrom | object | 1 | 1 | close |
| 32 | ConsiderHedgePnLForStgyPnL | object | 1 | 1 | no |
| 33 | StoplossCheckingInterval | int64 | 1 | 1 | 1.0 |
| 34 | TargetCheckingInterval | int64 | 1 | 1 | 1.0 |
| 35 | ReEntryCheckingInterval | int64 | 1 | 1 | 1.0 |
| 36 | OnExpiryDayTradeNextExpiry | object | 1 | 1 | no |

### LegParameter

- **Rows**: 4
- **Columns**: 38

#### Column Specification

| # | Column Name | Data Type | Non-Null | Unique | Sample Values |
|---|-------------|-----------|----------|---------|---------------|
| 1 | StrategyName | object | 4 | 1 | RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL, RS,9... |
| 2 | IsIdle | object | 4 | 1 | no, no, no |
| 3 | LegID | int64 | 4 | 4 | 1.0, 2.0, 3.0 |
| 4 | Instrument | object | 4 | 2 | call, put, call |
| 5 | Transaction | object | 4 | 2 | sell, sell, buy |
| 6 | Expiry | object | 4 | 1 | current, current, current |
| 7 | W&Type | object | 4 | 1 | percentage, percentage, percentage |
| 8 | W&TValue | int64 | 4 | 1 | 0.0, 0.0, 0.0 |
| 9 | TrailW&T | object | 4 | 1 | no, no, no |
| 10 | StrikeMethod | object | 4 | 1 | atm, atm, atm |
| 11 | MatchPremium | object | 4 | 1 | high, high, high |
| 12 | StrikeValue | int64 | 4 | 2 | 0.0, 0.0, 2.0 |
| 13 | StrikePremiumCondition | object | 4 | 1 | =, =, = |
| 14 | SLType | object | 4 | 1 | percentage, percentage, percentage |
| 15 | SLValue | int64 | 4 | 2 | 100.0, 100.0, 0.0 |
| 16 | TGTType | object | 4 | 1 | percentage, percentage, percentage |
| 17 | TGTValue | int64 | 4 | 1 | 0.0, 0.0, 0.0 |
| 18 | TrailSLType | object | 4 | 1 | percentage, percentage, percentage |
| 19 | SL_TrailAt | int64 | 4 | 1 | 0.0, 0.0, 0.0 |
| 20 | SL_TrailBy | int64 | 4 | 1 | 0.0, 0.0, 0.0 |
| 21 | Lots | int64 | 4 | 1 | 1.0, 1.0, 1.0 |
| 22 | ReEntryType | object | 4 | 1 | instant new strike, instant new strike, instant... |
| 23 | ReEnteriesCount | int64 | 4 | 1 | 0.0, 0.0, 0.0 |
| 24 | OnEntry_OpenTradeOn | int64 | 4 | 1 | 0.0, 0.0, 0.0 |
| 25 | OnEntry_SqOffTradeOff | int64 | 4 | 1 | 0.0, 0.0, 0.0 |
| 26 | OnEntry_SqOffAllLegs | object | 4 | 1 | no, no, no |
| 27 | OnEntry_OpenTradeDelay | int64 | 4 | 1 | 0.0, 0.0, 0.0 |
| 28 | OnEntry_SqOffDelay | int64 | 4 | 1 | 0.0, 0.0, 0.0 |
| 29 | OnExit_OpenTradeOn | int64 | 4 | 1 | 0.0, 0.0, 0.0 |
| 30 | OnExit_SqOffTradeOff | int64 | 4 | 1 | 0.0, 0.0, 0.0 |
| 31 | OnExit_SqOffAllLegs | object | 4 | 1 | no, no, no |
| 32 | OnExit_OpenAllLegs | object | 4 | 1 | no, no, no |
| 33 | OnExit_OpenTradeDelay | int64 | 4 | 1 | 0.0, 0.0, 0.0 |
| 34 | OnExit_SqOffDelay | int64 | 4 | 1 | 0.0, 0.0, 0.0 |
| 35 | OpenHedge | object | 4 | 1 | No, No, No |
| 36 | HedgeStrikeMethod | object | 4 | 1 | atm, atm, atm |
| 37 | HedgeStrikeValue | int64 | 4 | 1 | 0.0, 0.0, 0.0 |
| 38 | HedgeStrikePremiumCondition | object | 4 | 1 | =, =, = |

**Note**: The following columns contain formulas: StrikePremiumCondition, HedgeStrikePremiumCondition

### Metrics

- **Rows**: 25
- **Columns**: 3

#### Column Specification

| # | Column Name | Data Type | Non-Null | Unique | Sample Values |
|---|-------------|-----------|----------|---------|---------------|
| 1 | Particulars | object | 25 | 25 | Backtest Start Date, Backtest End Date, Margin ... |
| 2 | Combined | object | 24 | 15 | 2025-04-03T00:00:00, 2025-04-09T00:00:00, 79781.25 |
| 3 | RS,916-1200,ATM-SELL,OTM2-BUY WITH 100%SL | object | 24 | 15 | 2025-04-03T00:00:00, 2025-04-09T00:00:00, 79781.25 |

### Max Profit and Loss

- **Rows**: 8
- **Columns**: 5

#### Column Specification

| # | Column Name | Data Type | Non-Null | Unique | Sample Values |
|---|-------------|-----------|----------|---------|---------------|
| 1 | Date | datetime64[ns] | 8 | 8 | 2025-04-01T00:00:00, 2025-04-02T00:00:00, 2025-... |
| 2 | Max Profit | float64 | 8 | 3 | 0.0, 0.0, 960.0 |
| 3 | Max Profit Time | object | 2 | 2 | 10:01:00, 12:00:00 |
| 4 | Max Loss | float64 | 8 | 3 | 0.0, 0.0, -1797.0 |
| 5 | Max Loss Time | object | 2 | 2 | 11:21:00, 09:17:00 |

### PORTFOLIO Trans

- **Rows**: 8
- **Columns**: 32

#### Column Specification

| # | Column Name | Data Type | Non-Null | Unique | Sample Values |
|---|-------------|-----------|----------|---------|---------------|
| 1 | Portfolio Name | object | 8 | 1 | NIF0DTE, NIF0DTE, NIF0DTE |
| 2 | Strategy Name | object | 8 | 1 | RS,916-1200,ATM-SELL,OTM2-BUY WITH 100%SL, RS,9... |
| 3 | ID | int64 | 8 | 4 | 1.0, 2.0, 3.0 |
| 4 | Entry Date | datetime64[ns] | 8 | 2 | 2025-04-03T00:00:00, 2025-04-03T00:00:00, 2025-... |
| 5 | Enter On | object | 8 | 1 | 09:16:00, 09:16:00, 09:16:00 |
| 6 | Entry Day | object | 8 | 2 | Thursday, Thursday, Thursday |
| 7 | Exit Date | datetime64[ns] | 8 | 2 | 2025-04-03T00:00:00, 2025-04-03T00:00:00, 2025-... |
| 8 | Exit at | object | 8 | 2 | 10:24:00, 12:00:00, 12:00:00 |
| 9 | Exit Day | object | 8 | 2 | Thursday, Thursday, Thursday |
| 10 | Index | object | 8 | 1 | NIFTY, NIFTY, NIFTY |
| 11 | Expiry | datetime64[ns] | 8 | 2 | 2025-04-03T00:00:00, 2025-04-03T00:00:00, 2025-... |
| 12 | Strike | int64 | 8 | 6 | 23200.0, 23200.0, 23300.0 |
| 13 | CE/PE | object | 8 | 2 | CALL, PUT, CALL |
| 14 | Trade | object | 8 | 2 | SELL, SELL, BUY |
| 15 | Qty | int64 | 8 | 1 | 75.0, 75.0, 75.0 |
| 16 | Entry at | float64 | 8 | 8 | 48.25, 75.55, 14.7 |
| 17 | Exit at.1 | float64 | 8 | 8 | 96.5, 4.25, 9.69 |
| 18 | Points | float64 | 8 | 8 | -48.25, 71.3, -5.01 |
| 19 | Points After Slippage | float64 | 8 | 8 | -48.39474999999999, 71.22019999999999, -5.03438... |
| 20 | PNL | float64 | 8 | 8 | -3618.75, 5347.5, -375.75 |
| 21 | AfterSlippage | float64 | 8 | 8 | -3629.60625, 5341.514999999999, -377.5792499999999 |
| 22 | Taxes | int64 | 8 | 1 | 0.0, 0.0, 0.0 |
| 23 | Net PNL | float64 | 8 | 8 | -3629.60625, 5341.514999999999, -377.5792499999999 |
| 24 | Re-entry No | int64 | 8 | 1 | 0.0, 0.0, 0.0 |
| 25 | SL Re-entry No | float64 | 0 | 0 |  |
| 26 | TGT Re-entry No | float64 | 0 | 0 |  |
| 27 | Reason | object | 8 | 2 | Stop Loss Hit, Exit Time Hit, Exit Time Hit |
| 28 | Strategy Entry No | int64 | 8 | 1 | 0.0, 0.0, 0.0 |
| 29 | Index At Entry | float64 | 8 | 2 | 23193.7, 23193.7, 23193.7 |
| 30 | Index At Exit | float64 | 8 | 3 | 23283.35, 23289.25, 23289.25 |
| 31 | MaxProfit | float64 | 8 | 7 | 386.2499999999999, 5377.5, 1597.5 |
| 32 | MaxLoss | float64 | 8 | 8 | 5006.25, 0.0, 607.5 |

### PORTFOLIO Results

- **Rows**: 12
- **Columns**: 14

#### Column Specification

| # | Column Name | Data Type | Non-Null | Unique | Sample Values |
|---|-------------|-----------|----------|---------|---------------|
| 1 | Year | object | 8 | 3 | 2025.0, Total, Year |
| 2 | Monday | object | 8 | 2 | 0.0, 0.0, January |
| 3 | Tuesday | object | 8 | 2 | 0.0, 0.0, February |
| 4 | Wednesday | object | 8 | 3 | 4526.857500000005, 4526.857500000005, March |
| 5 | Thursday | object | 8 | 4 | -1462.58175, -1462.58175, April |
| 6 | Friday | object | 8 | 2 | 0.0, 0.0, May |
| 7 | Saturday | object | 8 | 2 | 0.0, 0.0, June |
| 8 | Total | object | 8 | 3 | 3064.275750000004, 3064.275750000004, July |
| 9 | Unnamed: 8 | object | 6 | 2 | August, 0.0, 0.0 |
| 10 | Unnamed: 9 | object | 6 | 2 | September, 0.0, 0.0 |
| 11 | Unnamed: 10 | object | 6 | 2 | October, 0.0, 0.0 |
| 12 | Unnamed: 11 | object | 6 | 2 | November, 0.0, 0.0 |
| 13 | Unnamed: 12 | object | 6 | 2 | December, 0.0, 0.0 |
| 14 | Unnamed: 13 | object | 6 | 3 | Total, 3064.275750000004, 3064.275750000004 |

### RS,916-1200,ATM-SELL,OTM2-BUY W

- **Rows**: 8
- **Columns**: 32

#### Column Specification

| # | Column Name | Data Type | Non-Null | Unique | Sample Values |
|---|-------------|-----------|----------|---------|---------------|
| 1 | Portfolio Name | object | 8 | 1 | NIF0DTE, NIF0DTE, NIF0DTE |
| 2 | Strategy Name | object | 8 | 1 | RS,916-1200,ATM-SELL,OTM2-BUY WITH 100%SL, RS,9... |
| 3 | ID | int64 | 8 | 4 | 1.0, 2.0, 3.0 |
| 4 | Entry Date | datetime64[ns] | 8 | 2 | 2025-04-03T00:00:00, 2025-04-03T00:00:00, 2025-... |
| 5 | Enter On | object | 8 | 1 | 09:16:00, 09:16:00, 09:16:00 |
| 6 | Entry Day | object | 8 | 2 | Thursday, Thursday, Thursday |
| 7 | Exit Date | datetime64[ns] | 8 | 2 | 2025-04-03T00:00:00, 2025-04-03T00:00:00, 2025-... |
| 8 | Exit at | object | 8 | 2 | 10:24:00, 12:00:00, 12:00:00 |
| 9 | Exit Day | object | 8 | 2 | Thursday, Thursday, Thursday |
| 10 | Index | object | 8 | 1 | NIFTY, NIFTY, NIFTY |
| 11 | Expiry | datetime64[ns] | 8 | 2 | 2025-04-03T00:00:00, 2025-04-03T00:00:00, 2025-... |
| 12 | Strike | int64 | 8 | 6 | 23200.0, 23200.0, 23300.0 |
| 13 | CE/PE | object | 8 | 2 | CALL, PUT, CALL |
| 14 | Trade | object | 8 | 2 | SELL, SELL, BUY |
| 15 | Qty | int64 | 8 | 1 | 75.0, 75.0, 75.0 |
| 16 | Entry at | float64 | 8 | 8 | 48.25, 75.55, 14.7 |
| 17 | Exit at.1 | float64 | 8 | 8 | 96.5, 4.25, 9.69 |
| 18 | Points | float64 | 8 | 8 | -48.25, 71.3, -5.01 |
| 19 | Points After Slippage | float64 | 8 | 8 | -48.39474999999999, 71.22019999999999, -5.03438... |
| 20 | PNL | float64 | 8 | 8 | -3618.75, 5347.5, -375.75 |
| 21 | AfterSlippage | float64 | 8 | 8 | -3629.60625, 5341.514999999999, -377.5792499999999 |
| 22 | Taxes | int64 | 8 | 1 | 0.0, 0.0, 0.0 |
| 23 | Net PNL | float64 | 8 | 8 | -3629.60625, 5341.514999999999, -377.5792499999999 |
| 24 | Re-entry No | int64 | 8 | 1 | 0.0, 0.0, 0.0 |
| 25 | SL Re-entry No | float64 | 0 | 0 |  |
| 26 | TGT Re-entry No | float64 | 0 | 0 |  |
| 27 | Reason | object | 8 | 2 | Stop Loss Hit, Exit Time Hit, Exit Time Hit |
| 28 | Strategy Entry No | int64 | 8 | 1 | 0.0, 0.0, 0.0 |
| 29 | Index At Entry | float64 | 8 | 2 | 23193.7, 23193.7, 23193.7 |
| 30 | Index At Exit | float64 | 8 | 3 | 23283.35, 23289.25, 23289.25 |
| 31 | MaxProfit | float64 | 8 | 7 | 386.2499999999999, 5377.5, 1597.5 |
| 32 | MaxLoss | float64 | 8 | 8 | 5006.25, 0.0, 607.5 |

### Recovered_Sheet1

- **Rows**: 12
- **Columns**: 14

#### Column Specification

| # | Column Name | Data Type | Non-Null | Unique | Sample Values |
|---|-------------|-----------|----------|---------|---------------|
| 1 | Year | object | 8 | 3 | 2025.0, Total, Year |
| 2 | Monday | object | 8 | 2 | 0.0, 0.0, January |
| 3 | Tuesday | object | 8 | 2 | 0.0, 0.0, February |
| 4 | Wednesday | object | 8 | 3 | 4526.857500000005, 4526.857500000005, March |
| 5 | Thursday | object | 8 | 4 | -1462.58175, -1462.58175, April |
| 6 | Friday | object | 8 | 2 | 0.0, 0.0, May |
| 7 | Saturday | object | 8 | 2 | 0.0, 0.0, June |
| 8 | Total | object | 8 | 3 | 3064.275750000004, 3064.275750000004, July |
| 9 | Unnamed: 8 | object | 6 | 2 | August, 0.0, 0.0 |
| 10 | Unnamed: 9 | object | 6 | 2 | September, 0.0, 0.0 |
| 11 | Unnamed: 10 | object | 6 | 2 | October, 0.0, 0.0 |
| 12 | Unnamed: 11 | object | 6 | 2 | November, 0.0, 0.0 |
| 13 | Unnamed: 12 | object | 6 | 2 | December, 0.0, 0.0 |
| 14 | Unnamed: 13 | object | 6 | 3 | Total, 3064.275750000004, 3064.275750000004 |

