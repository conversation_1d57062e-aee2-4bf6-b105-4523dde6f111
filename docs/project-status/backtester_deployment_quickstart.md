# Backtester Deployment Quick Start Guide

## Overview

This guide provides quick deployment options for both legacy (Windows) and new HeavyDB GPU-optimized (Linux) backtesters with external API access.

## Architecture Summary

```
┌─────────────┐     ┌─────────────┐     ┌─────────────┐
│   Clients   │     │   Clients   │     │   Clients   │
└──────┬──────┘     └──────┬──────┘     └──────┬──────┘
       │                   │                   │
       └───────────────────┴───────────────────┘
                           │
                    ┌──────▼──────┐
                    │  API Gateway │
                    │   (NGINX)    │
                    └──────┬──────┘
                           │
        ┌──────────────────┴──────────────────┐
        │                                      │
┌───────▼────────┐                   ┌────────▼────────┐
│  Legacy API    │                   │   GPU API       │
│  Windows:5000  │                   │   Linux:8000    │
└───────┬────────┘                   └────────┬────────┘
        │                                      │
┌───────▼────────┐                   ┌────────▼────────┐
│     MySQL      │                   │    HeavyDB      │
│ ************   │                   │  127.0.0.1:6274 │
└────────────────┘                   └─────────────────┘
```

## Quick Start Options

### Option 1: Immediate Solution (API Wrappers Only)

**Time Required**: 30 minutes

#### Windows Server (Legacy):
```powershell
# 1. Copy legacy_backtester_api.py to Windows server
# 2. Install dependencies
pip install flask redis

# 3. Enable external access
netsh advfirewall firewall add rule name="Backtester API" dir=in action=allow protocol=TCP localport=5000
netsh interface portproxy add v4tov4 listenport=5000 listenaddress=0.0.0.0 connectport=5000 connectaddress=***************

# 4. Run the API
python legacy_backtester_api.py
```

#### Linux Server (GPU):
```bash
# 1. Copy heavydb_backtester_api.py to Linux server
# 2. Install dependencies
pip3 install fastapi uvicorn redis celery

# 3. Run the API
PYTHONPATH=/srv/samba/shared/bt python3 heavydb_backtester_api.py
```

#### Test Both:
```bash
# Legacy API
curl http://************:5000/api/v1/health

# GPU API
curl http://your-gpu-server:8000/api/v2/health
```

### Option 2: Production Setup with Services

**Time Required**: 2 hours

1. **Run deployment script**:
   ```bash
   chmod +x deploy_backtester_services.sh
   ./deploy_backtester_services.sh
   ```

2. **Select option 4** for complete Linux setup

3. **For Windows**, copy the generated `setup_legacy_windows.bat` and run as Administrator

### Option 3: Docker Compose (Development)

**Time Required**: 1 hour

```bash
# 1. Create docker-compose.yml with provided configuration
# 2. Build and run
docker-compose up -d

# 3. Access services
# Legacy: http://localhost:5000
# GPU: http://localhost:8000
```

### Option 4: Kubernetes (Enterprise)

**Time Required**: 1 day

```bash
# 1. Apply Kubernetes manifests
kubectl apply -f k8s/

# 2. Get service endpoints
kubectl get services

# 3. Configure ingress for external access
```

## API Usage Examples

### Submit Backtest (Legacy)
```python
import requests

response = requests.post('http://************:5000/api/v1/backtest', json={
    'portfolio_excel': '/path/to/portfolio.xlsx',
    'start_date': '20250401',
    'end_date': '20250402'
})
job_id = response.json()['job_id']
```

### Submit Backtest (GPU)
```python
import requests

response = requests.post('http://your-gpu-server:8000/api/v2/backtest', json={
    'portfolio_excel': '/path/to/portfolio.xlsx',
    'start_date': '20250401',
    'end_date': '20250402',
    'use_gpu': True
})
job_id = response.json()['job_id']
```

### Check Status
```python
# Legacy
status = requests.get(f'http://************:5000/api/v1/backtest/{job_id}').json()

# GPU
status = requests.get(f'http://your-gpu-server:8000/api/v2/backtest/{job_id}').json()
```

## Security Checklist

- [ ] Enable HTTPS with SSL certificates
- [ ] Implement API authentication (JWT/OAuth2)
- [ ] Set up rate limiting
- [ ] Configure firewall rules
- [ ] Enable monitoring and logging
- [ ] Regular security updates
- [ ] Backup strategy in place

## Performance Optimization

### Legacy System
- Limited to CPU processing
- Consider load balancing multiple instances
- Optimize Excel file I/O

### GPU System
- Leverage HeavyDB GPU acceleration
- Scale Celery workers based on load
- Use Redis clustering for high availability
- Implement result caching

## Monitoring

### Quick Monitoring Setup
```bash
# Install Prometheus node exporter
wget https://github.com/prometheus/node_exporter/releases/download/v1.5.0/node_exporter-1.5.0.linux-amd64.tar.gz
tar xvf node_exporter-1.5.0.linux-amd64.tar.gz
./node_exporter-1.5.0.linux-amd64/node_exporter &

# Access metrics
curl http://localhost:9100/metrics
```

### Grafana Dashboard
1. Import dashboard ID: 1860 (Node Exporter Full)
2. Add custom panels for:
   - Backtest job queue depth
   - Average processing time
   - Success/failure rates

## Troubleshooting

### Common Issues

1. **Legacy API not accessible**
   - Check Windows firewall
   - Verify port forwarding: `netsh interface portproxy show all`
   - Test internal service: `curl http://***************:5000/health`

2. **GPU API slow**
   - Check HeavyDB connection: `heavysql -s 127.0.0.1 -u admin -p HyperInteractive`
   - Monitor GPU usage: `nvidia-smi`
   - Check Redis queue: `redis-cli llen celery`

3. **Authentication errors**
   - Verify API keys in environment variables
   - Check JWT token expiration
   - Review auth service logs

## Migration Path

### Phase 1 (Week 1)
- Deploy API wrappers
- Test with small workloads
- Monitor performance

### Phase 2 (Week 2-3)
- Implement authentication
- Set up monitoring
- Begin user migration

### Phase 3 (Week 4)
- Full production deployment
- Decommission direct access
- Documentation and training

## Support Contacts

- **Legacy System**: Windows Admin Team
- **GPU System**: DevOps Team
- **Database Issues**: DBA Team
- **API Issues**: Development Team

## Next Steps

1. Choose deployment option based on requirements
2. Follow security checklist
3. Set up monitoring
4. Test with sample workloads
5. Plan migration schedule
6. Train users on new API

## Additional Resources

- Full architecture document: `enterprise_backtester_architecture.md`
- API documentation: Auto-generated at `/docs` endpoint
- Client examples: `backtest_client_example.py`
- Deployment scripts: `deploy_backtester_services.sh` 