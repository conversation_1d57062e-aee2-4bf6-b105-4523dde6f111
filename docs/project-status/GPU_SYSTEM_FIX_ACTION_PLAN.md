# GPU System Critical Fix Action Plan

**Date**: June 9, 2025  
**Priority**: 🔴 CRITICAL - BLOCKING ALL TESTING  
**Owner**: Backend Development Team

---

## Executive Summary

The GPU backtester system has critical implementation issues that are causing 100% PnL variance with the archive system. These issues must be fixed immediately before any further E2E testing can proceed.

## Critical Issues Identified

### 1. ATM Calculation Incorrect
- **Symptom**: 400 point difference in strike selection (Archive: 22100, GPU: 22500)
- **Root Cause**: GPU system not implementing synthetic future ATM correctly
- **Impact**: All option strategies affected

### 2. Trade Completion Logic Broken
- **Symptom**: All trades remain in OPEN status, never closed
- **Root Cause**: Exit logic not triggering properly
- **Impact**: PnL shows as 0 for all trades

### 3. Multi-Leg Execution Failing
- **Symptom**: Only 1 leg executed out of 4 expected
- **Root Cause**: Portfolio/strategy linking not working
- **Impact**: Complex strategies cannot be tested

## Fix Implementation Plan

### Step 1: Fix ATM Calculation (4 hours)
```python
# File: /srv/samba/shared/bt/backtester_stable/BTRUN/query_builder/strike_selection.py

def get_atm_strike(data, spot_price, strike_increment):
    """
    CRITICAL FIX: Ensure synthetic future calculation matches HeavyDB
    """
    # Current issue: Not calculating synthetic future correctly
    # Fix: Implement exact formula as per HeavyDB views
    
    df = data.copy()
    
    # Step 1: Calculate synthetic future for ALL strikes
    df['synthetic_future'] = df['strike'] + df['ce_close'] - df['pe_close']
    
    # Step 2: Find strike where synthetic future is closest to spot
    df['diff'] = abs(df['synthetic_future'] - spot_price)
    atm_row = df.loc[df['diff'].idxmin()]
    
    # Step 3: Return the exact strike (no rounding)
    return float(atm_row['strike'])
```

### Step 2: Fix Trade Completion Logic (3 hours)
```python
# File: /srv/samba/shared/bt/backtester_stable/BTRUN/refactored_core/exit_logic.py

def check_exit_conditions(trade, current_data, strategy_params):
    """
    CRITICAL FIX: Ensure trades are properly closed
    """
    # Current issue: Exit conditions not being evaluated
    # Fix: Implement proper exit condition checks
    
    # Check time-based exit
    if current_time >= strategy_params['exit_time']:
        return True, "Time-based exit"
    
    # Check SL/TP conditions
    if trade.pnl <= strategy_params['stop_loss']:
        return True, "Stop loss hit"
    
    if trade.pnl >= strategy_params['target_profit']:
        return True, "Target profit hit"
    
    return False, None
```

### Step 3: Fix Multi-Leg Execution (2 hours)
```python
# File: /srv/samba/shared/bt/backtester_stable/BTRUN/refactored_strategies/tbs_strategy.py

def execute_portfolio_legs(portfolio_config, strategy_config):
    """
    CRITICAL FIX: Ensure all legs are executed
    """
    # Current issue: Only first leg being processed
    # Fix: Iterate through ALL legs in portfolio
    
    trades = []
    for leg_id, leg_config in portfolio_config['legs'].items():
        trade = execute_single_leg(leg_config, strategy_config)
        trades.append(trade)
    
    return trades
```

## Verification Plan

### 1. Unit Tests (1 hour)
- Test ATM calculation with known values
- Test trade completion scenarios
- Test multi-leg execution

### 2. Integration Test (2 hours)
- Run exact same TBS portfolio as archive
- Verify 4 trades executed
- Verify trades closed properly
- Verify PnL calculated

### 3. Comparison Test (1 hour)
- Re-run full comparison
- Verify PnL variance < 5%
- Document any remaining differences

## Expected Outcomes

After implementing these fixes:
1. **Strike Selection**: Within 50 points (1 ATM interval)
2. **Trade Count**: Exact match (4 trades)
3. **Trade Status**: All trades properly closed
4. **PnL Variance**: < 5% between systems

## Timeline

- **Total Time**: 1-2 days
- **Day 1**: Implement fixes (9 hours)
- **Day 2**: Testing and verification (4 hours)

## Success Criteria

1. GPU system selects same ATM strikes as archive (within 1 interval)
2. All trades are properly closed with calculated PnL
3. Multi-leg strategies execute all legs
4. PnL variance between systems < 5%

## Next Steps After Fix

1. Re-run TBS comparison tests
2. Update E2E testing plan with results
3. Proceed to Phase 3.2 (TV Strategy Testing)
4. Continue with remaining strategy tests

---

**Note**: This is a BLOCKING issue. No further E2E testing should proceed until these fixes are implemented and verified.