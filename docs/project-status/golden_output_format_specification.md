# Golden Output Format Specification

Based on analysis of `/srv/samba/shared/Nifty_Golden_Ouput.xlsx`, this document specifies the exact format requirements for the backtester output.

## Excel File Structure

The output Excel file must contain the following sheets in this order:

1. **PortfolioParameter** - Portfolio-level configuration
2. **GeneralParameter** - Strategy general settings
3. **LegParameter** - Individual leg settings
4. **Metrics** - Performance metrics summary
5. **Max Profit and Loss** - Daily P&L tracking
6. **PORTFOLIO Trans** - All trades (combined)
7. **PORTFOLIO Results** - Day-wise P&L summary
8. **[Strategy Name]** - Individual strategy trades
9. **Recovered_Sheet1** - Additional results sheet

## Sheet Specifications

### 1. PortfolioParameter Sheet
- **Structure**: 2 columns (Head, Value)
- **Key rows**:
  - StartDate: Format as "DD_MM_YYYY" (string)
  - EndDate: Format as "DD_MM_YYYY" (string)
  - IsTickBT: "yes" or "no"

### 2. GeneralParameter Sheet
- **36 columns** containing strategy configuration
- **Time format**: Integer HHMMSS (e.g., 91600 = 09:16:00)
- **Key columns**:
  - StrategyName: Full strategy name with commas
  - Time columns: StrikeSelectionTime, StartTime, LastEntryTime, EndTime
  - All stored as integers in HHMMSS format

### 3. LegParameter Sheet
- **38 columns** for leg configuration
- Multiple rows (one per leg)
- Contains strike methods, SL/TGT values, re-entry settings

### 4. Metrics Sheet
- **Structure**: 3 columns (Particulars, Combined, [Strategy Name])
- **25 standard metrics rows** including:
  - Backtest Start/End Date (datetime format)
  - Margin Required (float with 2 decimals)
  - Number of Trading Days
  - Total PnL, Average Profit/Loss
  - Win Rate, Max Drawdown, etc.

### 5. Max Profit and Loss Sheet
- **5 columns**: Date, Max Profit, Max Profit Time, Max Loss, Max Loss Time
- **Date format**: datetime64[ns]
- **Time format**: "HH:MM:SS" string (e.g., "10:01:00")
- One row per trading day

### 6. PORTFOLIO Trans Sheet (Most Important)
- **32 columns** with all trade details
- **Column specifications**:

| Column | Type | Format/Example |
|--------|------|----------------|
| Portfolio Name | string | "NIF0DTE" |
| Strategy Name | string | Full strategy name |
| ID | int64 | Trade ID |
| Entry Date | datetime64[ns] | 2025-04-03 00:00:00 |
| Enter On | string | "09:16:00" |
| Entry Day | string | "Thursday" |
| Exit Date | datetime64[ns] | 2025-04-03 00:00:00 |
| Exit at | string | "10:24:00" |
| Exit Day | string | "Thursday" |
| Index | string | "NIFTY" |
| Expiry | datetime64[ns] | 2025-04-03 00:00:00 |
| Strike | int64 | 22950 |
| CE/PE | string | "CE" or "PE" |
| Trade | string | "BUY" or "SELL" |
| Qty | int64 | 75 |
| Entry at | float64 | 48.25 (2 decimals) |
| Exit at.1 | float64 | 96.50 (2 decimals) |
| Points | float64 | -48.25 |
| Points After Slippage | float64 | -48.25 |
| PNL | float64 | -3618.75 |
| AfterSlippage | float64 | -3618.75 |
| Taxes | int64 | 0 |
| Net PNL | float64 | -3629.60625 |
| Re-entry No | int64 | 0 |
| SL Re-entry No | float64 | NaN |
| TGT Re-entry No | float64 | NaN |
| Reason | string | "TIME-EXIT" or "SL-HIT" |
| Strategy Entry No | int64 | 1 |
| Index At Entry | float64 | 22936.9 |
| Index At Exit | float64 | 22887.65 |
| MaxProfit | float64 | 386.25 |
| MaxLoss | float64 | 5006.25 |

### 7. PORTFOLIO Results Sheet
- Day-wise P&L summary grid
- Columns: Year, Monday-Saturday, Total
- Contains weekly/monthly summaries

### 8. Strategy-specific Sheet
- Same structure as PORTFOLIO Trans
- Contains only trades for that specific strategy
- Sheet name matches strategy name (truncated if too long)

## Important Format Rules

### Date/Time Formats
1. **Date parameters**: "DD_MM_YYYY" string format in PortfolioParameter
2. **Trade dates**: datetime64[ns] in YYYY-MM-DD HH:MM:SS format
3. **Time parameters**: Integer HHMMSS in GeneralParameter
4. **Trade times**: "HH:MM:SS" string format in transaction sheets

### Numeric Precision
1. **Prices (Entry at, Exit at)**: 2 decimal places
2. **Points**: 2 decimal places  
3. **PNL values**: Up to 12-13 decimal places for precision
4. **Percentages**: Integer or 2 decimal places
5. **Strike prices**: Integer values only

### String Formats
1. **Strategy names**: Can contain commas, spaces, special characters
2. **CE/PE**: Uppercase only
3. **Trade type**: "BUY" or "SELL" (uppercase)
4. **Exit reasons**: "TIME-EXIT", "SL-HIT", "TGT-HIT", etc.
5. **Day names**: Full names (e.g., "Thursday")

### Special Considerations
1. Empty cells should be NaN for numeric columns
2. Maintain column order exactly as specified
3. Sheet names must match exactly (case-sensitive)
4. Strategy sheet names may be truncated if too long
5. All datetime values should be timezone-naive

## Validation Checklist
- [ ] All 9 sheets present in correct order
- [ ] Column names match exactly (including "Exit at.1" not "Exit at")
- [ ] Date/time formats consistent
- [ ] Numeric precision maintained
- [ ] Strategy names consistent across sheets
- [ ] Metrics calculations correct
- [ ] Daily P&L summaries match trade details