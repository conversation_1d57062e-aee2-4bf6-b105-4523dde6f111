# Enterprise Integration Plan: StrikeRule & Parser Logic

## Overview
This document details the integration of an enterprise-grade `StrikeRule` enum and robust parser logic for option strategy backtesting. It covers the process, encountered issues, debugging steps, and best practices for future-proof, maintainable enterprise solutions.

---

## 1. Scope & Motivation
- **Goal:** Support all legacy and modern strike selection rules in a single, extensible enum and parser.
- **Context:** Needed for robust, maintainable, and scalable option strategy backtesting in enterprise environments.
- **Requirements:**
  - All legacy, comparative, and engine/internal strike selection rules must be supported.
  - Dynamic N-ITM/OTM (e.g., "2OTM", "3ITM") must be handled in parser logic, not as static enum members.
  - Code must be clear, well-documented, and testable.

---

## 2. Implementation Summary

### 2.1. Enum Upgrade
- `StrikeRule` enum in `bt/backtester_stable/BTRUN/models/common.py` expanded to include:
  - Basic/legacy: `ATM`, `ITM`, `OTM`, `FIXED`, `DELTA`, `PREMIUM`
  - Target/comparative: `DELTA_TARGET`, `PREMIUM_EQ`
  - Special/legacy: `ATM_WIDTH`, `STRADDLE_WIDTH`, `ATM_MATCH`, `ATM_DIFF`, `PREMIUM_VARIABLE2`
  - Engine/internal: `BY_ATM_STRIKE`, `BY_ATM_STRADDLE`, `BY_MATCH_PREMIUM`, `BY_MIN_STRIKE_DIFF`, `BY_CLOSEST_PREMIUM`, `BY_CLOSEST_DELTA`, `BY_GE_PREMIUM`, `BY_GE_DELTA`, `BY_LE_PREMIUM`, `BY_LE_DELTA`, `BY_DYNAMIC_FACTOR`, `BY_VALUE`
- **Note:** Dynamic N-ITM/OTM handled in parser logic, not as static enum members.

### 2.2. Parser Logic
- `bt/backtester_stable/BTRUN/excel_parser/strategy_parser.py` updated:
  - `LEG_STRIKE_MAP` covers all enum values.
  - `_parse_leg_row` robustly parses both static and dynamic strike rules (e.g., "2OTM").
  - Option type mapping fixed: both `CE`/`CALL` map to `OptionType.CALL`, `PE`/`PUT` to `OptionType.PUT`.
  - All required fields for `LegModel` are populated.

---

## 3. Testing & Debugging

### 3.1. Test Coverage
- `tests/test_common.py` extended to:
  - Assert all new `StrikeRule` members exist.
  - Test dynamic N-ITM/OTM parsing logic.

### 3.2. Debugging Process
- Initial test failures due to import path confusion: tests were importing the wrong `StrikeRule` enum (from top-level `models/` instead of BTRUN).
- Fixed by patching the test to import directly from the correct package.
- Further failures due to `OptionType` mapping (`CE`/`PE` not present in enum). Fixed by updating parser logic.
- Added debug output to quickly identify and resolve issues.
- Cleaned all `.pyc` and `__pycache__` files to ensure no stale bytecode.

---

## 4. Best Practices & Lessons Learned
- **Enum Consistency:** Always ensure only one canonical enum definition is used in the codebase and tests.
- **Explicit Imports:** In multi-root or legacy codebases, always import from the intended package/module.
- **Parser Robustness:** Handle all legacy and modern input variants (e.g., `CE`/`CALL`, `PE`/`PUT`).
- **Dynamic Logic:** For dynamic rules, use the archive convention: `ITM1`, `OTM2`, etc. (uppercase, number after label). Accept both `NITM`/`NOTM` and `ITM1`/`OTM2` as input for compatibility, but always output and store as `ITM1`/`OTM2`.
- **Test Alignment:** Ensure test data matches the model signature exactly (field names, types).
- **Debugging:** Add debug output to exception blocks in critical parsing/model code.
- **Bytecode Hygiene:** Clean `.pyc` and `__pycache__` after major refactors or enum changes.

---

## 5. Next Steps
- Integrate similar patterns for other enums and parser logic as needed.
- Maintain this plan as new rules or requirements emerge.
- Use this document as a reference for onboarding and future enterprise integration work.

---

**Document last updated:** [auto-generated] 