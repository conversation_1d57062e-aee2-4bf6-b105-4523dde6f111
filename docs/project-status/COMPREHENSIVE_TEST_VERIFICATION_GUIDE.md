# Comprehensive Test Verification Guide

## Overview

This guide helps you verify that all TBS column functionalities are working correctly after running the comprehensive test.

## Test Scenarios Created

### 1. Strike Selection Tests (4 strategies)
- **atm_all_variations**: ATM with offsets (0, +2, -2, +5)
- **itm_otm_methods**: Direct ITM1, OTM1, ITM3, OTM3
- **fixed_premium_strikes**: FIXED strike, PREMIUM based (GT, LT, EQ)
- **multi_expiry_test**: CW, NW, CWM, NWM expiries

### 2. Risk Management Tests (4 strategies)
- **all_sl_types**: PERCENT, POINTS, PREMIUM, NONE
- **all_target_types**: PERCENT, POINTS, PREMIUM targets
- **trailing_sl_all_types**: Trailing with different types
- **strategy_level_risk**: Strategy P&L limits and trailing

### 3. Re-entry Tests (3 strategies)
- **leg_reentry_all_types**: ASPERSTRIKE and STRIKE re-entry
- **strategy_reexecute**: Re-execution on profit/loss
- **complex_reentry_conditions**: Entry/exit actions

### 4. Advanced Features (4 strategies)
- **partial_exits_test**: SqOff at 11 AM (50%) and 2 PM (100%)
- **hedge_management_test**: OpenHedge with different methods
- **time_based_features**: Different time parameters
- **idle_and_wait_trade**: IsIdle and W&T features

### 5. Edge Cases (3 strategies)
- **maximum_complexity**: All features combined
- **dte_weekday_combo**: DTE=1 with weekday filters
- **move_sl_to_cost**: MoveSlToCost feature

## Running the Test

```bash
# Run comprehensive test with GPU optimization
python3 /srv/samba/shared/run_comprehensive_test_gpu.py
```

## Verification Checklist

### 1. Overall Execution
- [ ] All 18 strategies appear in output Excel
- [ ] No errors in log file
- [ ] Trades generated for April 2024

### 2. Strike Selection Verification

#### ATM Variations
Check in output Excel → Strategy sheets:
- [ ] ATM (StrikeValue=0) selected exact ATM strike
- [ ] ATM+2 selected 2 strikes OTM
- [ ] ATM-2 selected 2 strikes ITM

#### ITM/OTM Methods
- [ ] ITM1 selected 1st ITM strike
- [ ] OTM3 selected 3rd OTM strike

#### FIXED Strike
- [ ] FIXED 22000 selected exact 22000 strike

#### PREMIUM Based
- [ ] PREMIUM GT 50 found strike with premium > 50
- [ ] PREMIUM LT 100 found strike with premium < 100

### 3. Risk Management Verification

#### Stop Loss Types
Look for exit reasons in Trans sheets:
- [ ] "SL HIT" exits for PERCENT SL
- [ ] "POINTS SL HIT" for POINTS SL
- [ ] "PREMIUM SL HIT" for PREMIUM SL

#### Target Types
- [ ] "TGT HIT" exits for PERCENT target
- [ ] "POINTS TGT HIT" for POINTS target
- [ ] "PREMIUM TGT HIT" for PREMIUM target

#### Trailing Stop Loss
- [ ] "TRAILING SL HIT" exits
- [ ] Check if trailing activated after profit threshold

### 4. Re-entry Verification

#### Leg Level Re-entry
Check Re_Entry_No column:
- [ ] Re_Entry_No > 0 for configured legs
- [ ] ASPERSTRIKE: Same strike on re-entry
- [ ] STRIKE: New strike selection on re-entry

#### Strategy Re-execution
- [ ] Multiple strategy executions when P&L limits hit
- [ ] Check Strategy_Entry_Number column

### 5. Advanced Features

#### Partial Exits (SqOff)
Check exit times:
- [ ] Exits at 11:00:00 for 50% quantity
- [ ] Exits at 14:00:00 for remaining quantity

#### Time-based Features
- [ ] Trades entered at StartTime
- [ ] Strikes selected at StrikeSelectionTime
- [ ] No new entries after LastEntryTime

#### DTE and Weekday Filters
- [ ] DTE=0: Only Thursday trades (expiry days)
- [ ] Weekdays filter: Only specified days traded

### 6. Manual Verification Steps

#### Step 1: Open Output Excel
```bash
# Find latest output file
ls -la /srv/samba/shared/comprehensive_test_results_*.xlsx
```

#### Step 2: Check Metrics Sheet
- Total strategies executed
- Overall P&L
- Win rate across strategies

#### Step 3: Check Individual Strategy Sheets
For each strategy Trans sheet:
1. Verify trade count > 0
2. Check strike prices match expected logic
3. Verify exit reasons
4. Check re-entry numbers
5. Verify entry/exit times

#### Step 4: Verify Specific Columns

**Entry Columns:**
- Entry_Date
- Entry_Time (matches StartTime)
- Strike (matches selection method)
- Entry_Price

**Exit Columns:**
- Exit_Date
- Exit_Time
- Exit_Price
- Reason (SL/TGT/TIME/SQOFF)

**Re-entry Columns:**
- Re_Entry_No
- Strategy_Entry_Number

**P&L Columns:**
- Points
- Bookedpnl
- Netpnlafterexpenses

### 7. Common Issues & Solutions

#### No Trades Generated
- Check if data exists for April 2024
- Verify DTE and Weekday filters
- Check strike availability

#### SL/TP Not Triggering
- Verify tick data availability
- Check if values are reasonable (not too tight)
- Confirm price movements were sufficient

#### Re-entry Not Working
- Check LastEntryTime constraint
- Verify SL was actually hit
- Check ReEnteriesCount > 0

## Automated Verification

Run the auto-generated verification script:
```bash
python3 /srv/samba/shared/verify_comprehensive_test.py
```

This will check:
- Trade counts per strategy
- Exit reason distribution
- Re-entry occurrences
- Total P&L

## SQL Query Verification

To verify data processing:

```sql
-- Check strike selection for a specific date
SELECT DISTINCT strike, instrument_type, close_price
FROM nifty_option_chain
WHERE trade_date = '2024-04-04'
  AND time_value = '09:20:00'
ORDER BY strike;

-- Verify DTE=0 filtering
SELECT DISTINCT trade_date, expiry_date
FROM nifty_option_chain
WHERE trade_date BETWEEN '2024-04-01' AND '2024-04-30'
  AND trade_date = expiry_date;
```

## Performance Metrics

Expected performance with GPU:
- 18 strategies × 30 days = ~540 strategy-days
- With GPU: 2-5 minutes
- Without GPU: 10-20 minutes

Check GPU utilization:
```bash
# During backtest run
nvidia-smi -l 1
```

## Success Criteria

The test is successful if:
1. ✅ All 18 strategies executed
2. ✅ Each strategy generated trades
3. ✅ Strike selection methods work correctly
4. ✅ SL/TP exits occur as configured
5. ✅ Re-entries happen where set
6. ✅ Partial exits work at specified times
7. ✅ DTE and weekday filters apply correctly
8. ✅ No errors in execution

## Next Steps

After successful verification:
1. Review any failed scenarios
2. Fix identified issues
3. Re-run specific failed tests
4. Document any limitations found
5. Create production-ready strategies

---

*Comprehensive Test Verification Guide v1.0*