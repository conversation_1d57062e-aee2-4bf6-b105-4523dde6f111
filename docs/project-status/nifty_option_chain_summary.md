# Nifty Option Chain Table Summary

## Table Structure

The `nifty_option_chain` table in the HeavyDB database has been successfully created with 48 columns according to the schema specified in the `nifty_option_chain_shema.md` document. The table contains option chain data for the Nifty index with detailed metrics for calls, puts, and futures.

### Schema Overview

The table includes the following key column groups:
1. **Date/Time Fields**: `trade_date`, `trade_time`, `expiry_date`
2. **Index Information**: `index_name`, `spot`, `atm_strike`, `strike`
3. **Classification Fields**: `dte`, `expiry_bucket`, `zone_id`, `zone_name`, `call_strike_type`, `put_strike_type`
4. **Call Option Data**: 14 columns with `ce_` prefix (symbol, prices, volumes, greeks)
5. **Put Option Data**: 14 columns with `pe_` prefix (symbol, prices, volumes, greeks)
6. **Futures Data**: 7 columns with `future_` prefix (prices, volumes)

## Data Content

The table currently contains **1,408 rows** of synthetic data spanning from 2023 to 2025.

### Temporal Distribution

Data is available for the following time periods:
- **2023**: 622 rows (January: 281, June: 165, December: 176)
- **2024**: 528 rows (January: 184, June: 166, December: 178)
- **2025**: 258 rows (January: 175, April: 83)

### Expiry Classification

The data includes a variety of expiry buckets:
- **NW (Next Weekly)**: 416 rows
- **CW (Current Weekly)**: 377 rows
- **NM (Next Monthly)**: 325 rows
- **CM (Current Monthly)**: 290 rows

### Strike Classification

Strike prices are classified relative to the ATM (At-The-Money) strike:
- **ATM**: 525 rows
- **OTM1** (Out-The-Money, 1 step away): 459 rows
- **ITM1** (In-The-Money, 1 step away): 424 rows

## Implementation Details

- The table includes proper encodings for optimal GPU performance (TEXT ENCODING DICT for string columns)
- DATE and TIME fields use appropriate encodings
- The fragment_size is set to 32,000,000 for optimized query performance
- Strike classification follows a uniform 50-point increment system for Nifty

## SQL Scripts

Several SQL scripts were found that create and manage the table:
- `create_nifty_option_chain.sql`: Creates the table with full schema
- `hardcoded_inserts.sql`: Contains manual inserts with sample data
- Various test and debugging scripts in the `sql_functions/` directory

## Accessing the Data

The table is accessible via:
1. Python using the `heavyai` module
2. Direct SQL queries via HeavyDB client

## Usage Examples

Typical query patterns:
- Filter by `trade_date` and/or `expiry_date` to analyze options for specific days
- Filter by `strike_type` to focus on ATM, ITM, or OTM options
- Analyze data by `expiry_bucket` to compare current vs. next expiry behavior

## Conclusion

The `nifty_option_chain` table appears to be successfully implemented according to specifications. It contains a representative sample of synthetic data that covers various trading dates, expiry periods, and strike classifications, providing a solid foundation for testing option analytics applications. 