# NIFTY ST VWAP Strategy Backtesting Guide
## Windows to Ubuntu Server Connection and Operation Manual

---

## Table of Contents

1. [Introduction](#1-introduction)
2. [Server Information](#2-server-information)
3. [Connection Methods](#3-connection-methods)
   - [SSH/PuTTY Connection](#31-sshputty-connection)
   - [Samba File Share](#32-samba-file-share)
4. [Initial Setup](#4-initial-setup)
5. [Running Backtests](#5-running-backtests)
6. [Managing Long-Running Backtests](#6-managing-long-running-backtests)
7. [Debug Tools and Troubleshooting](#7-debug-tools-and-troubleshooting)
8. [Data Management](#8-data-management)
9. [Performance Optimization](#9-performance-optimization)
10. [Team Collaboration](#10-team-collaboration)
11. [Security Best Practices](#11-security-best-practices)
12. [Command Reference](#12-command-reference)
13. [Appendix: HeavyDB Integration](#13-appendix-heavydb-integration)

---

## 1. Introduction

This comprehensive guide provides instructions for Windows-based team members to connect to the Ubuntu server and run the NIFTY ST VWAP strategy backtests. The strategy uses HeavyDB's GPU acceleration capabilities and the `nifty_option_chain` regular view for efficient data processing and strategy backtesting.

### Strategy Overview

The NIFTY ST VWAP strategy:
- Focuses on shorting OTM options based on specific technical indicators
- Uses 3-minute candles for analysis
- Leverages both VWAP and Supertrend (10,2) indicators
- Implements profit targets and stop-loss mechanisms
- Supports multiple DTEs (Days to Expiry) testing

### Technology Stack

- **Ubuntu Server**: Hosts the backtesting environment
- **HeavyDB**: GPU-accelerated database for options data
- **Python**: Implementation language with optional GPU acceleration via cuDF/cuPy
- **Samba**: Provides Windows file sharing capabilities
- **SSH**: Secure shell access for command execution

---

## 2. Server Information

### Server Details

- **IP Address**: **************
- **SSH Access Port**: 22 (default)
- **Samba Share Path**: \\\\**************\\heavydb
- **Strategy Location**: /heavydb/bt/nifty_st_vwap

### Authentication

- **Login User**: administrator
- **Login Password**: Chetti@123
- **HeavyDB Database**:
  - Host: 127.0.0.1 (localhost on server)
  - Port: 6274
  - User: admin
  - Password: HyperInteractive
  - Database: heavyai

**Note**: It's recommended to change these default passwords for security purposes.

---

## 3. Connection Methods

### 3.1 SSH/PuTTY Connection

#### Installing PuTTY

1. Download PuTTY from the official website: [https://www.putty.org/](https://www.putty.org/)
2. Install PuTTY on your Windows machine
3. Optionally install WinSCP for graphical file transfers: [https://winscp.net/](https://winscp.net/)

#### Configuring PuTTY

1. Open PuTTY
2. Enter the connection details:
   - Host Name: **************
   - Port: 22
   - Connection Type: SSH
3. (Optional) Configure auto-login:
   - Go to Connection → Data
   - Enter "administrator" in "Auto-login username"
4. (Optional) For session persistence:
   - Go to Connection
   - Set "Seconds between keepalives" to 60
5. Save the session:
   - Go back to the Session category
   - Enter "HeavyDB-Ubuntu" in the "Saved Sessions" field
   - Click "Save"

#### Connecting to the Server

1. Select your saved session and click "Open"
2. Accept the security alert about the server's host key (first time only)
3. Enter the password when prompted
4. You should now see the Ubuntu terminal

#### Setting Up SSH Key Authentication (Recommended)

For password-less, more secure login:

1. Generate SSH keys on your Windows machine:
   ```
   # In PowerShell or Git Bash
   ssh-keygen -t rsa -b 4096
   ```

2. Copy your public key to the server:
   ```
   # Using ssh-copy-id (if available)
   ssh-copy-id administrator@**************
   
   # Alternative method
   type C:\Users\<USER>\.ssh\id_rsa.pub | ssh administrator@************** "mkdir -p ~/.ssh && cat >> ~/.ssh/authorized_keys"
   ```

3. Configure PuTTY to use your private key:
   - In PuTTY, go to Connection → SSH → Auth → Credentials
   - Browse to your private key file (typically `C:\Users\<USER>\.ssh\id_rsa.ppk`)
   - You may need to convert your key using PuTTYgen first

### 3.2 Samba File Share

#### Connecting to the Samba Share

1. Open File Explorer on your Windows machine
2. In the address bar, type: `\\**************\heavydb`
3. When prompted, enter the credentials:
   - Username: administrator
   - Password: Chetti@123
4. You should now see the shared directories, including `/bt/nifty_st_vwap`

#### Creating Shortcuts

For easier access:

1. Navigate to the strategy folder: `\\**************\heavydb\bt\nifty_st_vwap`
2. Right-click the folder and select "Create shortcut" or "Pin to Quick Access"
3. You can also map a network drive:
   - Right-click on "This PC" and select "Map network drive"
   - Drive letter: Choose any available letter (e.g., N:)
   - Folder: `\\**************\heavydb`
   - Check "Reconnect at sign-in" for persistence

#### File Editing

You can directly edit files through the Samba share using Windows applications:

1. Edit Python files with your preferred editor (VS Code, PyCharm, etc.)
2. View CSV results with Excel
3. Open PNG charts with your default image viewer

**Note**: For performance reasons, it's better to copy large files locally before opening them.

---

## 4. Initial Setup

### First-Time Environment Setup

Run these commands via SSH to prepare your environment:

```bash
# Navigate to the project directory
cd /heavydb/bt/nifty_st_vwap

# Create a Python virtual environment
python3 -m venv venv

# Activate the virtual environment
source venv/bin/activate

# Install required packages
pip install -r requirements_heavydb.txt

# Make the execution script executable
chmod +x run_heavydb_backtest.sh

# Create .env file from template
cp .env.heavydb .env

# Edit the .env file with the correct configuration
nano .env
```

### Environment Configuration in .env File

The `.env` file should contain:

```bash
# HeavyDB Connection Settings
HEAVYDB_HOST="127.0.0.1"
HEAVYDB_PORT="6274"
HEAVYDB_USER="admin"
HEAVYDB_PASSWORD="HyperInteractive"
HEAVYDB_DBNAME="heavyai"

# Backtesting Configuration
INITIAL_CAPITAL=250000.0
LOT_SIZE=50
STOP_LOSS_PCT=25.0
PROFIT_TARGET_PCT=50.0
DAILY_PROFIT_PCT=1.0
DAILY_LOSS_PCT=1.0

# GPU Acceleration
# Set to "true" to enable GPU acceleration (requires cuDF and cuPy)
USE_GPU="true"
```

### Verifying HeavyDB Connection

Test the HeavyDB connection:

```bash
# Connect to HeavyDB
/opt/heavyai/bin/heavysql -s 127.0.0.1 --port 6274 -u admin -p HyperInteractive -d heavyai

# Once connected, test a simple query
SELECT COUNT(*) FROM nifty_option_chain;

# Exit HeavyDB CLI
\q
```

---

## 5. Running Backtests

### Basic Backtest Execution

To run a basic backtest for a single DTE value:

```bash
cd /heavydb/bt/nifty_st_vwap
source venv/bin/activate
./run_heavydb_backtest.sh --start_date 240401 --end_date 240430 --dte 1
```

### Multiple DTE Testing

To test multiple DTE values in one command:

```bash
./run_heavydb_backtest.sh --start_date 240401 --end_date 240430 --dte "0,1,2,3"
```

### Custom Capital and Risk Parameters

Modify capital and risk parameters:

```bash
./run_heavydb_backtest.sh \
  --start_date 240401 \
  --end_date 240430 \
  --dte 1 \
  --capital 500000 \
  --lot_size 50 \
  --stop_loss_pct 20 \
  --profit_target_pct 40
```

### Custom Output Directory

Save results to a specific directory:

```bash
./run_heavydb_backtest.sh \
  --start_date 240401 \
  --end_date 240430 \
  --dte 1 \
  --output_dir "results/apr2024_test"
```

### Force GPU Acceleration

Explicitly enable GPU acceleration:

```bash
./run_heavydb_backtest.sh \
  --start_date 240401 \
  --end_date 240430 \
  --dte 1 \
  --gpu
```

### Understanding Backtest Results

After running a backtest, results will be saved to the specified output directory (default: `results_heavydb/`):

- **Trade logs**: CSV files containing all trades with entry/exit details
- **Daily P&L charts**: PNG files showing daily profit and loss
- **Log files**: Detailed execution logs for troubleshooting

---

## 6. Managing Long-Running Backtests

### Using Screen for Background Sessions

To keep backtests running after disconnecting from SSH:

```bash
# Install screen if not already installed
sudo apt install screen

# Create a new screen session
screen -S nifty_backtest

# Inside the screen session, run your backtest
cd /heavydb/bt/nifty_st_vwap
source venv/bin/activate
./run_heavydb_backtest.sh --start_date 240101 --end_date 240430 --dte "0,1,2,3"

# Detach from the screen session (backtest continues running)
# Press Ctrl+A then D
```

### Managing Screen Sessions

```bash
# List all screen sessions
screen -ls

# Reattach to a specific session
screen -r nifty_backtest

# Kill a screen session (if needed)
screen -X -S nifty_backtest quit
```

### Using Nohup Alternative

If screen is not available:

```bash
# Run backtest in the background with nohup
nohup ./run_heavydb_backtest.sh --start_date 240401 --end_date 240430 --dte 1 > backtest_output.log 2>&1 &

# Get the process ID
echo $!

# Check status
ps aux | grep run_heavydb_backtest.sh

# View output logs
tail -f backtest_output.log
```

---

## 7. Debug Tools and Troubleshooting

### Enabling Debug Mode

For troubleshooting and detailed logging:

```bash
./run_heavydb_backtest.sh \
  --start_date 240401 \
  --end_date 240430 \
  --dte 1 \
  --debug \
  --debug-level DEBUG \
  --debug-dir "debug/apr2024"
```

### SQL Query Tracing

To track SQL queries and performance:

```bash
./run_heavydb_backtest.sh \
  --start_date 240401 \
  --end_date 240430 \
  --dte 1 \
  --debug \
  --trace-sql
```

### Performance Profiling

For detailed performance analysis:

```bash
./run_heavydb_backtest.sh \
  --start_date 240401 \
  --end_date 240430 \
  --dte 1 \
  --debug \
  --profile
```

### Common Issues and Solutions

#### Connection Problems

**Issue**: Cannot connect to the server via SSH
**Solutions**:
- Verify network connectivity: `ping **************`
- Check VPN status if applicable
- Ensure SSH service is running on the server
- Verify firewall settings on both ends

#### HeavyDB Connection Errors

**Issue**: Cannot connect to HeavyDB
**Solutions**:
- Verify HeavyDB service is running: `ps aux | grep heavydb`
- Check connection parameters in `.env`
- Restart HeavyDB service if needed: `sudo systemctl restart heavydb`

#### Python Environment Issues

**Issue**: Missing packages or environment errors
**Solutions**:
- Recreate virtual environment:
  ```bash
  rm -rf venv
  python3 -m venv venv
  source venv/bin/activate
  pip install -r requirements_heavydb.txt
  ```
- Check for error messages in the log file: `cat heavydb_backtest.log`

#### Out of Memory Errors

**Issue**: Server running out of memory during large backtests
**Solutions**:
- Reduce date range or number of DTEs tested at once
- Close other applications on the server
- Add swap space if needed
- Monitor memory usage with `htop`

---

## 8. Data Management

### Understanding Data Flow

The backtesting process uses the following data flow:

1. Raw data stored in HeavyDB's `nifty_greeks` table
2. Processed through the `nifty_option_chain` regular view
3. Retrieved by the backtester based on date range and DTE filters
4. Processed into 3-minute candles
5. Technical indicators calculated (VWAP, Supertrend)
6. Entry/exit signals generated
7. Trades executed in the simulation
8. Results saved to the output directory

### Examining the Regular View

To understand the `nifty_option_chain` view structure:

```bash
# Connect to HeavyDB
/opt/heavyai/bin/heavysql -s 127.0.0.1 --port 6274 -u admin -p HyperInteractive -d heavyai

# Show view definition
SHOW CREATE VIEW nifty_option_chain;

# Sample data (limit rows to avoid overwhelming the server)
SELECT * FROM nifty_option_chain WHERE trade_date = '2024-04-01' AND trade_time = 91500 LIMIT 10;
```

### Data Limitations

Be aware of the following limitations:

- HeavyDB has a 128 million row processing limit per query stage
- Always use appropriate filters on dates and times to avoid hitting this limit
- For very large backtests, consider breaking them into smaller date ranges

### Backing Up Results

To back up important backtest results:

```bash
# Compress results directory
cd /heavydb/bt/nifty_st_vwap
tar -czvf backtest_results_20240430.tar.gz results_heavydb/

# Copy to Windows using WinSCP or via Samba share
# Or copy to another location on the server
cp backtest_results_20240430.tar.gz /path/to/backup/
```

---

## 9. Performance Optimization

### GPU Acceleration

The backtesting system is designed to use GPU acceleration when available:

```bash
# Check for GPU availability
nvidia-smi

# Force GPU mode in backtest
./run_heavydb_backtest.sh --start_date 240401 --end_date 240430 --dte 1 --gpu
```

### Resource Monitoring

Monitor server resources during backtests:

```bash
# Install monitoring tools if not available
sudo apt-get install htop

# Monitor overall system resources
htop

# Monitor GPU usage (if available)
watch -n 5 nvidia-smi

# Monitor disk space
df -h
```

### Optimizing HeavyDB Queries

For improved query performance:

- Use specific date ranges instead of full table scans
- Filter by DTE at the database level
- Limit the number of columns retrieved to those actually needed
- Consider time window filtering (e.g., trading hours only)

### Parallel Processing

For multiple independent backtests:

```bash
# Run backtests for different DTEs in separate screen sessions
screen -S dte0
./run_heavydb_backtest.sh --start_date 240401 --end_date 240430 --dte 0
# Detach with Ctrl+A, D

screen -S dte1
./run_heavydb_backtest.sh --start_date 240401 --end_date 240430 --dte 1
# Detach with Ctrl+A, D
```

---

## 10. Team Collaboration

### Standardized Directory Structure

Maintain a consistent organization:

```
/heavydb/bt/nifty_st_vwap/
├── src/                      # Source code
├── results_heavydb/          # Backtest results
│   ├── 20240401_dte0/        # Organized by date and DTE
│   ├── 20240401_dte1/
│   └── ...
├── debug_output/             # Debug logs and data
├── venv/                     # Python virtual environment
└── main_heavydb.py           # Main entry point
```

### Documentation

Keep notes on significant findings:

```bash
# Create a README for each important backtest
cd /heavydb/bt/nifty_st_vwap/results_heavydb/20240401_dte0/
nano README.md
```

Example README content:
```markdown
# Backtest Results: April 2024, DTE=0

## Parameters
- Date Range: 2024-04-01 to 2024-04-30
- DTE: 0
- Capital: ₹250,000
- Lot Size: 50
- Stop Loss: 25%
- Profit Target: 50%

## Key Findings
- Win Rate: 68.5%
- Profit Factor: 2.1
- Best Day: April 15 (+₹5,200)
- Worst Day: April 23 (-₹3,100)

## Notes
- Strategy performed well during low volatility periods
- Stop losses triggered frequently in the last week
- Consider adjusting profit target for DTE=0
```

### Version Control

If using Git for the codebase:

```bash
# Initialize repository (one-time setup)
cd /heavydb/bt/nifty_st_vwap
git init

# Create .gitignore
cat > .gitignore << EOF
venv/
__pycache__/
*.pyc
.env
results_heavydb/
debug_output/
*.log
EOF

# Stage and commit changes
git add .
git commit -m "Updated VWAP calculation for better accuracy"
```

---

## 11. Security Best Practices

### Password Management

- Change default passwords regularly
- Use SSH key authentication instead of passwords when possible
- Never store passwords in code or commit them to version control

### Access Control

- Create separate user accounts for team members
- Implement appropriate file permissions
- Restrict access to sensitive directories

### Network Security

- Use VPN when connecting remotely if possible
- Consider restricting SSH access to known IP addresses
- Keep the server updated with security patches

### Data Protection

- Encrypt sensitive data
- Regularly backup important files
- Implement retention policies for old backtest data

---

## 12. Command Reference

### Environment and Setup

```bash
# Activate virtual environment
source venv/bin/activate

# Deactivate virtual environment
deactivate

# Update Python packages
pip install -r requirements_heavydb.txt --upgrade

# Install a new package
pip install package_name
pip freeze > requirements_heavydb.txt
```

### Basic Operations

```bash
# Run backtest
./run_heavydb_backtest.sh --start_date 240401 --end_date 240430 --dte 1

# Check logs
tail -f heavydb_backtest.log

# View results
ls -la results_heavydb/

# Check disk space
df -h
```

### HeavyDB Commands

```bash
# Connect to HeavyDB
/opt/heavyai/bin/heavysql -s 127.0.0.1 --port 6274 -u admin -p HyperInteractive -d heavyai

# List tables
\dt

# Show table schema
\d nifty_greeks

# Execute query
SELECT COUNT(*) FROM nifty_option_chain WHERE trade_date = '2024-04-01';

# Exit
\q
```

### System Management

```bash
# Check system resources
htop

# Check GPU status
nvidia-smi

# Kill a process
kill <PID>

# Find large files
find /heavydb/bt/nifty_st_vwap -type f -size +100M

# Clear log files
truncate -s 0 *.log
```

---

## 13. Appendix: HeavyDB Integration

### HeavyDB Overview

The NIFTY ST VWAP strategy uses HeavyDB (formerly OmniSci/MapD) for GPU-accelerated data processing. Key points:

- HeavyDB is an analytics database designed for high-performance queries
- It utilizes GPU acceleration for faster data processing
- The strategy accesses data through the `nifty_option_chain` regular view
- This view provides pre-computed metrics like ATM strikes and DTEs

### Database Structure

- **Base Table**: `nifty_greeks`
  - Contains raw NIFTY options data with prices, volumes, etc.
  
- **Regular View**: `nifty_option_chain`
  - Provides a structured view of the options chain
  - Calculates ATM strikes, DTEs, and option classifications
  - Used directly by the backtesting system

### Viewing Database Schema

```bash
# Connect to HeavyDB
/opt/heavyai/bin/heavysql -s 127.0.0.1 --port 6274 -u admin -p HyperInteractive -d heavyai

# Show regular view schema
\d nifty_option_chain

# Example query to see structure
SELECT * FROM nifty_option_chain
WHERE trade_date = '2024-04-01' AND trade_time = 91500 AND dte = 1
LIMIT 10;
```

### HeavyDB Client Implementation

The backtesting system uses a custom `HeavyDBClient` class that:

1. Connects to the HeavyDB server
2. Queries the `nifty_option_chain` view with appropriate filters
3. Retrieves data efficiently using GPU acceleration when available
4. Transforms the data into the format needed by the strategy

This implementation allows for efficient processing of large datasets and supports the debug tools for performance analysis.

---

## Document Revision History

- **Version 1.0 (2025-05-09)**: Initial comprehensive documentation
- **Author**: Data Science Team

---

*End of Document*
