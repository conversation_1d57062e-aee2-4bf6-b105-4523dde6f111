# Enhanced Daily ETL Documentation

## Overview

The Enhanced Daily ETL system automates the loading of option chain data for multiple indices into HeavyDB. It handles NIFTY, BANKNIFTY, MIDCAPNIFTY, and SENSEX data with automatic error handling, logging, and optional notifications.

## Components

### 1. Enhanced ETL Script (`enhanced_daily_etl.py`)
- **Purpose**: Main ETL processor for all indices
- **Features**:
  - Multi-index support
  - Automatic table creation
  - ZIP file extraction
  - Data integrity verification
  - Comprehensive logging
  - Email notifications (optional)
  - Error recovery and retry logic

### 2. Cron Setup Script (`setup_enhanced_etl_cron.sh`)
- **Purpose**: Configures automated daily execution
- **Features**:
  - Backs up existing crontab
  - Tests ETL script before deployment
  - Creates manual run script
  - Sets up log cleanup

### 3. Manual Run Script (`run_etl_manually.sh`)
- **Purpose**: Execute ETL on-demand
- **Usage**: `./run_etl_manually.sh`

## Cron Schedule

1. **Enhanced ETL**: 6:30 PM IST (1:00 PM UTC) daily
   - Processes all configured indices
   - Loads new data from the last 24 hours
   
2. **Original NIFTY ETL**: 4:30 PM IST (11:00 AM UTC) daily
   - Kept for backward compatibility
   
3. **Log Cleanup**: 2:00 AM IST daily
   - Removes logs older than 30 days

## Configuration

### Index Configuration
Located in `ETLConfig.INDEX_CONFIG`:

```python
'NIFTY': {
    'table_name': 'nifty_option_chain',
    'data_dir': '/srv/samba/shared/market_data/nifty/oc_with_futures',
    'file_pattern': 'IV_*_nifty_futures.csv',
    'zip_file': None,
    'active': True
}
```

### Email Notifications (Optional)
To enable email notifications:

1. Edit `/srv/samba/shared/enhanced_daily_etl.py`
2. Update `ETLConfig.EMAIL_CONFIG`:
   ```python
   EMAIL_CONFIG = {
       'enabled': True,
       'smtp_server': 'smtp.gmail.com',
       'smtp_port': 587,
       'username': '<EMAIL>',
       'password': 'your-app-password',
       'from_address': '<EMAIL>',
       'to_addresses': ['<EMAIL>']
   }
   ```

## Data Sources

### NIFTY
- Location: `/srv/samba/shared/market_data/nifty/oc_with_futures/`
- Format: CSV files with futures data
- Table: `nifty_option_chain`

### BANKNIFTY
- Location: `/srv/samba/shared/market_data/banknifty/`
- Format: ZIP files containing CSV data
- Table: `banknifty_option_chain`

### MIDCAPNIFTY
- Location: `/srv/samba/shared/market_data/midcapnifty/`
- Format: ZIP file (`midcpnifty.zip`)
- Table: `midcpnifty_option_chain`

### SENSEX
- Location: `/srv/samba/shared/market_data/sensex/`
- Format: ZIP files by year
- Table: `sensex_option_chain`

## Monitoring

### Log Files
- Main ETL logs: `/srv/samba/shared/logs/daily_etl/enhanced_etl_YYYYMMDD.log`
- Cron logs: `/srv/samba/shared/logs/cron_etl.log`
- ETL reports: `/srv/samba/shared/logs/daily_etl/etl_report_YYYYMMDD.txt`

### Viewing Logs
```bash
# Today's ETL log
tail -f /srv/samba/shared/logs/daily_etl/enhanced_etl_$(date +%Y%m%d).log

# Cron execution log
tail -f /srv/samba/shared/logs/cron_etl.log

# Last ETL report
cat /srv/samba/shared/logs/daily_etl/etl_report_$(date +%Y%m%d).txt
```

## Troubleshooting

### Common Issues

1. **"No new files found"**
   - Check if data files exist in configured directories
   - Verify file modification times
   - Check zip file extraction

2. **"Failed to connect to database"**
   - Verify HeavyDB is running: `systemctl status heavydb`
   - Check database credentials in ETLConfig

3. **"COPY FROM failed"**
   - Check file permissions
   - Verify CSV format matches table schema
   - Check HeavyDB allowed-import-paths

### Manual Operations

```bash
# Run ETL manually
./run_etl_manually.sh

# Check cron jobs
crontab -l

# Disable ETL temporarily
crontab -e  # Comment out the ETL line

# View ETL statistics
python3 -c "
from heavydb import connect
conn = connect(host='localhost', port=6274, user='admin', password='HyperInteractive', dbname='heavyai')
cursor = conn.cursor()
for table in ['nifty_option_chain', 'banknifty_option_chain', 'midcpnifty_option_chain', 'sensex_option_chain']:
    try:
        cursor.execute(f'SELECT COUNT(*) FROM {table}')
        count = cursor.fetchone()[0]
        print(f'{table}: {count:,} rows')
    except:
        print(f'{table}: Not found')
"
```

## Performance

Expected performance metrics:
- NIFTY: 500,000-900,000 rows/sec
- Other indices: 100,000-300,000 rows/sec
- Daily processing time: 5-15 minutes (all indices)

## Maintenance

### Adding New Index
1. Add configuration to `ETLConfig.INDEX_CONFIG`
2. Ensure data files are in specified location
3. Test with manual run
4. ETL will automatically create table on first run

### Modifying Schedule
1. Edit cron schedule: `crontab -e`
2. Update timing (use https://crontab.guru for help)
3. Save and verify: `crontab -l`

### Backup
- ETL automatically backs up crontab before modifications
- Backups stored in `/tmp/crontab_backup_*.txt`

## Security Notes

1. Database credentials are hardcoded - consider using environment variables for production
2. Email passwords should use app-specific passwords, not main account passwords
3. Ensure proper file permissions on scripts and logs
4. Consider encrypting sensitive configuration