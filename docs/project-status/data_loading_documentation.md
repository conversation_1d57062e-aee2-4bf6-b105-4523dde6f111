# Data Loading: Nifty Option Chain

This document describes the process of loading data from the `nifty_greeks` table to the `nifty_option_chain` table in HeavyDB.

## Overview

The data loading process transforms raw options data from `nifty_greeks` into a structured format in `nifty_option_chain` with additional derived fields:

- ATM strike calculation
- Trading zone classification
- Days to Expiry (DTE) calculation using trading calendar (excluding weekends and holidays)
- Enhanced strike classification (ATM/ITM1-n/OTM1-n)
- Multiple expiry bucket assignment (CW, NW, CM, NM)

## Available Loading Methods

### 1. Enhanced Step-by-Step Loading Process (Recommended)

For production use, the enhanced loading process provides full functionality with multiple expiry buckets and trading calendar-based DTE calculation. The process follows these steps:

1. Create trading calendar (excluding weekends and holidays)
2. Create expiry ranks helper table
3. Add monthly expiry ranks
4. Calculate trading day DTE
5. Create filtered expiries for all buckets (CW, NW, CM, NM)
6. Truncate target table
7. Insert enhanced data
8. Clean up temporary tables

Execute the entire process using:
```bash
./run_enhanced_load.sh
```

This script handles the entire workflow and provides statistics on the loaded data.

### 2. Daily Incremental Loading

For daily updates, after the initial load:

1. Use the same step-by-step approach but filter for just the new trading date
2. Create a script similar to `run_enhanced_load.sh` that takes a date parameter:
   ```bash
   ./load_daily_update.sh YYYY-MM-DD
   ```

Example implementation:
```bash
#!/bin/bash

DATE=$1
echo "Loading data for $DATE..."

# Run steps with date filter added to relevant SQL files
/opt/heavyai/bin/heavysql -s 127.0.0.1 --port 6274 -u admin -p HyperInteractive -d heavyai -q << EOF
-- Filter expiry ranks for just this date
INSERT INTO expiry_ranks_helper(trade_date, expiry_date, exp_rank, is_thursday, is_last_thursday)
SELECT 
    g.trade_date,
    g.expiry_date,
    ROW_NUMBER() OVER (PARTITION BY g.trade_date ORDER BY g.expiry_date) AS exp_rank,
    CASE WHEN EXTRACT(DOW FROM g.expiry_date) = 4 THEN 1 ELSE 0 END AS is_thursday,
    0 AS is_last_thursday
FROM nifty_greeks g
WHERE g.expiry_date >= g.trade_date
  AND g.trade_date = '$DATE'
GROUP BY g.trade_date, g.expiry_date;

-- Update trading_dte for new entries
UPDATE expiry_ranks_helper
SET trading_dte = (
    SELECT COUNT(*) 
    FROM trading_calendar tc
    WHERE tc.cal_date BETWEEN expiry_ranks_helper.trade_date AND expiry_ranks_helper.expiry_date
)
WHERE trade_date = '$DATE';

-- Update filtered_expiries
INSERT INTO filtered_expiries
SELECT 
    trade_date,
    expiry_date,
    trading_dte,
    CASE
        WHEN exp_rank = 1 THEN 'CW'  -- Current Week (nearest expiry)
        WHEN exp_rank = 2 THEN 'NW'  -- Next Week (second nearest expiry)
        ELSE 'OTHER'
    END AS expiry_bucket
FROM expiry_ranks_helper
WHERE trade_date = '$DATE' AND exp_rank <= 2;

-- Insert the data
INSERT INTO nifty_option_chain
SELECT 
    g.trade_date,
    g.trade_time,
    g.expiry_date,
    'NIFTY' AS index_name,
    g.underlying_price,
    CASE 
        WHEN g.underlying_price < 10000 
        THEN ROUND(g.underlying_price / 50) * 50
        ELSE ROUND(g.underlying_price / 100) * 100
    END AS atm_strike,
    g.strike,
    f.trading_dte AS dte,
    f.expiry_bucket,
    CASE
        WHEN g.trade_time BETWEEN '09:15:00' AND '10:30:00' THEN 1
        WHEN g.trade_time BETWEEN '10:30:01' AND '12:00:00' THEN 2
        WHEN g.trade_time BETWEEN '12:00:01' AND '13:30:00' THEN 3
        WHEN g.trade_time BETWEEN '13:30:01' AND '15:00:00' THEN 4
        WHEN g.trade_time BETWEEN '15:00:01' AND '15:30:00' THEN 5
        ELSE 1
    END AS zone_id,
    CASE
        WHEN g.trade_time BETWEEN '09:15:00' AND '10:30:00' THEN 'OPEN'
        WHEN g.trade_time BETWEEN '10:30:01' AND '12:00:00' THEN 'MID_MORN'
        WHEN g.trade_time BETWEEN '12:00:01' AND '13:30:00' THEN 'LUNCH'
        WHEN g.trade_time BETWEEN '13:30:01' AND '15:00:00' THEN 'AFTERNOON'
        WHEN g.trade_time BETWEEN '15:00:01' AND '15:30:00' THEN 'CLOSE'
        ELSE 'OPEN'
    END AS zone_name,
    -- Enhanced call strike type classification with multiple levels
    CASE 
        WHEN g.strike = CASE 
                          WHEN g.underlying_price < 10000 
                          THEN ROUND(g.underlying_price / 50) * 50
                          ELSE ROUND(g.underlying_price / 100) * 100
                        END THEN 'ATM'
        WHEN g.strike < CASE 
                          WHEN g.underlying_price < 10000 
                          THEN ROUND(g.underlying_price / 50) * 50
                          ELSE ROUND(g.underlying_price / 100) * 100
                        END 
        THEN 'ITM' || CEILING(ABS(g.strike - CASE 
                                          WHEN g.underlying_price < 10000 
                                          THEN ROUND(g.underlying_price / 50) * 50
                                          ELSE ROUND(g.underlying_price / 100) * 100
                                        END) / 
                                CASE WHEN g.underlying_price < 10000 THEN 50 ELSE 100 END)
        ELSE 'OTM' || CEILING(ABS(g.strike - CASE 
                                          WHEN g.underlying_price < 10000 
                                          THEN ROUND(g.underlying_price / 50) * 50
                                          ELSE ROUND(g.underlying_price / 100) * 100
                                        END) / 
                                CASE WHEN g.underlying_price < 10000 THEN 50 ELSE 100 END)
    END AS call_strike_type,
    -- Enhanced put strike type classification with multiple levels
    CASE 
        WHEN g.strike = CASE 
                          WHEN g.underlying_price < 10000 
                          THEN ROUND(g.underlying_price / 50) * 50
                          ELSE ROUND(g.underlying_price / 100) * 100
                        END THEN 'ATM'
        WHEN g.strike > CASE 
                          WHEN g.underlying_price < 10000 
                          THEN ROUND(g.underlying_price / 50) * 50
                          ELSE ROUND(g.underlying_price / 100) * 100
                        END 
        THEN 'ITM' || CEILING(ABS(g.strike - CASE 
                                          WHEN g.underlying_price < 10000 
                                          THEN ROUND(g.underlying_price / 50) * 50
                                          ELSE ROUND(g.underlying_price / 100) * 100
                                        END) / 
                                CASE WHEN g.underlying_price < 10000 THEN 50 ELSE 100 END)
        ELSE 'OTM' || CEILING(ABS(g.strike - CASE 
                                          WHEN g.underlying_price < 10000 
                                          THEN ROUND(g.underlying_price / 50) * 50
                                          ELSE ROUND(g.underlying_price / 100) * 100
                                        END) / 
                                CASE WHEN g.underlying_price < 10000 THEN 50 ELSE 100 END)
    END AS put_strike_type,
    g.ce_symbol, g.ce_open, g.ce_high, g.ce_low, g.ce_close, 
    g.ce_volume, g.ce_oi, g.ce_coi, g.ce_iv, g.ce_delta, 
    g.ce_gamma, g.ce_theta, g.ce_vega, g.ce_rho,
    g.pe_symbol, g.pe_open, g.pe_high, g.pe_low, g.pe_close, 
    g.pe_volume, g.pe_oi, g.pe_coi, g.pe_iv, g.pe_delta, 
    g.pe_gamma, g.pe_theta, g.pe_vega, g.pe_rho
FROM nifty_greeks g
JOIN filtered_expiries f ON g.trade_date = f.trade_date AND g.expiry_date = f.expiry_date
WHERE g.trade_date = '$DATE';
EOF

echo "Data loading for $DATE completed."
echo "Record count for $DATE:"
echo "SELECT COUNT(*) FROM nifty_option_chain WHERE trade_date = '$DATE';" | /opt/heavyai/bin/heavysql -s 127.0.0.1 --port 6274 -u admin -p HyperInteractive -d heavyai
```

### 3. Simple Test Loading

For testing and development, simpler approaches are available:

#### Using Hardcoded Inserts
```bash
# Use the hardcoded SQL file
/opt/heavyai/bin/heavysql -s 127.0.0.1 --port 6274 -u admin -p HyperInteractive -d heavyai -q hardcoded_inserts.sql
```

#### Using Simple Data Loading
```bash
# Load test data for a specific date
./load_simple_data.sh YYYY-MM-DD
```

## Target Table Structure

The enhanced `nifty_option_chain` table has the following structure:

```sql
CREATE TABLE nifty_option_chain (
    trade_date DATE,           -- Trading date
    trade_time TIME,           -- Trading time
    expiry_date DATE,          -- Option expiry date
    index_name TEXT,           -- Index name (always 'NIFTY')
    underlying_price DOUBLE,   -- Underlying index price
    atm_strike DOUBLE,         -- At-The-Money strike price
    strike DOUBLE,             -- Strike price of the option
    dte INTEGER,               -- Trading days to Expiry (excluding weekends and holidays)
    expiry_bucket TEXT,        -- CW (Current Week), NW (Next Week), CM (Current Month), NM (Next Month)
    zone_id INTEGER,           -- Trading zone ID (1-5)
    zone_name TEXT,            -- Trading zone name (OPEN, MID_MORN, LUNCH, AFTERNOON, CLOSE)
    call_strike_type TEXT,     -- Call option moneyness (ATM, ITM1-n, OTM1-n)
    put_strike_type TEXT,      -- Put option moneyness (ATM, ITM1-n, OTM1-n)
    
    -- Call Option Fields
    ce_symbol TEXT,            -- Call option symbol
    ce_open DOUBLE,            -- Open price
    ce_high DOUBLE,            -- High price
    ce_low DOUBLE,             -- Low price
    ce_close DOUBLE,           -- Close price
    ce_volume DOUBLE,          -- Volume
    ce_oi DOUBLE,              -- Open Interest
    ce_coi DOUBLE,             -- Change in Open Interest
    ce_iv DOUBLE,              -- Implied Volatility
    ce_delta DOUBLE,           -- Delta
    ce_gamma DOUBLE,           -- Gamma
    ce_theta DOUBLE,           -- Theta
    ce_vega DOUBLE,            -- Vega
    ce_rho DOUBLE,             -- Rho
    
    -- Put Option Fields
    pe_symbol TEXT,            -- Put option symbol
    pe_open DOUBLE,            -- Open price
    pe_high DOUBLE,            -- High price
    pe_low DOUBLE,             -- Low price
    pe_close DOUBLE,           -- Close price
    pe_volume DOUBLE,          -- Volume
    pe_oi DOUBLE,              -- Open Interest
    pe_coi DOUBLE,             -- Change in Open Interest
    pe_iv DOUBLE,              -- Implied Volatility
    pe_delta DOUBLE,           -- Delta
    pe_gamma DOUBLE,           -- Gamma
    pe_theta DOUBLE,           -- Theta
    pe_vega DOUBLE,            -- Vega
    pe_rho DOUBLE              -- Rho
);
```

## Data Transformation Logic

### Trading Calendar

The trading calendar is created to exclude weekends and holidays for DTE calculation:

```sql
CREATE TABLE trading_calendar AS
SELECT cal_date
FROM (
      SELECT add_days('2019-01-01', seq4()) AS cal_date
      FROM table(generate_series(0, 8 * 366))  -- covers 2019-2026
) x
WHERE DATE_PART(DOW, cal_date) NOT IN (0,6)    -- Mon-Fri only
  AND cal_date NOT IN (SELECT holiday_date FROM nse_holidays);  -- exclude holidays
```

### Expiry Buckets

The system supports four expiry buckets:
- **CW** (Current Week): Nearest expiry
- **NW** (Next Week): Second nearest expiry
- **CM** (Current Month): Nearest monthly expiry (last Thursday of month)
- **NM** (Next Month): Second nearest monthly expiry

### Trading Day DTE Calculation

Days to Expiry calculation using the trading calendar:

```sql
UPDATE expiry_ranks_helper 
SET trading_dte = (
    SELECT COUNT(*) - 1  -- Subtract 1 since we're calculating days BETWEEN
    FROM trading_calendar tc
    WHERE tc.cal_date BETWEEN expiry_ranks_helper.trade_date AND expiry_ranks_helper.expiry_date
);
```

### Enhanced Strike Classification

The enhanced strike type classification calculates multiple levels of ITM/OTM based on distance from ATM:

```sql
CASE 
    WHEN strike = atm_strike THEN 'ATM'
    WHEN strike < atm_strike 
    THEN 'ITM' || CEILING(ABS(strike - atm_strike) / 
                   CASE WHEN underlying_price < 10000 THEN 50 ELSE 100 END)
    ELSE 'OTM' || CEILING(ABS(strike - atm_strike) / 
                   CASE WHEN underlying_price < 10000 THEN 50 ELSE 100 END)
END AS call_strike_type
```

## Troubleshooting

Common issues with the enhanced loading process:

1. **HeavyDB SQL Dialect Limitations**:
   - HeavyDB SQL has some differences from standard SQL
   - Avoid leading comments in SQL files executed with `-q`
   - Break down complex queries into simpler steps
   - Use intermediate tables instead of complex CTEs

2. **Trading Calendar Issues**:
   - Ensure the `nse_holidays` table is populated
   - Verify date ranges covered by the trading calendar (2019-2026)
   - Check proper weekday filtering (0=Sunday, 6=Saturday)

3. **Expiry Classification**:
   - Verify the expiry rank calculations
   - Check if monthly expiry identification is working (last Thursday)
   - Confirm filtered expiries have the right expiry bucket assignments

4. **Performance Considerations**:
   - Load incrementally when possible
   - For full reloads, consider using `COPY FROM` when available
   - Monitor memory usage during large data loads
   - Consider breaking very large loads into smaller date ranges