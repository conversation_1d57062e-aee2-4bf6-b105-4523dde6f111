# TV (TradingView) File Structure Analysis

## Overview

The TV backtesting system uses a hierarchical file structure where `input_tv.xlsx` serves as the main configuration file that references other files for signal data, portfolio settings, and strategy configurations.

## File Hierarchy and Relationships

### 1. **input_tv.xlsx** (Main Configuration)
- **Role**: Central configuration file for TV backtesting
- **Sheet**: `Setting`
- **Key Fields**:
  - `StartDate`, `EndDate`: Backtest period
  - `SignalFilePath`: Path to TradingView signal data
  - `LongPortfolioFilePath`: Path to long portfolio configuration
  - `ShortPortfolioFilePath`: Path to short portfolio configuration
  - `ManualPortfolioFilePath`: Path to manual portfolio (optional)
  - `TvExitApplicable`: Whether to use TV exit signals
  - `IntradayExitTime`: Time for intraday square-off
  - `ExpiryDayExitTime`: Exit time on expiry days
  - `UseDbExitTiming`: Use database for exit timing
  - `SlippagePercent`: Slippage percentage

### 2. **sample_nifty_list_of_trades.xlsx** (Signal Data)
- **Role**: Contains actual trading signals from TradingView
- **Sheets**:
  - `List of trades`: Main signal data
    - Trade #, Type (Entry/Exit), Signal (Buy/Sell)
    - Date/Time, Price, Contracts
    - Profit metrics
  - `Performance`: Overall performance metrics
  - `Trades analysis`: Trade statistics
  - `Risk performance ratios`: Risk metrics
  - `Properties`: Trading configuration

### 3. **input_portfolio_long.xlsx** (Long Portfolio)
- **Role**: Configuration for long positions
- **Sheets**:
  - `PortfolioSetting`: Portfolio-level parameters
    - Portfolio name, targets, stoploss
    - Trailing settings
    - Square-off times and percentages
  - `StrategySetting`: Strategy reference
    - Links to `input_tbs_long.xlsx`

### 4. **input_portfolio_short.xlsx** (Short Portfolio)
- **Role**: Configuration for short positions
- **Structure**: Identical to long portfolio
- **Reference**: Links to `input_tbs_short.xlsx`

### 5. **input_tbs_long.xlsx** (Long TBS Strategy)
- **Role**: Detailed strategy configuration for long positions
- **Sheets**:
  - `GeneralParameter`:
    - StrategyName: "L"
    - Underlying, Index, DTE, Weekdays
    - Entry/Exit times
    - Profit/Loss settings
    - Trailing parameters
  - `LegParameter`:
    - Up to 4 legs configuration
    - Instrument type (CE/PE)
    - Strike selection method
    - SL/Target settings

### 6. **input_tbs_short.xlsx** (Short TBS Strategy)
- **Role**: Detailed strategy configuration for short positions
- **Structure**: Same as long TBS
- **StrategyName**: "S"

### 7. **input_signal_long.xlsx** (Alternative Portfolio File)
- **Note**: This appears to be an alternative portfolio file with same structure
- **Sheets**: `PortfolioSetting`, `StrategySetting`

## Data Flow

```
input_tv.xlsx (Main Config)
    │
    ├──→ sample_nifty_list_of_trades.xlsx (Signals)
    │
    ├──→ input_portfolio_long.xlsx
    │         │
    │         └──→ input_tbs_long.xlsx (Strategy)
    │
    └──→ input_portfolio_short.xlsx
              │
              └──→ input_tbs_short.xlsx (Strategy)
```

## Key Observations

1. **File References**: All file paths in the sample data use Windows-style paths (C:\\ or D:\\), indicating these files were created on Windows systems.

2. **Strategy Types**: The system supports both long and short strategies with separate configuration files.

3. **Leg Configuration**: Each TBS strategy supports up to 4 legs, allowing complex option strategies.

4. **Signal Integration**: TradingView signals are imported through the signal file, which contains detailed trade-by-trade data.

5. **Portfolio Management**: Portfolio files act as an intermediate layer between the main TV config and the detailed TBS strategy files.

## File Path Dependencies

The system relies on absolute file paths, which means:
- Files must be placed in the exact locations specified
- Path updates are needed when moving files
- Consider using relative paths for better portability

## Visual Diagrams

Two visual diagrams have been created:
1. `tv_file_flow_diagram.png`: Shows the file relationships and data flow
2. `tv_file_detailed_structure.png`: Shows detailed contents of each file