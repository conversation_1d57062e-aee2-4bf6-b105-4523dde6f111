# TBS Comprehensive Test Scenarios and Edge Cases

## Understanding and Flow Summary

Based on my analysis of the TBS Excel template structure and parser code, here's my understanding:

### Current TBS Architecture:
1. **Input Structure**: Two main Excel files:
   - Portfolio Excel with `PortfolioSetting` and `StrategySetting` sheets
   - TBS Multi-leg Excel with `GeneralParameter` and `LegParameter` sheets

2. **Parser Flow**: 
   - TBSParser → TBSQueryBuilder → TBSProcessor → Output
   - Portfolio settings control date range and strategy references
   - Strategy settings link to specific TBS Excel files
   - General parameters define strategy-level behavior
   - Leg parameters define individual option positions

3. **Query Generation**: Uses HeavyDB-optimized SQL queries with ATM calculation, option selection, and P&L tracking

## Complete Test Scenarios Required

### 1. Strike Selection Methods

#### 1.1 ATM-Based Selections
- **ATM** (value=0): Exact at-the-money strike
- **ATM+N** (value=N): ATM + N steps (CE=OTM, PE=ITM) 
- **ATM-N** (value=-N): ATM - N steps (CE=ITM, PE=OTM)
- **ATM_WIDTH**: Special width-based selection
- **ATM_MATCH**: Match premium conditions
- **ATM_DIFF**: Difference-based selection
- **STRADDLE_WIDTH**: Straddle width calculation

#### 1.2 ITM/OTM Direct Methods
- **ITM1-ITM10**: In-the-money by 1-10 strikes
- **OTM1-OTM10**: Out-of-the-money by 1-10 strikes

#### 1.3 Absolute Value Methods
- **FIXED**: Exact strike value (e.g., 22000)
- **PREMIUM**: Premium-based selection with conditions (<=, >=, =)
- **DELTA**: Delta-based selection (0.1-0.9)

### 2. Risk Management Types

#### 2.1 Stop Loss Types (SLType)
- **PERCENTAGE**: SL as percentage of entry price
- **POINT**: SL as absolute point value
- **INDEXPOINT**: SL as index points
- **ABSOLUTE**: SL as absolute price level

#### 2.2 Target Types (TGTType)
- **PERCENTAGE**: Target as percentage profit
- **POINT**: Target as absolute point value
- **INDEXPOINT**: Target as index points
- **ABSOLUTE**: Target as absolute price level

#### 2.3 Trailing SL Types (TrailSLType)
- **POINT**: Trail by absolute points
- **PERCENTAGE**: Trail by percentage
- **LOCK_PROFIT**: Lock profit and trail

### 3. Expiry Rule Combinations

#### 3.1 Weekly Expiries
- **CW/CURRENT**: Current week expiry
- **NW/NEXT**: Next week expiry

#### 3.2 Monthly Expiries  
- **CM/MONTHLY**: Current month expiry
- **NM/NEXT MONTHLY**: Next month expiry

#### 3.3 Fixed DTE
- **FIXED**: Specific days to expiry (0-30)

### 4. Time-Based Parameters

#### 4.1 Critical Times
- **StrikeSelectionTime**: When to calculate strikes (e.g., 09:20:00)
- **StartTime**: Entry start time (e.g., 09:16:00)
- **LastEntryTime**: Last possible entry (e.g., 12:00:00)
- **EndTime**: Force exit time (e.g., 15:30:00)
- **PnLCalTime**: When to calculate P&L

#### 4.2 Partial Exit Times
- **SqOff1Time**: First partial exit time
- **SqOff2Time**: Second partial exit time
- **SqOff1Percent**: Percentage to exit at first time
- **SqOff2Percent**: Percentage to exit at second time

### 5. Strategy-Level Parameters

#### 5.1 DTE Filtering
- **DTE=0**: Only trade on expiry days
- **DTE=1**: Only trade 1 day before expiry
- **DTE=1-5**: Trade within 1-5 days of expiry

#### 5.2 Day Filtering
- **Weekdays**: "1,2,3,4,5" (Mon-Fri filter)
- **OnExpiryDayTradeNextExpiry**: Use next expiry on expiry days

#### 5.3 Trailing Types
- **NONE**: No trailing
- **TRAIL_PROFIT**: Trail profits only
- **LOCK & TRAIL**: Lock profit then trail
- **Lock & Trail Profits**: Full trailing system

### 6. Leg Parameter Combinations

#### 6.1 Transaction Types
- **BUY**: Long positions
- **SELL**: Short positions
- **BOTH**: Mixed strategies

#### 6.2 Option Types
- **CE/CALL**: Call options
- **PE/PUT**: Put options  
- **FUT**: Futures
- **OPTIONS**: Generic options

#### 6.3 Wait & Time (W&T) Types
- **NONE**: No waiting conditions
- **TIME**: Time-based waiting
- **PRICE**: Price-based waiting
- **PERCENT**: Percentage-based waiting

### 7. Advanced Features

#### 7.1 Re-entry Logic
- **ReEntryType**: "instant new strike", "original", "none"
- **ReEnteriesCount**: Maximum re-entries (0-5)
- **SL_ReEntryType**: Re-entry on SL hit
- **TGT_ReEntryType**: Re-entry on target hit

#### 7.2 Hedge Parameters
- **OpenHedge**: Enable hedging (Yes/No)
- **HedgeStrikeMethod**: How to select hedge strike
- **HedgeStrikeValue**: Hedge strike offset
- **HedgeStrikePremiumCondition**: Hedge premium conditions

#### 7.3 Entry/Exit Actions
- **OnEntry_OpenTradeOn**: Open other legs on entry
- **OnEntry_SqOffTradeOff**: Square off other legs
- **OnExit_OpenAllLegs**: Open all legs on exit
- **OnExit_SqOffAllLegs**: Square off all legs

### 8. Edge Cases to Test

#### 8.1 Time Edge Cases
- Entry time = Strike selection time
- Exit time before entry time
- Partial exits after end time
- Weekend/holiday handling

#### 8.2 Strike Edge Cases
- ATM strike not available
- Fixed strike out of range
- Premium/Delta targets not met
- Hedge strike calculation failures

#### 8.3 Data Edge Cases
- Missing expiry dates
- Zero premium values
- Negative strike values
- Invalid time formats

#### 8.4 Calculation Edge Cases
- DTE=0 on non-expiry days
- Percentage SL > 1000%
- Negative target values
- Division by zero in trailing

### 9. Recommended Test Values

#### 9.1 For SELL Legs (Short Options)
- **SL**: 500% (allows premium to go 5x)
- **Target**: 50-100% (capture 50-100% of premium)
- **Entry**: ATM, ITM1, OTM1
- **Quantity**: 1-5 lots

#### 9.2 For BUY Legs (Long Options)
- **SL**: 50% (stop at 50% loss)
- **Target**: 100-200% (double/triple money)
- **Entry**: OTM1-3 for directional plays
- **Quantity**: 1-10 lots

#### 9.3 Time Combinations
- **Conservative**: 09:20 entry, 15:15 exit
- **Aggressive**: 09:16 entry, 15:30 exit
- **Partial**: 50% at 12:00, 30% at 14:00, 20% at 15:30

### 10. Critical Test Scenarios

#### 10.1 Iron Condor Test
```
Leg 1: SELL ATM+1 CE
Leg 2: SELL ATM-1 PE  
Leg 3: BUY ATM+3 CE (hedge)
Leg 4: BUY ATM-3 PE (hedge)
```

#### 10.2 Straddle/Strangle Test
```
Leg 1: SELL ATM CE
Leg 2: SELL ATM PE
SL: 500%, Target: 50%
Time: 09:20-15:15
```

#### 10.3 DTE=0 Expiry Day Test
```
Strategy on Thursday expiry:
- Should only execute on Thursday
- Should use Thursday expiry contracts
- Should exit before 15:30
```

#### 10.4 Premium Difference Test
```
Check CE-PE premium difference
If > 10%, adjust strikes
If CE premium >> PE premium, bias toward PE
```

#### 10.5 Re-entry Test
```
Initial: SELL ATM CE
If SL hit: Re-enter with OTM1 CE
Max re-entries: 2
Original vs new strike logic
```

### 11. Column Mapping Validation

#### 11.1 Excel Column → Internal Field Mapping
```python
{
    'StrikeMethod': 'strike_selection',
    'StrikeValue': 'strike_value', 
    'SLType': 'sl_type',
    'SLValue': 'sl_percent',
    'TGTType': 'tgt_type',
    'TGTValue': 'target_percent',
    'W&Type': 'wait_type',
    'W&TValue': 'wait_value',
    'TrailW&T': 'trail_wait'
}
```

#### 11.2 Time Format Conversions
```python
# Input formats
91600 → "09:16:00"
"9:16:00" → "09:16:00" 
120000 → "12:00:00"
"15:30:00" → "15:30:00"
```

#### 11.3 Boolean Conversions
```python
# Input formats
"YES"/"NO" → True/False
"Y"/"N" → True/False
"1"/"0" → True/False
True/False → True/False
```

### 12. Performance Test Scenarios

#### 12.1 Large Strategy Count
- 100+ strategies with 4 legs each
- Mixed expiries (CW, NW, CM, NM)
- Different time ranges
- GPU vs CPU performance

#### 12.2 Complex Date Ranges
- 1-year backtests (250+ trading days)
- Multiple expiry cycles
- Holiday handling
- Weekend data gaps

This comprehensive test framework should cover all possible TBS column mappings, edge cases, and business logic variations. Each scenario should be implemented as unit tests with expected vs actual validation.