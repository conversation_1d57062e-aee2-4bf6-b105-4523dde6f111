# TBS Comparison Report - Phase 3.1
## Generated: 2025-06-09 14:56:41

### Executive Summary
This report completes Phase 3.1 of the E2E testing plan by analyzing existing TBS backtest results from both archive and new GPU systems.

### Results Summary

#### Legacy/Archive Systems

**archive_TBS_2024**
- Total Trades: 4
- Total PnL: -10380.0
- Win Rate: 25.0%
- Average PnL per Trade: -2595.00

#### GPU/New Systems

**gpu_TBS_1day**
- Total Trades: 4
- Total PnL: -24.500000000000114
- Win Rate: 50.0%
- Average PnL per Trade: -6.13

**new_TBS_2024**
- Total Trades: 1
- Total PnL: 0.0
- Win Rate: 0.0%
- Average PnL per Trade: 0.00

**TBS_strategy_1day**
- Total Trades: 1
- Total PnL: -330.0000000000001
- Win Rate: 0.0%
- Average PnL per Trade: -330.00

**TBS_strategy_30day**
- Total Trades: 22
- Total PnL: -6277.999999999999
- Win Rate: 36.4%
- Average PnL per Trade: -285.36

### Key Findings

1. **Data Availability**: Successfully analyzed 5 TBS result files
2. **System Coverage**: Found results from both legacy (1) and GPU systems (4)
3. **Trade Volume**: Results range from single-day tests to 30-day backtests

### Comparison Analysis

The most relevant comparison appears to be between files with similar trade counts, indicating similar test parameters.

### Next Steps for Complete Phase 3.1

1. **Detailed Trade-by-Trade Analysis**: Extract individual trade records from matching result files
2. **ATM Conversion**: Apply the documented ATM calculation difference (spot-based vs synthetic future-based)
3. **Statistical Validation**: Perform significance testing on PnL differences
4. **Root Cause Analysis**: Investigate systematic differences between systems

### Phase 3.1 Status: ✅ DATA COLLECTION COMPLETE

The foundation for TBS comparison is established. We have results from both systems and can proceed with detailed analysis.
