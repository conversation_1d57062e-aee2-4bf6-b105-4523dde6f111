# TBS Input File Troubleshooting Guide

## Common Input Errors and Solutions

### 1. Data Type Errors

#### Error: "Invalid numeric value for MatchPremium: high"
**Cause**: Text value in numeric field
**Solution**: Replace text with numeric values
- ❌ Wrong: `high`, `low`, `NA`
- ✅ Correct: `50.5`, `100`, Leave blank

#### Error: "Object of type int64 is not JSON serializable"
**Cause**: NumPy/Pandas data types not converted properly
**Solution**: Ensure all numeric values are standard Python int/float

### 2. Date/Time Format Errors

#### Error: "Missing date" or "Invalid date format"
**Common Issues**:
- Using DD/MM/YYYY instead of supported formats
- Missing dates in required fields
- Excel auto-formatting issues

**Supported Date Formats**:
- `YYYY-MM-DD` (Recommended)
- `DD_MM_YYYY` (Legacy)
- `YYYYMMDD`

**Time Format Requirements**:
- Use 6-digit HHMMSS format: `091500` (not `9:15:00` or `915`)
- Or use HH:MM:SS format: `09:15:00`
- Avoid Excel time formatting

### 3. Column Mapping Errors

#### Error: "Unmapped column in GeneralParameter"
**Cause**: Extra columns not recognized by parser
**Solution**: 
- Remove unnecessary columns
- Check column spelling and case
- Refer to allowed columns in specification

#### Error: "Missing required columns"
**Required Columns by Sheet**:

**PortfolioSetting**:
- StartDate, EndDate, IsTickBT, Enabled, PortfolioName

**StrategySetting**:
- Enabled, PortfolioName, StrategyType, StrategyExcelFilePath

**GeneralParameter**:
- StrategyName, StartTime, EndTime

**LegParameter**:
- StrategyName, LegID, Instrument, Transaction, Lots

### 4. Value Validation Errors

#### Error: "Invalid StrategyType"
**Valid Values**: TBS, TV, ORB, OI, INDICATOR
**Common Mistakes**:
- Custom strategy names (IBS, HEIKIN_RSI_EMA)
- Lowercase values
- Misspellings

#### Error: "Invalid Instrument"
**Valid Values**: CE, PE, FUT (or CALL, PUT, FUTURE)
**Note**: Case-sensitive, use uppercase

#### Error: "Invalid Transaction"
**Valid Values**: BUY, SELL (or B, S)

#### Error: "Invalid Expiry"
**Valid Values**:
- Current Week: CURRENT, CW, CURRENT_WEEK
- Next Week: NEXT, NW, NEXT_WEEK
- Monthly: MONTHLY, MONTH, M

### 5. File Path Errors

#### Error: "Strategy Excel file not found"
**Common Issues**:
- Incorrect relative path
- Absolute path not accessible
- File name mismatch

**Solutions**:
- Use relative paths from portfolio file location
- Ensure file exists at specified path
- Check for typos in file names

### 6. Strike Method Errors

#### Error: "Invalid StrikeMethod"
**Valid Basic Methods**: ATM, ITM, OTM, FIXED, DELTA, PREMIUM

**Dynamic ITM/OTM Format**:
- ✅ Correct: `ITM1`, `OTM2`, `ITM10`
- ❌ Wrong: `ITM 1`, `1ITM`, `ITM-1`

### 7. Risk Management Errors

#### Error: "Unknown SLType/TGTType"
**Valid Types and Aliases**:
- POINT → POINT
- POINTS → POINT
- PERCENT → PERCENTAGE
- PERCENTAGE → PERCENTAGE
- INDEX POINT → INDEX_POINTS
- INDEX PERCENTAGE → INDEX_PERCENTAGE
- ABSOLUTE → PREMIUM
- DELTA → ABSOLUTE_DELTA

### 8. Parser-Specific Issues

#### Issue: EndDate column with date-formatted name
**Symptom**: Parser looks for column named like "10_05_2024"
**Solution**: Name the column "EndDate" explicitly

#### Issue: Extra parameters not recognized
**Symptom**: Custom columns ignored
**Solution**: Extra parameters are stored but may not affect backtesting

## Validation Checklist

Before running backtest, verify:

### ✅ Portfolio File
- [ ] StartDate and EndDate are valid dates
- [ ] All Enabled values are YES/NO (uppercase)
- [ ] PortfolioName matches in both sheets
- [ ] StrategyExcelFilePath points to existing file
- [ ] StrategyType is from valid list

### ✅ Strategy File
- [ ] StrategyName consistent across sheets
- [ ] Time fields in HHMMSS format
- [ ] All numeric fields contain only numbers
- [ ] Instrument values are CE/PE/FUT
- [ ] Transaction values are BUY/SELL
- [ ] Strike methods are valid

## Debug Steps

1. **Run Validation Script**:
   ```bash
   python3 analyze_tbs_input_issues.py
   ```

2. **Check Excel Formatting**:
   - Disable auto-formatting for time columns
   - Use Text format for YES/NO columns
   - Verify no hidden characters in cells

3. **Test with Minimal File**:
   - Start with minimal required columns
   - Add optional columns one by one
   - Identify which column causes issues

4. **Common Excel Issues**:
   - Leading/trailing spaces in text
   - Smart quotes instead of regular quotes
   - Hidden formulas in cells
   - Merged cells causing problems

## Error Prevention Tips

1. **Use Templates**: Start with working template files
2. **Consistent Formatting**: Apply same format rules across all files
3. **Validation First**: Always validate before running backtest
4. **Version Control**: Keep backups of working configurations
5. **Documentation**: Document any custom parameters used

## Getting Help

If errors persist after following this guide:

1. Check the detailed parser code in:
   - `/srv/samba/shared/bt/backtester_stable/BTRUN/excel_parser/`

2. Review example files that work correctly

3. Enable debug logging to see exact parsing steps

4. Verify database connection and data availability