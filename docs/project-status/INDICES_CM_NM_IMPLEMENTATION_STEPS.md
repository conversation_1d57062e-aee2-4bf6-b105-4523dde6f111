# BANKNIFTY & MIDCAPNIFTY CM/NM Implementation Steps

## Current Status

### ✅ Completed
1. **Data Discovery**: Found existing data in `/srv/samba/shared/market_data/`
   - BANKNIFTY: 2023, 2024, 2025 data available (zipped)
   - MIDCAPNIFTY: Complete data available (zipped)

2. **Schema Analysis**: Confirmed `nifty_option_chain` table already supports multi-index via `index_name` column

3. **Data Transformation**: Created transformation script that:
   - Filters for CM (Current Month) and NM (Next Month) expiries only
   - Converts date/time formats to match schema
   - Adds required columns (zone_id, zone_name, expiry_bucket, etc.)
   - Handles different strike increments (BANKNIFTY: 100, MIDCAPNIFTY: 25)

4. **Sample Data Processing**: Successfully transformed sample data (Jan 1-3, 2025)

### 📁 Files Created
- `/srv/samba/shared/transform_indices_cm_nm.py` - Transformation script
- `/srv/samba/shared/load_indices_cm_nm_data.py` - HeavyDB loading script
- `/srv/samba/shared/indices_cm_nm_data/` - Transformed data directory

## Next Steps

### 1. Full Data Processing
```bash
# Modify transform_indices_cm_nm.py to process all dates (remove sample_dates filter)
# Or create a new script for full processing
python3 /srv/samba/shared/transform_indices_cm_nm_full.py
```

### 2. Load into HeavyDB
```bash
# Run the loading script
python3 /srv/samba/shared/load_indices_cm_nm_data.py
```

### 3. Verify Data Quality
```sql
-- Check loaded data
SELECT 
    index_name,
    expiry_bucket,
    COUNT(*) as rows,
    COUNT(DISTINCT trade_date) as days,
    COUNT(DISTINCT expiry_date) as expiries
FROM nifty_option_chain
WHERE index_name IN ('BANKNIFTY', 'MIDCAPNIFTY')
GROUP BY index_name, expiry_bucket
ORDER BY index_name, expiry_bucket;
```

### 4. Test with Backtester
```python
# Create test strategy for BANKNIFTY
strategy = {
    'underlying': 'BANKNIFTY',
    'start_date': '2025-01-02',
    'end_date': '2025-01-03',
    'dte': 'CM',  # Current month expiries
    'legs': [{
        'instrument': 'CE',
        'transaction': 'SELL',
        'strike_method': 'ATM'
    }]
}
```

## Data Specifications

### BANKNIFTY
- **Strike Increment**: 100
- **Expiry Pattern**: Weekly (Thursdays)
- **Available Months**: Jan-Feb 2025 (sample)
- **CM Expiries**: Last Thursday of current month
- **NM Expiries**: Thursdays of next month

### MIDCAPNIFTY  
- **Strike Increment**: 25
- **Expiry Pattern**: Weekly (Mondays mostly)
- **Available Months**: Jan-Feb 2025 (sample)
- **CM Expiries**: Mondays within current month
- **NM Expiries**: First few Mondays of next month

## Important Notes

1. **Futures Data**: Currently not available in source data, all futures columns set to NaN/0

2. **Greek Values**: Original data has IV, delta, theta, gamma, rho, vega - all preserved

3. **Zone Classification**: Simplified to 5 zones based on strike distance from ATM

4. **Performance**: Sample processing shows good performance, full data may take longer

5. **Storage**: Each month of data is ~100-150MB, plan storage accordingly

## Troubleshooting

### If COPY FROM fails:
1. Check file permissions
2. Verify CSV format matches schema exactly
3. Check for special characters in symbol names
4. Ensure date formats are YYYY-MM-DD

### If queries are slow:
1. Create index on (index_name, trade_date)
2. Create index on (index_name, expiry_bucket, trade_date)
3. Run ANALYZE on table after loading

---
**Last Updated**: June 3, 2025