# GPU System Critical Fix Plan V2 - Corrected Understanding

**Date**: June 9, 2025  
**Priority**: 🔴 CRITICAL - BLOCKING ALL TESTING  
**Owner**: Backend Development Team

---

## Executive Summary

The GPU backtester has critical issues preventing proper trade-by-trade comparison with the MySQL-based archive system. The ATM calculation difference is EXPECTED and NORMAL. The real issues are trade completion and output format.

## System Architecture Clarification

1. **Archive System (Old)**:
   - Database: MySQL
   - ATM Calculation: Spot-based (`round(spot/50)*50`)
   - Output Format: Golden standard Excel

2. **GPU System (New)**:
   - Database: HeavyDB (GPU-accelerated)
   - ATM Calculation: Synthetic future-based
   - Output Format: Must match golden standard

## Critical Issues to Fix

### 1. ❌ Trade Completion Logic (HIGHEST PRIORITY)
- **Issue**: All trades remain OPEN, never closed
- **Impact**: PnL shows as 0 for all trades
- **Fix Required**:
  ```python
  # File: /srv/samba/shared/bt/backtester_stable/BTRUN/refactored_core/exit_logic.py
  
  def process_exit_conditions(trade, current_bar, strategy_params):
      """Fix: Ensure trades are properly closed"""
      # Check exit time
      if current_bar['time'] >= strategy_params['exit_time']:
          trade.status = 'CLOSED'
          trade.exit_time = current_bar['time']
          trade.exit_price = current_bar['close']
          trade.calculate_pnl()
          return True
      
      # Check stop loss/target
      if check_sl_tp_conditions(trade, current_bar, strategy_params):
          trade.status = 'CLOSED'
          return True
      
      return False
  ```

### 2. ❌ Multi-Leg Execution (CRITICAL)
- **Issue**: Only 1 leg executed out of 4 expected
- **Impact**: Portfolio strategies incomplete
- **Fix Required**:
  ```python
  # File: /srv/samba/shared/bt/backtester_stable/BTRUN/refactored_strategies/tbs_strategy.py
  
  def execute_portfolio_strategy(portfolio_config, market_data):
      """Fix: Execute ALL legs in portfolio"""
      executed_trades = []
      
      # CRITICAL: Loop through ALL legs, not just first
      for leg_id in portfolio_config['legs']:
          leg_params = portfolio_config['legs'][leg_id]
          trade = execute_single_leg(leg_params, market_data)
          executed_trades.append(trade)
      
      return executed_trades  # Return ALL trades, not just one
  ```

### 3. ❌ Output Format Compliance (CRITICAL)
- **Issue**: Output doesn't match golden Excel format
- **Impact**: Cannot compare results properly
- **Fix Required**:
  ```python
  # Must generate Excel with exact structure:
  # - 9 required sheets in specific order
  # - 32 columns in transaction sheets
  # - Exact column names including special characters
  # - Time format: HH:MM:SS strings
  # - Date format: datetime64[ns]
  
  # Use /srv/samba/shared/golden_output_template.py as reference
  ```

### 4. ✅ ATM Calculation (NO FIX NEEDED)
- **Current State**: Working correctly
- **Note**: The ~16 strike (800 point) difference is EXPECTED
- **MySQL**: Spot-based ATM
- **HeavyDB**: Synthetic future ATM
- **Action**: NO CHANGE REQUIRED - handle through normalization

## Verification Steps

### 1. Test Trade Completion
```bash
# Run simple single-leg test
python BTRunPortfolio.py --input test_single_leg.xlsx --output test_output.xlsx

# Check output
python -c "
import pandas as pd
df = pd.read_excel('test_output.xlsx', sheet_name='PORTFOLIO Trans')
print(f'Total trades: {len(df)}')
print(f'Open trades: {len(df[df['Status'] == 'OPEN'])}')
print(f'Closed trades: {len(df[df['Status'] == 'CLOSED'])}')
print(f'Zero PnL trades: {len(df[df['PnL'] == 0])}')
"
```

### 2. Test Multi-Leg Execution
```bash
# Run multi-leg portfolio test
python BTRunPortfolio.py --input input_portfolio.xlsx --output portfolio_output.xlsx

# Verify all legs executed
python -c "
import pandas as pd
df = pd.read_excel('portfolio_output.xlsx', sheet_name='PORTFOLIO Trans')
print(f'Strategies found: {df['Strategy'].unique()}')
print(f'Total legs executed: {len(df)}')
"
```

### 3. Validate Output Format
```bash
# Use validation script
python /srv/samba/shared/validate_gpu_output_format.py \
  --gpu-output portfolio_output.xlsx \
  --archive-output /srv/samba/shared/Nifty_Golden_Ouput.xlsx
```

## Expected Results After Fix

1. **Trade Status**: All trades show "CLOSED" with proper exit times
2. **PnL Calculation**: Non-zero PnL values for all trades
3. **Multi-Leg**: 4 trades for 4-leg strategy (not just 1)
4. **Output Format**: Exact match to golden Excel structure
5. **ATM Strikes**: ~800 point difference is NORMAL (no fix needed)

## Success Criteria

- [ ] All trades have Status = "CLOSED"
- [ ] All trades have non-zero PnL
- [ ] Multi-leg strategies execute all legs
- [ ] Output Excel has all 9 required sheets
- [ ] Transaction sheets have exactly 32 columns
- [ ] Output passes format validation script

## Timeline

- **Day 1 (4 hours)**: Fix trade completion logic
- **Day 1 (3 hours)**: Fix multi-leg execution
- **Day 1 (2 hours)**: Fix output format
- **Day 2 (3 hours)**: Testing and verification

## Next Steps After Fix

1. Run `/srv/samba/shared/validate_gpu_output_format.py` to verify format
2. Execute TBS comparison test with normalization for ATM differences
3. Verify PnL values are reasonable (not exact match due to different strikes)
4. Proceed to Phase 3.2 (TV Strategy Testing)

---

**Note**: The ATM calculation difference between MySQL and HeavyDB is EXPECTED and should be handled through the normalization layer, not by changing either system's calculation method.