# TBS Input Sheet Format Documentation

## Overview
The TBS (Time Based Strategy) input format consists of two separate Excel files:
1. **Portfolio Settings File** - Contains overall portfolio and strategy configuration
2. **Strategy Settings File** - Contains detailed strategy parameters and leg definitions

## File Structure

### 1. Portfolio Settings File (e.g., INPUT PORTFOLIO.xlsx)

#### Sheet: PortfolioSetting
Contains portfolio-level settings and controls.

**Columns:**
- `StartDate` (text): Start date for backtesting (format: DD_MM_YYYY)
- `EndDate` (text): End date for backtesting (format: DD_MM_YYYY)
- `IsTickBT` (text): Whether to use tick-by-tick backtesting ("yes"/"no")
- `Enabled` (text): Whether portfolio is enabled ("YES"/"NO")
- `PortfolioName` (text): Name of the portfolio
- `PortfolioTarget` (int): Portfolio-level target profit (0 = no target)
- `PortfolioStoploss` (int): Portfolio-level stop loss (0 = no stop loss)
- `PortfolioTrailingType` (text): Type of trailing ("portfolio lock trail", etc.)
- `PnLCalTime` (int): Time for P&L calculation (HHMMSS format, e.g., 233000)
- `LockPercent` (int): Percentage of profit to lock
- `TrailPercent` (int): Percentage for trailing
- `SqOff1Time` (int): First square-off time (HHMMSS)
- `SqOff1Percent` (int): Percentage to square off at first time
- `SqOff2Time` (int): Second square-off time (HHMMSS)
- `SqOff2Percent` (int): Percentage to square off at second time
- `ProfitReaches` (int): Profit level that triggers trailing
- `LockMinProfitAt` (int): Minimum profit to start locking
- `IncreaseInProfit` (int): Profit increase threshold
- `TrailMinProfitBy` (int): Minimum profit for trailing
- `Multiplier` (int): Position size multiplier
- `SlippagePercent` (float): Slippage percentage

#### Sheet: StrategySetting
Links strategies to the portfolio.

**Columns:**
- `Enabled` (text): Whether strategy is enabled ("YES"/"NO")
- `PortfolioName` (text): Portfolio name (must match PortfolioSetting)
- `StrategyType` (text): Type of strategy ("TBS", "ORB", "IBS", "TV", "OI", etc.)
- `StrategyExcelFilePath` (text): Full path to strategy Excel file

### 2. TBS Strategy File (e.g., INPUT TBS MULTI LEGS.xlsx)

#### Sheet: GeneralParameter
Contains general strategy-level settings.

**Key Columns:**
- `StrategyName` (text): Name of the strategy
- `MoveSlToCost` (text): Move stop loss to cost ("yes"/"no")
- `Underlying` (text): Underlying instrument ("SPOT", "FUTURE")
- `Index` (text): Index name ("NIFTY", "BANKNIFTY", etc.)
- `Weekdays` (text): Trading days (comma-separated: "1,2,3,4,5")
- `DTE` (int): Days to expiry (0 for current expiry)
- `StrikeSelectionTime` (int): Time to select strikes (HHMMSS)
- `StartTime` (int): Strategy start time (HHMMSS)
- `LastEntryTime` (int): Last entry allowed time (HHMMSS)
- `EndTime` (int): Strategy end time (HHMMSS)
- `StrategyProfit` (int): Strategy-level target profit
- `StrategyLoss` (int): Strategy-level stop loss
- `StrategyProfitReExecuteNo` (int): Re-entries on profit
- `StrategyLossReExecuteNo` (int): Re-entries on loss
- `StrategyTrailingType` (text): Trailing type ("Lock & Trail Profits")
- `PnLCalTime` (int): P&L calculation time
- `LockPercent` (int): Lock percentage
- `TrailPercent` (int): Trail percentage
- `SqOff1Time` (int): Square-off time 1
- `SqOff1Percent` (int): Square-off percentage 1
- `SqOff2Time` (int): Square-off time 2
- `SqOff2Percent` (int): Square-off percentage 2
- `ProfitReaches` (int): Profit trigger level
- `LockMinProfitAt` (int): Minimum profit to lock
- `IncreaseInProfit` (int): Profit increase threshold
- `TrailMinProfitBy` (int): Minimum trailing profit
- `TgtTrackingFrom` (text): Target tracking ("high/low")
- `TgtRegisterPriceFrom` (text): Target price registration ("tracking")
- `SlTrackingFrom` (text): Stop loss tracking ("high/low")
- `SlRegisterPriceFrom` (text): Stop loss price registration ("tracking")
- `PnLCalculationFrom` (text): P&L calculation basis ("close")
- `ConsiderHedgePnLForStgyPnL` (text): Include hedge P&L ("yes"/"no")
- `StoplossCheckingInterval` (int): SL check interval (seconds)
- `TargetCheckingInterval` (int): Target check interval (seconds)
- `ReEntryCheckingInterval` (int): Re-entry check interval (seconds)
- `OnExpiryDayTradeNextExpiry` (text): Trade next expiry on expiry day ("yes"/"no")

#### Sheet: LegParameter
Contains individual leg definitions for the strategy.

**Key Columns:**
- `StrategyName` (text): Must match GeneralParameter strategy name
- `IsIdle` (text): Whether leg is idle ("yes"/"no")
- `LegID` (int): Unique leg identifier (1, 2, 3, etc.)
- `Instrument` (text): Option type ("call"/"put")
- `Transaction` (text): Transaction type ("buy"/"sell")
- `Expiry` (text): Expiry selection ("current", "next", etc.)
- `W&Type` (text): Wait type ("percentage", "points")
- `W&TValue` (int): Wait value
- `TrailW&T` (text): Trail wait ("yes"/"no")
- `StrikeMethod` (text): Strike selection method ("atm", "otm", "itm", etc.)
- `MatchPremium` (text): Premium matching ("high", "low")
- `StrikeValue` (int): Strike offset value
- `StrikePremiumCondition` (text): Premium condition ("=", ">", "<", etc.)
- `SLType` (text): Stop loss type ("percentage", "points")
- `SLValue` (int): Stop loss value
- `TGTType` (text): Target type ("percentage", "points")
- `TGTValue` (int): Target value
- `TrailSLType` (text): Trailing stop loss type
- `SL_TrailAt` (int): When to start trailing
- `SL_TrailBy` (int): Trail by amount
- `Lots` (int): Number of lots
- `ReEntryType` (text): Re-entry type ("instant new strike", etc.)
- `ReEnteriesCount` (int): Number of re-entries allowed
- `OnEntry_OpenTradeOn` (int): Leg ID to open on entry
- `OnEntry_SqOffTradeOff` (int): Leg ID to square off on entry
- `OnEntry_SqOffAllLegs` (text): Square off all legs on entry ("yes"/"no")
- `OnEntry_OpenTradeDelay` (int): Delay for opening trade
- `OnEntry_SqOffDelay` (int): Delay for squaring off
- `OnExit_OpenTradeOn` (int): Leg ID to open on exit
- `OnExit_SqOffTradeOff` (int): Leg ID to square off on exit
- `OnExit_SqOffAllLegs` (text): Square off all legs on exit ("yes"/"no")
- `OnExit_OpenAllLegs` (text): Open all legs on exit ("yes"/"no")
- `OnExit_OpenTradeDelay` (int): Delay for opening on exit
- `OnExit_SqOffDelay` (int): Delay for squaring off on exit
- `OpenHedge` (text): Open hedge position ("Yes"/"No")
- `HedgeStrikeMethod` (text): Hedge strike method
- `HedgeStrikeValue` (int): Hedge strike value
- `HedgeStrikePremiumCondition` (text): Hedge premium condition

## Example Strategy: "RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL"

This is a typical TBS strategy with multiple legs:
- **Leg 1**: Sell ATM Call
- **Leg 2**: Sell ATM Put
- **Leg 3**: Buy OTM Call (2 strikes away)
- **Leg 4**: Buy OTM Put (2 strikes away)

Each leg has individual stop loss (100%) and target (100%) settings.

## Important Notes for POS Strategy Creation

1. **File Structure**: You'll need both Portfolio and Strategy Excel files
2. **Naming Convention**: Strategy names must match between GeneralParameter and LegParameter sheets
3. **Time Format**: All times use HHMMSS format (e.g., 91600 = 9:16:00 AM)
4. **Date Format**: Dates use DD_MM_YYYY format with underscores
5. **Enabled Status**: Both portfolio and individual strategies must be enabled
6. **File Paths**: Use absolute paths for strategy files in StrategySetting sheet
7. **Leg Dependencies**: Legs can depend on other legs for entry/exit actions