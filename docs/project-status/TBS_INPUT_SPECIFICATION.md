# TBS (Time-Based Strategy) Input File Specification

## Overview

The TBS backtesting system requires two Excel files:
1. **Portfolio File** - Contains portfolio settings and strategy references
2. **Strategy File** - Contains strategy parameters and leg definitions

This document provides the complete specification for creating valid TBS input files.

## 1. Portfolio File Structure

The portfolio file must contain two sheets: `PortfolioSetting` and `StrategySetting`.

### 1.1 PortfolioSetting Sheet

This sheet defines the overall portfolio parameters.

#### Required Columns:

| Column | Type | Format | Description | Valid Values |
|--------|------|--------|-------------|--------------|
| StartDate | Date | YYYY-MM-DD | Backtest start date | Valid date |
| EndDate | Date | YYYY-MM-DD or DD_MM_YYYY | Backtest end date | Valid date |
| IsTickBT | Boolean | Text | Enable tick-by-tick backtesting | YES/NO |
| Enabled | Boolean | Text | Enable this portfolio | YES/NO |
| PortfolioName | String | Text | Unique portfolio identifier | Any text |

#### Optional Columns (Extra Parameters):

| Column | Type | Default | Description |
|--------|------|---------|-------------|
| PortfolioTarget | Float | None | Portfolio-level profit target |
| PortfolioStoploss | Float | None | Portfolio-level stop loss |
| PortfolioTrailingType | String | None | Type of trailing for portfolio |
| PnLCalTime | Integer | None | Time for P&L calculation (HHMMSS) |
| LockPercent | Float | 0.0 | Percentage to lock profits |
| TrailPercent | Float | 0.0 | Percentage for trailing |
| SqOff1Time | Integer | None | First square-off time (HHMMSS) |
| SqOff1Percent | Float | 0.0 | First square-off percentage |
| SqOff2Time | Integer | None | Second square-off time (HHMMSS) |
| SqOff2Percent | Float | 0.0 | Second square-off percentage |
| ProfitReaches | Float | None | Profit level to trigger actions |
| LockMinProfitAt | Float | None | Minimum profit to lock |
| IncreaseInProfit | Float | None | Profit increment threshold |
| TrailMinProfitBy | Float | None | Minimum profit for trailing |
| Multiplier | Float | 1.0 | Lot size multiplier |
| SlippagePercent | Float | 0.0 | Slippage percentage |

### 1.2 StrategySetting Sheet

This sheet maps strategies to the portfolio.

#### Required Columns:

| Column | Type | Format | Description | Valid Values |
|--------|------|--------|-------------|--------------|
| Enabled | Boolean | Text | Enable this strategy | YES/NO |
| PortfolioName | String | Text | Must match PortfolioSetting | Portfolio name |
| StrategyType | String | Text | Type of strategy | TBS, TV, ORB, OI, INDICATOR |
| StrategyExcelFilePath | String | Path | Path to strategy Excel file | Valid file path |

## 2. Strategy File Structure (TBS Multi Legs)

The strategy file must contain two sheets: `GeneralParameter` and `LegParameter`.

### 2.1 GeneralParameter Sheet

This sheet defines strategy-level parameters.

#### Required Columns:

| Column | Type | Format | Description |
|--------|------|--------|-------------|
| StrategyName | String | Text | Unique strategy identifier |
| StartTime | Time | HHMMSS | Strategy start time (e.g., 091500) |
| EndTime | Time | HHMMSS | Strategy end time (e.g., 152500) |

#### Optional Columns:

| Column | Type | Default | Description |
|--------|------|---------|-------------|
| MoveSlToCost | Boolean | NO | Move SL to cost basis |
| Underlying | String | SPOT | Underlying type (SPOT/FUTURE) |
| Index | String | NIFTY | Index name |
| Weekdays | String | All | Trading days (comma-separated) |
| DTE | Integer | 0 | Days to expiry |
| StrikeSelectionTime | Time | StartTime | Time to select strikes (HHMMSS) |
| LastEntryTime | Time | EndTime | Last entry time (HHMMSS) |
| StrategyProfit | Float | None | Strategy-level profit target |
| StrategyLoss | Float | None | Strategy-level stop loss |
| StrategyProfitReExecuteNo | Integer | 0 | Re-executions after profit |
| StrategyLossReExecuteNo | Integer | 0 | Re-executions after loss |
| StrategyTrailingType | String | None | Strategy trailing type |
| PnLCalTime | Time | None | P&L calculation time |
| ConsiderHedgePnLForStgyPnL | Boolean | NO | Include hedge P&L |
| OnExpiryDayTradeNextExpiry | Boolean | NO | Trade next expiry on expiry day |

### 2.2 LegParameter Sheet

This sheet defines individual legs of the strategy.

#### Required Columns:

| Column | Type | Format | Description | Valid Values |
|--------|------|--------|-------------|--------------|
| StrategyName | String | Text | Must match GeneralParameter | Strategy name |
| LegID | String | Text | Unique leg identifier | Any text |
| Instrument | String | Text | Option type | CE, PE, FUT |
| Transaction | String | Text | Transaction type | BUY, SELL |
| Lots | Integer | Number | Number of lots | > 0 |

#### Important Optional Columns:

| Column | Type | Default | Description | Valid Values |
|--------|------|---------|-------------|--------------|
| IsIdle | Boolean | NO | Mark leg as inactive | YES/NO |
| Expiry | String | CURRENT | Expiry selection | See Expiry Rules |
| StrikeMethod | String | ATM | Strike selection method | See Strike Methods |
| StrikeValue | Float | 0 | Strike offset/value | Numeric |
| StrikeDistance | Integer | 0 | Strike distance (for ITM/OTM) | Numeric |
| W&Type | String | POINT | Wait & Trail type | See W&T Types |
| W&TValue | Float | 0 | Wait & Trail value | Numeric |
| TrailW&T | Boolean | NO | Enable trailing | YES/NO |
| MatchPremium | Float | None | Premium to match | Numeric only |
| StrikePremiumCondition | String | = | Premium condition | =, >, < |

#### Risk Management Columns:

| Column | Type | Description | Valid Values |
|--------|------|-------------|--------------|
| SLType | String | Stop loss type | See SL/TGT Types |
| SLValue | Float | Stop loss value | Numeric |
| TGTType | String | Target type | See SL/TGT Types |
| TGTValue | Float | Target value | Numeric |
| TrailSLType | String | Trailing SL type | See SL/TGT Types |
| SL_TrailAt | Float | Trail activation level | Numeric |
| SL_TrailBy | Float | Trail amount | Numeric |

#### Re-entry Columns:

| Column | Type | Description | Valid Values |
|--------|------|-------------|--------------|
| ReEntryType | String | Re-entry method | See Re-entry Types |
| ReEnteriesCount | Integer | Number of re-entries | >= 0 |

## 3. Valid Values Reference

### Expiry Rules:
- **Current Week**: CURRENT, CW, CURRENT_WEEK, CURRENTWEEK
- **Next Week**: NEXT, NW, NEXT_WEEK, NEXTWEEK
- **Monthly**: MONTHLY, MONTH, M
- **Bucketed**: CM (Current Month), NM (Next Month)

### Strike Methods:
- **Basic**: ATM, ITM, OTM, FIXED
- **Dynamic ITM/OTM**: ITM1, ITM2, OTM1, OTM2, etc.
- **Advanced**: DELTA, PREMIUM, DELTA_TARGET, PREMIUM_EQ
- **Special**: ATM_WIDTH, STRADDLE_WIDTH, ATM_MATCH, ATM_DIFF
- **By Methods**: BY_ATM_STRIKE, BY_CLOSEST_PREMIUM, BY_CLOSEST_DELTA, etc.

### SL/TGT Types (W&Type):
- POINT, POINTS
- PERCENT, PERCENTAGE
- INDEX POINT, INDEX PERCENTAGE
- ABSOLUTE (maps to PREMIUM)
- DELTA (maps to ABSOLUTE_DELTA)

### Re-entry Types:
- IMMEDIATE, IMMIDIATE, IMMIDIATE_NC
- ORIGINAL, AS_ORIGINAL
- RE_COST
- INSTANT_SAME_STRIKE, INSTANT_NEW_STRIKE

## 4. Common Errors and Solutions

### Error: "Invalid numeric value for MatchPremium: high"
**Solution**: Use numeric values only. Replace "high" with actual premium value.

### Error: "Invalid StrategyType"
**Solution**: Use only valid strategy types: TBS, TV, ORB, OI, INDICATOR

### Error: "Missing required columns"
**Solution**: Ensure all required columns are present, even if some rows are empty.

### Error: "Invalid date format"
**Solution**: Use YYYY-MM-DD format for dates (e.g., 2024-01-01)

### Error: "Invalid time format"
**Solution**: Use HHMMSS format for times (e.g., 091500 for 9:15 AM)

## 5. Best Practices

1. **Consistency**: Use uppercase for all YES/NO values
2. **Time Format**: Always use 6-digit HHMMSS format (091500, not 9:15)
3. **File Paths**: Use relative paths from the portfolio file location
4. **Empty Fields**: Leave optional fields blank rather than using placeholder text
5. **Numeric Fields**: Never use text in numeric fields
6. **Strategy Names**: Ensure exact match between GeneralParameter and LegParameter
7. **Validation**: Run validation script before backtesting

## 6. Example Files

### Minimal Portfolio File:

**PortfolioSetting Sheet:**
| StartDate | EndDate | IsTickBT | Enabled | PortfolioName |
|-----------|---------|----------|---------|---------------|
| 2024-01-01 | 2024-01-31 | NO | YES | NIFTY_TBS |

**StrategySetting Sheet:**
| Enabled | PortfolioName | StrategyType | StrategyExcelFilePath |
|---------|---------------|--------------|----------------------|
| YES | NIFTY_TBS | TBS | ./nifty_straddle.xlsx |

### Minimal Strategy File:

**GeneralParameter Sheet:**
| StrategyName | StartTime | EndTime |
|--------------|-----------|---------|
| NIFTY_STRADDLE | 091600 | 152000 |

**LegParameter Sheet:**
| StrategyName | LegID | Instrument | Transaction | Lots |
|--------------|-------|------------|-------------|------|
| NIFTY_STRADDLE | LEG1 | CE | SELL | 1 |
| NIFTY_STRADDLE | LEG2 | PE | SELL | 1 |

## 7. Validation Script

Use the provided `analyze_tbs_input_issues.py` script to validate your input files:

```bash
python3 analyze_tbs_input_issues.py
```

This will check for:
- Missing required columns
- Invalid data formats
- Invalid enumeration values
- Common data entry errors