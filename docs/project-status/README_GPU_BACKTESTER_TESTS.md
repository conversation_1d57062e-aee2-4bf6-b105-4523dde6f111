# GPU Backtester Playwright MCP Test Suite

## Overview

This comprehensive test suite validates the Enhanced GPU Backtester web application using Playwright MCP (Model Context Protocol) browser automation. The tests cover UI functionality, responsiveness, security, performance, and backend operations.

## Test Coverage

### 1. **Authentication Page**
- Design verification against reference image
- Responsive design testing across multiple viewports
- OTP-based login flow validation
- Form field interaction testing

### 2. **Navigation**
- Menu functionality across all pages
- Logo presence verification (MarvelQuant)
- URL routing validation
- Responsive navigation testing

### 3. **New Backtest Page**
- Excel file upload validation
- Invalid file rejection with error messages
- Valid file acceptance (portfolio + strategy files)
- Progress indicator verification
- Excel-to-YAML pipeline validation

### 4. **Logs UI**
- Collapsible log categories (Information, Warning, BT, Error)
- Log filtering and search
- Download functionality
- Debug mode options

### 5. **GPU Performance**
- GPU utilization monitoring
- Performance metrics collection
- Multi-worker configuration testing
- Resource usage tracking

### 6. **Backtesting Systems**
- TBS (Time-Based Strategies) validation
- TV (TradingView) functionality
- OI (Open Interest) testing
- ORB (Opening Range Breakout) verification
- Documentation availability for each system

### 7. **Output Validation**
- Golden format Excel output verification
- Download functionality
- API endpoint testing
- Result accuracy validation

### 8. **Performance & Scalability**
- Load testing with varying strategy counts
- System health monitoring
- HeavyDB connectivity verification
- Response time benchmarking

### 9. **Security Testing**
- Authentication requirement validation
- CSRF protection verification
- Secure cookie handling
- Console security warnings detection
- Overall security scoring

### 10. **Browser Compatibility**
- Chrome testing
- Firefox testing
- Edge testing
- Cross-browser functionality verification

## Files Structure

```
/srv/samba/shared/
├── test_gpu_backtester_mcp.py          # Main test suite implementation
├── run_gpu_backtester_tests.py          # MCP browser executor
├── gpu_backtester_ci_integration.yml    # CI/CD pipeline configuration
├── README_GPU_BACKTESTER_TESTS.md       # This documentation
└── test_screenshots/                    # Screenshot storage directory
```

## Prerequisites

- Python 3.8+
- Access to MCP Playwright browser tools
- Test credentials (Phone: **********, OTP: 123456)
- Network access to http://**************:8000

## Running Tests

### Manual Execution

```bash
# Make the script executable
chmod +x /srv/samba/shared/run_gpu_backtester_tests.py

# Run the test suite
python3 /srv/samba/shared/run_gpu_backtester_tests.py
```

### With Specific Browser

```bash
# Set browser environment variable
export BROWSER=firefox
python3 /srv/samba/shared/run_gpu_backtester_tests.py
```

### Debug Mode

```bash
# Enable debug logging
export LOG_LEVEL=DEBUG
python3 /srv/samba/shared/run_gpu_backtester_tests.py
```

## Test Reports

After execution, the following reports are generated:

1. **Detailed JSON Report**: `gpu_backtester_test_report_YYYYMMDD_HHMMSS.json`
   - Complete test results with all metrics
   - Performance data
   - Security findings
   - Browser compatibility results

2. **Summary Text Report**: `gpu_backtester_test_summary_YYYYMMDD_HHMMSS.txt`
   - High-level overview
   - Pass/fail statistics
   - Key recommendations

3. **Test Logs**: `gpu_backtester_test_YYYYMMDD_HHMMSS.log`
   - Operational logs
   - Test execution details

4. **Debug Logs**: `gpu_backtester_debug_YYYYMMDD_HHMMSS.log`
   - Detailed debug information
   - Browser interactions
   - API calls

## CI/CD Integration

The test suite includes a GitHub Actions workflow (`gpu_backtester_ci_integration.yml`) that:

1. Runs tests on every push to main/develop branches
2. Executes daily scheduled tests
3. Tests across multiple browsers in parallel
4. Generates and archives test artifacts
5. Performs security scanning
6. Runs performance benchmarks
7. Sends notifications on failures

### Setting up CI/CD

1. Copy `gpu_backtester_ci_integration.yml` to `.github/workflows/`
2. Configure secrets in GitHub repository settings:
   - `BASE_URL`: Application URL
   - `TEST_PHONE`: Test phone number
   - `TEST_OTP`: Test OTP code

## Test Scenarios

### Happy Path Testing
- Complete user journey from login to backtest completion
- All UI elements functioning correctly
- Successful file uploads and processing

### Error Handling
- Invalid file uploads
- Network failures
- Authentication failures
- Missing required fields

### Edge Cases
- Large file uploads
- Concurrent backtest runs
- Session timeouts
- Browser back/forward navigation

## Interpreting Results

### Pass Criteria
- All critical tests pass (Authentication, Navigation, File Upload)
- Pass rate > 95%
- No high-severity security issues
- API response times < 1 second

### Warning Indicators
- Missing UI elements that don't block functionality
- Performance degradation under load
- Minor responsive design issues
- Console warnings

### Failure Indicators
- Authentication failures
- Navigation breakage
- File upload failures
- API endpoint errors
- Security vulnerabilities

## Maintenance

### Adding New Tests

1. Add test method to `GPUBacktesterTestSuite` class
2. Follow naming convention: `test_feature_name()`
3. Use consistent logging and error handling
4. Update test execution order in `run_tests_with_mcp()`

### Updating Selectors

When UI changes occur, update element selectors in the test methods:
- Use semantic selectors when possible
- Fallback to multiple selector options
- Document selector changes

### Performance Baselines

Regularly update performance baselines:
- Average GPU utilization targets
- API response time thresholds
- Page load time expectations

## Troubleshooting

### Common Issues

1. **Browser Installation Failed**
   - Run MCP browser install command manually
   - Check system requirements

2. **Authentication Failures**
   - Verify test credentials are correct
   - Check if login flow has changed

3. **Element Not Found**
   - UI may have changed
   - Update selectors in test code
   - Check if page fully loaded

4. **Performance Test Failures**
   - System may be under load
   - Adjust performance thresholds
   - Check GPU availability

### Debug Tips

1. Enable debug logging for detailed output
2. Run individual tests to isolate issues
3. Check screenshots for visual debugging
4. Review network requests in logs
5. Verify test environment matches production

## Best Practices

1. **Test Independence**: Each test should be able to run independently
2. **Cleanup**: Always cleanup test data after execution
3. **Idempotency**: Tests should produce same results on repeated runs
4. **Documentation**: Update this README when adding new tests
5. **Error Handling**: Gracefully handle and report all errors

## Contact

For issues or questions regarding the test suite:
- Check logs in `/srv/samba/shared/`
- Review test reports for specific failures
- Consult UI screenshots for visual issues

---

**Last Updated**: January 2025  
**Version**: 1.0.0  
**Maintainer**: MarvelQuant Test Automation Team