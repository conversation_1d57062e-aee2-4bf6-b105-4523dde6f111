# Simple Shotgun

A simplified version of the [shotgun_code](https://github.com/glebkudr/shotgun_code) tool for capturing your codebase as a single context for use with LLMs.

## Overview

Simple Shotgun generates a text representation of your code directory structure and file contents, formatted in a way that makes it easy for Large Language Models (LLMs) to understand and process your codebase.

## Features

- Creates a visual directory tree structure
- Includes full file contents with XML-like delimiters
- Supports file/directory exclusion through:
  - Command-line exclude patterns
  - Standard .gitignore-style patterns in ignore.glob file

## Usage

```
./simple_shotgun -dir /path/to/your/project -out output.txt
```

### Command-line Options

```
Usage of ./simple_shotgun:
  -dir string
        Directory to scan (required)
  -exclude string
        Comma-separated list of paths to exclude
  -ignore string
        Path to ignore.glob file (default "ignore.glob")
  -no-ignore
        Disable using ignore.glob file
  -out string
        Output file path (default "shotgun_output.txt")
```

## Example

```bash
# Generate a shotgun output for a project
./simple_shotgun -dir /path/to/project -out project_context.txt

# Generate output excluding specific directories
./simple_shotgun -dir /path/to/project -exclude node_modules,dist -out project_context.txt

# Use a custom ignore file
./simple_shotgun -dir /path/to/project -ignore custom_ignore.glob -out project_context.txt

# Disable ignore file processing
./simple_shotgun -dir /path/to/project -no-ignore -out project_context.txt
```

## Output Format

The output file contains:
1. A directory tree representation
2. File contents wrapped in XML-like tags

Example:
```
myproject/
├── src/
│   ├── main.go
│   └── utils/
│       └── helper.go
└── README.md

<file path="src/main.go">
package main

func main() {
    println("Hello, world!")
}
</file>

<file path="src/utils/helper.go">
package utils

func Helper() string {
    return "I'm a helper function"
}
</file>

<file path="README.md">
# My Project

A simple example project.
</file>
```

## How to Use with LLMs

1. Generate the shotgun output:
   ```
   ./simple_shotgun -dir /path/to/project -out context.txt
   ```

2. Copy the contents of the output file

3. Paste into your LLM prompt, along with your question or task:
   ```
   Here's my codebase:

   [Paste shotgun output here]

   Can you help me understand how the XYZ functionality works?
   ```

## Building from Source

```bash
go build -o simple_shotgun simple_shotgun.go
```

## Acknowledgments

This is a simplified version of the [shotgun_code](https://github.com/glebkudr/shotgun_code) tool by Gleb Kudr. 