{"portfolio_name": "NIF0DTE", "portfolio_config": {"StartDate": "01_04_2025", "EndDate": "26_12_2026", "IsTickBT": "no", "Enabled": "YES", "PortfolioName": "NIF0DTE", "PortfolioTarget": 0, "PortfolioStoploss": 0, "PortfolioTrailingType": "portfolio lock trail", "PnLCalTime": 233000, "LockPercent": 0, "TrailPercent": 0, "SqOff1Time": 233000, "SqOff1Percent": 0, "SqOff2Time": 233000, "SqOff2Percent": 0, "ProfitReaches": 0, "LockMinProfitAt": 0, "IncreaseInProfit": 0, "TrailMinProfitBy": 0, "Multiplier": 1, "SlippagePercent": 0.1}, "start_date": "01_04_2025", "end_date": "26_12_2026", "strategies": [{"Enabled": "NO", "PortfolioName": "NIF0DTE", "StrategyType": "ORB", "StrategyExcelFilePath": "D:\\Ajay\\BT PORTFOLIO\\INPUT SHEETS\\INPUT ORB.xlsx"}, {"Enabled": "NO", "PortfolioName": "NIF0DTE", "StrategyType": "ORB", "StrategyExcelFilePath": "D:\\Ajay\\BT PORTFOLIO\\INPUT SHEETS\\INPUT EXPIRY 1505.xlsx"}, {"Enabled": "NO", "PortfolioName": "NIF0DTE", "StrategyType": "IBS", "StrategyExcelFilePath": "D:\\Ajay\\BT PORTFOLIO\\INPUT SHEETS\\INPUT INDICATOR.xlsx"}, {"Enabled": "NO", "PortfolioName": "NIF0DTE", "StrategyType": "HEIKIN_RSI_EMA", "StrategyExcelFilePath": "D:\\Ajay\\BT PORTFOLIO\\INPUT SHEETS\\INPUT HA (RSI+EMA).xlsx"}, {"Enabled": "NO", "PortfolioName": "NIF0DTE", "StrategyType": "HEIKIN_RSI_ST", "StrategyExcelFilePath": "D:\\Ajay\\BT PORTFOLIO\\INPUT SHEETS\\INPUT HA (RSI+ST).xlsx"}, {"Enabled": "NO", "PortfolioName": "NIF0DTE", "StrategyType": "TBS", "StrategyExcelFilePath": "D:\\Ajay\\BT PORTFOLIO\\INPUT SHEETS\\INPUT TBS.xlsx"}, {"Enabled": "NO", "PortfolioName": "NIF0DTE", "StrategyType": "TBS", "StrategyExcelFilePath": "D:\\Ajay\\BT Portfolio\\INPUT SHEETS\\NGSTR.xlsx"}, {"Enabled": "NO", "PortfolioName": "NIF0DTE", "StrategyType": "TBS", "StrategyExcelFilePath": "D:\\Ajay\\BT Portfolio\\INPUT SHEETS\\CRSTR.xlsx"}, {"Enabled": "NO", "PortfolioName": "NIF0DTE", "StrategyType": "TBS", "StrategyExcelFilePath": "D:\\Ajay\\BT PORTFOLIO\\INPUT SHEETS\\INPUT TBS 3.xlsx"}, {"Enabled": "NO", "PortfolioName": "NIF0DTE", "StrategyType": "TBS", "StrategyExcelFilePath": "D:\\Ajay\\BT PORTFOLIO\\INPUT SHEETS\\INPUT TBS 4.xlsx"}, {"Enabled": "NO", "PortfolioName": "NIF0DTE", "StrategyType": "TBS", "StrategyExcelFilePath": "D:\\Ajay\\BT PORTFOLIO\\INPUT SHEETS\\INPUT TBS 5.xlsx"}, {"Enabled": "NO", "PortfolioName": "NIF0DTE", "StrategyType": "TBS", "StrategyExcelFilePath": "D:\\Ajay\\BT Portfolio\\INPUT SHEETS\\INPUT MCX TBS.xlsx"}, {"Enabled": "NO", "PortfolioName": "NIF0DTE", "StrategyType": "5_EMA", "StrategyExcelFilePath": "D:\\Ajay\\BT PORTFOLIO\\INPUT SHEETS\\INPUT 5EMA.xlsx"}, {"Enabled": "NO", "PortfolioName": "NIF0DTE", "StrategyType": "TBS", "StrategyExcelFilePath": "D:\\Ajay\\BT Portfolio\\INPUT SHEETS\\INPUT MCX TBS.xlsx"}, {"Enabled": "YES", "PortfolioName": "NIF0DTE", "StrategyType": "TBS", "StrategyExcelFilePath": "/srv/samba/shared/bt/backtester_stable/input_sheets/input_tbs_multi_legs.xlsx"}, {"Enabled": "NO", "PortfolioName": "NIF0DTE", "StrategyType": "OI", "StrategyExcelFilePath": "D:\\Ajay\\BT PORTFOLIO\\INPUT SHEETS\\INPUT OI.xlsx"}, {"Enabled": "NO", "PortfolioName": "NIF0DTE", "StrategyType": "TBS", "StrategyExcelFilePath": "D:\\Ajay\\BT Portfolio\\INPUT SHEETS\\INPUT TBS - BN.xlsx"}, {"Enabled": "NO", "PortfolioName": "NIF0DTE", "StrategyType": "TBS", "StrategyExcelFilePath": "D:\\Ajay\\BT Portfolio\\INPUT SHEETS\\INPUT TBS - NF.xlsx"}, {"Enabled": "NO", "PortfolioName": "NIF0DTE", "StrategyType": "TBS", "StrategyExcelFilePath": "D:\\Ajay\\BT Portfolio\\INPUT SHEETS\\INPUT TBS - SN.xlsx"}, {"Enabled": "NO", "PortfolioName": "NIF0DTE", "StrategyType": "TBS", "StrategyExcelFilePath": "D:\\Ajay\\BT Portfolio\\INPUT SHEETS\\INPUT TBS - NF - Copy.xlsx"}, {"Enabled": "NO", "PortfolioName": "NIF0DTE", "StrategyType": "TBS", "StrategyExcelFilePath": "D:\\Ajay\\BT Portfolio\\INPUT SHEETS\\INPUT TBS - SN - Copy.xlsx"}], "data": {"trades": [], "current_m2m": {}}}