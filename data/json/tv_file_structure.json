[{"file": "input_tv.xlsx", "sheets": {"Setting": {"columns": ["StartDate", "EndDate", "SignalDateFormat", "Enabled", "TvExitApplicable", "ManualTradeEntryTime", "ManualTradeLots", "IncreaseEntrySignalTimeBy", "IncreaseExitSignalTimeBy", "IntradaySqOffApplicable", "FirstTradeEntryTime", "IntradayExitTime", "ExpiryDayExitTime", "DoRollover", "RolloverTime", "Name", "SignalFilePath", "LongPortfolioFilePath", "ShortPortfolioFilePath", "ManualPortfolioFilePath", "UseDbExitTiming", "ExitSearchInterval", "ExitPriceSource", "SlippagePercent"], "shape": [1, 24], "references": ["C:\\Users\\<USER>\\Downloads\\sample_nifty_list_of_trades.xlsx", "C:\\Users\\<USER>\\Downloads\\input_portfolio_long.xlsx", "D:\\Ajay\\BT PORTFOLIO\\INPUT SHEETS\\INPUT PORTFOLIO SHORT.xlsx"]}}}, {"file": "input_tbs_long.xlsx", "sheets": {"GeneralParameter": {"columns": ["StrategyName", "MoveSlToCost", "Underlying", "Index", "Weekdays", "DTE", "StrikeSelectionTime", "StartTime", "LastEntryTime", "EndTime", "StrategyProfit", "StrategyLoss", "StrategyProfitReExecuteNo", "StrategyLossReExecuteNo", "StrategyTrailingType", "ProfitReaches", "LockMinProfitAt", "IncreaseInProfit", "TrailMinProfitBy", "TgtTrackingFrom", "TgtRegisterPriceFrom", "SlTrackingFrom", "SlRegisterPriceFrom", "PnLCalculationFrom", "ConsiderHedgePnLForStgyPnL", "CheckPremiumDiffCondition", "PremiumDiffType", "PremiumDiffValue", "PremiumDiffChangeStrike", "PremiumDiffDoForceEntry", "PremiumDiffDoForceAfter", "PremiumDiffForceEntryConsiderPremium", "StoplossCheckingInterval", "TargetCheckingInterval", "ReEntryCheckingInterval", "OnExpiryDayTradeNextExpiry"], "shape": [1, 36], "references": []}, "LegParameter": {"columns": ["StrategyName", "LegID", "Instrument", "Transaction", "Expiry", "MatchPremium", "W&Type", "W&TValue", "TrailW&T", "StrikeMethod", "StrikeValue", "StrikePremiumCondition", "SLType", "SLValue", "TGTType", "TGTValue", "TrailSLType", "SL_TrailAt", "SL_TrailBy", "Lots", "SL_ReEntryType", "SL_ReEntryNo", "TGT_ReEntryType", "TGT_ReEntryNo", "OpenHedge", "HedgeStrikeMethod", "HedgeStrikeValue", "HedgeStrikePremiumCondition"], "shape": [4, 28], "references": []}}}, {"file": "input_tbs_short.xlsx", "sheets": {"GeneralParameter": {"columns": ["StrategyName", "MoveSlToCost", "Underlying", "Index", "Weekdays", "DTE", "StrikeSelectionTime", "StartTime", "LastEntryTime", "EndTime", "StrategyProfit", "StrategyLoss", "StrategyProfitReExecuteNo", "StrategyLossReExecuteNo", "StrategyTrailingType", "ProfitReaches", "LockMinProfitAt", "IncreaseInProfit", "TrailMinProfitBy", "TgtTrackingFrom", "TgtRegisterPriceFrom", "SlTrackingFrom", "SlRegisterPriceFrom", "PnLCalculationFrom", "ConsiderHedgePnLForStgyPnL", "CheckPremiumDiffCondition", "PremiumDiffType", "PremiumDiffValue", "PremiumDiffChangeStrike", "PremiumDiffDoForceEntry", "PremiumDiffDoForceAfter", "PremiumDiffForceEntryConsiderPremium", "StoplossCheckingInterval", "TargetCheckingInterval", "ReEntryCheckingInterval", "OnExpiryDayTradeNextExpiry"], "shape": [1, 36], "references": []}, "LegParameter": {"columns": ["StrategyName", "LegID", "Instrument", "Transaction", "Expiry", "W&Type", "MatchPremium", "W&TValue", "TrailW&T", "StrikeMethod", "StrikeValue", "StrikePremiumCondition", "SLType", "SLValue", "TGTType", "TGTValue", "TrailSLType", "SL_TrailAt", "SL_TrailBy", "Lots", "SL_ReEntryType", "SL_ReEntryNo", "TGT_ReEntryType", "TGT_ReEntryNo", "OpenHedge", "HedgeStrikeMethod", "HedgeStrikeValue", "HedgeStrikePremiumCondition"], "shape": [4, 28], "references": []}}}, {"file": "input_portfolio_long.xlsx", "sheets": {"PortfolioSetting": {"columns": ["StartDate", "EndDate", "IsTickBT", "Enabled", "PortfolioName", "PortfolioTarget", "PortfolioStoploss", "PortfolioTrailingType", "PnLCalTime", "LockPercent", "TrailPercent", "SqOff1Time", "SqOff1Percent", "SqOff2Time", "SqOff2Percent", "ProfitReaches", "LockMinProfitAt", "IncreaseInProfit", "TrailMinProfitBy", "Multiplier"], "shape": [1, 20], "references": []}, "StrategySetting": {"columns": ["Enabled", "PortfolioName", "StrategyType", "StrategyExcelFilePath"], "shape": [1, 4], "references": ["C:\\Users\\<USER>\\Downloads\\input_tbs_long.xlsx"]}}}, {"file": "input_portfolio_short.xlsx", "sheets": {"PortfolioSetting": {"columns": ["StartDate", "EndDate", "IsTickBT", "Enabled", "PortfolioName", "PortfolioTarget", "PortfolioStoploss", "PortfolioTrailingType", "PnLCalTime", "LockPercent", "TrailPercent", "SqOff1Time", "SqOff1Percent", "SqOff2Time", "SqOff2Percent", "ProfitReaches", "LockMinProfitAt", "IncreaseInProfit", "TrailMinProfitBy", "Multiplier"], "shape": [1, 20], "references": []}, "StrategySetting": {"columns": ["Enabled", "PortfolioName", "StrategyType", "StrategyExcelFilePath"], "shape": [1, 4], "references": ["C:\\Users\\<USER>\\Downloads\\input_tbs_short.xlsx"]}}}, {"file": "sample_nifty_list_of_trades.xlsx", "sheets": {"Performance": {"columns": ["Unnamed: 0", "All INR", "All %", "Long INR", "Long %", "Short INR", "Short %"], "shape": [9, 7], "references": []}, "Trades analysis": {"columns": ["Unnamed: 0", "All INR", "All %", "Long INR", "Long %", "Short INR", "Short %"], "shape": [16, 7], "references": []}, "Risk performance ratios": {"columns": ["Unnamed: 0", "All INR", "All %", "Long INR", "Long %", "Short INR", "Short %"], "shape": [4, 7], "references": []}, "List of trades": {"columns": ["Trade #", "Type", "Signal", "Date/Time", "Price INR", "Contracts", "Profit INR", "Profit %", "Cumulative profit INR", "Cumulative profit %", "Run-up INR", "Run-up %", "Drawdown INR", "Drawdown %"], "shape": [6886, 14], "references": []}, "Properties": {"columns": ["name", "value"], "shape": [110, 2], "references": []}}}]