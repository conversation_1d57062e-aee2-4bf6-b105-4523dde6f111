{"phase": "3.1", "start_time": "2025-06-09T19:00:38.932266", "tests": {"basic_straddle": {"executed": true, "returncode": 2, "output_exists": false, "stdout": "CONFIG.PY: Attempting to load config.py...\nCONFIG.PY: Finished loading config.py.\nRunning GPU backtester with E2E testing fixes...\n", "stderr": "                              PORTFOLIO_EXCEL --output-path OUTPUT_PATH\n                                   [--gpu-workers GPU_WORKERS]\n                                   [--cpu-workers CPU_WORKERS]\n                                   [--batch-days BATCH_DAYS]\n                                   [--gpu-threshold GPU_THRESHOLD]\n                                   [--use-gpu-optimization]\nBTRunPortfolio_GPU_Fixed.py: error: ambiguous option: --portfolio could match --portfolio-name, --portfolio-excel\n"}}, "decision": {"criteria": {"diagnostic_ready": true, "fixes_applied": true, "basic_test_passed": false, "trades_closed": false}, "ready_for_phase_3_2": false, "timestamp": "2025-06-09T19:00:42.431314"}, "diagnostic": {"timestamp": "2025-06-09T19:00:38.964661", "checks": {"gpu_backtester_exists": true, "test_files_count": 5, "heavydb_connected": true, "heavydb_rows": 16659808, "gpu_available": true, "code_analysis": {"exit_logic": true, "multi_leg_loop": true, "output_format": true}}, "ready_for_testing": true, "blocking_issues": [], "gpu_backtester_path": "/srv/samba/shared/bt/backtester_stable/BTRUN/BTRunPortfolio_GPU.py", "gpu_info": "NVIDIA A100-SXM4-40GB, 40960 MiB"}, "fixes_applied": true, "end_time": "2025-06-09T19:00:42.431742", "duration": "0:00:03.499481"}