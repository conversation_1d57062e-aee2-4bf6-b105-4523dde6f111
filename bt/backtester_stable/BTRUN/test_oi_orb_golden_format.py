#!/usr/bin/env python3
"""
OI and ORB Golden Format Integration Test

This script tests the OI and ORB golden format integration:
1. OI golden format generator with dynamic strategy count
2. ORB golden format generator with dynamic timeframes
3. Integration with BT_OI_GPU.py
4. 32-column transaction format compliance
5. Day-wise results format
"""

import os
import sys
import logging
import pandas as pd
from datetime import datetime
import tempfile

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s [%(levelname)s] %(name)s: %(message)s"
)
logger = logging.getLogger("OI_ORB_Golden_Format_Test")

# Add BTRUN to path
script_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, script_dir)

def test_oi_golden_format_generator():
    """Test the OI golden format generator"""
    logger.info("🧪 Testing OI Golden Format Generator")
    
    try:
        # Import golden format generator
        sys.path.append(os.path.join(script_dir, 'utils'))
        from golden_format_excel_generator import GoldenFormatExcelGenerator
        
        generator = GoldenFormatExcelGenerator()
        logger.info("✅ OI golden format generator imported successfully")
        
        # Create test data
        portfolio_trans_df = pd.DataFrame({
            'entry_date': ['2024-01-03'] * 50,
            'exit_date': ['2024-01-03'] * 50,
            'pnl': [1000.0, -500.0] * 25,
            'symbol': ['NIFTY'] * 50,
            'strategy': ['MAXOI', 'MAXCOI'] * 25
        })
        
        strategy_data = {
            'MAXOI': {
                'transactions': portfolio_trans_df[:25],
                'total_trades': 25,
                'total_pnl': 12500.0,
                'strategy_type': 'OI'
            },
            'MAXCOI': {
                'transactions': portfolio_trans_df[25:],
                'total_trades': 25,
                'total_pnl': 12500.0,
                'strategy_type': 'OI'
            }
        }
        
        oi_settings = {
            'portfolio_file': 'test_oi_portfolio.xlsx',
            'strategy_type': 'OI',
            'symbol': 'NIFTY',
            'total_strategies': 2
        }
        
        # Test OI golden format generation
        with tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False) as tmp:
            output_path = tmp.name
        
        success = generator.generate_oi_golden_format(
            portfolio_trans_df=portfolio_trans_df,
            strategy_data=strategy_data,
            oi_settings=oi_settings,
            output_path=output_path
        )
        
        if success and os.path.exists(output_path):
            # Verify the file structure
            xl = pd.ExcelFile(output_path)
            sheet_names = xl.sheet_names
            
            logger.info(f"✅ OI golden format generated with {len(sheet_names)} sheets")
            
            # Check for required base sheets
            base_sheets = ['PortfolioParameter', 'GeneralParameter', 'LegParameter', 'Metrics', 'Max Profit and Loss']
            base_sheets_found = all(sheet in sheet_names for sheet in base_sheets)
            
            # Check for strategy sheets
            strategy_sheets_found = any(sheet.startswith('MAXOI') and 'Trans' in sheet for sheet in sheet_names)
            
            if base_sheets_found and strategy_sheets_found:
                logger.info("✅ OI golden format test passed")
                os.unlink(output_path)
                return True
            else:
                logger.error(f"❌ Missing required sheets. Found: {sheet_names}")
                os.unlink(output_path)
                return False
        else:
            logger.error("❌ OI golden format generation test failed")
            return False
            
    except Exception as e:
        logger.error(f"❌ OI golden format generator test failed: {e}")
        return False

def test_orb_golden_format_generator():
    """Test the ORB golden format generator"""
    logger.info("🧪 Testing ORB Golden Format Generator")
    
    try:
        # Import golden format generator
        sys.path.append(os.path.join(script_dir, 'utils'))
        from golden_format_excel_generator import GoldenFormatExcelGenerator
        
        generator = GoldenFormatExcelGenerator()
        logger.info("✅ ORB golden format generator imported successfully")
        
        # Create test data
        portfolio_trans_df = pd.DataFrame({
            'entry_date': ['2024-01-03'] * 60,
            'exit_date': ['2024-01-03'] * 60,
            'pnl': [1000.0, -500.0, 750.0] * 20,
            'symbol': ['NIFTY'] * 60,
            'strategy': ['ORB_15MIN', 'ORB_30MIN', 'ORB_60MIN'] * 20
        })
        
        strategy_data = {
            'ORB_15MIN': {
                'transactions': portfolio_trans_df[:20],
                'total_trades': 20,
                'total_pnl': 10000.0,
                'strategy_type': 'ORB'
            },
            'ORB_30MIN': {
                'transactions': portfolio_trans_df[20:40],
                'total_trades': 20,
                'total_pnl': 10000.0,
                'strategy_type': 'ORB'
            },
            'ORB_60MIN': {
                'transactions': portfolio_trans_df[40:],
                'total_trades': 20,
                'total_pnl': 15000.0,
                'strategy_type': 'ORB'
            }
        }
        
        orb_settings = {
            'portfolio_file': 'test_orb_portfolio.xlsx',
            'strategy_type': 'ORB',
            'symbol': 'NIFTY',
            'total_strategies': 3
        }
        
        # Test ORB golden format generation
        with tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False) as tmp:
            output_path = tmp.name
        
        success = generator.generate_orb_golden_format(
            portfolio_trans_df=portfolio_trans_df,
            strategy_data=strategy_data,
            orb_settings=orb_settings,
            output_path=output_path
        )
        
        if success and os.path.exists(output_path):
            # Verify the file structure
            xl = pd.ExcelFile(output_path)
            sheet_names = xl.sheet_names
            
            logger.info(f"✅ ORB golden format generated with {len(sheet_names)} sheets")
            
            # Check for required base sheets
            base_sheets = ['PortfolioParameter', 'GeneralParameter', 'LegParameter', 'Metrics', 'Max Profit and Loss']
            base_sheets_found = all(sheet in sheet_names for sheet in base_sheets)
            
            # Check for strategy sheets
            strategy_sheets_found = any(sheet.startswith('ORB_') and 'Trans' in sheet for sheet in sheet_names)
            
            if base_sheets_found and strategy_sheets_found:
                logger.info("✅ ORB golden format test passed")
                os.unlink(output_path)
                return True
            else:
                logger.error(f"❌ Missing required sheets. Found: {sheet_names}")
                os.unlink(output_path)
                return False
        else:
            logger.error("❌ ORB golden format generation test failed")
            return False
            
    except Exception as e:
        logger.error(f"❌ ORB golden format generator test failed: {e}")
        return False

def test_dynamic_strategy_generation():
    """Test dynamic strategy generation based on input"""
    logger.info("🧪 Testing Dynamic Strategy Generation")
    
    try:
        # Import golden format generator
        sys.path.append(os.path.join(script_dir, 'utils'))
        from golden_format_excel_generator import GoldenFormatExcelGenerator
        
        generator = GoldenFormatExcelGenerator()
        
        # Test with different strategy counts
        test_cases = [
            {
                'name': 'Single OI Strategy',
                'strategy_data': {
                    'MAXOI': {'transactions': pd.DataFrame({'pnl': [1000]}), 'total_trades': 1, 'total_pnl': 1000, 'strategy_type': 'OI'}
                },
                'expected_sheets': 7  # 5 base + 2 strategy sheets
            },
            {
                'name': 'Multiple ORB Strategies',
                'strategy_data': {
                    'ORB_15MIN': {'transactions': pd.DataFrame({'pnl': [1000]}), 'total_trades': 1, 'total_pnl': 1000, 'strategy_type': 'ORB'},
                    'ORB_30MIN': {'transactions': pd.DataFrame({'pnl': [500]}), 'total_trades': 1, 'total_pnl': 500, 'strategy_type': 'ORB'},
                    'ORB_60MIN': {'transactions': pd.DataFrame({'pnl': [750]}), 'total_trades': 1, 'total_pnl': 750, 'strategy_type': 'ORB'}
                },
                'expected_sheets': 11  # 5 base + 6 strategy sheets (3 strategies × 2 sheets each)
            }
        ]
        
        for test_case in test_cases:
            logger.info(f"Testing: {test_case['name']}")
            
            portfolio_trans_df = pd.DataFrame({'pnl': [1000], 'symbol': ['NIFTY']})
            settings = {'strategy_type': 'TEST', 'symbol': 'NIFTY'}
            
            with tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False) as tmp:
                output_path = tmp.name
            
            # Test with OI format (same logic applies to ORB)
            success = generator.generate_oi_golden_format(
                portfolio_trans_df=portfolio_trans_df,
                strategy_data=test_case['strategy_data'],
                oi_settings=settings,
                output_path=output_path
            )
            
            if success and os.path.exists(output_path):
                xl = pd.ExcelFile(output_path)
                actual_sheets = len(xl.sheet_names)
                
                if actual_sheets == test_case['expected_sheets']:
                    logger.info(f"✅ {test_case['name']}: {actual_sheets} sheets generated correctly")
                else:
                    logger.error(f"❌ {test_case['name']}: Expected {test_case['expected_sheets']}, got {actual_sheets}")
                    return False
                
                os.unlink(output_path)
            else:
                logger.error(f"❌ {test_case['name']}: Generation failed")
                return False
        
        logger.info("✅ Dynamic strategy generation test passed")
        return True
        
    except Exception as e:
        logger.error(f"❌ Dynamic strategy generation test failed: {e}")
        return False

def test_universal_base_sheets():
    """Test universal base sheets creation"""
    logger.info("🧪 Testing Universal Base Sheets Creation")
    
    try:
        # Import golden format generator
        sys.path.append(os.path.join(script_dir, 'utils'))
        from golden_format_excel_generator import GoldenFormatExcelGenerator
        
        generator = GoldenFormatExcelGenerator()
        
        # Test universal base sheets for different strategy types
        strategy_types = ['OI', 'ORB', 'TBS', 'TV']
        
        for strategy_type in strategy_types:
            logger.info(f"Testing base sheets for {strategy_type}")
            
            portfolio_trans_df = pd.DataFrame({'pnl': [1000], 'symbol': ['NIFTY']})
            settings = {'strategy_type': strategy_type, 'symbol': 'NIFTY'}
            
            with tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False) as tmp:
                output_path = tmp.name
            
            # Create Excel writer and test base sheets creation
            with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
                generator._create_universal_base_sheets(writer, portfolio_trans_df, settings, strategy_type)
            
            # Verify base sheets
            xl = pd.ExcelFile(output_path)
            sheet_names = xl.sheet_names
            
            expected_base_sheets = ['PortfolioParameter', 'GeneralParameter', 'LegParameter', 'Metrics', 'Max Profit and Loss']
            base_sheets_found = all(sheet in sheet_names for sheet in expected_base_sheets)
            
            if base_sheets_found and len(sheet_names) == 5:
                logger.info(f"✅ {strategy_type} base sheets created correctly")
            else:
                logger.error(f"❌ {strategy_type} base sheets creation failed. Found: {sheet_names}")
                os.unlink(output_path)
                return False
            
            os.unlink(output_path)
        
        logger.info("✅ Universal base sheets test passed")
        return True
        
    except Exception as e:
        logger.error(f"❌ Universal base sheets test failed: {e}")
        return False

def main():
    """Run all OI and ORB golden format tests"""
    logger.info("🚀 Starting OI and ORB Golden Format Integration Tests")
    logger.info("="*70)
    
    tests = [
        ("OI Golden Format Generator", test_oi_golden_format_generator),
        ("ORB Golden Format Generator", test_orb_golden_format_generator),
        ("Dynamic Strategy Generation", test_dynamic_strategy_generation),
        ("Universal Base Sheets", test_universal_base_sheets)
    ]
    
    results = []
    for test_name, test_func in tests:
        logger.info(f"\n📋 Running: {test_name}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            logger.error(f"💥 {test_name} crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    logger.info("\n" + "="*70)
    logger.info("📊 OI AND ORB GOLDEN FORMAT TEST RESULTS SUMMARY")
    logger.info("="*70)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        logger.info(f"{status} - {test_name}")
        if result:
            passed += 1
    
    logger.info(f"\n🎯 Overall Result: {passed}/{total} tests passed")
    
    if passed == total:
        logger.info("🎉 ALL OI AND ORB GOLDEN FORMAT TESTS PASSED!")
        logger.info("✅ OI and ORB Golden Format Integration Phase 3 is ready for deployment")
        return 0
    else:
        logger.error("❌ Some tests failed. Please review and fix issues before deployment.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
