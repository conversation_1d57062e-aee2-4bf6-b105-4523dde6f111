#!/usr/bin/env python3
"""
Technical Verification Backtesting with GPU Acceleration

This script runs technical verification backtests with GPU acceleration.
Maintains backward compatibility with legacy input formats.
"""

import os
import sys
import json
import argparse
import logging
import pandas as pd
from datetime import datetime, date
from typing import Dict, Any, Optional, List
import re # For extracting underlying from name
from pydantic import ValidationError

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler(f"bt_tv_gpu_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log")
    ]
)
logger = logging.getLogger(__name__)

# ─────────────────────────────────────────────────────────────────────────────
# GPU Optimization imports (Phase 2.B.5)
# ─────────────────────────────────────────────────────────────────────────────
GPU_OPTIMIZATION_AVAILABLE = False
try:
    from common.gpu_worker_pool import GPUWorkerPool, GPUWorkerConfig
    from common.gpu_query_optimizer import HeavyDBQueryOptimizer, QueryBatcher
    GPU_OPTIMIZATION_AVAILABLE = True
except ImportError:
    # Will log this warning after logger is initialized
    pass

# Import BTRUN modules
try:
    # Try package imports first
    from bt.backtester_stable.BTRUN.core import config, gpu_helpers, runtime, io
    from bt.backtester_stable.models.tv_models import TvSettingModel, RawTvSignalModel, ProcessedTvSignalModel
    from bt.backtester_stable.BTRUN.models import PortfolioModel, StrategyModel, LegModel # For type hinting
    from bt.backtester_stable.BTRUN.models.common import hhmmss_to_time # For time conversion
    from bt.backtester_stable.BTRUN.strategies.tv_processor import process_tv_signals
    from bt.backtester_stable.BTRUN.excel_parser import portfolio_parser as main_portfolio_parser
except ImportError:
    # Fallback to local imports
    from core import config
    # Also import ProcessedTvSignalModel for isinstance checks
    try:
        from models.tv_models import ProcessedTvSignalModel as LocalProcessedTvSignalModel
        # Make it available in the module namespace
        if 'ProcessedTvSignalModel' not in globals():
            ProcessedTvSignalModel = LocalProcessedTvSignalModel
    except ImportError:
        pass
    try:
        from core import gpu_helpers, runtime, io
    except ImportError:
        # Create minimal stubs if modules don't exist
        class gpu_helpers:
            @staticmethod
            def force_cpu_mode(): pass
            @staticmethod
            def is_gpu_available(): return False
            @staticmethod
            def get_gpu_memory_info(): return None
        
        class runtime:
            @staticmethod
            def run_full_backtest(params): 
                return {"success": False, "error": "Runtime not available"}
        
        class io:
            @staticmethod
            def write_results(output_file, backtest_result, portfolio_model):
                pass
    
    # Import models with fallback
    try:
        import sys
        import os
        models_path = os.path.join(os.path.dirname(__file__), 'models')
        if models_path not in sys.path:
            sys.path.insert(0, models_path)
        from bt.backtester_stable.models.tv_models import TvSettingModel, RawTvSignalModel, ProcessedTvSignalModel
    except ImportError:
        print("Warning: Could not import TV models")
        class TvSettingModel: pass
        class RawTvSignalModel: pass
        class ProcessedTvSignalModel: pass
    
    try:
        from models import PortfolioModel, StrategyModel, LegModel
        from models.common import hhmmss_to_time
    except ImportError:
        print("Warning: Could not import portfolio models")
        class PortfolioModel: pass
        class StrategyModel: pass
        class LegModel: pass
        def hhmmss_to_time(s): 
            from datetime import time
            s = str(s).zfill(6)
            return time(int(s[:2]), int(s[2:4]), int(s[4:6]))
    
    try:
        from strategies import tv_processor
        process_tv_signals = tv_processor.process_tv_signals
    except ImportError:
        print("Warning: Could not import TV processor")
        def process_tv_signals(*args, **kwargs):
            return {"LONG": [], "SHORT": [], "MANUAL": []}
    
    try:
        from excel_parser import portfolio_parser as main_portfolio_parser
    except ImportError:
        print("Warning: Could not import portfolio parser")
        class main_portfolio_parser:
            @staticmethod
            def parse_portfolio_excel(path):
                return {}

# Import ValidationError for model validation
from pydantic import ValidationError

# Optional imports for legacy compatibility
try:
    import pyttsx3
    TEXT_TO_SPEECH_AVAILABLE = True
except ImportError:
    TEXT_TO_SPEECH_AVAILABLE = False
    logger.warning("pyttsx3 not available - text-to-speech notifications will be disabled")

def parse_args():
    """Parse command-line arguments."""
    parser = argparse.ArgumentParser(description="Run Technical Verification backtests with GPU acceleration")
    
    # Input arguments
    parser.add_argument("--config", "-c", type=str,
                        help="Path to JSON config file for TV backtest")
    parser.add_argument("--legacy-excel", "-l", action="store_true",
                        help="Use legacy Excel input format from INPUT SHEETS/TV.xlsx")
    parser.add_argument("--symbol", "-s", type=str, default="NIFTY",
                        help="Symbol to backtest (default: NIFTY)")
    parser.add_argument("--instrument", "-i", type=str, default="FUT",
                        help="Instrument type (FUT, OPT) (default: FUT)")
    
    # Date range arguments
    parser.add_argument("--start-date", type=str,
                        help="Start date in YYMMDD format")
    parser.add_argument("--end-date", type=str,
                        help="End date in YYMMDD format")
    
    # Output arguments
    parser.add_argument("--output-dir", "-o", type=str, default="Trades",
                        help="Directory for output files (default: Trades)")
    
    # Runtime configuration
    parser.add_argument("--slippage", type=float, default=0.1,
                        help="Slippage percentage to apply (default: 0.1)")
    parser.add_argument("--capital", type=float, default=100000.0,
                        help="Initial capital for the backtest (default: 100000.0)")
    
    # GPU control
    parser.add_argument("--cpu-only", action="store_true",
                        help="Force CPU-only mode (override environment variable)")
    
    # GPU Optimization arguments (Phase 2.B.5)
    parser.add_argument("--gpu-workers", type=int, default=2, 
                      help="Number of GPU workers for parallel processing (default: 2)")
    parser.add_argument("--cpu-workers", type=int, default=2,
                      help="Number of CPU workers for fallback (default: 2)")
    parser.add_argument("--gpu-threshold", type=float, default=0.7,
                      help="GPU memory threshold for CPU fallback (default: 0.7)")
    parser.add_argument("--use-gpu-optimization", action="store_true",
                      help="Enable GPU worker pool optimization (Phase 2.B)")
    parser.add_argument("--batch-size", type=int, default=5,
                      help="Number of signals to process per batch (default: 5)")
    
    # Legacy behavior settings
    parser.add_argument("--merge-output", action="store_true",
                        help="Copy output files to merge folder (legacy behavior)")
    
    args = parser.parse_args()
    
    # If neither config nor legacy-excel is provided, default to legacy Excel
    if not args.config and not args.legacy_excel:
        args.legacy_excel = True
        logger.info("No config file provided, defaulting to legacy Excel format")
    
    return args

def load_tv_config(config_file: str) -> Dict[str, Any]:
    """Load TV backtest configuration from a JSON file."""
    try:
        with open(config_file, 'r') as f:
            config_data = json.load(f)
        return config_data
    except (FileNotFoundError, json.JSONDecodeError) as e:
        logger.error(f"Error loading TV config file: {e}")
        sys.exit(1)

def load_legacy_tv_config() -> List[TvSettingModel]:
    """Load TV backtest configuration from legacy Excel format into Pydantic models."""
    filepath = os.path.join(config.INPUT_FILE_FOLDER, config.TV_FILE_PATH)
    if not os.path.exists(filepath):
        logger.debug(f"Legacy TV input file not found at default: {filepath}")
        alt_paths = [
            os.path.join(os.getcwd(), config.INPUT_FILE_FOLDER, config.TV_FILE_PATH),
            os.path.join("input", config.INPUT_FILE_FOLDER, config.TV_FILE_PATH),
            os.path.join(os.getcwd(), "input_sheets", config.TV_FILE_PATH), # Common local structure
            os.path.join("input", "inputs", config.TV_FILE_PATH) # another common one
        ]
        found_filepath = None
        for alt_path in alt_paths:
            if os.path.exists(alt_path):
                logger.info(f"Found legacy TV file at alternative path: {alt_path}")
                found_filepath = alt_path
                break
        if not found_filepath:
            logger.error(f"Unable to find legacy input file. Checked: {filepath} and {alt_paths}")
            sys.exit(1)
        filepath = found_filepath
    
    tv_setting_models = []
    try:
        # Use na_filter=False to keep empty strings, which Pydantic can handle for Optional fields with validators
        tv_setting_df = pd.read_excel(filepath, sheet_name="Setting", na_filter=False)
        
        for index, row in tv_setting_df.iterrows():
            try:
                row_data = row.to_dict()
                model = TvSettingModel(**row_data) # Pydantic validators handle conversions
                if model.Enabled:
                    tv_setting_models.append(model)
                else:
                    logger.info(f"Skipping disabled TV setting: {model.Name}")        
            except ValidationError as e:
                # +2 for 1-based Excel index and header row
                logger.error(f"Validation error for TV Setting Excel row {index + 2}: {e}. Row data: {row.to_dict()}")
            except Exception as e:
                logger.error(f"Error processing TV Setting Excel row {index + 2}: {e}. Row data: {row.to_dict()}")

        if not tv_setting_models:
            logger.warning("No valid & enabled settings found in legacy Excel file after Pydantic validation. This may be expected if all are disabled.")
            # Depending on requirements, might not want to sys.exit(1) here if empty list is okay.
        
        return tv_setting_models
    except FileNotFoundError:
        logger.error(f"Critical error: TV Setting Excel file not found at {filepath}. Exiting.")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Critical error loading legacy TV config from {filepath}: {e}")
        sys.exit(1)

def build_tv_request(args, tv_config: Dict[str, Any]) -> Dict[str, Any]:
    """Build the request payload for TV backtest from JSON config."""
    # Basic request structure
    request = {
        "symbol": args.symbol,
        "instrument": args.instrument,
        "start_date": args.start_date,
        "end_date": args.end_date,
        "technical_indicators": tv_config.get("technical_indicators", []),
        "conditions": tv_config.get("conditions", []),
        "trade_settings": tv_config.get("trade_settings", {})
    }
    
    # Add default values if not provided
    if "trade_settings" not in request or not request["trade_settings"]:
        request["trade_settings"] = {
            "entry_time": "09:30:00",
            "exit_time": "15:15:00",
            "quantity": 1,
            "direction": "BOTH"  # LONG, SHORT, or BOTH
        }
    
    return request

def build_legacy_tv_request(tv_setting: TvSettingModel) -> Optional[Dict[str, Any]]:
    """Build the request payload for TV backtest from a TvSettingModel."""
    
    signal_file_path_original = tv_setting.SignalFilePath
    signal_file_path = signal_file_path_original
    
    if not os.path.isabs(signal_file_path):
        # Attempt to resolve relative to standard input folder or current dir if not absolute
        # This logic mirrors the original script's attempts for finding signal files
        base_signal_file_name = os.path.basename(signal_file_path)
        potential_paths = [
            signal_file_path, # as is
            os.path.join(config.INPUT_FILE_FOLDER, base_signal_file_name),
            os.path.join(os.getcwd(), config.INPUT_FILE_FOLDER, base_signal_file_name),
            os.path.join("input", "signals", base_signal_file_name), # from original code
            os.path.join(os.getcwd(), "input_sheets", base_signal_file_name), # common local structure
            os.path.join(os.getcwd(), signal_file_path_original) # relative to CWD
        ]
        # If SignalFilePath itself contains directory structure, use it directly before basename attempts
        if os.path.dirname(signal_file_path_original):
            potential_paths.insert(1, os.path.join(config.INPUT_FILE_FOLDER, signal_file_path_original))
            potential_paths.insert(3, os.path.join(os.getcwd(), config.INPUT_FILE_FOLDER, signal_file_path_original))
        
        resolved_path = None
        for p_path in potential_paths:
            if os.path.exists(p_path):
                resolved_path = os.path.abspath(p_path)
                logger.info(f"Resolved signal file path for '{signal_file_path_original}' to: {resolved_path}")
                break
        if not resolved_path:
            logger.error(f"TV signal file not found. Original path: '{signal_file_path_original}'. Checked: {potential_paths}")
            return None
        signal_file_path = resolved_path
    elif not os.path.exists(signal_file_path):
        logger.error(f"TV signal file not found at absolute path: {signal_file_path}")
        return None
    
    raw_signals_data = []
    try:
        signals_df = pd.read_excel(signal_file_path, sheet_name="List of trades", na_filter=False)
        for index, row in signals_df.iterrows():
            try:
                raw_signal = RawTvSignalModel(**row.to_dict())
                raw_signals_data.append(raw_signal)
            except ValidationError as e:
                logger.warning(f"Skipping invalid signal row {index + 2} in {signal_file_path}: {e}") # +2 for 1-based Excel index and header
        
        if not raw_signals_data:
            logger.error(f"No valid signals found in {signal_file_path} after Pydantic validation.")
            return None
            
    except FileNotFoundError:
        logger.error(f"TV Signal Excel file not found at {signal_file_path}. Exiting.")
        return None # Or sys.exit(1) depending on desired behavior for a batch run
    except Exception as e:
        logger.error(f"Error loading TV signals from {signal_file_path}: {e}")
        return None
    
    # Validations from original script using model attributes
    if not tv_setting.TvExitApplicable:
        if tv_setting.DoRollover:
            logger.error(f"[{tv_setting.Name}] Cannot run backtest when TvExitApplicable is no and DoRollover is yes.")
            return None
        elif not tv_setting.IntradaySqOffApplicable:
            logger.error(f"[{tv_setting.Name}] Cannot run backtest when TvExitApplicable is no and IntradaySqOffApplicable is no.")
            return None
        elif tv_setting.ManualTradeEntryTime is not None: 
            logger.error(f"[{tv_setting.Name}] Cannot run backtest when TvExitApplicable is no and ManualTradeEntryTime is given.")
            return None
    
    if (tv_setting.FirstTradeEntryTime is not None) and \
       (tv_setting.ManualTradeEntryTime is not None):
        logger.error(f"[{tv_setting.Name}] Cannot run backtest when both FirstTradeEntryTime & ManualTradeEntryTime is required.")
        return None
    
    # Crude date conversion, assuming DD_MM_YYYY or similar. Needs robust parsing.
    try:
        start_date_dt = datetime.strptime(tv_setting.StartDate, "%d_%m_%Y")
        end_date_dt = datetime.strptime(tv_setting.EndDate, "%d_%m_%Y")
        start_date_str = start_date_dt.strftime("%y%m%d")
        end_date_str = end_date_dt.strftime("%y%m%d")
    except ValueError:
        logger.error(f"[{tv_setting.Name}] Invalid StartDate/EndDate format: {tv_setting.StartDate}/{tv_setting.EndDate}. Expected DD_MM_YYYY.")
        start_date_str = tv_setting.StartDate # Fallback, might fail later
        end_date_str = tv_setting.EndDate

    # Determine underlying for rollover using improved extraction function
    underlying_for_rollover = extract_underlying_from_tv_name(tv_setting.Name)
    
    if underlying_for_rollover not in ["NIFTY", "BANKNIFTY", "FINNIFTY", "SENSEX", "MIDCPNIFTY", "CRUDEOIL", "NATURALGAS"]:
        logger.warning(f"[{tv_setting.Name}] Could not reliably determine underlying from name for rollover ('{underlying_for_rollover}'). Defaulting to NIFTY for rollover processing if enabled. Consider adding explicit 'RolloverUnderlyingSymbol' to TvSettingModel.")
        underlying_for_rollover = "NIFTY" if tv_setting.DoRollover else None
    else:
        logger.info(f"[{tv_setting.Name}] Extracted underlying '{underlying_for_rollover}' for rollover processing.")
        if not tv_setting.DoRollover:
            underlying_for_rollover = None  # Don't pass underlying if rollover is disabled

    # Process the raw signals
    processed_signal_dict = process_tv_signals(
        raw_signals_data, 
        tv_setting,
        underlying_for_rollover=underlying_for_rollover
        # dal_instance=... # Pass when available
        # trading_calendar_instance=... # Pass when available
    )
    
    # Add debugging information about signal processing results
    total_processed = sum(len(signals) for signals in processed_signal_dict.values() if signals)
    logger.info(f"[{tv_setting.Name}] Signal processing completed: {total_processed} total signals processed")
    for direction, signals in processed_signal_dict.items():
        if signals:
            logger.info(f"[{tv_setting.Name}] - {direction}: {len(signals)} signals")
    
    if not any(processed_signal_dict.values()): # Check if any signals were processed
        logger.warning(f"[{tv_setting.Name}] No signals were processed for date range {tv_setting.StartDate} to {tv_setting.EndDate}. This may be normal if no signals exist in this date range.")
        # Instead of returning None, return a valid request with empty signals so tests can detect this condition
        request = {
            "symbol": tv_setting.Name.split(' ')[0] if ' ' in tv_setting.Name else 'NIFTY', 
            "instrument": 'FUT',
            "start_date": start_date_str,
            "end_date": end_date_str, 
            "tv_setting_model": tv_setting.model_dump(), 
            "processed_tv_signals_by_direction": processed_signal_dict,  # Empty but valid
            "trade_settings": {
                "entry_time": "09:30:00", 
                "exit_time": "15:15:00",  
                "quantity": 1,            
                "direction": "BOTH"       
            },
            "no_signals_in_range": True  # Flag to indicate this condition
        }
        return request

    request = {
        "symbol": tv_setting.Name.split(' ')[0] if ' ' in tv_setting.Name else 'NIFTY', 
        "instrument": 'FUT', # Placeholder - this should be derived from strategy or context
        "start_date": start_date_str,
        "end_date": end_date_str, 
        "tv_setting_model": tv_setting.model_dump(), 
        # Replace raw_tv_signals with processed_signal_dict
        "processed_tv_signals_by_direction": processed_signal_dict, 
        "trade_settings": { # These are placeholders, should come from TvSettingModel or a sub-model
            "entry_time": "09:30:00", 
            "exit_time": "15:15:00",  
            "quantity": 1,            
            "direction": "BOTH"       
        }
    }
    
    return request

def build_tv_portfolio_request(
    processed_signal: ProcessedTvSignalModel, 
    tv_setting: TvSettingModel,
    # portfolio_model_cache: Dict[str, PortfolioModel] # Optional cache for parsed strategy_excel_path files
) -> Optional[PortfolioModel]:
    """ 
    Builds a PortfolioModel for a single processed TV signal. It loads a base PortfolioModel
    (which includes strategy definitions) from the Excel file specified in TV Settings 
    (e.g., LongPortfolioFilePath) and then augments/overrides parts of it 
    (like dates, times, lots) with specifics from the processed TV signal.
    """
    strategy_excel_path_original: Optional[str] = None
    if processed_signal.signal_direction == "LONG":
        strategy_excel_path_original = tv_setting.LongPortfolioFilePath
    elif processed_signal.signal_direction == "SHORT":
        strategy_excel_path_original = tv_setting.ShortPortfolioFilePath
    elif processed_signal.signal_direction == "MANUAL":
        strategy_excel_path_original = tv_setting.ManualPortfolioFilePath
    
    if not strategy_excel_path_original:
        logger.error(f"[{tv_setting.Name}] No strategy Excel path defined for signal direction '{processed_signal.signal_direction}'. Cannot build portfolio request.")
        return None

    # Resolve strategy_excel_path 
    strategy_excel_path = strategy_excel_path_original
    if not os.path.isabs(strategy_excel_path):
        base_name = os.path.basename(strategy_excel_path)
        potential_paths = [
            os.path.join(config.INPUT_FILE_FOLDER, strategy_excel_path), 
            os.path.join(config.INPUT_FILE_FOLDER, base_name),          
            strategy_excel_path,                                        
            os.path.join(os.getcwd(), "input_sheets", base_name),         
        ]
        if os.path.dirname(strategy_excel_path_original):
            potential_paths.insert(2, os.path.join(os.getcwd(), config.INPUT_FILE_FOLDER, strategy_excel_path_original))
            potential_paths.append(os.path.join(os.getcwd(), strategy_excel_path_original)) 
        
        resolved_path = None
        for p_path in potential_paths:
            if os.path.exists(p_path):
                resolved_path = os.path.abspath(p_path)
                logger.info(f"Resolved strategy excel for {processed_signal.signal_direction} ('{strategy_excel_path_original}') to: {resolved_path}")
                break
        if not resolved_path:
            logger.error(f"[{tv_setting.Name}] Strategy Excel file not found for {processed_signal.signal_direction}. Original path: '{strategy_excel_path_original}'. Checked: {potential_paths}")
            return None
        strategy_excel_path = resolved_path
    elif not os.path.exists(strategy_excel_path):
        logger.error(f"[{tv_setting.Name}] Strategy Excel file not found at absolute path: {strategy_excel_path}")
        return None

    try:
        logger.info(f"[{tv_setting.Name}] Attempting to parse strategy excel: {strategy_excel_path} for TV signal {processed_signal.original_tradeno}.")
        parsed_portfolios: Dict[str, PortfolioModel] = main_portfolio_parser.parse_portfolio_excel(strategy_excel_path)

        if not parsed_portfolios:
            logger.error(f"[{tv_setting.Name}] No portfolios parsed from strategy Excel: {strategy_excel_path}")
            return None

        base_portfolio_model_name = list(parsed_portfolios.keys())[0]
        # IMPORTANT: Use model_copy for deep copying to avoid modifying cached/shared models if caching is implemented later.
        base_portfolio_model = parsed_portfolios[base_portfolio_model_name].model_copy(deep=True)
        logger.info(f"[{tv_setting.Name}] Loaded base portfolio '{base_portfolio_model.portfolio_name}' from {strategy_excel_path}")

        if not base_portfolio_model.strategies:
            logger.error(f"[{tv_setting.Name}] No strategies found in loaded portfolio '{base_portfolio_model.portfolio_name}' from {strategy_excel_path}")
            return None

        tv_target_strategy_model = base_portfolio_model.strategies[0]
        original_strategy_name = tv_target_strategy_model.strategy_name
        logger.info(f"[{tv_setting.Name}] Augmenting strategy '{original_strategy_name}' (from '{base_portfolio_model.portfolio_name}') for TV signal {processed_signal.original_tradeno}.")

        # Portfolio-level overrides
        base_portfolio_model.portfolio_name = f"{tv_setting.Name}_{processed_signal.signal_direction}_Trade{processed_signal.original_tradeno or 'Signal'}"
        try:
            # Ensure dates are python date objects for the model
            base_portfolio_model.start_date = datetime.strptime(str(processed_signal.entrydate), "%y%m%d").date()
            base_portfolio_model.end_date = datetime.strptime(str(processed_signal.exitdate), "%y%m%d").date()
        except ValueError as e:
            logger.error(f"[{tv_setting.Name}] Invalid date format in ProcessedTvSignalModel for {processed_signal.original_tradeno}: {e}. Dates: {processed_signal.entrydate}, {processed_signal.exitdate}")
            return None

        if tv_setting.SlippagePercent is not None:
            base_portfolio_model.slippage_percent = tv_setting.SlippagePercent / 100.0
        
        # Strategy-level overrides
        tv_target_strategy_model.strategy_name = f"TV_{original_strategy_name}_{processed_signal.original_tradeno or 'Signal'}"
        try:
            tv_target_strategy_model.entry_start = hhmmss_to_time(processed_signal.entrytime)
            tv_target_strategy_model.exit_time = hhmmss_to_time(processed_signal.exittime)
            if hasattr(tv_target_strategy_model, 'entry_end'):
                tv_target_strategy_model.entry_end = tv_target_strategy_model.exit_time
        except Exception as e: 
            logger.error(f"[{tv_setting.Name}] Invalid time format in ProcessedTvSignalModel for {processed_signal.original_tradeno}: {e}. Times: {processed_signal.entrytime}, {processed_signal.exittime}")
            return None

        if not tv_target_strategy_model.legs:
            logger.error(f"[{tv_setting.Name}] Strategy '{original_strategy_name}' has no legs defined. Cannot apply TV signal lots.")
            return None
            
        for leg in tv_target_strategy_model.legs:
            leg.lots = processed_signal.lots
            if processed_signal.isrollovertrade:
                logger.info(f"[{tv_setting.Name}] Signal {processed_signal.original_tradeno} (Leg: {leg.leg_id}) is part of a rollover. Specific leg expiry override may be needed (not yet implemented). Current expiry_rule: {leg.expiry_rule}")

        logger.info(f"[{tv_setting.Name}] Successfully built and augmented PortfolioModel: {base_portfolio_model.portfolio_name} for signal {processed_signal.original_tradeno}")
        return base_portfolio_model

    except FileNotFoundError as e:
        logger.error(f"[{tv_setting.Name}] Critical: Strategy Excel file not found during parsing by main_portfolio_parser: {strategy_excel_path}. Error: {e}")
        return None
    except Exception as e:
        logger.error(f"[{tv_setting.Name}] Error in build_tv_portfolio_request for signal {processed_signal.original_tradeno} using strategy file {strategy_excel_path}: {e}", exc_info=True)
        return None

def extract_underlying_from_tv_name(tv_name: str) -> str:
    """Extract underlying symbol from TV setting name for rollover purposes."""
    tv_name_upper = tv_name.upper()
    
    # Common patterns for underlying extraction
    if "NF" in tv_name_upper or "NIFTY" in tv_name_upper:
        return "NIFTY"
    elif "BN" in tv_name_upper or "BANKNIFTY" in tv_name_upper or "BANK" in tv_name_upper:
        return "BANKNIFTY"
    elif "FN" in tv_name_upper or "FINNIFTY" in tv_name_upper:
        return "FINNIFTY"
    elif "SN" in tv_name_upper or "SENSEX" in tv_name_upper:
        return "SENSEX"
    elif "MD" in tv_name_upper or "MIDCAP" in tv_name_upper:
        return "MIDCPNIFTY"
    elif "CR" in tv_name_upper or "CRUDE" in tv_name_upper:
        return "CRUDEOIL"
    elif "NG" in tv_name_upper or "NATURAL" in tv_name_upper:
        return "NATURALGAS"
    else:
        # Default to NIFTY for any unrecognized patterns
        logger.warning(f"Could not determine underlying from TV name '{tv_name}'. Defaulting to NIFTY.")
        return "NIFTY"

def _gpu_optimized_tv_signal_worker(signal_batch: Dict) -> List[Dict]:
    """GPU-optimized worker function for processing TV signal batches.
    
    This function runs in a worker process with its own HeavyDB connection.
    """
    import time
    import logging
    from datetime import datetime
    
    _log = logging.getLogger(__name__)
    start_time = time.time()
    batch_results = []
    
    # Access worker state and connection
    try:
        from common.gpu_worker_pool import _worker_state
        conn = _worker_state.get('heavydb_conn')
        if not conn:
            raise RuntimeError("No HeavyDB connection available in worker")
    except Exception as e:
        _log.error(f"Failed to get HeavyDB connection: {e}")
        return [{
            'success': False,
            'error': str(e),
            'batch_name': signal_batch.get('batch_name', 'unknown')
        }]
    
    # Extract batch configuration
    batch_name = signal_batch.get('batch_name', 'unknown')
    signals = signal_batch.get('signals', [])
    tv_setting = signal_batch.get('tv_setting')
    args = signal_batch.get('args')
    
    _log.info(f"Processing TV signal batch: {batch_name} with {len(signals)} signals")
    
    # Process each signal in the batch
    for signal_info in signals:
        try:
            # Extract signal details
            processed_signal = signal_info['signal']
            direction = signal_info['direction']
            
            # Build portfolio model for this signal
            final_portfolio_model_for_bt = build_tv_portfolio_request(
                processed_signal=processed_signal,
                tv_setting=tv_setting
            )
            
            if final_portfolio_model_for_bt is None:
                _log.warning(f"Failed to build PortfolioModel for signal {processed_signal.original_tradeno}. Skipping.")
                batch_results.append({
                    'success': False,
                    'signal_tradeno': processed_signal.original_tradeno,
                    'direction': direction,
                    'error': 'Failed to build portfolio model'
                })
                continue
            
            # Create unique output identifier
            signal_tradeno = processed_signal.original_tradeno or "unknown"
            output_file_signal = f"{tv_setting.Name}_{direction}_T{signal_tradeno}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            
            # Prepare backtest parameters
            bt_params = {
                "portfolio_model": final_portfolio_model_for_bt.model_dump(),
                "portfolio_name": final_portfolio_model_for_bt.portfolio_name,
                "start_date": str(final_portfolio_model_for_bt.start_date),
                "end_date": str(final_portfolio_model_for_bt.end_date),
            }
            
            # Import runtime for backtest execution
            from bt.backtester_stable.BTRUN.core import runtime
            
            # Run the backtest
            backtest_result = runtime.run_full_backtest(
                bt_params=bt_params,
                output_base_path="",  # Will be handled later
                slippage_percent=args.slippage / 100.0,
                initial_capital=args.capital,
                save_json=False,  # Don't save intermediate results
                save_excel=False
            )
            
            if backtest_result and backtest_result.get('success', False):
                batch_results.append({
                    'success': True,
                    'tv_setting': tv_setting.Name,
                    'signal_direction': direction,
                    'signal_tradeno': signal_tradeno,
                    'output_identifier': output_file_signal,
                    'result': backtest_result,
                    'portfolio_model': final_portfolio_model_for_bt.model_dump()
                })
            else:
                batch_results.append({
                    'success': False,
                    'signal_tradeno': signal_tradeno,
                    'direction': direction,
                    'error': backtest_result.get('error', 'Unknown error')
                })
                
        except Exception as e:
            _log.error(f"Error processing signal in batch: {e}")
            batch_results.append({
                'success': False,
                'error': str(e),
                'direction': signal_info.get('direction', 'unknown')
            })
    
    # Add batch timing information
    batch_duration = time.time() - start_time
    _log.info(f"Batch {batch_name} completed in {batch_duration:.2f}s")
    
    return batch_results

def run_tv_backtest(args):
    """Run Technical Verification backtest with GPU acceleration."""
    # Check for CPU-only flag
    if args.cpu_only:
        gpu_helpers.force_cpu_mode()
        logger.info("Forced CPU-only mode via command-line argument")
    
    # Log GPU optimization availability after logger is initialized
    if not GPU_OPTIMIZATION_AVAILABLE and args.use_gpu_optimization:
        logger.warning("GPU optimization modules not available. Running in sequential mode.")
        args.use_gpu_optimization = False
    elif GPU_OPTIMIZATION_AVAILABLE and args.use_gpu_optimization:
        logger.info("GPU optimization enabled with %d GPU workers, %d CPU workers", 
                   args.gpu_workers, args.cpu_workers)
    
    # Log GPU status
    logger.info(f"GPU acceleration enabled: {gpu_helpers.is_gpu_available()}")
    if gpu_helpers.is_gpu_available():
        mem_info = gpu_helpers.get_gpu_memory_info()
        if mem_info:
            logger.info(f"GPU Memory: {mem_info['free_gpu_memory_mb']}MB free / {mem_info['total_gpu_memory_mb']}MB total")
    
    # Create output directory
    os.makedirs(args.output_dir, exist_ok=True)
    
    # Initialize results tracking
    all_results = []
    files_to_copy = []
    
    # Process based on input type
    if args.legacy_excel:
        logger.info("Using legacy Excel input format")
        settings = load_legacy_tv_config()
        
        # Check if GPU optimization is requested and available
        if args.use_gpu_optimization and GPU_OPTIMIZATION_AVAILABLE:
            logger.info("Using GPU-optimized signal batch processing")
            
            # Collect all signals to process
            all_signal_batches = []
            signal_count = 0
            
            for tv_setting_item in settings:
                logger.info(f"Preparing TV setting: {tv_setting_item.Name}")
                
                # Initial request built with TvSettingModel and RawTvSignalModels
                initial_request = build_legacy_tv_request(tv_setting_item)
                if initial_request is None:
                    logger.warning(f"[{tv_setting_item.Name}] Failed to build initial legacy request. Skipping.")
                    continue
                
                processed_signals_map = initial_request.get("processed_tv_signals_by_direction", {})
                
                # Collect signals for batching
                for direction, processed_signals_list in processed_signals_map.items():
                    if not processed_signals_list:
                        continue
                    
                    for processed_one_signal in processed_signals_list:
                        # Check if processed_one_signal is already a ProcessedTvSignalModel instance
                        if isinstance(processed_one_signal, ProcessedTvSignalModel):
                            current_processed_signal = processed_one_signal
                        else:
                            # It's a dict, so we need to re-instantiate
                            try:
                                current_processed_signal = ProcessedTvSignalModel(**processed_one_signal)
                            except ValidationError as e:
                                logger.error(f"[{tv_setting_item.Name}] Error re-parsing ProcessedTvSignalModel: {e}")
                                continue
                        
                        all_signal_batches.append({
                            'signal': current_processed_signal,
                            'direction': direction,
                            'tv_setting': tv_setting_item
                        })
                        signal_count += 1
            
            logger.info(f"Total signals to process: {signal_count}")
            
            if all_signal_batches:
                # Create batches for parallel processing
                batch_size = args.batch_size
                signal_batches = []
                
                for i in range(0, len(all_signal_batches), batch_size):
                    batch = all_signal_batches[i:i + batch_size]
                    signal_batches.append({
                        'batch_name': f'batch_{i//batch_size}',
                        'signals': batch,
                        'tv_setting': batch[0]['tv_setting'],  # Use first signal's setting
                        'args': args
                    })
                
                logger.info(f"Created {len(signal_batches)} batches for parallel processing")
                
                # Configure worker pool
                worker_config = GPUWorkerConfig(
                    max_workers=args.gpu_workers,
                    cpu_workers=args.cpu_workers,
                    gpu_memory_threshold=args.gpu_threshold,
                    batch_size=1  # Process one batch at a time
                )
                
                # Process batches in parallel
                batch_results = []
                with GPUWorkerPool(worker_config) as pool:
                    # Submit all batches
                    futures = []
                    for signal_batch in signal_batches:
                        future = pool.submit_task(_gpu_optimized_tv_signal_worker, signal_batch)
                        futures.append(future)
                    
                    # Collect results
                    for future in futures:
                        try:
                            result = future.result(timeout=300)  # 5 minute timeout per batch
                            batch_results.extend(result)
                        except Exception as e:
                            logger.error(f"Batch processing failed: {e}")
                
                # Process batch results and save output files
                for result in batch_results:
                    if result.get('success', False):
                        # Save results to output file
                        output_identifier = result['output_identifier']
                        output_base = os.path.join(args.output_dir, output_identifier)
                        output_file = f"{output_base}.xlsx"
                        
                        # Reconstruct portfolio model
                        portfolio_model = PortfolioModel(**result['portfolio_model'])
                        
                        # Use golden format generator for TV output
                        golden_format_success = _generate_tv_golden_format_output(
                            result['result'],
                            output_file,
                            result['tv_setting'],
                            result['signal_direction'],
                            result['signal_tradeno']
                        )

                        if not golden_format_success:
                            # Fallback to legacy format
                            logger.warning(f"Golden format failed for {result['signal_tradeno']}, using legacy format")
                            io.write_results(
                                combined_result=result['result'],
                                output_path=output_file,
                                use_legacy_format=True
                            )
                        
                        files_to_copy.append(output_file)
                        all_results.append({
                            "tv_setting": result['tv_setting'],
                            "signal_direction": result['signal_direction'],
                            "signal_tradeno": result['signal_tradeno'],
                            "output_file": output_file,
                            "result": result['result']
                        })
                        
                        logger.info(f"Saved results for signal {result['signal_tradeno']} to {output_file}")
                    else:
                        logger.error(f"Failed to process signal: {result.get('error', 'Unknown error')}")
        
        else:
            # Original sequential processing
            for tv_setting_item in settings: # Renamed to avoid conflict
                logger.info(f"Processing TV setting: {tv_setting_item.Name}")
                
                # Initial request built with TvSettingModel and RawTvSignalModels
                initial_request = build_legacy_tv_request(tv_setting_item)
                if initial_request is None:
                    logger.warning(f"[{tv_setting_item.Name}] Failed to build initial legacy request. Skipping.")
                    continue
                
                processed_signals_map = initial_request.get("processed_tv_signals_by_direction", {})
                underlying_symbol_for_tv = initial_request.get("symbol", "NIFTY") # Get from request or default

                # Iterate through each direction (LONG, SHORT, MANUAL) and then each processed signal
                for direction, processed_signals_list in processed_signals_map.items():
                    if not processed_signals_list:
                        continue
                    logger.info(f"[{tv_setting_item.Name}] Processing {len(processed_signals_list)} '{direction}' signals.")
                    for processed_one_signal in processed_signals_list:
                        # Now, for each ProcessedTvSignalModel, build the final PortfolioModel
                        # Need to cast processed_one_signal (dict from model_dump) back to ProcessedTvSignalModel
                        # or pass dicts and have build_tv_portfolio_request expect that.
                        # For now, let's assume it was kept as model in initial_request or re-parse.
                        # Corrected: initial_request["processed_tv_signals_by_direction"] contains dicts from model_dump().
                        # So, we need to re-instantiate ProcessedTvSignalModel here.
                        # Check if processed_one_signal is already a ProcessedTvSignalModel instance
                        if isinstance(processed_one_signal, ProcessedTvSignalModel):
                            current_processed_signal = processed_one_signal
                        else:
                            # It's a dict, so we need to re-instantiate
                            try:
                                current_processed_signal = ProcessedTvSignalModel(**processed_one_signal)
                            except ValidationError as e:
                                logger.error(f"[{tv_setting_item.Name}] Error re-parsing ProcessedTvSignalModel for direction {direction}: {e}. Data: {processed_one_signal}")
                                continue

                        final_portfolio_model_for_bt = build_tv_portfolio_request(
                            processed_signal=current_processed_signal,
                            tv_setting=tv_setting_item
                        )

                        if final_portfolio_model_for_bt is None:
                            logger.warning(f"[{tv_setting_item.Name}] Failed to build final PortfolioModel for a processed signal. Skipping this signal execution.")
                            continue
                        
                        # Create a unique output base for this specific signal execution
                        signal_tradeno = current_processed_signal.original_tradeno or "unknown"
                        output_file_signal = f"{tv_setting_item.Name}_{direction}_T{signal_tradeno}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
                        output_base_signal = os.path.join(args.output_dir, output_file_signal)

                        # The `runtime.run_full_backtest` expects `bt_params`
                        bt_params = {
                            "portfolio": final_portfolio_model_for_bt,
                            "output_base": output_base_signal,
                            "slippage_percent": args.slippage / 100.0,
                            "initial_capital": args.capital
                        }

                        # Execute the backtest using the modern runtime
                        try:
                            logger.info(f"[{tv_setting_item.Name}] Starting backtest for signal {signal_tradeno} ({direction})")
                            
                            # Import the runtime module for backtest execution
                            from bt.backtester_stable.BTRUN import runtime
                            
                            # Run the backtest
                            backtest_result = runtime.run_full_backtest(bt_params)
                            
                            if backtest_result and backtest_result.get('success', False):
                                logger.info(f"[{tv_setting_item.Name}] Backtest completed successfully for signal {signal_tradeno}")
                                
                                # Save results to output file
                                output_file = f"{output_base_signal}.xlsx"
                                
                                # Use golden format generator for TV output
                                golden_format_success = _generate_tv_golden_format_output(
                                    backtest_result,
                                    output_file,
                                    tv_setting_item.Name,
                                    direction,
                                    signal_tradeno
                                )

                                if not golden_format_success:
                                    # Fallback to legacy format
                                    logger.warning(f"Golden format failed for {signal_tradeno}, using legacy format")
                                    io.write_results(
                                        combined_result=backtest_result,
                                        output_path=output_file,
                                        use_legacy_format=True
                                    )
                                
                                files_to_copy.append(output_file)
                                all_results.append({
                                    "tv_setting": tv_setting_item.Name,
                                    "signal_direction": direction,
                                    "signal_tradeno": signal_tradeno,
                                    "output_file": output_file,
                                    "result": backtest_result
                                })
                                
                            else:
                                logger.error(f"[{tv_setting_item.Name}] Backtest failed for signal {signal_tradeno}: {backtest_result}")
                                
                        except Exception as e:
                            logger.error(f"[{tv_setting_item.Name}] Error running backtest for signal {signal_tradeno}: {e}", exc_info=True)
                            continue
    
    else:
        # Handle JSON config based TV backtesting
        logger.info("Using JSON config input format")
        tv_config = load_tv_config(args.config)
        request = build_tv_request(args, tv_config)
        
        # TODO: Implement JSON-based TV backtesting when needed
        logger.warning("JSON-based TV backtesting not yet implemented. Use --legacy-excel for now.")
        return False
    
    # Summary and cleanup
    logger.info(f"\n{'='*60}")
    logger.info(f"TV BACKTESTING COMPLETED")
    logger.info(f"{'='*60}")
    logger.info(f"Total results: {len(all_results)}")
    
    if all_results:
        for result in all_results:
            logger.info(f"  - {result['tv_setting']}/{result['signal_direction']}/T{result['signal_tradeno']}: {result['output_file']}")
    
    # Copy files to merge folder if requested
    if args.merge_output and files_to_copy:
        try:
            from bt.backtester_stable.BTRUN import config
            merge_folder = os.path.join(config.MERGE_FOLDER)
            os.makedirs(merge_folder, exist_ok=True)
            
            for file_path in files_to_copy:
                if os.path.exists(file_path):
                    import shutil
                    dest_path = os.path.join(merge_folder, os.path.basename(file_path))
                    shutil.copy2(file_path, dest_path)
                    logger.info(f"Copied {file_path} to merge folder")
                    
        except Exception as e:
            logger.error(f"Error copying files to merge folder: {e}")
    
    # Text-to-speech notification (legacy compatibility)
    if TEXT_TO_SPEECH_AVAILABLE:
        try:
            engine = pyttsx3.init()
            engine.setProperty('rate', 200)
            engine.say("TV Backtest completed")
            engine.runAndWait()
        except Exception as e:
            logger.warning(f"Text-to-speech notification failed: {e}")
    
    return len(all_results) > 0


def _generate_tv_golden_format_output(backtest_result: Dict[str, Any],
                                    output_file: str,
                                    tv_setting_name: str,
                                    signal_direction: str,
                                    signal_tradeno: str) -> bool:
    """
    Generate TV strategy output in exact 16-sheet golden format

    Args:
        backtest_result: Backtest result dictionary
        output_file: Output file path
        tv_setting_name: TV setting name
        signal_direction: Signal direction (LONG/SHORT)
        signal_tradeno: Signal trade number

    Returns:
        bool: Success status
    """
    try:
        logger.info(f"🎯 Generating TV golden format for {signal_tradeno}")

        # Import golden format generator
        import sys
        sys.path.append(os.path.join(os.path.dirname(__file__), 'utils'))
        from golden_format_excel_generator import GoldenFormatExcelGenerator

        generator = GoldenFormatExcelGenerator()

        # Extract transaction data from backtest result
        transaction_dfs = backtest_result.get('transaction_dfs', {})
        if not transaction_dfs:
            logger.warning(f"No transaction data found for {signal_tradeno}")
            return False

        # Get main portfolio transactions
        portfolio_trans_df = transaction_dfs.get('portfolio', pd.DataFrame())
        if portfolio_trans_df.empty:
            logger.warning(f"No portfolio transactions found for {signal_tradeno}")
            return False

        # Prepare data for golden format
        tv_signals_df = _prepare_tv_signals_data_from_result(backtest_result, tv_setting_name, signal_direction, signal_tradeno)
        long_trans_df = portfolio_trans_df[portfolio_trans_df.get('signal_direction', signal_direction) == 'LONG'] if 'signal_direction' in portfolio_trans_df.columns else portfolio_trans_df
        short_trans_df = portfolio_trans_df[portfolio_trans_df.get('signal_direction', signal_direction) == 'SHORT'] if 'signal_direction' in portfolio_trans_df.columns else pd.DataFrame()
        tv_settings = _prepare_tv_settings_from_result(tv_setting_name, signal_direction)

        # Generate golden format
        success = generator.generate_tv_golden_format(
            tv_signals_df=tv_signals_df,
            portfolio_trans_df=portfolio_trans_df,
            long_trans_df=long_trans_df,
            short_trans_df=short_trans_df,
            tv_settings=tv_settings,
            output_path=output_file
        )

        if success:
            logger.info(f"✅ TV golden format generated for {signal_tradeno}")
        else:
            logger.error(f"❌ TV golden format generation failed for {signal_tradeno}")

        return success

    except Exception as e:
        logger.error(f"Error generating TV golden format for {signal_tradeno}: {e}")
        return False


def _prepare_tv_signals_data_from_result(backtest_result: Dict[str, Any],
                                       tv_setting_name: str,
                                       signal_direction: str,
                                       signal_tradeno: str) -> pd.DataFrame:
    """Prepare TV signals data from backtest result"""
    try:
        # Create a single signal entry based on the backtest
        signal_data = {
            'tradeno': signal_tradeno,
            'direction': signal_direction,
            'tv_setting': tv_setting_name,
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'symbol': 'NIFTY',  # Default symbol
            'status': 'EXECUTED'
        }

        return pd.DataFrame([signal_data])

    except Exception as e:
        logger.error(f"Error preparing TV signals data: {e}")
        return pd.DataFrame()


def _prepare_tv_settings_from_result(tv_setting_name: str, signal_direction: str) -> Dict[str, Any]:
    """Prepare TV settings data from result"""
    return {
        'name': tv_setting_name,
        'direction': signal_direction,
        'symbol': 'NIFTY',
        'strategy_type': 'TV',
        'generated_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    }


def main():
    """Main entry point for TV backtesting."""
    args = parse_args()
    
    logger.info("=" * 60)
    logger.info("TV BACKTESTING WITH GPU ACCELERATION")
    logger.info("=" * 60)
    logger.info(f"Start time: {datetime.now()}")
    logger.info(f"Arguments: {vars(args)}")
    
    try:
        success = run_tv_backtest(args)
        
        if success:
            logger.info("\n🎉 TV backtesting completed successfully!")
            sys.exit(0)
        else:
            logger.error("\n❌ TV backtesting failed or produced no results!")
            sys.exit(1)
            
    except KeyboardInterrupt:
        logger.info("\n⚠️ TV backtesting interrupted by user")
        sys.exit(2)
    except Exception as e:
        logger.error(f"\n💥 TV backtesting crashed: {e}", exc_info=True)
        sys.exit(1)
    finally:
        logger.info(f"End time: {datetime.now()}")
        logger.info("TV backtesting session ended")


if __name__ == "__main__":
    main()