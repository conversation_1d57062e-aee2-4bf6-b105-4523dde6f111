#!/usr/bin/env python3
"""
Phase 4: Real Database Integration Testing
Tests all strategy types with actual HeavyDB and input sheets
"""
import os
import sys
import time
import json
import logging
from datetime import datetime
from typing import Dict, List, Any

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(message)s'
)
logger = logging.getLogger(__name__)

# Input sheets directory
INPUT_SHEETS_DIR = "/srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets"

class RealDatabaseTester:
    """Test all strategies with real HeavyDB integration"""
    
    def __init__(self):
        self.results = {}
        self.start_time = time.time()
        
    def test_heavydb_connection(self) -> bool:
        """Test HeavyDB connection"""
        try:
            logger.info("Testing HeavyDB connection...")

            # Direct connection without config dependencies
            from heavyai import connect

            conn = connect(
                host='127.0.0.1',
                port=6274,
                user='admin',
                password='HyperInteractive',
                dbname='heavyai'
            )

            cursor = conn.cursor()
            cursor.execute("SELECT COUNT(*) FROM nifty_option_chain")
            count = cursor.fetchone()[0]

            logger.info(f"✅ HeavyDB connected - {count:,} rows available")
            cursor.close()
            conn.close()
            return True

        except Exception as e:
            logger.error(f"❌ HeavyDB connection failed: {e}")
            return False
    
    def test_tv_strategy(self) -> Dict[str, Any]:
        """Test TV strategy with real database"""
        logger.info("\n" + "="*60)
        logger.info("Testing TV Strategy with Real Database")
        logger.info("="*60)
        
        try:
            # Use the aggregated TV script for archive parity
            tv_script = "/srv/samba/shared/bt/backtester_stable/BTRUN/BT_TV_GPU_aggregated_v4.py"
            
            if not os.path.exists(tv_script):
                return {"success": False, "error": f"TV script not found: {tv_script}"}
            
            # Test with actual input files
            input_file = f"{INPUT_SHEETS_DIR}/tv/input_tv.xlsx"
            if not os.path.exists(input_file):
                return {"success": False, "error": f"TV input file not found: {input_file}"}
            
            logger.info(f"✓ Using TV script: {os.path.basename(tv_script)}")
            logger.info(f"✓ Using input file: {os.path.basename(input_file)}")
            
            # Import and test TV functionality
            import subprocess
            output_dir = "/tmp/tv_test_output"
            os.makedirs(output_dir, exist_ok=True)

            result = subprocess.run([
                "python3", tv_script,
                "--input-file", input_file,
                "--output-dir", output_dir,
                "--start-date", "240103",
                "--end-date", "240103"
            ], capture_output=True, text=True, timeout=120)
            
            if result.returncode == 0:
                logger.info("✅ TV strategy executed successfully")
                return {
                    "success": True,
                    "execution_time": 0,
                    "output": result.stdout[-500:] if result.stdout else ""
                }
            else:
                logger.error(f"❌ TV strategy failed: {result.stderr}")
                return {
                    "success": False,
                    "error": result.stderr,
                    "output": result.stdout
                }
                
        except Exception as e:
            logger.error(f"❌ TV strategy error: {str(e)}")
            return {"success": False, "error": str(e)}
    
    def test_tbs_strategy(self) -> Dict[str, Any]:
        """Test TBS strategy with real database"""
        logger.info("\n" + "="*60)
        logger.info("Testing TBS Strategy with Real Database")
        logger.info("="*60)
        
        try:
            # Use the main TBS script
            tbs_script = "/srv/samba/shared/bt/backtester_stable/BTRUN/BTRunPortfolio_GPU.py"
            
            if not os.path.exists(tbs_script):
                return {"success": False, "error": f"TBS script not found: {tbs_script}"}
            
            # Test with actual input files
            portfolio_file = f"{INPUT_SHEETS_DIR}/tbs/input_portfolio.xlsx"
            if not os.path.exists(portfolio_file):
                return {"success": False, "error": f"TBS portfolio file not found: {portfolio_file}"}
            
            logger.info(f"✓ Using TBS script: {os.path.basename(tbs_script)}")
            logger.info(f"✓ Using portfolio file: {os.path.basename(portfolio_file)}")
            
            # Import and test TBS functionality
            import subprocess
            output_dir = "/tmp/tbs_test_output"
            os.makedirs(output_dir, exist_ok=True)

            result = subprocess.run([
                "python3", tbs_script,
                "--portfolio-excel", portfolio_file,
                "--output-path", output_dir,
                "--start-date", "240103",
                "--end-date", "240103"
            ], capture_output=True, text=True, timeout=120)
            
            if result.returncode == 0:
                logger.info("✅ TBS strategy executed successfully")
                return {
                    "success": True,
                    "execution_time": 0,
                    "output": result.stdout[-500:] if result.stdout else ""
                }
            else:
                logger.error(f"❌ TBS strategy failed: {result.stderr}")
                return {
                    "success": False,
                    "error": result.stderr,
                    "output": result.stdout
                }
                
        except Exception as e:
            logger.error(f"❌ TBS strategy error: {str(e)}")
            return {"success": False, "error": str(e)}
    
    def test_oi_strategy(self) -> Dict[str, Any]:
        """Test OI strategy with real database"""
        logger.info("\n" + "="*60)
        logger.info("Testing OI Strategy with Real Database")
        logger.info("="*60)
        
        try:
            # Use the OI script
            oi_script = "/srv/samba/shared/bt/backtester_stable/BTRUN/BT_OI_GPU.py"
            
            if not os.path.exists(oi_script):
                return {"success": False, "error": f"OI script not found: {oi_script}"}
            
            # Test with actual input files
            input_file = f"{INPUT_SHEETS_DIR}/oi/input_maxoi.xlsx"
            setting_file = f"{INPUT_SHEETS_DIR}/oi/bt_setting.xlsx"
            
            if not os.path.exists(input_file):
                return {"success": False, "error": f"OI input file not found: {input_file}"}
            if not os.path.exists(setting_file):
                return {"success": False, "error": f"OI setting file not found: {setting_file}"}
            
            logger.info(f"✓ Using OI script: {os.path.basename(oi_script)}")
            logger.info(f"✓ Using input file: {os.path.basename(input_file)}")
            logger.info(f"✓ Using setting file: {os.path.basename(setting_file)}")
            
            # Import and test OI functionality
            import subprocess
            output_dir = "/tmp/oi_test_output"
            os.makedirs(output_dir, exist_ok=True)

            result = subprocess.run([
                "python3", oi_script,
                "--portfolio-excel", setting_file,
                "--output-dir", output_dir,
                "--start-date", "240103",
                "--end-date", "240103"
            ], capture_output=True, text=True, timeout=120)
            
            if result.returncode == 0:
                logger.info("✅ OI strategy executed successfully")
                return {
                    "success": True,
                    "execution_time": 0,
                    "output": result.stdout[-500:] if result.stdout else ""
                }
            else:
                logger.error(f"❌ OI strategy failed: {result.stderr}")
                return {
                    "success": False,
                    "error": result.stderr,
                    "output": result.stdout
                }
                
        except Exception as e:
            logger.error(f"❌ OI strategy error: {str(e)}")
            return {"success": False, "error": str(e)}

    def test_direct_query(self) -> Dict[str, Any]:
        """Test direct HeavyDB queries for strategy data"""
        logger.info("\n" + "="*60)
        logger.info("Testing Direct HeavyDB Queries")
        logger.info("="*60)

        try:
            from heavyai import connect

            conn = connect(
                host='127.0.0.1',
                port=6274,
                user='admin',
                password='HyperInteractive',
                dbname='heavyai'
            )

            cursor = conn.cursor()

            # Test basic data availability
            logger.info("Testing data availability...")

            # Check recent data
            cursor.execute("""
                SELECT trade_date, COUNT(*) as records
                FROM nifty_option_chain
                WHERE trade_date >= '2024-01-01'
                GROUP BY trade_date
                ORDER BY trade_date DESC
                LIMIT 10
            """)

            recent_data = cursor.fetchall()
            logger.info(f"✓ Found {len(recent_data)} recent trading days")

            if recent_data:
                latest_date = recent_data[0][0]
                latest_count = recent_data[0][1]
                logger.info(f"✓ Latest data: {latest_date} with {latest_count:,} records")

            # Test ATM strike calculation
            cursor.execute("""
                SELECT trade_date, trade_time, spot, strike, ce_symbol
                FROM nifty_option_chain
                WHERE trade_date = '2024-01-03'
                AND trade_time = '09:16:00'
                AND ce_symbol IS NOT NULL
                ORDER BY ABS(strike - spot)
                LIMIT 5
            """)

            atm_data = cursor.fetchall()
            logger.info(f"✓ Found {len(atm_data)} ATM strikes for test date")

            # Test OI data
            cursor.execute("""
                SELECT COUNT(*) as oi_records
                FROM nifty_option_chain
                WHERE (ce_oi > 0 OR pe_oi > 0)
                AND trade_date = '2024-01-03'
            """)

            oi_count = cursor.fetchone()[0]
            logger.info(f"✓ Found {oi_count:,} records with OI data")

            cursor.close()
            conn.close()

            logger.info("✅ Direct query test completed successfully")
            return {
                "success": True,
                "execution_time": 0,
                "data_summary": {
                    "recent_days": len(recent_data),
                    "latest_date": str(latest_date) if recent_data else None,
                    "latest_records": latest_count if recent_data else 0,
                    "atm_strikes": len(atm_data),
                    "oi_records": oi_count
                }
            }

        except Exception as e:
            logger.error(f"❌ Direct query test failed: {str(e)}")
            return {"success": False, "error": str(e)}
    
    def run_all_tests(self):
        """Run all real database integration tests"""
        logger.info("Starting Phase 4: Real Database Integration Testing")
        logger.info(f"Input sheets directory: {INPUT_SHEETS_DIR}")
        
        # Test HeavyDB connection first
        if not self.test_heavydb_connection():
            logger.error("❌ Cannot proceed without HeavyDB connection")
            return
        
        # Test each strategy with real database
        strategies = {
            "TV": self.test_tv_strategy,
            "TBS": self.test_tbs_strategy,
            "OI": self.test_oi_strategy,
            "DIRECT_QUERY": self.test_direct_query
        }
        
        for strategy_name, test_func in strategies.items():
            logger.info(f"\nTesting {strategy_name} strategy...")
            result = test_func()
            self.results[strategy_name] = result
            
            if result["success"]:
                logger.info(f"✅ {strategy_name} test passed")
            else:
                logger.error(f"❌ {strategy_name} test failed: {result.get('error', 'Unknown error')}")
        
        # Generate summary report
        self.generate_report()
    
    def generate_report(self):
        """Generate test summary report"""
        total_time = time.time() - self.start_time
        
        logger.info(f"\n{'='*60}")
        logger.info("PHASE 4 INTEGRATION TEST SUMMARY")
        logger.info(f"{'='*60}")
        logger.info(f"Total execution time: {total_time:.2f}s")
        logger.info(f"Strategies tested: {len(self.results)}")
        
        # Count successes/failures
        successful = sum(1 for r in self.results.values() if r['success'])
        failed = len(self.results) - successful
        
        logger.info(f"✅ Successful: {successful}")
        logger.info(f"❌ Failed: {failed}")
        
        # Detailed results
        logger.info(f"\n{'='*60}")
        logger.info("DETAILED RESULTS")
        logger.info(f"{'='*60}")
        
        for strategy, result in self.results.items():
            status = "✅ PASS" if result['success'] else "❌ FAIL"
            logger.info(f"\n{strategy}: {status}")
            
            if result['success']:
                logger.info(f"  ✓ Strategy executed successfully")
                if result.get('output'):
                    logger.info(f"  Output preview: {result['output'][:200]}...")
            else:
                logger.info(f"  Error: {result.get('error', 'Unknown error')}")
                if result.get('output'):
                    logger.info(f"  Output: {result['output'][:200]}...")
        
        # Save results to file
        report_file = f"/tmp/phase4_integration_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w') as f:
            json.dump({
                "test_date": datetime.now().isoformat(),
                "phase": "Phase 4 - Real Database Integration",
                "total_time": total_time,
                "total_tests": len(self.results),
                "successful": successful,
                "failed": failed,
                "results": self.results
            }, f, indent=2)
        
        logger.info(f"\n📄 Full report saved to: {report_file}")
        
        # Determine next steps
        if successful == len(self.results):
            logger.info(f"\n🎉 ALL TESTS PASSED! Phase 4 integration successful!")
            logger.info("✅ Ready for production deployment")
        else:
            logger.info(f"\n⚠️  {failed} tests failed. Fixes needed before production.")
            logger.info("🔧 Review errors and implement fixes")


def main():
    """Main test runner"""
    tester = RealDatabaseTester()
    tester.run_all_tests()


if __name__ == "__main__":
    main()
