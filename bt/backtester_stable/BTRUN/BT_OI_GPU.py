#!/usr/bin/env python3
"""
HeavyDB Open Interest (OI) Strategy Backtester

This script runs OI-based option trading strategies using HeavyDB for GPU-accelerated processing.
It supports MAXOI (maximum open interest) and MAXCOI (maximum change in open interest) strategies
with dynamic strike switching based on OI changes.

Usage:
    python BT_OI_GPU.py --portfolio-excel path/to/portfolio.xlsx
    python BT_OI_GPU.py --config path/to/config.json
    python BT_OI_GPU.py --symbol NIFTY --start-date 250401 --end-date 250430
"""

import sys
import os
import logging
import argparse
import json
import traceback
from pathlib import Path
from datetime import datetime, date
from typing import List, Dict, Optional, Tuple

# Add project root to path - same pattern as BTRunPortfolio_GPU.py
_SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
_PROJECT_ROOT = os.path.abspath(os.path.join(_SCRIPT_DIR, '../..')) 
if _PROJECT_ROOT not in sys.path:
    sys.path.insert(0, _PROJECT_ROOT)
if _SCRIPT_DIR not in sys.path:
    sys.path.insert(1, _SCRIPT_DIR)

from backtester_stable.BTRUN.models.oi import OiStrategyModel, OiMethod, CoiBasedOn
from backtester_stable.BTRUN.models import PortfolioModel, StrategyModel
from backtester_stable.BTRUN.excel_parser.portfolio_parser import parse_portfolio_excel
from backtester_stable.BTRUN.strategies.heavydb_oi_processing import OiTradeProcessor, OiTradeEntry
from backtester_stable.BTRUN.core.heavydb_connection import get_connection
from backtester_stable.BTRUN.core import io
from backtester_stable.BTRUN.core.runtime import save_backtest_results
from backtester_stable.BTRUN.core import config

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('bt_oi_gpu.log')
    ]
)
logger = logging.getLogger(__name__)

class OiBacktester:
    """Main class for running OI backtests."""
    
    def __init__(self, connection=None):
        self.connection = connection or get_connection()
        self.processor = OiTradeProcessor(self.connection)
        self.results = []
        
    def run_portfolio_backtest(
        self,
        portfolio: PortfolioModel,
        start_date: date,
        end_date: date,
        output_dir: Optional[str] = None
    ) -> Dict[str, List[OiTradeEntry]]:
        """Run backtest for a complete portfolio."""
        logger.info(f"Starting OI backtest for portfolio: {portfolio.portfolio_name}")
        logger.info(f"Date range: {start_date} to {end_date}")
        
        all_results = {}
        
        for strategy in portfolio.strategies:
            # Check if this is an OI strategy
            oi_strategy = self._extract_oi_strategy(strategy)
            
            if oi_strategy:
                logger.info(f"Processing OI strategy: {strategy.strategy_name}")
                logger.info(f"OI Method: {oi_strategy.oi_config.strike_selection.method.value}")
                logger.info(f"Timeframe: {oi_strategy.timeframe} minutes")
                logger.info(f"Max Positions: {oi_strategy.max_open_positions}")
                
                # Process the strategy
                strategy_results = self.processor.process_oi_strategy(
                    strategy, oi_strategy, start_date, end_date
                )
                
                all_results[strategy.strategy_name] = strategy_results
                logger.info(f"Completed {strategy.strategy_name}: {len(strategy_results)} trades")
            else:
                logger.warning(f"Strategy {strategy.strategy_name} is not an OI strategy, skipping")
        
        # Generate output
        if output_dir:
            self._generate_output(portfolio, all_results, output_dir)
        
        return all_results
    
    def run_strategy_backtest(
        self,
        strategy: StrategyModel,
        oi_config: OiStrategyModel,
        start_date: date,
        end_date: date
    ) -> List[OiTradeEntry]:
        """Run backtest for a single OI strategy."""
        logger.info(f"Running single strategy backtest: {strategy.strategy_name}")
        
        results = self.processor.process_oi_strategy(
            strategy, oi_config, start_date, end_date
        )
        
        logger.info(f"Strategy completed: {len(results)} trades")
        return results
    
    def _extract_oi_strategy(self, strategy: StrategyModel) -> Optional[OiStrategyModel]:
        """Extract OI strategy configuration from strategy model."""
        try:
            # Check if strategy is marked as OI type
            if strategy.evaluator.upper() == 'OI':
                return OiStrategyModel.from_excel_params(strategy.extra_params)
            
            # Check if any legs use OI-based strike selection
            for leg in strategy.legs:
                if leg.strike_rule and leg.strike_rule.value.upper().startswith(('MAXOI', 'MAXCOI')):
                    # Create OI strategy from leg parameters
                    leg_params = leg.extra_params.copy()
                    leg_params.update(strategy.extra_params)
                    leg_params['StrikeMethod'] = leg.strike_rule.value
                    return OiStrategyModel.from_excel_params(leg_params)
                
                # Check extra params for OI methods
                if leg.extra_params:
                    for key, value in leg.extra_params.items():
                        value_str = str(value).lower()
                        if value_str.startswith(('maxoi', 'maxcoi')):
                            leg_params = leg.extra_params.copy()
                            leg_params.update(strategy.extra_params)
                            return OiStrategyModel.from_excel_params(leg_params)
            
        except Exception as e:
            logger.error(f"Error extracting OI strategy from {strategy.strategy_name}: {e}")
        
        return None
    
    def _generate_output(
        self,
        portfolio: PortfolioModel,
        results: Dict[str, List[OiTradeEntry]],
        output_dir: str
    ):
        """Generate output files for backtest results."""
        try:
            output_path = Path(output_dir)
            output_path.mkdir(parents=True, exist_ok=True)
            
            # Prepare summary data
            summary_data = []
            detailed_data = []
            
            for strategy_name, trades in results.items():
                strategy_pnl = 0.0
                trade_count = len(trades)
                
                for trade in trades:
                    pnl = self.processor.calculate_trade_pnl(trade)
                    strategy_pnl += pnl
                    
                    # Add to detailed data
                    detailed_data.append({
                        'strategy_name': trade.strategy_name,
                        'leg_id': trade.leg_id,
                        'symbol': trade.symbol,
                        'strike': trade.strike,
                        'option_type': trade.option_type,
                        'entry_date': trade.entry_date.strftime('%Y-%m-%d'),
                        'entry_time': trade.entry_time.strftime('%H:%M:%S'),
                        'entry_price': trade.entry_price,
                        'exit_date': trade.exit_date.strftime('%Y-%m-%d') if trade.exit_date else '',
                        'exit_time': trade.exit_time.strftime('%H:%M:%S') if trade.exit_time else '',
                        'exit_price': trade.exit_price or 0.0,
                        'exit_reason': trade.exit_reason or '',
                        'lots': trade.lots,
                        'oi_rank': trade.oi_rank,
                        'oi_method': trade.oi_method,
                        'pnl': pnl
                    })
                
                # Add to summary
                summary_data.append({
                    'strategy_name': strategy_name,
                    'total_trades': trade_count,
                    'total_pnl': strategy_pnl,
                    'avg_pnl_per_trade': strategy_pnl / trade_count if trade_count > 0 else 0.0
                })
            
            # Write results using the existing write_results function
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"oi_backtest_{portfolio.portfolio_name}_{timestamp}.xlsx"
            
            save_backtest_results({
                'Summary': summary_data,
                'Detailed_Trades': detailed_data,
                'Portfolio_Config': [portfolio.model_dump()]
            }, output_path / filename)
            
            logger.info(f"Results written to: {output_path / filename}")
            
        except Exception as e:
            logger.error(f"Error generating output: {e}")
            logger.error(traceback.format_exc())

def parse_arguments():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(
        description="HeavyDB Open Interest Strategy Backtester",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Run from portfolio Excel file
  python BT_OI_GPU.py --portfolio-excel input_portfolio.xlsx
  
  # Run with custom date range
  python BT_OI_GPU.py --portfolio-excel input_portfolio.xlsx --start-date 250401 --end-date 250430
  
  # Run from JSON configuration
  python BT_OI_GPU.py --config oi_config.json
  
  # Run single symbol with defaults
  python BT_OI_GPU.py --symbol NIFTY --oi-method maxoi_1 --timeframe 3
        """
    )
    
    # Input options
    input_group = parser.add_mutually_exclusive_group(required=True)
    input_group.add_argument('--portfolio-excel', '-p', type=str,
                           help='Path to portfolio Excel file')
    input_group.add_argument('--config', '-c', type=str,
                           help='Path to JSON configuration file')
    input_group.add_argument('--symbol', '-s', type=str,
                           help='Symbol to backtest (for quick testing)')
    
    # Date range options
    parser.add_argument('--start-date', type=str,
                       help='Start date in YYMMDD format (e.g., 250401)')
    parser.add_argument('--end-date', type=str,
                       help='End date in YYMMDD format (e.g., 250430)')
    
    # OI-specific options
    parser.add_argument('--oi-method', type=str, choices=[method.value for method in OiMethod],
                       help='OI method for single symbol testing')
    parser.add_argument('--timeframe', type=int, default=3,
                       help='Timeframe in minutes (must be multiple of 3)')
    parser.add_argument('--max-positions', type=int, default=1,
                       help='Maximum concurrent positions')
    parser.add_argument('--oi-threshold', type=int, default=800000,
                       help='Minimum OI threshold')
    parser.add_argument('--coi-method', type=str, choices=[method.value for method in CoiBasedOn],
                       default='yesterday_close', help='COI calculation method')
    
    # Output options
    parser.add_argument('--output-dir', '-o', type=str, default='./results',
                       help='Output directory for results')
    parser.add_argument('--merge-output', action='store_true',
                       help='Copy output to Merge folder')
    
    # Execution options
    parser.add_argument('--cpu-only', action='store_true',
                       help='Force CPU-only processing (disable GPU)')
    parser.add_argument('--debug', action='store_true',
                       help='Enable debug logging')
    
    return parser.parse_args()

def create_test_strategy(
    symbol: str,
    oi_method: str,
    timeframe: int,
    max_positions: int,
    oi_threshold: int,
    coi_method: str
) -> Tuple[StrategyModel, OiStrategyModel]:
    """Create a test strategy for single symbol backtesting."""
    from backtester_stable.BTRUN.models import LegModel, OptionType, TransactionType, StrikeRule, ExpiryRule
    
    # Create OI strategy model
    oi_strategy = OiStrategyModel.from_excel_params({
        'Timeframe': str(timeframe),
        'MaxOpenPositions': str(max_positions),
        'OiThreshold': str(oi_threshold),
        'StrikeMethod': oi_method,
        'CoiBasedOn': coi_method,
        'StrikeCount': '15'
    })
    
    # Create legs for both CE and PE
    legs = [
        LegModel(
            leg_id='ce_leg',
            index=symbol,
            option_type=OptionType.CALL,
            transaction=TransactionType.SELL,
            lots=1,
            strike_rule=StrikeRule.ATM,  # Will be overridden by OI logic
            expiry_rule=ExpiryRule.CURRENT_WEEK,
            entry_time='09:16:00',
            exit_time='15:15:00',
            risk_rules=[],
            extra_params={'StrikeMethod': oi_method}
        ),
        LegModel(
            leg_id='pe_leg',
            index=symbol,
            option_type=OptionType.PUT,
            transaction=TransactionType.SELL,
            lots=1,
            strike_rule=StrikeRule.ATM,  # Will be overridden by OI logic
            expiry_rule=ExpiryRule.CURRENT_WEEK,
            entry_time='09:16:00',
            exit_time='15:15:00',
            risk_rules=[],
            extra_params={'StrikeMethod': oi_method}
        )
    ]
    
    # Create strategy model
    strategy = StrategyModel(
        strategy_name=f"{symbol}_OI_{oi_method.upper()}",
        evaluator='OI',
        is_tick_bt=False,
        entry_start='09:16:00',
        entry_end='15:15:00',
        legs=legs,
        extra_params={
            'Timeframe': str(timeframe),
            'MaxOpenPositions': str(max_positions),
            'OiThreshold': str(oi_threshold),
            'CoiBasedOn': coi_method
        },
        strategy_excel_path="test_oi_strategy.xlsx"  # Required field
    )
    
    return strategy, oi_strategy

def parse_date(date_str: str) -> date:
    """Parse date string in YYMMDD format."""
    try:
        return datetime.strptime(date_str, '%y%m%d').date()
    except ValueError:
        try:
            return datetime.strptime(date_str, '%Y-%m-%d').date()
        except ValueError:
            raise ValueError(f"Invalid date format: {date_str}. Use YYMMDD or YYYY-MM-DD")

def main():
    """Main execution function."""
    args = parse_arguments()
    
    # Set up debug logging if requested
    if args.debug:
        logging.getLogger().setLevel(logging.DEBUG)
        logger.setLevel(logging.DEBUG)
    
    # Disable GPU if requested
    if args.cpu_only:
        config.GPU_ENABLED = False
        logger.info("GPU processing disabled - using CPU only")
    
    try:
        # Initialize backtester
        backtester = OiBacktester()
        
        # Determine date range
        start_date = parse_date(args.start_date) if args.start_date else date(2025, 4, 1)
        end_date = parse_date(args.end_date) if args.end_date else date(2025, 4, 2)
        
        logger.info(f"Date range: {start_date} to {end_date}")
        
        # Process based on input type
        if args.portfolio_excel:
            # Load from Excel file
            logger.info(f"Loading portfolio from Excel: {args.portfolio_excel}")
            portfolios_dict = parse_portfolio_excel(args.portfolio_excel)

            # Get the first (and typically only) portfolio
            if not portfolios_dict:
                raise ValueError("No portfolios found in Excel file")

            portfolio_name = list(portfolios_dict.keys())[0]
            portfolio = portfolios_dict[portfolio_name]
            logger.info(f"Using portfolio: {portfolio_name}")

            # Override dates if provided
            if args.start_date:
                portfolio.start_date = start_date
            if args.end_date:
                portfolio.end_date = end_date
            
            # Run backtest
            results = backtester.run_portfolio_backtest(
                portfolio, start_date, end_date, args.output_dir
            )
            
        elif args.config:
            # Load from JSON config
            logger.info(f"Loading configuration from JSON: {args.config}")
            with open(args.config, 'r') as f:
                config_data = json.load(f)
            
            # This would need to be implemented based on config structure
            logger.error("JSON configuration loading not yet implemented")
            return 1
            
        elif args.symbol:
            # Create test strategy for single symbol
            if not args.oi_method:
                logger.error("--oi-method is required when using --symbol")
                return 1
            
            logger.info(f"Creating test strategy for {args.symbol}")
            strategy, oi_strategy = create_test_strategy(
                args.symbol, args.oi_method, args.timeframe,
                args.max_positions, args.oi_threshold, args.coi_method
            )
            
            # Run single strategy backtest
            results = backtester.run_strategy_backtest(
                strategy, oi_strategy, start_date, end_date
            )
            
            # Generate simple output
            if results:
                logger.info(f"Test strategy completed: {len(results)} trades")
                for trade in results[:5]:  # Show first 5 trades
                    pnl = backtester.processor.calculate_trade_pnl(trade)
                    logger.info(f"Trade: {trade.symbol}@{trade.strike} P&L: {pnl:.2f}")
            else:
                logger.info("No trades generated")
        
        logger.info("OI Backtest completed successfully")
        return 0
        
    except Exception as e:
        logger.error(f"Error during backtest execution: {e}")
        logger.error(traceback.format_exc())
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code) 