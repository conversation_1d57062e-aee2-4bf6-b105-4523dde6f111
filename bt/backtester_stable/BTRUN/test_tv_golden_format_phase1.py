#!/usr/bin/env python3
"""
Phase 1 TV Golden Format Integration Test

This script tests the golden format integration across all TV strategy files:
1. BT_TV_GPU.py - Main TV processor
2. BT_TV_GPU_aggregated_v4.py - Archive parity aggregated processor  
3. BTTVFromFrontend_GPU.py - Frontend API integration
4. Enterprise server integration

Tests both golden format generation and fallback mechanisms.
"""

import os
import sys
import logging
import pandas as pd
from datetime import datetime
import tempfile
import shutil

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s [%(levelname)s] %(name)s: %(message)s"
)
logger = logging.getLogger("TV_Golden_Format_Phase1_Test")

# Add BTRUN to path
script_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, script_dir)

def test_golden_format_generator():
    """Test the golden format generator directly"""
    logger.info("🧪 Testing Golden Format Generator")
    
    try:
        # Import golden format generator
        sys.path.append(os.path.join(script_dir, 'utils'))
        from golden_format_excel_generator import GoldenFormatExcelGenerator
        
        generator = GoldenFormatExcelGenerator()
        logger.info("✅ Golden format generator imported successfully")
        
        # Create test data
        tv_signals_df = pd.DataFrame({
            'tradeno': ['T001', 'T002'],
            'direction': ['LONG', 'SHORT'],
            'symbol': ['NIFTY', 'NIFTY'],
            'status': ['EXECUTED', 'EXECUTED']
        })
        
        portfolio_trans_df = pd.DataFrame({
            'tradeno': ['T001', 'T002'],
            'pnl': [1000.0, -500.0],
            'entry_date': ['2024-01-03', '2024-01-03'],
            'exit_date': ['2024-01-03', '2024-01-03']
        })
        
        tv_settings = {
            'name': 'PHASE1_TEST',
            'strategy_type': 'TV',
            'symbol': 'NIFTY'
        }
        
        # Test golden format generation
        with tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False) as tmp:
            output_path = tmp.name
        
        success = generator.generate_tv_golden_format(
            tv_signals_df=tv_signals_df,
            portfolio_trans_df=portfolio_trans_df,
            long_trans_df=portfolio_trans_df,
            short_trans_df=pd.DataFrame(),
            tv_settings=tv_settings,
            output_path=output_path
        )
        
        if success and os.path.exists(output_path):
            logger.info("✅ Golden format generation test passed")
            os.unlink(output_path)
            return True
        else:
            logger.error("❌ Golden format generation test failed")
            return False
            
    except Exception as e:
        logger.error(f"❌ Golden format generator test failed: {e}")
        return False

def test_bt_tv_gpu_functions():
    """Test BT_TV_GPU.py golden format functions"""
    logger.info("🧪 Testing BT_TV_GPU.py Functions")
    
    try:
        # Test function imports
        from BT_TV_GPU import _prepare_tv_signals_data_from_result, _prepare_tv_settings_from_result
        logger.info("✅ BT_TV_GPU.py functions imported successfully")
        
        # Test data preparation functions
        test_result = {
            'transaction_dfs': {
                'portfolio': pd.DataFrame({
                    'tradeno': ['T001'],
                    'pnl': [1000.0]
                })
            }
        }
        
        signals_df = _prepare_tv_signals_data_from_result(test_result, 'TEST_TV', 'LONG', 'T001')
        settings = _prepare_tv_settings_from_result('TEST_TV', 'LONG')
        
        if len(signals_df) > 0 and len(settings) > 0:
            logger.info("✅ BT_TV_GPU.py functions test passed")
            return True
        else:
            logger.error("❌ BT_TV_GPU.py functions test failed")
            return False
            
    except Exception as e:
        logger.error(f"❌ BT_TV_GPU.py functions test failed: {e}")
        return False

def test_bt_tv_gpu_aggregated_functions():
    """Test BT_TV_GPU_aggregated_v4.py golden format functions"""
    logger.info("🧪 Testing BT_TV_GPU_aggregated_v4.py Functions")
    
    try:
        # Test function imports
        from BT_TV_GPU_aggregated_v4 import _prepare_tv_signals_data_from_aggregated_result, _prepare_tv_settings_from_aggregated_result
        logger.info("✅ BT_TV_GPU_aggregated_v4.py functions imported successfully")
        
        # Create mock TV setting
        class MockTVSetting:
            def __init__(self):
                self.Name = "TEST_AGGREGATED"
                self.SignalFilePath = None
        
        tv_setting = MockTVSetting()
        all_results = [
            {'signal_direction': 'LONG'},
            {'signal_direction': 'SHORT'}
        ]
        
        signals_df = _prepare_tv_signals_data_from_aggregated_result(tv_setting, all_results)
        settings = _prepare_tv_settings_from_aggregated_result(tv_setting)
        
        if len(signals_df) >= 0 and len(settings) > 0:
            logger.info("✅ BT_TV_GPU_aggregated_v4.py functions test passed")
            return True
        else:
            logger.error("❌ BT_TV_GPU_aggregated_v4.py functions test failed")
            return False
            
    except Exception as e:
        logger.error(f"❌ BT_TV_GPU_aggregated_v4.py functions test failed: {e}")
        return False

def test_bttv_frontend_functions():
    """Test BTTVFromFrontend_GPU.py golden format functions"""
    logger.info("🧪 Testing BTTVFromFrontend_GPU.py Functions")
    
    try:
        # Test function imports
        from BTTVFromFrontend_GPU import _prepare_tv_signals_data_from_frontend_result, _prepare_tv_settings_from_frontend_result
        logger.info("✅ BTTVFromFrontend_GPU.py functions imported successfully")
        
        # Test data preparation functions
        portfolio_trans_df = pd.DataFrame({
            'entry_date': ['2024-01-03'],
            'entry_time': ['09:15:00'],
            'symbol': ['NIFTY']
        })
        
        signals_df = _prepare_tv_signals_data_from_frontend_result(portfolio_trans_df, 'TEST_API')
        settings = _prepare_tv_settings_from_frontend_result('TEST_API')
        
        if len(signals_df) > 0 and len(settings) > 0:
            logger.info("✅ BTTVFromFrontend_GPU.py functions test passed")
            return True
        else:
            logger.error("❌ BTTVFromFrontend_GPU.py functions test failed")
            return False
            
    except Exception as e:
        logger.error(f"❌ BTTVFromFrontend_GPU.py functions test failed: {e}")
        return False

def test_enterprise_server_mapping():
    """Test enterprise server TV script mapping"""
    logger.info("🧪 Testing Enterprise Server TV Mapping")
    
    try:
        # Check if the enterprise server has correct TV mapping
        enterprise_file = os.path.join(script_dir, 'enterprise_server_v2.py')
        if os.path.exists(enterprise_file):
            with open(enterprise_file, 'r') as f:
                content = f.read()
                if "'TV': 'BT_TV_GPU.py'" in content:
                    logger.info("✅ Enterprise server TV mapping test passed")
                    return True
                else:
                    logger.error("❌ Enterprise server TV mapping test failed")
                    return False
        else:
            logger.warning("⚠️ Enterprise server file not found, skipping test")
            return True
            
    except Exception as e:
        logger.error(f"❌ Enterprise server mapping test failed: {e}")
        return False

def main():
    """Run all Phase 1 tests"""
    logger.info("🚀 Starting TV Golden Format Phase 1 Integration Tests")
    logger.info("="*60)
    
    tests = [
        ("Golden Format Generator", test_golden_format_generator),
        ("BT_TV_GPU Functions", test_bt_tv_gpu_functions),
        ("BT_TV_GPU_aggregated_v4 Functions", test_bt_tv_gpu_aggregated_functions),
        ("BTTVFromFrontend_GPU Functions", test_bttv_frontend_functions),
        ("Enterprise Server Mapping", test_enterprise_server_mapping)
    ]
    
    results = []
    for test_name, test_func in tests:
        logger.info(f"\n📋 Running: {test_name}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            logger.error(f"💥 {test_name} crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    logger.info("\n" + "="*60)
    logger.info("📊 PHASE 1 TEST RESULTS SUMMARY")
    logger.info("="*60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        logger.info(f"{status} - {test_name}")
        if result:
            passed += 1
    
    logger.info(f"\n🎯 Overall Result: {passed}/{total} tests passed")
    
    if passed == total:
        logger.info("🎉 ALL PHASE 1 TESTS PASSED!")
        logger.info("✅ TV Golden Format Integration Phase 1 is ready for deployment")
        return 0
    else:
        logger.error("❌ Some tests failed. Please review and fix issues before deployment.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
