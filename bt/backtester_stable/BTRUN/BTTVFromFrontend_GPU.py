#!/usr/bin/env python3
"""
GPU-Accelerated BTTVFromFrontend

This script serves as an API endpoint for running Technical Verification backtests
from a frontend application. It uses GPU acceleration when available and
integrates with HeavyDB for data processing.
"""

import os
import sys
import json
import logging
import argparse
from datetime import datetime
import time
import traceback
from flask import Flask, request, jsonify

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s [%(levelname)s] %(name)s: %(message)s"
)
logger = logging.getLogger("BTTVFromFrontend_GPU")

# Add parent directory to path to import backtester modules
script_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(script_dir)
sys.path.insert(0, parent_dir)

# Import BTRUN modules
from BTRUN import runtime
from BTRUN import gpu_helpers as gh
from BTRUN import stats
from BTRUN import io

# Create Flask app
app = Flask(__name__)

# Global configuration
OUTPUT_DIR = "tv_api_output"
os.makedirs(OUTPUT_DIR, exist_ok=True)

@app.route('/health', methods=['GET'])
def health_check():
    """Health check endpoint to verify API is running."""
    return jsonify({
        "status": "ok",
        "service": "BTTV_GPU",
        "gpu_available": gh.CUDF_AVAILABLE,
        "gpu_enabled": gh.GPU_ENABLED,
        "timestamp": datetime.now().isoformat()
    })

@app.route('/run_tv_backtest', methods=['POST'])
def run_tv_backtest_endpoint():
    """Endpoint to run a Technical Verification backtest with the provided parameters."""
    start_time = time.time()
    
    try:
        # Parse JSON input
        if not request.is_json:
            return jsonify({"error": "Request must be JSON"}), 400
        
        # Get TV backtest parameters from request
        tv_params = request.json
        
        # Extract any special parameters from the request
        disable_gpu = tv_params.pop("disable_gpu", False)
        initial_capital = tv_params.pop("initial_capital", 100000.0)
        result_format = tv_params.pop("result_format", "both")  # 'json', 'excel', or 'both'
        
        # Set GPU mode based on request
        if disable_gpu:
            gh.GPU_ENABLED = False
            logger.info("GPU acceleration disabled for this request")
        
        # Run the TV backtest
        logger.info("Starting Technical Verification backtest...")
        bt_response = runtime.run_tv_backtest(tv_params=tv_params)
        logger.info("TV backtest complete")
        
        # Process the results
        logger.info("Processing backtest results...")
        slippage_percent = tv_params.get("slippage_percent", 0.05)
        
        (
            trades_df, 
            metrics_df, 
            stgy_trans_dfs, 
            day_stats, 
            month_stats, 
            margin_stats,
            daily_max_pl_df
        ) = runtime.process_backtest_results(
            bt_response=bt_response,
            slippage_percent=slippage_percent,
            initial_capital=initial_capital
        )
        
        # Generate output file name
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        request_id = request.headers.get("X-Request-ID", f"tv_request_{timestamp}")
        output_base = os.path.join(OUTPUT_DIR, f"tv_{request_id}")
        
        # Save results based on requested format
        json_path = None
        excel_path = None
        
        if result_format in ["json", "both"]:
            json_path, _ = runtime.save_backtest_results(
                output_base_path=output_base,
                metrics_df=metrics_df,
                transaction_dfs=stgy_trans_dfs,
                day_stats=day_stats,
                month_stats=month_stats,
                margin_stats=margin_stats,
                daily_max_pl_df=daily_max_pl_df,
                save_json=True,
                save_excel=False
            )
        
        if result_format in ["excel", "both"]:
            # Try golden format first
            excel_path = f"{output_base}.xlsx"
            golden_format_success = _generate_tv_frontend_golden_format_output(
                metrics_df=metrics_df,
                transaction_dfs=stgy_trans_dfs,
                day_stats=day_stats,
                month_stats=month_stats,
                margin_stats=margin_stats,
                daily_max_pl_df=daily_max_pl_df,
                output_path=excel_path,
                request_id=request_id
            )

            if not golden_format_success:
                # Fallback to legacy format
                logger.warning("Golden format generation failed, using legacy format")
                _, excel_path = runtime.save_backtest_results(
                    output_base_path=output_base,
                    metrics_df=metrics_df,
                    transaction_dfs=stgy_trans_dfs,
                    day_stats=day_stats,
                    month_stats=month_stats,
                    margin_stats=margin_stats,
                    daily_max_pl_df=daily_max_pl_df,
                    save_json=False,
                    save_excel=True
                )
        
        # Calculate summary metrics
        total_pnl = metrics_df[metrics_df["Particulars"] == "Total PnL"]["Value"].values[0] if not metrics_df.empty else 0
        win_rate = metrics_df[metrics_df["Particulars"] == "Win Rate"]["Value"].values[0] * 100 if not metrics_df.empty else 0
        num_trades = metrics_df[metrics_df["Particulars"] == "Number of Trades"]["Value"].values[0] if not metrics_df.empty else 0
        
        # Prepare response
        response = {
            "status": "success",
            "request_id": request_id,
            "elapsed_time": time.time() - start_time,
            "gpu_used": gh.GPU_ENABLED and gh.CUDF_AVAILABLE,
            "summary": {
                "total_pnl": float(total_pnl),
                "win_rate": float(win_rate),
                "num_trades": int(num_trades)
            },
            "output_files": {
                "json": json_path,
                "excel": excel_path
            }
        }
        
        logger.info(f"TV backtest completed successfully in {response['elapsed_time']:.2f} seconds")
        return jsonify(response)
    
    except Exception as e:
        error_message = str(e)
        stack_trace = traceback.format_exc()
        
        logger.error(f"Error running TV backtest: {error_message}\n{stack_trace}")
        
        return jsonify({
            "status": "error",
            "error": error_message,
            "stack_trace": stack_trace,
            "elapsed_time": time.time() - start_time
        }), 500


def _generate_tv_frontend_golden_format_output(metrics_df, transaction_dfs, day_stats,
                                             month_stats, margin_stats, daily_max_pl_df,
                                             output_path, request_id):
    """
    Generate TV frontend output in exact 16-sheet golden format

    Args:
        metrics_df: Metrics DataFrame
        transaction_dfs: Transaction DataFrames dictionary
        day_stats: Day-wise statistics
        month_stats: Month-wise statistics
        margin_stats: Margin statistics
        daily_max_pl_df: Daily max P&L DataFrame
        output_path: Output file path
        request_id: Request ID for tracking

    Returns:
        bool: Success status
    """
    try:
        logger.info(f"🎯 Generating TV frontend golden format: {output_path}")

        # Import required modules
        import sys
        import pandas as pd
        sys.path.append(os.path.join(os.path.dirname(__file__), 'utils'))
        from golden_format_excel_generator import GoldenFormatExcelGenerator

        generator = GoldenFormatExcelGenerator()

        # Get main portfolio transactions
        portfolio_trans_df = transaction_dfs.get('portfolio', transaction_dfs.get('Portfolio', None))
        if portfolio_trans_df is None and transaction_dfs:
            # Take the first available transaction DataFrame
            portfolio_trans_df = list(transaction_dfs.values())[0]

        if portfolio_trans_df is None or portfolio_trans_df.empty:
            logger.warning("No portfolio transactions found for frontend request")
            return False

        # Prepare data for golden format
        tv_signals_df = _prepare_tv_signals_data_from_frontend_result(portfolio_trans_df, request_id)
        long_trans_df = portfolio_trans_df[portfolio_trans_df.get('side', '') == 'BUY'] if 'side' in portfolio_trans_df.columns else portfolio_trans_df
        short_trans_df = portfolio_trans_df[portfolio_trans_df.get('side', '') == 'SELL'] if 'side' in portfolio_trans_df.columns else pd.DataFrame()
        tv_settings = _prepare_tv_settings_from_frontend_result(request_id)

        # Generate golden format
        success = generator.generate_tv_golden_format(
            tv_signals_df=tv_signals_df,
            portfolio_trans_df=portfolio_trans_df,
            long_trans_df=long_trans_df,
            short_trans_df=short_trans_df,
            tv_settings=tv_settings,
            output_path=output_path
        )

        if success:
            logger.info(f"✅ TV frontend golden format generated successfully")
        else:
            logger.error(f"❌ TV frontend golden format generation failed")

        return success

    except Exception as e:
        logger.error(f"Error generating TV frontend golden format: {e}")
        return False


def _prepare_tv_signals_data_from_frontend_result(portfolio_trans_df, request_id):
    """Prepare TV signals data from frontend result"""
    try:
        import pandas as pd

        # Create signals based on unique trades
        unique_trades = portfolio_trans_df.groupby(['entry_date', 'entry_time']).first().reset_index()

        signal_data = []
        for i, trade in unique_trades.iterrows():
            signal_data.append({
                'tradeno': f"API_{i+1:03d}",
                'direction': 'LONG',  # Default for API requests
                'tv_setting': f'API_REQUEST_{request_id}',
                'timestamp': f"{trade.get('entry_date', '')} {trade.get('entry_time', '')}",
                'symbol': trade.get('symbol', 'NIFTY'),
                'status': 'EXECUTED'
            })

        return pd.DataFrame(signal_data)

    except Exception as e:
        logger.error(f"Error preparing TV signals data from frontend: {e}")
        import pandas as pd
        return pd.DataFrame()


def _prepare_tv_settings_from_frontend_result(request_id):
    """Prepare TV settings data from frontend result"""
    return {
        'name': f'API_REQUEST_{request_id}',
        'strategy_type': 'TV_API',
        'symbol': 'NIFTY',
        'generated_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
        'source': 'Frontend API'
    }


def parse_arguments():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description="Start the TV backtest API server with GPU acceleration")
    
    parser.add_argument("--host", default="127.0.0.1",
                      help="Host to bind the server to (default: 127.0.0.1)")
    parser.add_argument("--port", "-p", type=int, default=5001,
                      help="Port to bind the server to (default: 5001)")
    parser.add_argument("--output", "-o", 
                      help=f"Output directory for results (default: {OUTPUT_DIR})")
    parser.add_argument("--cpu-only", action="store_true",
                      help="Disable GPU acceleration and use CPU only")
    parser.add_argument("--debug", action="store_true",
                      help="Run the server in debug mode")
    
    return parser.parse_args()

def main():
    """Main entry point."""
    global OUTPUT_DIR
    
    args = parse_arguments()
    
    # Configure output directory
    if args.output:
        OUTPUT_DIR = args.output
        os.makedirs(OUTPUT_DIR, exist_ok=True)
    
    # Set debug mode for Flask if requested
    debug_mode = args.debug
    
    # Check GPU status and set mode
    if args.cpu_only:
        gh.GPU_ENABLED = False
        logger.info("Running in CPU-only mode (GPU disabled)")
    else:
        logger.info(f"GPU acceleration available: {gh.CUDF_AVAILABLE}")
        logger.info(f"GPU acceleration enabled: {gh.GPU_ENABLED}")
    
    # Print startup message
    logger.info(f"Starting BTTVFromFrontend_GPU API server on {args.host}:{args.port}")
    logger.info(f"Output directory: {OUTPUT_DIR}")
    
    # Start Flask server
    app.run(host=args.host, port=args.port, debug=debug_mode)
    
    return 0

if __name__ == "__main__":
    sys.exit(main()) 