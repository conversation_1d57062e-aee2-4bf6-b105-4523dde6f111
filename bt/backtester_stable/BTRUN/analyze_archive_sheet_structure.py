#!/usr/bin/env python3
"""
Deep Archive Sheet Structure Analysis

This script analyzes the actual archive code to understand how sheets are generated
based on input portfolios for different strategy types (TV, TBS, OI, ORB).

Key findings from archive code analysis:
1. Util.prepareOutputFile() is the main function that creates sheets
2. Sheet generation is based on stgywiseTransactionDf keys (strategy names)
3. Each strategy gets: [STRATEGY] Trans + [STRATEGY] Results
4. Base sheets: PortfolioParameter, GeneralParameter, LegParameter, Metrics, Max Profit and Loss
"""

import os
import sys
import logging
import pandas as pd
from pathlib import Path

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s [%(levelname)s] %(name)s: %(message)s"
)
logger = logging.getLogger("Archive_Sheet_Structure_Analyzer")

def analyze_archive_util_code():
    """Analyze the archive Util.py code to understand sheet generation"""
    logger.info("🔍 Analyzing Archive Util.py Sheet Generation Logic")
    
    # Key findings from archive code analysis:
    archive_findings = {
        'sheet_generation_pattern': {
            'base_sheets': [
                'PortfolioParameter',
                'GeneralParameter', 
                'LegParameter',
                'Metrics',
                'Max Profit and Loss'
            ],
            'strategy_sheets_per_strategy': [
                '[STRATEGY_NAME] Trans',
                '[STRATEGY_NAME] Results'
            ],
            'strategy_naming': {
                'TV': 'Uses signal direction (LONG, SHORT) or custom names',
                'TBS': 'Uses strategy names from StrategySetting (H1, H2, etc.)',
                'OI': 'Uses OI strategy names',
                'ORB': 'Uses ORB strategy names'
            }
        },
        'key_functions': {
            'prepareOutputFile': 'Main function that creates all sheets',
            'sheet_creation_logic': 'Loops through stgywiseTransactionDf keys',
            'column_mapping': 'Uses COLUMN_RENAME_MAPPING for consistency',
            'column_order': 'Uses COLUMN_ORDER for proper arrangement'
        }
    }
    
    logger.info("📋 Archive Sheet Generation Pattern:")
    logger.info(f"   Base Sheets: {len(archive_findings['sheet_generation_pattern']['base_sheets'])}")
    logger.info(f"   Strategy Sheets: 2 per strategy (Trans + Results)")
    logger.info(f"   Total Formula: {len(archive_findings['sheet_generation_pattern']['base_sheets'])} + (2 × number_of_strategies)")
    
    return archive_findings

def analyze_strategy_specific_patterns():
    """Analyze how different strategy types generate sheets"""
    logger.info("🎯 Analyzing Strategy-Specific Sheet Patterns")
    
    strategy_patterns = {
        'TV': {
            'description': 'TV strategies based on signal files',
            'sheet_pattern': 'Base(5) + LONG Trans + LONG Results + SHORT Trans + SHORT Results = 9 sheets',
            'strategy_naming': 'LONG, SHORT (or custom from signal file)',
            'input_dependency': 'Number of signal directions in TV signal file',
            'example_sheets': ['Tv Setting', 'Tv Signals', 'PORTFOLIO Trans', 'LONG_L Trans', 'SHORT_S Trans', 'Metrics']
        },
        'TBS': {
            'description': 'Time-Based Strategies with multiple sub-strategies',
            'sheet_pattern': 'Base(5) + PORTFOLIO Trans + PORTFOLIO Results + (H1-HN Trans + H1-HN Results)',
            'strategy_naming': 'H1, H2, H3, ..., HN (where N = number of strategies)',
            'input_dependency': 'Number of enabled strategies in StrategySetting sheet',
            'example_sheets': ['PortfolioParameter', 'GeneralParameter', 'LegParameter', 'PORTFOLIO Trans', 'H1 Trans', 'H1 Results']
        },
        'OI': {
            'description': 'Open Interest strategies',
            'sheet_pattern': 'Base(5) + Strategy-specific sheets based on OI configuration',
            'strategy_naming': 'Based on OI strategy names (MAXOI, MAXCOI, etc.)',
            'input_dependency': 'OI strategy configuration and timeframes',
            'example_sheets': ['PortfolioParameter', 'GeneralParameter', 'LegParameter', 'MAXOI Trans', 'MAXOI Results']
        },
        'ORB': {
            'description': 'Opening Range Breakout strategies',
            'sheet_pattern': 'Base(5) + ORB strategy sheets',
            'strategy_naming': 'Based on ORB configuration (ORB_15MIN, ORB_30MIN, etc.)',
            'input_dependency': 'ORB timeframe and breakout configurations',
            'example_sheets': ['PortfolioParameter', 'GeneralParameter', 'LegParameter', 'ORB_15MIN Trans', 'ORB_15MIN Results']
        }
    }
    
    for strategy_type, pattern in strategy_patterns.items():
        logger.info(f"\n📊 {strategy_type} Strategy Pattern:")
        logger.info(f"   Description: {pattern['description']}")
        logger.info(f"   Sheet Pattern: {pattern['sheet_pattern']}")
        logger.info(f"   Strategy Naming: {pattern['strategy_naming']}")
        logger.info(f"   Input Dependency: {pattern['input_dependency']}")
    
    return strategy_patterns

def analyze_input_portfolio_relationship():
    """Analyze how input portfolio configuration affects sheet generation"""
    logger.info("🔗 Analyzing Input Portfolio → Sheet Generation Relationship")
    
    portfolio_relationships = {
        'portfolio_excel_structure': {
            'PortfolioSetting': 'Defines enabled portfolios',
            'StrategySetting': 'Maps strategies to portfolios, defines strategy files',
            'key_columns': ['Enabled', 'PortfolioName', 'StrategyType', 'StrategyExcelFilePath']
        },
        'sheet_generation_logic': {
            'step1': 'Read PortfolioSetting → Filter Enabled=YES portfolios',
            'step2': 'For each portfolio, read StrategySetting → Filter Enabled=YES strategies',
            'step3': 'For each strategy, load StrategyExcelFilePath → Extract strategy parameters',
            'step4': 'Generate sheets: Base(5) + 2×(number_of_enabled_strategies)',
            'step5': 'Strategy naming based on strategy type and configuration'
        },
        'dynamic_sheet_count': {
            'formula': 'Total Sheets = 5 (base) + 2 × (enabled strategies count)',
            'examples': {
                'single_strategy': '5 + 2×1 = 7 sheets',
                'multi_strategy_5': '5 + 2×5 = 15 sheets', 
                'tbs_80_strategies': '5 + 2×80 = 165 sheets (+ PORTFOLIO = 167 sheets)',
                'tv_2_directions': '5 + 2×2 = 9 sheets (+ special TV sheets = 16 sheets)'
            }
        }
    }
    
    logger.info("📈 Dynamic Sheet Generation Formula:")
    logger.info(f"   {portfolio_relationships['dynamic_sheet_count']['formula']}")
    logger.info("📋 Examples:")
    for example, count in portfolio_relationships['dynamic_sheet_count']['examples'].items():
        logger.info(f"   {example}: {count}")
    
    return portfolio_relationships

def create_unified_golden_format_structure():
    """Create unified golden format structure for all strategy types"""
    logger.info("🎯 Creating Unified Golden Format Structure")
    
    unified_structure = {
        'base_sheets': {
            'count': 5,
            'sheets': [
                'PortfolioParameter',
                'GeneralParameter',
                'LegParameter', 
                'Metrics',
                'Max Profit and Loss'
            ],
            'description': 'Common base sheets for all strategy types'
        },
        'strategy_sheets': {
            'pattern': '[STRATEGY_NAME] Trans + [STRATEGY_NAME] Results',
            'count_per_strategy': 2,
            'description': 'Each enabled strategy gets 2 sheets'
        },
        'special_sheets': {
            'TV': ['Tv Setting', 'Tv Signals'],
            'TBS': ['PORTFOLIO Trans', 'PORTFOLIO Results'],
            'OI': [],
            'ORB': []
        },
        'total_formula': 'Base(5) + Special + 2×(enabled_strategies)',
        'strategy_naming_rules': {
            'TV': 'LONG_L, SHORT_S (or custom from signals)',
            'TBS': 'H1, H2, H3, ..., HN + PORTFOLIO',
            'OI': 'MAXOI, MAXCOI, etc. (based on OI config)',
            'ORB': 'ORB_15MIN, ORB_30MIN, etc. (based on timeframe)'
        }
    }
    
    logger.info("🏗️ Unified Golden Format Structure:")
    logger.info(f"   Base Sheets: {unified_structure['base_sheets']['count']}")
    logger.info(f"   Strategy Sheets: {unified_structure['strategy_sheets']['count_per_strategy']} per strategy")
    logger.info(f"   Total Formula: {unified_structure['total_formula']}")
    
    logger.info("\n📝 Strategy Naming Rules:")
    for strategy_type, naming in unified_structure['strategy_naming_rules'].items():
        logger.info(f"   {strategy_type}: {naming}")
    
    return unified_structure

def main():
    """Run comprehensive archive sheet structure analysis"""
    logger.info("🚀 Starting Deep Archive Sheet Structure Analysis")
    logger.info("="*70)
    
    # Analyze archive code
    archive_findings = analyze_archive_util_code()
    
    # Analyze strategy patterns
    strategy_patterns = analyze_strategy_specific_patterns()
    
    # Analyze input relationships
    portfolio_relationships = analyze_input_portfolio_relationship()
    
    # Create unified structure
    unified_structure = create_unified_golden_format_structure()
    
    # Summary
    logger.info("\n" + "="*70)
    logger.info("📊 ARCHIVE SHEET STRUCTURE ANALYSIS COMPLETE")
    logger.info("="*70)
    
    logger.info("🎯 KEY FINDINGS:")
    logger.info("1. Sheet count is DYNAMIC based on enabled strategies in input portfolio")
    logger.info("2. Formula: Base(5) + Special + 2×(enabled_strategies)")
    logger.info("3. Strategy naming follows specific patterns per strategy type")
    logger.info("4. Archive code uses stgywiseTransactionDf keys for sheet generation")
    logger.info("5. All strategies follow same Trans + Results pattern")
    
    logger.info("\n✅ READY TO UPDATE GOLDEN FORMAT GENERATORS:")
    logger.info("1. Update TV golden format to handle dynamic signal count")
    logger.info("2. Update TBS golden format to handle dynamic strategy count") 
    logger.info("3. Implement OI golden format with dynamic OI strategies")
    logger.info("4. Implement ORB golden format with dynamic timeframes")
    logger.info("5. Use unified structure for all future strategies (POS, ML)")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
