#!/usr/bin/env python3
"""
TV Unified Script Validation Test

This script validates that we have the correct TV script for UI integration:
- BT_TV_GPU_aggregated_v4.py should be the primary script
- Enterprise server should use the aggregated version
- Golden format should be working
"""

import os
import sys
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s [%(levelname)s] %(name)s: %(message)s"
)
logger = logging.getLogger("TV_Unified_Script_Test")

def test_enterprise_server_mapping():
    """Test that enterprise server uses the correct TV script"""
    logger.info("🧪 Testing Enterprise Server TV Script Mapping")
    
    try:
        enterprise_file = "enterprise_server_v2.py"
        if os.path.exists(enterprise_file):
            with open(enterprise_file, 'r') as f:
                content = f.read()
                
            if "'TV': 'BT_TV_GPU_aggregated_v4.py'" in content:
                logger.info("✅ Enterprise server correctly maps TV to BT_TV_GPU_aggregated_v4.py")
                return True
            else:
                logger.error("❌ Enterprise server has incorrect TV mapping")
                logger.error("Expected: 'TV': 'BT_TV_GPU_aggregated_v4.py'")
                
                # Show what it actually has
                lines = content.split('\n')
                for i, line in enumerate(lines):
                    if "'TV':" in line:
                        logger.error(f"Found at line {i+1}: {line.strip()}")
                return False
        else:
            logger.error("❌ Enterprise server file not found")
            return False
            
    except Exception as e:
        logger.error(f"❌ Enterprise server mapping test failed: {e}")
        return False

def test_tv_script_exists():
    """Test that the primary TV script exists and has golden format"""
    logger.info("🧪 Testing TV Script Existence and Golden Format")
    
    try:
        script_file = "BT_TV_GPU_aggregated_v4.py"
        if os.path.exists(script_file):
            with open(script_file, 'r') as f:
                content = f.read()
                
            # Check for golden format integration
            if "_generate_tv_aggregated_golden_format_output" in content:
                logger.info("✅ BT_TV_GPU_aggregated_v4.py exists and has golden format integration")
                return True
            else:
                logger.error("❌ BT_TV_GPU_aggregated_v4.py missing golden format integration")
                return False
        else:
            logger.error("❌ BT_TV_GPU_aggregated_v4.py not found")
            return False
            
    except Exception as e:
        logger.error(f"❌ TV script test failed: {e}")
        return False

def test_redundant_scripts():
    """Check for redundant TV scripts that might cause confusion"""
    logger.info("🧪 Testing for Redundant TV Scripts")
    
    tv_scripts = [
        "BT_TV_GPU.py",
        "BT_TV_GPU_aggregated.py", 
        "BT_TV_GPU_aggregated_v2.py",
        "BT_TV_GPU_aggregated_v3.py",
        "BT_TV_GPU_enhanced.py"
    ]
    
    found_scripts = []
    for script in tv_scripts:
        if os.path.exists(script):
            found_scripts.append(script)
    
    if found_scripts:
        logger.warning(f"⚠️ Found {len(found_scripts)} additional TV scripts:")
        for script in found_scripts:
            logger.warning(f"  - {script}")
        logger.warning("Consider consolidating or documenting their specific purposes")
    else:
        logger.info("✅ No redundant TV scripts found")
    
    return True

def test_ui_integration_readiness():
    """Test overall UI integration readiness"""
    logger.info("🧪 Testing UI Integration Readiness")
    
    checks = [
        ("Enterprise Server Mapping", test_enterprise_server_mapping),
        ("Primary TV Script", test_tv_script_exists),
        ("Redundant Scripts Check", test_redundant_scripts)
    ]
    
    passed = 0
    total = len(checks)
    
    for check_name, check_func in checks:
        try:
            result = check_func()
            if result:
                passed += 1
                logger.info(f"✅ {check_name}: PASSED")
            else:
                logger.error(f"❌ {check_name}: FAILED")
        except Exception as e:
            logger.error(f"💥 {check_name}: CRASHED - {e}")
    
    return passed, total

def main():
    """Run TV unified script validation"""
    logger.info("🚀 Starting TV Unified Script Validation")
    logger.info("="*60)
    
    passed, total = test_ui_integration_readiness()
    
    logger.info("\n" + "="*60)
    logger.info("📊 TV UNIFIED SCRIPT VALIDATION RESULTS")
    logger.info("="*60)
    
    logger.info(f"🎯 Overall Result: {passed}/{total} checks passed")
    
    if passed == total:
        logger.info("🎉 TV UNIFIED SCRIPT VALIDATION PASSED!")
        logger.info("✅ Ready for UI integration with BT_TV_GPU_aggregated_v4.py")
        logger.info("✅ Enterprise server correctly configured")
        logger.info("✅ Golden format integration working")
        return 0
    else:
        logger.error("❌ Some validation checks failed")
        logger.error("Please fix issues before proceeding to next phases")
        return 1

if __name__ == "__main__":
    sys.exit(main())
