#!/usr/bin/env python3
"""
TBS Strategy Query Optimizer for HeavyDB
Provides optimized SQL queries for TBS strategy execution
"""

import logging
from typing import Dict, List, Any, Optional, Union
from datetime import date

logger = logging.getLogger(__name__)

class TBSQueryOptimizer:
    """Optimized query builder for TBS strategies with GPU acceleration"""
    
    def __init__(self, table_name: str = "nifty_option_chain"):
        self.table_name = table_name
        
    def build_optimized_strike_query(self, 
                                   trade_date: str,
                                   expiry_bucket: str,
                                   entry_time: str,
                                   exit_time: str,
                                   symbol: str = "NIFTY") -> str:
        """
        Build optimized query for strike selection with GPU hints
        
        Args:
            trade_date: Trading date in YYYY-MM-DD format
            expiry_bucket: Expiry bucket (CW, NW, CM, NM)
            entry_time: Entry time in HH:MM:SS format
            exit_time: Exit time in HH:MM:SS format
            symbol: Symbol name (default: NIFTY)
            
        Returns:
            Optimized SQL query string
        """
        query = f"""
        SELECT /*+ gpu_enable(true), watchdog_max_size=0 */
            trade_date,
            trade_time,
            expiry_bucket,
            strike,
            atm_strike,
            dte,
            ce_symbol,
            pe_symbol,
            ce_close,
            pe_close,
            spot_price
        FROM {self.table_name}
        WHERE trade_date = DATE '{trade_date}'
            AND symbol = '{symbol}'
            AND expiry_bucket = '{expiry_bucket}'
            AND strike = atm_strike
            AND (trade_time >= TIME '{entry_time}' OR trade_time <= TIME '{exit_time}')
            AND ce_symbol IS NOT NULL
            AND pe_symbol IS NOT NULL
        ORDER BY trade_time
        """
        return query.strip()
    
    def build_optimized_price_query(self,
                                  trade_date: str,
                                  symbol_name: str,
                                  option_type: str,
                                  strike: float,
                                  expiry_bucket: str) -> str:
        """
        Build optimized query for price data retrieval
        
        Args:
            trade_date: Trading date in YYYY-MM-DD format
            symbol_name: Option symbol name
            option_type: Option type (CE or PE)
            strike: Strike price
            expiry_bucket: Expiry bucket (CW, NW, CM, NM)
            
        Returns:
            Optimized SQL query string
        """
        price_col = f"{option_type.lower()}_close"
        symbol_col = f"{option_type.lower()}_symbol"
        
        query = f"""
        SELECT /*+ gpu_enable(true) */
            trade_date,
            trade_time,
            strike,
            {price_col} as option_close,
            {symbol_col} as option_symbol,
            spot_price
        FROM {self.table_name}
        WHERE trade_date = DATE '{trade_date}'
            AND strike = {strike}
            AND expiry_bucket = '{expiry_bucket}'
            AND {symbol_col} = '{symbol_name}'
            AND {price_col} IS NOT NULL
        ORDER BY trade_time
        """
        return query.strip()
    
    def build_batch_strike_query(self,
                               trade_dates: List[str],
                               expiry_bucket: str,
                               symbol: str = "NIFTY") -> str:
        """
        Build optimized batch query for multiple trading dates
        
        Args:
            trade_dates: List of trading dates in YYYY-MM-DD format
            expiry_bucket: Expiry bucket (CW, NW, CM, NM)
            symbol: Symbol name (default: NIFTY)
            
        Returns:
            Optimized batch SQL query string
        """
        date_list = "', '".join(trade_dates)
        
        query = f"""
        WITH atm_strikes AS (
            SELECT /*+ gpu_enable(true) */
                trade_date,
                trade_time,
                expiry_bucket,
                strike,
                atm_strike,
                dte,
                ce_symbol,
                pe_symbol,
                ce_close,
                pe_close,
                spot_price,
                ROW_NUMBER() OVER (
                    PARTITION BY trade_date, trade_time 
                    ORDER BY ABS(strike - spot_price)
                ) as strike_rank
            FROM {self.table_name}
            WHERE trade_date IN ('{date_list}')
                AND symbol = '{symbol}'
                AND expiry_bucket = '{expiry_bucket}'
                AND ce_symbol IS NOT NULL
                AND pe_symbol IS NOT NULL
        )
        SELECT 
            trade_date,
            trade_time,
            expiry_bucket,
            strike,
            atm_strike,
            dte,
            ce_symbol,
            pe_symbol,
            ce_close,
            pe_close,
            spot_price
        FROM atm_strikes
        WHERE strike_rank = 1
        ORDER BY trade_date, trade_time
        """
        return query.strip()
    
    def build_performance_optimized_query(self,
                                        trade_date: str,
                                        expiry_bucket: str,
                                        time_range: tuple,
                                        symbol: str = "NIFTY") -> str:
        """
        Build performance-optimized query with minimal data transfer
        
        Args:
            trade_date: Trading date in YYYY-MM-DD format
            expiry_bucket: Expiry bucket (CW, NW, CM, NM)
            time_range: Tuple of (start_time, end_time) in HH:MM:SS format
            symbol: Symbol name (default: NIFTY)
            
        Returns:
            Performance-optimized SQL query string
        """
        start_time, end_time = time_range
        
        query = f"""
        SELECT /*+ gpu_enable(true), fragment_size=32000000 */
            trade_date,
            trade_time,
            strike,
            ce_close,
            pe_close,
            spot_price
        FROM {self.table_name}
        WHERE trade_date = DATE '{trade_date}'
            AND symbol = '{symbol}'
            AND expiry_bucket = '{expiry_bucket}'
            AND strike = atm_strike
            AND trade_time BETWEEN TIME '{start_time}' AND TIME '{end_time}'
            AND ce_symbol IS NOT NULL
            AND pe_symbol IS NOT NULL
        ORDER BY trade_time
        LIMIT 1000
        """
        return query.strip()
    
    def get_optimization_hints(self) -> Dict[str, str]:
        """
        Get HeavyDB optimization hints for TBS queries
        
        Returns:
            Dictionary of optimization hints
        """
        return {
            "gpu_enable": "/*+ gpu_enable(true) */",
            "watchdog_disable": "/*+ watchdog_max_size=0 */",
            "fragment_size": "/*+ fragment_size=32000000 */",
            "cpu_fallback": "/*+ cpu_mode=false */",
            "parallel_execution": "/*+ parallel_execution=true */",
            "memory_limit": "/*+ memory_limit=8GB */",
            "disable_loop_join": "/*+ disable_loop_join */",
            "enable_columnar_output": "/*+ enable_columnar_output=true */"
        }
    
    def optimize_existing_query(self, query: str) -> str:
        """
        Add optimization hints to existing query
        
        Args:
            query: Original SQL query
            
        Returns:
            Optimized SQL query with hints
        """
        # Remove existing hints if any
        query = query.replace("/*+", "").replace("*/", "")
        
        # Add comprehensive optimization hints
        hints = "/*+ gpu_enable(true), watchdog_max_size=0, fragment_size=32000000 */"
        
        # Insert hints after SELECT
        if "SELECT" in query.upper():
            query = query.replace("SELECT", f"SELECT {hints}", 1)
        
        return query
