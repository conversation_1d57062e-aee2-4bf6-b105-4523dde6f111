#!/usr/bin/env python3
"""
HeavyDB Data Access and Query Functions
"""

import os
import logging
import pandas as pd
import traceback
from typing import Dict, List, Any, Optional, Union, Tuple
from datetime import date, datetime, timedelta

from . import config
from . import gpu_helpers as gh
from .heavydb_connection import execute_query, get_connection # Import from new module
from .heavydb_utils import _parse_yyymmdd # Import from new module
from ..models.leg import LegModel # Import LegModel
from ..models.common import OptionType # Import OptionType for direct comparison

# Set up logger first
logger = logging.getLogger(__name__)

# Query-builder import (optional dependency – guard if missing)
try:
    from ..query_builder.strategy_sql import build_strategy_sql
    from ..models.strategy import StrategyModel # For type hinting in get_leg_snapshot
    HAS_QUERY_BUILDER = True
except ImportError:
    HAS_QUERY_BUILDER = False
    build_strategy_sql = None # Placeholder
    StrategyModel = None # Placeholder
    logger.warning("Query builder (strategy_sql) not available. Functions like get_leg_snapshot might be limited.")

# entry/exit SQL builders (optional)
try:
    from ..query_builder.entry_exit_sql import build_entry_sql, build_exit_sql, _ensure_hhmmss 
    HAS_ENTRY_EXIT_BUILDER = True
except ImportError:
    HAS_ENTRY_EXIT_BUILDER = False
    build_entry_sql = None # Placeholder
    build_exit_sql = None # Placeholder
    _ensure_hhmmss = None # Placeholder
    logger.warning("Entry/exit SQL builders (entry_exit_sql) not available. Functions like get_leg_entry_exit_rows might be limited.")

_cached_trade_dates: List[date] = [] # Define at module level

# Remove the conditional stub for LegModel as we are importing it directly now
# if StrategyModel is None: 
#     class LegModel:
#         pass 

def get_price_data(
    symbol: str,
    start_date: Union[int, str, date],
    end_date: Union[int, str, date],
    timeframe: int = 1,  # minutes
    connection=None,
    leg_info: Optional[LegModel] = None # New parameter for leg details
) -> pd.DataFrame:
    """
    Get price data for a symbol within a date range.
    If leg_info is provided for an option, it attempts to fetch option-specific OHLC.
    """
    symbol_upper = symbol.upper()
    logger.debug(f"GET_PRICE_DATA: Called for Symbol: {symbol_upper}, LegInfo provided: {leg_info is not None}")
    
    if leg_info and hasattr(leg_info, 'option_type'):
        logger.debug(f"GET_PRICE_DATA: LegInfo OptionType raw: {leg_info.option_type}, Type: {type(leg_info.option_type)}, ID: {id(leg_info.option_type)}")
        logger.debug(f"GET_PRICE_DATA: Imported OptionType.CALL ID: {id(OptionType.CALL)}, OptionType.PUT ID: {id(OptionType.PUT)}")
        logger.debug(f"GET_PRICE_DATA: leg_info.option_type in [OptionType.CALL, OptionType.PUT]? {leg_info.option_type in [OptionType.CALL, OptionType.PUT]}")
        # No need to log hasattr(leg_info, 'strike') as it's not the deciding factor for is_option_leg anymore

    start_date_obj = _parse_yyymmdd(start_date)
    end_date_obj = _parse_yyymmdd(end_date)
    start_date_sql_str = start_date_obj.strftime('%Y-%m-%d') 
    end_date_sql_str = end_date_obj.strftime('%Y-%m-%d')

    query = "" 
    final_expected_cols = ['date', 'time', 'open', 'high', 'low', 'close', 'volume', 'strike'] # Added strike to expected cols

    table_name = config.get_table_name('OPTION_CHAIN')
    is_option_leg = False
    option_type_str = ""

    if (leg_info and hasattr(leg_info, 'option_type') and 
        leg_info.option_type in [OptionType.CALL, OptionType.PUT]): # Only check option_type for this flag
        is_option_leg = True
        option_type_str = "ce" if leg_info.option_type == OptionType.CALL else "pe"
        option_specific_extras = [f'{option_type_str}_iv', f'{option_type_str}_delta']
        for osc_extra in option_specific_extras:
            if osc_extra not in final_expected_cols:
                final_expected_cols.append(osc_extra)
    
    logger.debug(f"GET_PRICE_DATA: Determined: is_option_leg={is_option_leg}, option_type_str='{option_type_str}'")

    # Import multi-index configuration
    try:
        from .multi_index_config import is_index_supported
    except ImportError:
        # Fallback to original list if import fails
        def is_index_supported(idx):
            return idx in ['NIFTY', 'BANKNIFTY', 'FINNIFTY', 'MIDCAPNIFTY', 'SENSEX', 'BANKEX']
    
    if is_index_supported(symbol_upper):
        if is_option_leg: # If it's an option leg
            logger.info(f"GET_PRICE_DATA: Fetching OPTION data for {symbol_upper} Type: {option_type_str.upper()}")
            select_clause_parts = [
                'trade_date AS trade_date_alias',
                'trade_time AS trade_time_alias', 
                'strike AS strike_alias', # Select the strike price itself
                f'{option_type_str}_open AS option_open', 
                f'{option_type_str}_high AS option_high', 
                f'{option_type_str}_low AS option_low', 
                f'{option_type_str}_close AS option_close', 
                f'COALESCE({option_type_str}_volume, 0) AS option_volume',
                f'{option_type_str}_iv AS {option_type_str}_iv', 
                f'{option_type_str}_delta AS {option_type_str}_delta'
            ]
            select_statement = ", ".join(select_clause_parts)
            # Query fetches all strikes for the given option type and date range
            query = f"""SELECT {select_statement} FROM {table_name}
                       WHERE index_name = '{symbol_upper}'
                         AND trade_date >= '{start_date_sql_str}' AND trade_date <= '{end_date_sql_str}'
                       ORDER BY trade_date_alias, trade_time_alias, strike_alias"""
        else: # Fallback to FUTURES data for the index
            logger.info(f"GET_PRICE_DATA: Fetching FUTURES data for {symbol_upper}")
            select_clause_parts = [
                'trade_date AS trade_date_alias',
                'trade_time AS trade_time_alias',
                'future_open AS future_open_alias',
                'future_high AS future_high_alias',
                'future_low AS future_low_alias',
                'future_close AS future_close_alias',
                'COALESCE(future_volume, 0) AS future_volume_alias'
            ]
            select_statement = ", ".join(select_clause_parts)
            query = f"""SELECT {select_statement} FROM {table_name}
                       WHERE index_name = '{symbol_upper}'
                         AND trade_date >= '{start_date_sql_str}' AND trade_date <= '{end_date_sql_str}'
                       ORDER BY trade_date_alias, trade_time_alias"""
    else:
        logger.warning(f"Attempting to use 'ohlcv_data' for non-index symbol '{symbol_upper}'.")
        table_name = config.get_table_name('OHLCV_DATA')
        query = f"""SELECT "date", "time", "open", "high", "low", "close", "volume"
                   FROM {table_name} WHERE symbol = '{symbol_upper}'
                   AND "date" >= '{start_date_sql_str}' AND "date" <= '{end_date_sql_str}'
                   ORDER BY "date", "time" """
    
    df = pd.DataFrame(columns=final_expected_cols)

    conn_to_use = connection or get_connection()
    try:
        if not query:
            logger.warning(f"Query could not be constructed for symbol {symbol_upper}")
            return df # Return empty df
        elif conn_to_use is None:
            logger.error(f"No database connection to execute query for {symbol_upper}.")
        else:
            logger.debug(f"GET_PRICE_DATA: Executing Query: {query}")
            fetched_df = execute_query(query, connection=conn_to_use, close_conn=(not connection))
            if not fetched_df.empty:
                logger.debug(f"GET_PRICE_DATA: Fetched {len(fetched_df)} rows. Columns: {fetched_df.columns.tolist()}")
                rename_map = {}
                if 'trade_date_alias' in fetched_df.columns: rename_map['trade_date_alias'] = 'date'
                if 'trade_time_alias' in fetched_df.columns: rename_map['trade_time_alias'] = 'time'
                if 'strike_alias' in fetched_df.columns: rename_map['strike_alias'] = 'strike' # Rename strike_alias to strike
                
                if is_option_leg:
                    if 'option_open' in fetched_df.columns: rename_map['option_open'] = 'open'
                    if 'option_high' in fetched_df.columns: rename_map['option_high'] = 'high'
                    if 'option_low' in fetched_df.columns: rename_map['option_low'] = 'low'
                    if 'option_close' in fetched_df.columns: rename_map['option_close'] = 'close'
                    if 'option_volume' in fetched_df.columns: rename_map['option_volume'] = 'volume'
                elif symbol_upper in ['NIFTY', 'BANKNIFTY', 'FINNIFTY']: # Futures for indices
                    if 'future_open_alias' in fetched_df.columns: rename_map['future_open_alias'] = 'open'
                    if 'future_high_alias' in fetched_df.columns: rename_map['future_high_alias'] = 'high'
                    if 'future_low_alias' in fetched_df.columns: rename_map[f'future_low_alias'] = 'low'
                    if 'future_close_alias' in fetched_df.columns: rename_map[f'future_close_alias'] = 'close'
                    if 'future_volume_alias' in fetched_df.columns: rename_map[f'future_volume_alias'] = 'volume'
                # For non-index symbols from ohlcv_data, names are already standard, no rename needed here for ohlcv.
                
                if rename_map:
                    fetched_df.rename(columns=rename_map, inplace=True)
                    logger.debug(f"GET_PRICE_DATA: Columns after rename: {fetched_df.columns.tolist()}")

                if 'date' in fetched_df.columns and 'time' in fetched_df.columns:
                    if 'datetime' not in fetched_df.columns:
                        try:
                            date_str_series = fetched_df['date'].astype(str)
                            time_col = fetched_df['time']
                            # Ensure time_col is consistently string formatted as HH:MM:SS
                            if pd.api.types.is_integer_dtype(time_col):
                                time_str_series = time_col.astype(str).str.zfill(6).apply(lambda x: f"{x[:2]}:{x[2:4]}:{x[4:]}")
                            elif hasattr(time_col.iloc[0], 'strftime'): # Check if it's time object
                                time_str_series = time_col.apply(lambda x: x.strftime('%H:%M:%S') if pd.notna(x) else None)
                            else: # Assume it might already be a string or needs direct conversion
                                time_str_series = time_col.astype(str)
                                
                            fetched_df['datetime'] = pd.to_datetime(date_str_series + ' ' + time_str_series, errors='coerce')
                        except Exception as e_conv_dt:
                            logger.error(f"GET_PRICE_DATA: Error converting date/time to datetime column: {e_conv_dt}")
                    fetched_df.dropna(subset=['datetime'], inplace=True)
                else:
                    logger.warning("GET_PRICE_DATA: 'date' or 'time' column missing after rename for datetime creation.")

                df = fetched_df.drop_duplicates(subset=['datetime' if 'datetime' in fetched_df.columns else ('date', 'time')], keep='first').copy()
                logger.debug(f"GET_PRICE_DATA: DataFrame after drop_duplicates, before resampling: {len(df)} rows.")
            else:
                logger.debug(f"GET_PRICE_DATA: Fetched_df is empty.")
    except Exception as e: 
        logger.warning(f"Could not execute query or process data for {symbol_upper} (table might be missing or query failed): {e}")
    finally:
        if not connection and conn_to_use: # Close if we opened it
            try:
                conn_to_use.close()
            except Exception as e_close:
                logger.error(f"Error closing connection in get_price_data: {e_close}")

    missing_cols_current_df = [col for col in final_expected_cols if col not in df.columns]
    for col in missing_cols_current_df:
        df[col] = pd.Series(dtype='object')
    
    if timeframe > 1 and not df.empty and 'datetime' in df.columns:
        pdf = gh.to_pandas(df) 
        try:
            pdf.set_index('datetime', inplace=True)
            if pdf.index.tz is not None:
                 pdf.index = pdf.index.tz_localize(None) 

            rule = f"{timeframe}T"
            resampled = pdf.resample(rule).agg({
                'open': 'first',
                'high': 'max',
                'low': 'min',
                'close': 'last',
                'volume': 'sum'
            }).dropna()
            resampled.reset_index(inplace=True)
            resampled['date'] = resampled['datetime'].dt.strftime('%Y-%m-%d')
            resampled['time'] = resampled['datetime'].dt.strftime('%H%M%S').astype(int)
            # Keep option-specific columns if they exist, by joining back or ensuring agg includes them
            if is_option_leg:
                option_extras_to_carry = [f'{option_type_str}_iv', f'{option_type_str}_delta']
                for oe_col in option_extras_to_carry:
                    if oe_col in pdf.columns: # Check if original pdf (before resample) had them
                        # This simple agg might not be right for IV/Delta, might need first/last or mean
                        resampled_extra = pdf[oe_col].resample(rule).first() 
                        resampled = resampled.merge(resampled_extra.rename(oe_col), on='datetime', how='left')
            
            resampled.drop('datetime', axis=1, inplace=True)
            return gh.ensure_cudf(resampled) if config.GPU_ENABLED else resampled
        except Exception as e_resample:
            logger.error(f"GET_PRICE_DATA: Error during resampling: {e_resample}")
            return df # Return un-resampled df on error
    
    return df

def get_option_chain(
    symbol: str,
    trade_date_input: Union[int, str, date],
    time_input: Optional[Union[int, str]] = None,
    connection=None
) -> pd.DataFrame:
    """Get option chain data for a symbol at a specific date and time with GPU optimization."""
    symbol = symbol.upper()
    trade_date_obj = _parse_yyymmdd(trade_date_input)
    date_sql_str = trade_date_obj.strftime('%Y-%m-%d')

    table_name = config.get_table_name('OPTION_CHAIN')

    # Optimized query with GPU hints and selective columns
    query = f"""
    SELECT /*+ gpu_enable(true), watchdog_max_size=0 */
        trade_date,
        trade_time,
        expiry_date,
        expiry_bucket,
        strike,
        atm_strike,
        dte,
        ce_symbol,
        pe_symbol,
        ce_open,
        ce_high,
        ce_low,
        ce_close,
        pe_open,
        pe_high,
        pe_low,
        pe_close,
        spot_price,
        call_strike_type,
        put_strike_type
    FROM {table_name}
    WHERE symbol = '{symbol}'
        AND trade_date = DATE '{date_sql_str}'
    """

    if time_input is not None:
        time_sql_str = str(time_input).zfill(6) # Assuming HHMMSS int or string
        query += f" AND trade_time = TIME '{time_sql_str[:2]}:{time_sql_str[2:4]}:{time_sql_str[4:6]}'"

    query += " ORDER BY trade_time, strike"
    return execute_query(query, connection=connection) # execute_query handles its own connection closing

def get_distinct_trade_dates(start_date_input: Union[int, str, date] | None = None,
                              end_date_input: Union[int, str, date] | None = None,
                              connection=None) -> List[date]:
    """Return all distinct trade_date values from option-chain view ordered ascending."""
    global _cached_trade_dates
    # This simple cache assumes all calls want the full range first, then filter.
    # For more efficiency with varying date ranges, a more complex cache might be needed.
    if not _cached_trade_dates:
        conn_to_use = connection or get_connection()
        if conn_to_use is None:
            logger.error("Cannot fetch trade dates – no HeavyDB connection")
            return []
        try:
            table_name = config.get_table_name('OPTION_CHAIN')
            sql = f"SELECT DISTINCT trade_date FROM {table_name}"
            df = execute_query(sql, connection=conn_to_use, return_gpu_df=False, close_conn=(not connection))
            if df.empty:
                logger.warning(f"No trade_date rows found in {table_name}")
                _cached_trade_dates = [] # Ensure cache is empty list not None
            else:
                # Convert to Python date objects and sort
                _cached_trade_dates = sorted([pd.to_datetime(v).date() for v in df["trade_date"].tolist()])
            logger.info("Cached %d distinct trade dates", len(_cached_trade_dates))
        except Exception as e_dist_dates:
            logger.error(f"Error fetching distinct trade dates: {e_dist_dates}")
            _cached_trade_dates = [] # Ensure cache is empty on error
        finally:
            if not connection and conn_to_use: # Close if we opened it
                try: conn_to_use.close() 
                except: pass

    dates_to_filter = list(_cached_trade_dates) # Work on a copy
    if start_date_input is not None:
        start_dt_obj = _parse_yyymmdd(start_date_input)
        dates_to_filter = [d for d in dates_to_filter if d >= start_dt_obj]
    if end_date_input is not None:
        end_dt_obj = _parse_yyymmdd(end_date_input)
        dates_to_filter = [d for d in dates_to_filter if d <= end_dt_obj]
    return dates_to_filter

def get_holidays(start_date_input: Union[date, str], end_date_input: Union[date, str], connection=None) -> List[date]:
    """Fetch holidays from the nse_holidays table between two dates."""
    start_date_obj = _parse_yyymmdd(start_date_input)
    end_date_obj = _parse_yyymmdd(end_date_input)
    start_date_sql_str = start_date_obj.strftime('%Y-%m-%d')
    end_date_sql_str = end_date_obj.strftime('%Y-%m-%d')

    table_name = config.get_table_name('NSE_HOLIDAYS')
    query = f"""
    SELECT holiday_date 
    FROM {table_name} 
    WHERE holiday_date >= '{start_date_sql_str}' AND holiday_date <= '{end_date_sql_str}'
    ORDER BY holiday_date
    """
    df_holidays = execute_query(query, connection=connection, return_gpu_df=False)
    if df_holidays.empty:
        return []
    return pd.to_datetime(df_holidays['holiday_date']).dt.date.tolist()

def get_next_trading_day(current_date: date, holidays: List[date]) -> date:
    next_day = current_date + timedelta(days=1)
    while next_day.weekday() >= 5 or next_day in holidays:
        next_day += timedelta(days=1)
    return next_day

def get_previous_trading_day(current_date: date, holidays: List[date]) -> date:
    prev_day = current_date - timedelta(days=1)
    while prev_day.weekday() >= 5 or prev_day in holidays:
        prev_day -= timedelta(days=1)
    return prev_day

def get_future_expiry_dates(
    underlying_symbol: str, 
    trade_date_input: Union[date, str], 
    holidays: List[date], 
    connection=None
) -> Optional[Tuple[date, date, date]]:
    """Fetches current and next future expiry dates."""
    trade_date_obj = _parse_yyymmdd(trade_date_input)
    trade_date_sql_str = trade_date_obj.strftime('%Y-%m-%d')
    
    table_name = config.get_table_name('OPTION_CHAIN') 
    query = f"""
    SELECT DISTINCT expiry_date
    FROM {table_name}
    WHERE index_name = '{underlying_symbol.upper()}'
          AND expiry_date >= '{trade_date_sql_str}'
    ORDER BY expiry_date ASC
    LIMIT 2 
    """

    try:
        expiries_df = execute_query(query, connection=connection, return_gpu_df=False)
        if expiries_df.empty or len(expiries_df) < 1:
            logger.warning(f"No future expiry dates found for {underlying_symbol} on or after {trade_date_sql_str}.")
            return None

        expiry_dates = sorted(pd.to_datetime(expiries_df['expiry_date']).dt.date.tolist())
        current_expiry_date = expiry_dates[0]
        next_contract_expiry_date = expiry_dates[1] if len(expiry_dates) > 1 else current_expiry_date
        next_trading_day_after_curr_expiry = get_next_trading_day(current_expiry_date, holidays)

        logger.info(f"[DAL] Future Expiry Details for {underlying_symbol} (Trade Date: {trade_date_obj}): CurrExp={current_expiry_date}, NextTradDay={next_trading_day_after_curr_expiry}, NextContExp={next_contract_expiry_date}")
        return current_expiry_date, next_trading_day_after_curr_expiry, next_contract_expiry_date
    except Exception as e:
        logger.error(f"Failed to fetch future expiry dates for {underlying_symbol} on {trade_date_sql_str}: {e}")
        logger.error(traceback.format_exc())
        return None

def get_leg_snapshot(strategy: "StrategyModel", trade_date_input: Union[date,str], connection=None):
    if not HAS_QUERY_BUILDER or not build_strategy_sql:
        logger.error("query_builder package or build_strategy_sql unavailable – cannot build SQL for leg snapshot")
        return pd.DataFrame()
    trade_date_obj = _parse_yyymmdd(trade_date_input)
    sql = build_strategy_sql(strategy, trade_date_obj)
    logger.debug("Executing strategy SQL for leg snapshot:\n%s", sql)
    return execute_query(sql, connection=connection, return_gpu_df=False) 

def get_leg_entry_exit_rows(leg: Any, trade_date_input: Union[date,str], connection=None):
    if not HAS_ENTRY_EXIT_BUILDER or not build_entry_sql or not build_exit_sql or not _ensure_hhmmss:
        logger.error("entry_exit_sql builder unavailable – cannot fetch leg entry/exit rows")
        return pd.DataFrame(), pd.DataFrame()

    trade_date_obj = _parse_yyymmdd(trade_date_input)
    trade_date_iso = trade_date_obj.isoformat()
    
    entry_time_int = _ensure_hhmmss(leg.entry_time)
    exit_time_int = _ensure_hhmmss(leg.exit_time)

    entry_sql = build_entry_sql(leg, trade_date_iso, entry_time_int, alias="le")
    exit_sql = build_exit_sql(leg, trade_date_iso, exit_time_int, alias="lx")

    entry_df = execute_query(entry_sql, connection=connection, return_gpu_df=False, optimise=False)
    exit_df = execute_query(exit_sql, connection=connection, return_gpu_df=False, optimise=False)
    return entry_df, exit_df 

def _build_union_sql(legs: List[Any], trade_date_iso: str, is_entry: bool, alias_prefix: str = "ux") -> str:
    if not HAS_ENTRY_EXIT_BUILDER or not build_entry_sql or not build_exit_sql or not _ensure_hhmmss:
        raise RuntimeError("entry_exit_sql builder unavailable – cannot build union SQL for batching")

    sql_parts: List[str] = []
    seen_leg_ids = set()
    for idx, leg in enumerate(legs):
        if leg.leg_id in seen_leg_ids:
            continue 
        seen_leg_ids.add(leg.leg_id)
        time_int = _ensure_hhmmss(leg.entry_time if is_entry else leg.exit_time)
        inner_sql_builder = build_entry_sql if is_entry else build_exit_sql
        inner_sql = inner_sql_builder(leg, trade_date_iso, time_int, alias=f"{alias_prefix}{idx}")
        inner_sql = inner_sql.rstrip().rstrip(";")
        wrapped = f"SELECT *, '{leg.leg_id}' AS leg_id FROM ({inner_sql}) sub{idx}"
        sql_parts.append(wrapped)
    if not sql_parts:
        logger.warning("_build_union_sql: No SQL parts generated for legs, returning empty string.")
        return "" # Return empty string if no legs or all duplicates
    logger.info(f"_build_union_sql: included leg_ids: {list(seen_leg_ids)}")
    return " UNION ALL \n".join(sql_parts) 

