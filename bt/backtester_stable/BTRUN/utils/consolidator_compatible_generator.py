#!/usr/bin/env python3
"""
Strategy Consolidator Compatible Golden Format Generator
Generates Excel files that match exactly what the Strategy Consolidator expects
"""

import pandas as pd
import logging
from datetime import datetime
from typing import Dict, List, Any, Optional
from pathlib import Path

logger = logging.getLogger(__name__)

class ConsolidatorCompatibleGenerator:
    """
    Generates Excel files compatible with Strategy Consolidator requirements
    
    Supports:
    - FORMAT_2_PYTHON_XLSX: Single strategy with PORTFOLIO Trans sheet
    - FORMAT_6_PYTHON_MULTI_XLSX: Multi-strategy with Strategy column
    """
    
    def __init__(self):
        """Initialize the consolidator compatible generator"""
        logger.info("ConsolidatorCompatibleGenerator initialized")
    
    def generate_single_strategy_format(self,
                                      transactions_df: pd.DataFrame,
                                      strategy_name: str,
                                      output_path: str) -> bool:
        """
        Generate FORMAT_2_PYTHON_XLSX compatible file
        
        Required structure:
        - Sheet: 'PORTFOLIO Trans'
        - Columns: ['Exit Date', 'PNL', 'Enter On', 'Day']
        
        Args:
            transactions_df: Transaction data
            strategy_name: Name of the strategy
            output_path: Output file path
            
        Returns:
            bool: Success status
        """
        try:
            logger.info(f"🎯 Generating single strategy format for: {strategy_name}")
            
            # Convert to consolidator format
            consolidator_df = self._convert_to_consolidator_format(transactions_df)
            
            # Create Excel file with required sheet name
            with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
                consolidator_df.to_excel(writer, sheet_name='PORTFOLIO Trans', index=False)
            
            logger.info(f"✅ Single strategy format generated: {output_path}")
            return True
            
        except Exception as e:
            logger.error(f"Error generating single strategy format: {e}")
            return False
    
    def generate_multi_strategy_format(self,
                                     strategy_transactions: Dict[str, pd.DataFrame],
                                     output_path: str) -> bool:
        """
        Generate FORMAT_6_PYTHON_MULTI_XLSX compatible file
        
        Required structure:
        - Sheet: 'PORTFOLIO Trans'
        - Columns: ['Exit Date', 'PNL', 'Enter On', 'Day', 'Strategy']
        
        Args:
            strategy_transactions: Dict of {strategy_name: transactions_df}
            output_path: Output file path
            
        Returns:
            bool: Success status
        """
        try:
            logger.info(f"🎯 Generating multi-strategy format for {len(strategy_transactions)} strategies")
            
            # Combine all strategies
            combined_df_list = []
            
            for strategy_name, transactions_df in strategy_transactions.items():
                # Convert to consolidator format
                strategy_df = self._convert_to_consolidator_format(transactions_df)
                
                # Add strategy column
                strategy_df['Strategy'] = strategy_name
                
                combined_df_list.append(strategy_df)
            
            # Combine all strategies
            if combined_df_list:
                combined_df = pd.concat(combined_df_list, ignore_index=True)
            else:
                combined_df = self._create_empty_consolidator_format()
                combined_df['Strategy'] = 'No_Strategies'
            
            # Create Excel file with required sheet name
            with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
                combined_df.to_excel(writer, sheet_name='PORTFOLIO Trans', index=False)
            
            logger.info(f"✅ Multi-strategy format generated: {output_path}")
            return True
            
        except Exception as e:
            logger.error(f"Error generating multi-strategy format: {e}")
            return False
    
    def _convert_to_consolidator_format(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Convert transaction DataFrame to consolidator-compatible format
        
        Required columns: ['Exit Date', 'PNL', 'Enter On', 'Day']
        
        Args:
            df: Input transaction DataFrame
            
        Returns:
            DataFrame with consolidator-compatible columns
        """
        try:
            if df.empty:
                return self._create_empty_consolidator_format()
            
            result_df = df.copy()
            
            # Column mapping from our format to consolidator format
            column_mapping = {
                # Date columns
                'exit_date': 'Exit Date',
                'Exit Date': 'Exit Date',
                'trade_date': 'Exit Date',
                'date': 'Exit Date',
                
                # PNL columns
                'pnl': 'PNL',
                'PNL': 'PNL',
                'bookedPnL': 'PNL',
                'net_pnl': 'PNL',
                'netPnlAfterExpenses': 'PNL',
                
                # Time columns
                'entry_time': 'Enter On',
                'Enter On': 'Enter On',
                'trade_time': 'Enter On',
                'time': 'Enter On',
                
                # Day columns
                'entry_day': 'Day',
                'Day': 'Day',
                'day': 'Day',
                'trade_day': 'Day'
            }
            
            # Apply column mapping
            for old_col, new_col in column_mapping.items():
                if old_col in result_df.columns:
                    result_df = result_df.rename(columns={old_col: new_col})
            
            # Ensure required columns exist
            required_columns = ['Exit Date', 'PNL', 'Enter On', 'Day']
            
            for col in required_columns:
                if col not in result_df.columns:
                    if col == 'Exit Date':
                        result_df[col] = '2024-01-03'
                    elif col == 'PNL':
                        result_df[col] = 0.0
                    elif col == 'Enter On':
                        result_df[col] = '09:15:00'
                    elif col == 'Day':
                        result_df[col] = 'Wednesday'
            
            # Format data properly
            result_df = self._format_consolidator_data(result_df)
            
            # Select only required columns in correct order
            result_df = result_df[required_columns]
            
            return result_df
            
        except Exception as e:
            logger.error(f"Error converting to consolidator format: {e}")
            return self._create_empty_consolidator_format()
    
    def _format_consolidator_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """Format data to match consolidator expectations"""
        try:
            result_df = df.copy()
            
            # Format Exit Date
            if 'Exit Date' in result_df.columns:
                # Convert to datetime if needed, then to string
                try:
                    result_df['Exit Date'] = pd.to_datetime(result_df['Exit Date']).dt.strftime('%Y-%m-%d')
                except:
                    # If conversion fails, keep as is
                    pass
            
            # Format PNL as numeric
            if 'PNL' in result_df.columns:
                try:
                    result_df['PNL'] = pd.to_numeric(result_df['PNL'], errors='coerce').fillna(0.0)
                except:
                    result_df['PNL'] = 0.0
            
            # Format Enter On (time)
            if 'Enter On' in result_df.columns:
                # Ensure time format
                try:
                    # If it's numeric (like 91500), convert to HH:MM:SS
                    def format_time(time_val):
                        if pd.isna(time_val):
                            return '09:15:00'
                        
                        time_str = str(time_val)
                        if ':' in time_str:
                            return time_str  # Already formatted
                        elif len(time_str) == 6:
                            # HHMMSS format
                            return f"{time_str[:2]}:{time_str[2:4]}:{time_str[4:6]}"
                        elif len(time_str) == 5:
                            # HMMSS format
                            return f"0{time_str[0]}:{time_str[1:3]}:{time_str[3:5]}"
                        else:
                            return '09:15:00'  # Default
                    
                    result_df['Enter On'] = result_df['Enter On'].apply(format_time)
                except:
                    result_df['Enter On'] = '09:15:00'
            
            # Format Day
            if 'Day' in result_df.columns:
                # Ensure proper day names
                day_mapping = {
                    'mon': 'Monday', 'monday': 'Monday',
                    'tue': 'Tuesday', 'tuesday': 'Tuesday',
                    'wed': 'Wednesday', 'wednesday': 'Wednesday',
                    'thu': 'Thursday', 'thursday': 'Thursday',
                    'fri': 'Friday', 'friday': 'Friday',
                    'sat': 'Saturday', 'saturday': 'Saturday',
                    'sun': 'Sunday', 'sunday': 'Sunday'
                }
                
                def format_day(day_val):
                    if pd.isna(day_val):
                        return 'Wednesday'
                    day_str = str(day_val).lower().strip()
                    return day_mapping.get(day_str, day_str.title())
                
                result_df['Day'] = result_df['Day'].apply(format_day)
            
            return result_df
            
        except Exception as e:
            logger.error(f"Error formatting consolidator data: {e}")
            return df
    
    def _create_empty_consolidator_format(self) -> pd.DataFrame:
        """Create empty DataFrame with consolidator-compatible structure"""
        return pd.DataFrame({
            'Exit Date': ['2024-01-03'],
            'PNL': [0.0],
            'Enter On': ['09:15:00'],
            'Day': ['Wednesday']
        })
    
    def validate_consolidator_format(self, file_path: str) -> Dict[str, Any]:
        """
        Validate if generated file matches consolidator expectations
        
        Args:
            file_path: Path to Excel file to validate
            
        Returns:
            Dict with validation results
        """
        try:
            # Read Excel file
            excel_file = pd.ExcelFile(file_path)
            
            validation_result = {
                'is_valid': False,
                'format_type': None,
                'issues': [],
                'sheet_names': excel_file.sheet_names,
                'row_count': 0,
                'column_names': []
            }
            
            # Check for required sheet
            if 'PORTFOLIO Trans' not in excel_file.sheet_names:
                validation_result['issues'].append("Missing required sheet: 'PORTFOLIO Trans'")
                return validation_result
            
            # Read the main sheet
            df = pd.read_excel(file_path, sheet_name='PORTFOLIO Trans')
            validation_result['row_count'] = len(df)
            validation_result['column_names'] = list(df.columns)
            
            # Check required columns
            required_single = ['Exit Date', 'PNL', 'Enter On']
            required_multi = ['Exit Date', 'PNL', 'Enter On', 'Strategy']
            
            has_single_cols = all(col in df.columns for col in required_single)
            has_multi_cols = all(col in df.columns for col in required_multi)
            
            if has_multi_cols:
                validation_result['format_type'] = 'FORMAT_6_PYTHON_MULTI_XLSX'
                validation_result['is_valid'] = True
            elif has_single_cols:
                validation_result['format_type'] = 'FORMAT_2_PYTHON_XLSX'
                validation_result['is_valid'] = True
            else:
                missing_cols = [col for col in required_single if col not in df.columns]
                validation_result['issues'].append(f"Missing required columns: {missing_cols}")
            
            return validation_result
            
        except Exception as e:
            return {
                'is_valid': False,
                'format_type': None,
                'issues': [f"Validation error: {str(e)}"],
                'sheet_names': [],
                'row_count': 0,
                'column_names': []
            }
