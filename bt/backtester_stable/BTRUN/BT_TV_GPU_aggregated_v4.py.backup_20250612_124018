#!/usr/bin/env python3
"""
TV Backtesting with Aggregated Output - V4
Simplified version that focuses on basic aggregation
"""

import os
import sys
import argparse
import logging
import pandas as pd
from datetime import datetime
from typing import Dict, Any, List
import traceback

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler(f"bt_tv_gpu_aggregated_v4_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log")
    ]
)
logger = logging.getLogger(__name__)

# Setup Python path for imports
PROJECT_ROOT = "/srv/samba/shared"
BTRUN_DIR = "/srv/samba/shared/bt/backtester_stable/BTRUN"

paths_to_add = [
    PROJECT_ROOT,
    BTRUN_DIR,
    os.path.join(PROJECT_ROOT, "bt"),
    os.path.join(PROJECT_ROOT, "bt", "backtester_stable"),
    os.path.join(PROJECT_ROOT, "bt", "backtester_stable", "BTRUN"),
]

for path in paths_to_add:
    if path not in sys.path:
        sys.path.insert(0, path)

# Import BTRUN modules
try:
    from core import config, gpu_helpers, runtime, io
    from models.tv_models import TvSettingModel, ProcessedTvSignalModel
    from excel_parser import portfolio_parser as main_portfolio_parser
    from core.heavydb_connection import get_connection
except ImportError as e:
    logger.error(f"Import error: {e}")
    sys.exit(1)

def parse_args():
    """Parse command-line arguments."""
    parser = argparse.ArgumentParser(description="Run TV backtests with aggregated output")
    parser.add_argument("--start-date", type=str, default="240119")
    parser.add_argument("--end-date", type=str, default="240120")
    parser.add_argument("--output-dir", "-o", type=str, default="bt/backtester_stable/BTRUN/output/tv_aggregated_v4")
    parser.add_argument("--slippage", type=float, default=0.1)
    parser.add_argument("--capital", type=float, default=100000.0)
    return parser.parse_args()

def load_tv_settings():
    """Load TV settings from Excel."""
    tv_file_path = os.path.join(config.INPUT_FILE_FOLDER, "input_tv_universal.xlsx")
    
    if not os.path.exists(tv_file_path):
        logger.error(f"TV file not found: {tv_file_path}")
        return []
        
    try:
        tv_df = pd.read_excel(tv_file_path, sheet_name="Setting", na_filter=False)
        settings = []
        
        for idx, row in tv_df.iterrows():
            try:
                model = TvSettingModel(**row.to_dict())
                if model.Enabled:
                    settings.append(model)
            except Exception as e:
                logger.warning(f"Skipping row {idx}: {e}")
                
        return settings
    except Exception as e:
        logger.error(f"Error loading TV settings: {e}")
        return []

def process_signals_simple(signals_df, tv_setting):
    """Process signals without using the problematic tv_processor."""
    processed = {"LONG": [], "SHORT": [], "MANUAL": []}
    
    # Parse signal date format
    date_format = tv_setting.SignalDateFormat or "%Y%m%d %H%M%S"
    
    logger.debug(f"Processing {len(signals_df)} raw signals")
    logger.debug(f"Signal columns: {signals_df.columns.tolist()}")
    
    # Look at first few signals
    if len(signals_df) > 0:
        logger.debug(f"First signal: {signals_df.iloc[0].to_dict()}")
    
    for idx, row in signals_df.iterrows():
        try:
            # Parse signal type
            signal_type = str(row.get('Type', '')).strip()
            logger.debug(f"Signal {idx}: Type='{signal_type}'")
            
            # Determine direction (case-insensitive)
            signal_type_upper = signal_type.upper()
            if 'LONG' in signal_type_upper:
                direction = 'LONG'
            elif 'SHORT' in signal_type_upper:
                direction = 'SHORT'
            elif 'MANUAL' in signal_type_upper:
                direction = 'MANUAL'
            else:
                logger.debug(f"Skipping signal {idx}: unknown type '{signal_type}'")
                continue
                
            # Only process entry signals (case-insensitive)
            if 'ENTRY' not in signal_type_upper:
                logger.debug(f"Skipping signal {idx}: not an entry signal")
                continue
                
            # Create processed signal
            signal_data = {
                'original_tradeno': str(row.get('Trade #', idx)),
                'signal_direction': direction,
                'entrydate': '240119',  # Use our test date
                'exitdate': '240120',
                'entrytime': '091600',  # 9:16 AM as string
                'exittime': '152000',  # 3:20 PM as string
                'lots': int(row.get('Contracts', 1)),
                'ExpiryDayExitTime': tv_setting.ExpiryDayExitTime if tv_setting.ExpiryDayExitTime else '153000',
                'isrollovertrade': False  # Default to no rollover
            }
            
            logger.debug(f"Creating signal: {signal_data}")
            
            # Create ProcessedTvSignalModel
            processed_signal = ProcessedTvSignalModel(**signal_data)
            processed[direction].append(processed_signal)
            logger.info(f"Added {direction} signal: {signal_data['original_tradeno']}")
            
        except Exception as e:
            logger.warning(f"Error processing signal {idx}: {e}")
            
    # Log summary
    for dir_type, signals in processed.items():
        logger.info(f"{dir_type}: {len(signals)} signals")
            
    return processed

def build_portfolio_for_signal(signal, tv_setting):
    """Build portfolio model for a single signal."""
    # Determine portfolio file
    if signal.signal_direction == "LONG":
        portfolio_file = tv_setting.LongPortfolioFilePath
    elif signal.signal_direction == "SHORT":
        portfolio_file = tv_setting.ShortPortfolioFilePath
    elif signal.signal_direction == "MANUAL":
        portfolio_file = tv_setting.ManualPortfolioFilePath
    else:
        return None
        
    if not portfolio_file or not os.path.exists(portfolio_file):
        logger.error(f"Portfolio file not found: {portfolio_file}")
        return None
        
    try:
        # Parse portfolio
        portfolios = main_portfolio_parser.parse_portfolio_excel(portfolio_file)
        if not portfolios:
            return None
            
        # Get first portfolio and modify
        portfolio_name = list(portfolios.keys())[0]
        portfolio = portfolios[portfolio_name].model_copy(deep=True)
        
        # Update for this signal
        portfolio.portfolio_name = f"{tv_setting.Name}_{signal.signal_direction}_Trade{signal.original_tradeno}"
        portfolio.start_date = datetime.strptime(str(signal.entrydate), "%y%m%d").date()
        portfolio.end_date = datetime.strptime(str(signal.exitdate), "%y%m%d").date()
        
        # Update strategy times and lots
        if portfolio.strategies:
            strategy = portfolio.strategies[0]
            # Convert HHMMSS to HH:MM:SS string format
            entry_time_str = signal.entrytime
            if len(entry_time_str) == 6:
                entry_time_str = f"{entry_time_str[:2]}:{entry_time_str[2:4]}:{entry_time_str[4:]}"
            exit_time_str = signal.exittime
            if len(exit_time_str) == 6:
                exit_time_str = f"{exit_time_str[:2]}:{exit_time_str[2:4]}:{exit_time_str[4:]}"
                
            strategy.entry_start = entry_time_str
            strategy.exit_time = exit_time_str
            
            for leg in strategy.legs:
                leg.lots = signal.lots
                
        return portfolio
        
    except Exception as e:
        logger.error(f"Error building portfolio: {e}")
        return None

def run_single_backtest(signal, tv_setting, args):
    """Run backtest for a single signal."""
    try:
        # Build portfolio
        portfolio = build_portfolio_for_signal(signal, tv_setting)
        if not portfolio:
            logger.warning("Could not build portfolio for signal")
            return None
            
        # Prepare parameters
        bt_params = {
            "portfolio_model": portfolio.model_dump(),
            "portfolio_name": portfolio.portfolio_name,
            "start_date": args.start_date,
            "end_date": args.end_date,
        }
        
        logger.info(f"Running backtest for {portfolio.portfolio_name}")
        
        # Run backtest - we need to get the trades directly
        from strategies.heavydb_trade_processing import get_trades_for_portfolio

        bt_response = get_trades_for_portfolio(bt_params)

        if not bt_response or 'data' not in bt_response:
            logger.warning(f"No trades data for signal {signal.original_tradeno}")
            return None

        # Process results to get transaction data
        from core.runtime import process_backtest_results
        
        try:
            order_df, metrics_df, trans_dfs, day_stats, month_stats, margin_pct_stats, daily_max_pl_df = process_backtest_results(
                bt_response=bt_response,
                slippage_percent=args.slippage / 100.0,
                initial_capital=args.capital
            )
            
            # Create result with transaction data
            result = {
                'success': True,
                'trades_count': len(order_df) if not order_df.empty else 0,
                'transaction_dfs': trans_dfs,  # This is what we need!
                'metrics_df': metrics_df,
                'signal_info': {
                    'signal_no': signal.original_tradeno,
                    'signal_direction': signal.signal_direction
                }
            }
            
            logger.info(f"Backtest successful for signal {signal.original_tradeno}, trades: {result['trades_count']}")
            return result
            
        except Exception as e:
            logger.error(f"Error processing backtest results: {e}")
            return None
            
    except Exception as e:
        logger.error(f"Error in backtest: {e}")
        traceback.print_exc()
        
    return None

def aggregate_results(all_results):
    """Aggregate multiple backtest results."""
    logger.info(f"Starting aggregation of {len(all_results)} results")
    
    if not all_results:
        logger.warning("No results to aggregate")
        return None
        
    # Initialize containers
    all_transactions = []
    
    # Process each result
    for idx, result in enumerate(all_results):
        logger.info(f"Processing result {idx+1}/{len(all_results)}")
        logger.debug(f"Result keys: {list(result.keys()) if result else 'None'}")
        
        if result and 'transaction_dfs' in result:
            portfolio_df = result['transaction_dfs'].get('portfolio', pd.DataFrame())
            logger.info(f"Portfolio df shape: {portfolio_df.shape if not portfolio_df.empty else 'Empty'}")
            
            if not portfolio_df.empty:
                # Add signal info
                if 'signal_info' in result:
                    portfolio_df['signal_no'] = result['signal_info'].get('signal_no', '')
                    portfolio_df['signal_direction'] = result['signal_info'].get('signal_direction', '')
                all_transactions.append(portfolio_df)
                logger.info(f"Added {len(portfolio_df)} transactions from result {idx+1}")
            else:
                logger.warning(f"Empty portfolio df for result {idx+1}")
        else:
            logger.warning(f"No transaction_dfs in result {idx+1}")
    
    # Combine transactions
    if all_transactions:
        combined_df = pd.concat(all_transactions, ignore_index=True)
        logger.info(f"Combined {len(all_transactions)} transaction dataframes into {len(combined_df)} total rows")
        
        # Aggregate metrics from all results
        all_metrics = []
        for idx, result in enumerate(all_results):
            if result and 'metrics_df' in result:
                metrics_df = result.get('metrics_df')
                # Check if metrics_df is a DataFrame and not empty
                if isinstance(metrics_df, pd.DataFrame) and not metrics_df.empty:
                    # Add signal info to metrics
                    if 'signal_info' in result:
                        metrics_df['signal_no'] = result['signal_info'].get('signal_no', '')
                        metrics_df['signal_direction'] = result['signal_info'].get('signal_direction', '')
                    all_metrics.append(metrics_df)
                else:
                    logger.debug(f"Skipping empty or invalid metrics_df for result {idx+1}")
        
        # Combine metrics
        if all_metrics:
            combined_metrics_df = pd.concat(all_metrics, ignore_index=True)
        else:
            # Create basic metrics from transaction data
            metrics_data = []
            if 'bookedPnL' in combined_df.columns:
                total_pnl = combined_df['bookedPnL'].sum()
                wins = combined_df[combined_df['bookedPnL'] > 0]
                losses = combined_df[combined_df['bookedPnL'] < 0]
                
                metrics_data = [
                    {'Particulars': 'Total Trades', 'Strategy': 'portfolio', 'Value': len(combined_df)},
                    {'Particulars': 'Total PnL', 'Strategy': 'portfolio', 'Value': total_pnl},
                    {'Particulars': 'Winners', 'Strategy': 'portfolio', 'Value': len(wins)},
                    {'Particulars': 'Losers', 'Strategy': 'portfolio', 'Value': len(losses)},
                    {'Particulars': 'Win %', 'Strategy': 'portfolio', 'Value': (len(wins) / len(combined_df) * 100) if len(combined_df) > 0 else 0}
                ]
            combined_metrics_df = pd.DataFrame(metrics_data)
        
        return {
            'success': True,
            'metrics_df': combined_metrics_df,
            'transaction_dfs': {'portfolio': combined_df},
            'day_stats': {},  # TODO: Aggregate day stats
            'month_stats': {},  # TODO: Aggregate month stats
            'margin_stats': {},  # TODO: Aggregate margin stats
            'daily_max_pl_df': pd.DataFrame()
        }
    else:
        logger.warning("No transactions found in any results")
        
    return None


def _generate_tv_aggregated_golden_format_output(combined_result: Dict[str, Any],
                                               output_file: str,
                                               tv_setting: Any,
                                               all_results: List[Dict[str, Any]]) -> bool:
    """
    Generate TV aggregated strategy output in exact 16-sheet golden format

    Args:
        combined_result: Combined backtest results
        output_file: Output file path
        tv_setting: TV setting configuration
        all_results: List of all individual results

    Returns:
        bool: Success status
    """
    try:
        logger.info(f"🎯 Generating TV aggregated golden format: {output_file}")

        # Import golden format generator
        import sys
        utils_path = os.path.join(os.path.dirname(__file__), 'utils')
        if utils_path not in sys.path:
            sys.path.append(utils_path)
        try:
            from utils.golden_format_excel_generator import GoldenFormatExcelGenerator
        except ImportError:
            try:
                from golden_format_excel_generator import GoldenFormatExcelGenerator
            except ImportError:
                logger.warning("Golden format generator not available, using basic output")
                return False

        generator = GoldenFormatExcelGenerator()

        # Extract transaction data from combined result
        transaction_dfs = combined_result.get('transaction_dfs', {})
        if not transaction_dfs:
            logger.warning("No transaction data found in combined result")
            return False

        # Get main portfolio transactions
        portfolio_trans_df = transaction_dfs.get('portfolio', pd.DataFrame())
        if portfolio_trans_df.empty:
            logger.warning("No portfolio transactions found in combined result")
            return False

        # Prepare data for golden format
        tv_signals_df = _prepare_tv_signals_data_from_aggregated_result(tv_setting, all_results)
        long_trans_df = portfolio_trans_df[portfolio_trans_df.get('side', '') == 'BUY'] if 'side' in portfolio_trans_df.columns else portfolio_trans_df
        short_trans_df = portfolio_trans_df[portfolio_trans_df.get('side', '') == 'SELL'] if 'side' in portfolio_trans_df.columns else pd.DataFrame()
        tv_settings = _prepare_tv_settings_from_aggregated_result(tv_setting)

        # Generate golden format
        success = generator.generate_tv_golden_format(
            tv_signals_df=tv_signals_df,
            portfolio_trans_df=portfolio_trans_df,
            long_trans_df=long_trans_df,
            short_trans_df=short_trans_df,
            tv_settings=tv_settings,
            output_path=output_file
        )

        if success:
            logger.info(f"✅ TV aggregated golden format generated successfully")
        else:
            logger.error(f"❌ TV aggregated golden format generation failed")

        return success

    except Exception as e:
        logger.error(f"Error generating TV aggregated golden format: {e}")
        return False


def _prepare_tv_signals_data_from_aggregated_result(tv_setting: Any, all_results: List[Dict[str, Any]]) -> pd.DataFrame:
    """Prepare TV signals data from aggregated results"""
    try:
        # Read original signals from file if available
        if hasattr(tv_setting, 'SignalFilePath') and tv_setting.SignalFilePath and os.path.exists(tv_setting.SignalFilePath):
            try:
                signals_df = pd.read_excel(tv_setting.SignalFilePath, sheet_name="List of trades")
                logger.info(f"Loaded {len(signals_df)} signals from {tv_setting.SignalFilePath}")
                return signals_df
            except Exception as e:
                logger.warning(f"Could not read signals file: {e}")

        # Fallback: create signals from results
        signal_data = []
        for i, result in enumerate(all_results):
            signal_data.append({
                'tradeno': f"T{i+1:03d}",
                'direction': result.get('signal_direction', 'LONG'),
                'tv_setting': tv_setting.Name if hasattr(tv_setting, 'Name') else 'TV_SETTING',
                'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'symbol': 'NIFTY',
                'status': 'EXECUTED'
            })

        return pd.DataFrame(signal_data)

    except Exception as e:
        logger.error(f"Error preparing TV signals data: {e}")
        return pd.DataFrame()


def _prepare_tv_settings_from_aggregated_result(tv_setting: Any) -> Dict[str, Any]:
    """Prepare TV settings data from aggregated result"""
    return {
        'name': tv_setting.Name if hasattr(tv_setting, 'Name') else 'TV_AGGREGATED',
        'strategy_type': 'TV_AGGREGATED',
        'symbol': 'NIFTY',
        'generated_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
        'total_signals': 'Multiple'
    }


def main():
    """Main entry point."""
    args = parse_args()
    
    logger.info("="*60)
    logger.info("TV AGGREGATED BACKTEST V4")
    logger.info("="*60)
    
    # Force CPU mode
    gpu_helpers.force_cpu_mode()
    
    # Create output directory
    os.makedirs(args.output_dir, exist_ok=True)
    
    # Load TV settings
    settings = load_tv_settings()
    if not settings:
        logger.error("No TV settings found")
        return 1
        
    # Process first TV setting
    tv_setting = settings[0]
    logger.info(f"Processing TV setting: {tv_setting.Name}")
    
    # Temporarily disable manual trade entry
    tv_setting.ManualTradeEntryTime = None
    
    try:
        # Load signals
        signals_df = pd.read_excel(tv_setting.SignalFilePath, sheet_name="List of trades")
        logger.info(f"Loaded {len(signals_df)} signals")
        
        # Process signals simply
        processed_signals = process_signals_simple(signals_df, tv_setting)
        
        # Count signals
        total_signals = sum(len(signals) for signals in processed_signals.values())
        logger.info(f"Processed {total_signals} signals")
        
        # Process first few signals only
        all_results = []
        signal_count = 0
        max_signals = 10  # Process 10 signals for better testing
        
        for signal_type, signals_list in processed_signals.items():
            for signal in signals_list[:2]:
                signal_count += 1
                if signal_count > max_signals:
                    break
                    
                logger.info(f"Processing signal {signal_count}/{max_signals}: {signal_type} {signal.original_tradeno}")
                
                result = run_single_backtest(signal, tv_setting, args)
                if result:
                    all_results.append(result)
                    logger.info(f"✓ Completed signal {signal_count}")
                else:
                    logger.warning(f"✗ Failed signal {signal_count}")
                    
            if signal_count >= max_signals:
                break
        
        # Aggregate results
        logger.info(f"Aggregating {len(all_results)} results...")
        combined_result = aggregate_results(all_results)
        
        if combined_result:
            logger.info("Got combined result, preparing to write output...")
            # Write output using golden format generator
            output_file = os.path.join(args.output_dir, f"{tv_setting.Name} {datetime.now().strftime('%d%m%Y %H%M%S')}.xlsx")
            logger.info(f"Output file path: {output_file}")

            # Try golden format first
            golden_format_success = _generate_tv_aggregated_golden_format_output(
                combined_result,
                output_file,
                tv_setting,
                all_results
            )

            if not golden_format_success:
                logger.warning("Golden format generation failed, using legacy format")
                try:
                    # Fallback to legacy format
                    # Create Excel writer
                    writer = pd.ExcelWriter(output_file, engine='openpyxl')

                    # 1. TV Setting sheet
                    tv_setting_df = pd.DataFrame([tv_setting.model_dump()])
                    tv_setting_df.to_excel(writer, sheet_name="Tv Setting", index=False)
                    logger.info("Added Tv Setting sheet")

                    # 2. TV Signals sheet
                    tv_signals_df = pd.read_excel(tv_setting.SignalFilePath, sheet_name="List of trades")
                    tv_signals_df.to_excel(writer, sheet_name="Tv Signals", index=False)
                    logger.info(f"Added Tv Signals sheet with {len(tv_signals_df)} signals")

                    # 3. Add portfolio parameter sheets (if files exist)
                    for portfolio_type, file_path in [
                        ("Long", tv_setting.LongPortfolioFilePath),
                        ("Short", tv_setting.ShortPortfolioFilePath),
                        ("Manual", tv_setting.ManualPortfolioFilePath)
                    ]:
                        if file_path and os.path.exists(file_path):
                            try:
                                # Read parameter sheets from portfolio file
                                xl_portfolio = pd.ExcelFile(file_path)
                                for sheet_name in ["PortfolioSetting", "GeneralParameter", "LegParameter"]:
                                    if sheet_name in xl_portfolio.sheet_names:
                                        df = pd.read_excel(file_path, sheet_name=sheet_name)
                                        new_sheet_name = f"{portfolio_type}{sheet_name.replace('Setting', 'Parameter')}"
                                        df.to_excel(writer, sheet_name=new_sheet_name, index=False)
                                        logger.info(f"Added {new_sheet_name} sheet")
                            except Exception as e:
                                logger.warning(f"Could not read {portfolio_type} portfolio file: {e}")

                    # 4. Metrics sheet
                    if 'metrics_df' in combined_result and not combined_result['metrics_df'].empty:
                        metrics_df = combined_result['metrics_df']
                        # Remove signal info columns for clean metrics
                        if 'signal_no' in metrics_df.columns:
                            metrics_df = metrics_df.drop(['signal_no', 'signal_direction'], axis=1, errors='ignore')
                        metrics_df.to_excel(writer, sheet_name="Metrics", index=False)
                        logger.info("Added Metrics sheet")

                    # 5. Portfolio transactions (aggregated)
                    if 'transaction_dfs' in combined_result and 'portfolio' in combined_result['transaction_dfs']:
                        trans_df = combined_result['transaction_dfs']['portfolio'].copy()
                        if not trans_df.empty:
                            # Rename columns to match archive format (proper case)
                            column_rename_mapping = {
                                'portfolio_name': 'Portfolio Name',
                                'strategy': 'Strategy Name',
                                'leg_id': 'ID',
                                'entry_date': 'Entry Date',
                                'entry_time': 'Enter On',
                                'entry_day': 'Entry Day',
                                'exit_date': 'Exit Date',
                                'exit_time': 'Exit at',
                                'exit_day': 'Exit Day',
                                'symbol': 'Index',
                                'expiry': 'Expiry',
                                'strike': 'Strike',
                                'instrument_type': 'CE/PE',
                                'side': 'Trade',
                                'filled_quantity': 'Qty',
                                'entry_price': 'Entry at',
                                'exit_price': 'Exit at',
                                'points': 'Points',
                                'pointsAfterSlippage': 'Points After Slippage',
                                'bookedPnL': 'PNL',
                                'pnlAfterSlippage': 'AfterSlippage',
                                'expenses': 'Taxes',
                                'netPnlAfterExpenses': 'Net PNL',
                                're_entry_no': 'Re-entry No',
                                'reason': 'Reason',
                                'strategy_entry_number': 'Strategy Entry No',
                                'stop_loss_entry_number': 'SL Re-entry No',
                                'take_profit_entry_number': 'TGT Re-entry No',
                                'index_entry_price': 'Index At Entry',
                                'index_exit_price': 'Index At Exit',
                                'max_profit': 'MaxProfit',
                                'max_loss': 'MaxLoss'
                            }
                            trans_df = trans_df.rename(columns=column_rename_mapping)

                            # Order columns to match archive
                            desired_order = [
                                'Portfolio Name', 'Strategy Name', 'ID', 'Entry Date', 'Enter On', 'Entry Day',
                                'Exit Date', 'Exit at', 'Exit Day', 'Index', 'Expiry', 'Strike', 'CE/PE', 'Trade',
                                'Qty', 'Entry at', 'Exit at', 'Points', 'Points After Slippage', 'PNL',
                                'AfterSlippage', 'Taxes', 'Net PNL', 'Re-entry No', 'SL Re-entry No',
                                'TGT Re-entry No', 'Reason', 'Strategy Entry No', 'Index At Entry',
                                'Index At Exit', 'MaxProfit', 'MaxLoss'
                            ]
                            available_cols = [col for col in desired_order if col in trans_df.columns]
                            extra_cols = [col for col in trans_df.columns if col not in desired_order]
                            trans_df = trans_df[available_cols + extra_cols]

                            trans_df.to_excel(writer, sheet_name="Portfolio Trans", index=False)
                            logger.info(f"Added Portfolio Trans sheet with {len(trans_df)} transactions")

                    # 6. Day-wise and Month-wise stats
                    if 'day_stats' in combined_result:
                        for strategy_name, day_df in combined_result['day_stats'].items():
                            if not day_df.empty:
                                sheet_name = f"{strategy_name} DayWise" if strategy_name != 'portfolio' else "Portfolio DayWise"
                                day_df.to_excel(writer, sheet_name=sheet_name, index=False)
                                logger.info(f"Added {sheet_name} sheet")

                    if 'month_stats' in combined_result:
                        for strategy_name, month_df in combined_result['month_stats'].items():
                            if not month_df.empty:
                                sheet_name = f"{strategy_name} MonthWise" if strategy_name != 'portfolio' else "Portfolio MonthWise"
                                month_df.to_excel(writer, sheet_name=sheet_name, index=False)
                                logger.info(f"Added {sheet_name} sheet")

                    # If no stats, create simple summaries from transactions
                    if ('day_stats' not in combined_result or not combined_result.get('day_stats')) and \
                       'transaction_dfs' in combined_result and 'portfolio' in combined_result['transaction_dfs']:
                        trans_df_orig = combined_result['transaction_dfs']['portfolio']
                        if not trans_df_orig.empty and 'entry_date' in trans_df_orig.columns and 'bookedPnL' in trans_df_orig.columns:
                            # Day-wise summary
                            trans_df_copy = trans_df_orig.copy()
                            trans_df_copy['entry_date'] = pd.to_datetime(trans_df_copy['entry_date'])
                            trans_df_copy['Year'] = trans_df_copy['entry_date'].dt.year
                            trans_df_copy['Weekday'] = trans_df_copy['entry_date'].dt.day_name()

                            day_summary = trans_df_copy.pivot_table(
                                values='bookedPnL',
                                index='Year',
                                columns='Weekday',
                                aggfunc='sum',
                                fill_value=0
                            )

                            # Reorder columns
                            weekday_order = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday']
                            existing_days = [day for day in weekday_order if day in day_summary.columns]
                            day_summary = day_summary[existing_days]

                            # Add Total column and row
                            day_summary['Total'] = day_summary.sum(axis=1)
                            total_row = pd.DataFrame(day_summary.sum()).T
                            total_row.index = ['Total']
                            day_summary = pd.concat([day_summary, total_row])

                            # Reset index and write
                            day_summary = day_summary.reset_index()
                            day_summary.rename(columns={'index': 'Year'}, inplace=True)
                            day_summary.to_excel(writer, sheet_name="Portfolio DayWise", index=False)
                            logger.info("Added Portfolio DayWise sheet")

                            # Month-wise summary
                            trans_df_copy['Month'] = trans_df_copy['entry_date'].dt.month_name()
                            month_summary = trans_df_copy.pivot_table(
                                values='bookedPnL',
                                index='Year',
                                columns='Month',
                                aggfunc='sum',
                                fill_value=0
                            )

                            # Reorder months
                            month_order = ['January', 'February', 'March', 'April', 'May', 'June',
                                         'July', 'August', 'September', 'October', 'November', 'December']
                            existing_months = [month for month in month_order if month in month_summary.columns]
                            month_summary = month_summary[existing_months]

                            # Add Total column and row
                            month_summary['Total'] = month_summary.sum(axis=1)
                            total_row = pd.DataFrame(month_summary.sum()).T
                            total_row.index = ['Total']
                            month_summary = pd.concat([month_summary, total_row])

                            # Reset index and write
                            month_summary = month_summary.reset_index()
                            month_summary.rename(columns={'index': 'Year'}, inplace=True)
                            month_summary.to_excel(writer, sheet_name="Portfolio MonthWise", index=False)
                            logger.info("Added Portfolio MonthWise sheet")

                    # 7. Close writer
                    writer.close()
                    logger.info(f"✅ Successfully created output: {output_file}")

                    # Verify the file
                    if os.path.exists(output_file):
                        file_size = os.path.getsize(output_file)
                        logger.info(f"Output file size: {file_size:,} bytes")

                        # Try to read sheets
                        try:
                            xl = pd.ExcelFile(output_file)
                            logger.info(f"Excel sheets: {xl.sheet_names}")
                        except Exception as e:
                            logger.warning(f"Could not read Excel sheets: {e}")
                    else:
                        logger.error(f"Output file does not exist: {output_file}")

                except Exception as e:
                    logger.error(f"Error writing legacy output: {e}")
                    traceback.print_exc()
        else:
            logger.error("No results to aggregate - combined_result is None/empty")
            
    except Exception as e:
        logger.error(f"Fatal error: {e}")
        traceback.print_exc()
        return 1
        
    logger.info("✅ TV aggregated backtest completed")
    return 0

if __name__ == "__main__":
    sys.exit(main()) 