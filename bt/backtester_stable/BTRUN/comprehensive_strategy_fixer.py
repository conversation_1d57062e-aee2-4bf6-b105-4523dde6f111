#!/usr/bin/env python3
"""
Comprehensive Strategy Fixer
Fixes all strategy types and validates all columns are working properly
"""
import os
import sys
import pandas as pd
import shutil
import logging
from datetime import datetime
from typing import Dict, List, Any, Optional

# Setup Python path
PROJECT_ROOT = "/srv/samba/shared"
BTRUN_DIR = "/srv/samba/shared/bt/backtester_stable/BTRUN"

paths_to_add = [
    PROJECT_ROOT,
    BTRUN_DIR,
    os.path.join(PROJECT_ROOT, "bt"),
    os.path.join(PROJECT_ROOT, "bt", "backtester_stable"),
    os.path.join(PROJECT_ROOT, "bt", "backtester_stable", "BTRUN"),
]

for path in paths_to_add:
    if path not in sys.path:
        sys.path.insert(0, path)

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(message)s'
)
logger = logging.getLogger(__name__)

class ComprehensiveStrategyFixer:
    """Fix all strategy types and validate columns"""
    
    def __init__(self):
        self.input_sheets_dir = "/srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets"
        self.backup_dir = os.path.join(self.input_sheets_dir, "comprehensive_backup")
        self.fix_log = []
        
        # Strategy file mappings
        self.strategy_files = {
            'TV': {
                'main': 'tv/input_tv.xlsx',
                'script': 'BT_TV_GPU_aggregated_v4.py'
            },
            'TBS': {
                'main': 'tbs/input_portfolio.xlsx',
                'script': 'BTRunPortfolio_GPU.py'
            },
            'OI': {
                'main': 'oi/bt_setting.xlsx',
                'strategy': 'oi/input_maxoi.xlsx',
                'script': 'BT_OI_GPU.py'
            },
            'ORB': {
                'main': 'orb/input_orb.xlsx',
                'script': 'BT_ORB_GPU.py'
            }
        }
        
    def create_backup(self, file_path: str) -> str:
        """Create backup of original file"""
        os.makedirs(self.backup_dir, exist_ok=True)
        
        backup_name = f"{os.path.basename(file_path)}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        backup_path = os.path.join(self.backup_dir, backup_name)
        
        shutil.copy2(file_path, backup_path)
        logger.info(f"Created backup: {backup_path}")
        return backup_path
    
    def fix_tv_strategy(self) -> bool:
        """Fix TV strategy completely"""
        logger.info("🔧 Fixing TV Strategy...")
        
        try:
            # Fix TV Excel file
            tv_file = os.path.join(self.input_sheets_dir, self.strategy_files['TV']['main'])
            if not os.path.exists(tv_file):
                logger.error(f"TV file not found: {tv_file}")
                return False
            
            self.create_backup(tv_file)
            
            # Read and fix TV Excel
            excel_data = pd.read_excel(tv_file, sheet_name=None)
            
            # Fix Setting sheet - ensure 'Enabled' is 'YES' (uppercase)
            if 'Setting' in excel_data:
                setting_df = excel_data['Setting']
                if 'Enabled' in setting_df.columns:
                    setting_df['Enabled'] = 'YES'
                excel_data['Setting'] = setting_df
            
            # Fix StrategySetting sheet - ensure boolean True for Enabled
            if 'StrategySetting' in excel_data:
                strategy_df = excel_data['StrategySetting']
                if 'Enabled' in strategy_df.columns:
                    strategy_df['Enabled'] = True
                excel_data['StrategySetting'] = strategy_df
            
            # Save updated file
            with pd.ExcelWriter(tv_file, engine='openpyxl') as writer:
                for sheet_name, df in excel_data.items():
                    df.to_excel(writer, sheet_name=sheet_name, index=False)
            
            # Fix TV script imports
            tv_script = os.path.join(BTRUN_DIR, self.strategy_files['TV']['script'])
            if os.path.exists(tv_script):
                self.fix_tv_script_imports(tv_script)
            
            logger.info("✅ TV Strategy fixed successfully")
            self.fix_log.append("TV Strategy: Excel and script imports fixed")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to fix TV strategy: {e}")
            return False
    
    def fix_tv_script_imports(self, script_path: str):
        """Fix TV script import issues"""
        try:
            with open(script_path, 'r') as f:
                content = f.read()
            
            # Create backup
            backup_path = f"{script_path}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            with open(backup_path, 'w') as f:
                f.write(content)
            
            # Fix imports - replace relative imports with absolute
            fixes = [
                ('from backtester_stable.BTRUN.core import', 'from core import'),
                ('from backtester_stable.models.tv_models import', 'from models.tv_models import'),
                ('from backtester_stable.BTRUN.excel_parser import', 'from excel_parser import'),
                ('from backtester_stable.BTRUN.core.heavydb_connection import', 'from core.heavydb_connection import'),
                ('from backtester_stable.BTRUN.strategies.heavydb_trade_processing import', 'from strategies.heavydb_trade_processing import'),
                ('from backtester_stable.BTRUN.core.runtime import', 'from core.runtime import')
            ]
            
            for old_import, new_import in fixes:
                content = content.replace(old_import, new_import)
            
            # Ensure Python path setup is at the top
            if 'PROJECT_ROOT = "/srv/samba/shared"' not in content:
                python_setup = '''#!/usr/bin/env python3
"""
TV Strategy with proper Python path setup
"""
import sys
import os

# Setup Python path
PROJECT_ROOT = "/srv/samba/shared"
BTRUN_DIR = "/srv/samba/shared/bt/backtester_stable/BTRUN"

paths_to_add = [
    PROJECT_ROOT,
    BTRUN_DIR,
    os.path.join(PROJECT_ROOT, "bt"),
    os.path.join(PROJECT_ROOT, "bt", "backtester_stable"),
    os.path.join(PROJECT_ROOT, "bt", "backtester_stable", "BTRUN"),
]

for path in paths_to_add:
    if path not in sys.path:
        sys.path.insert(0, path)

'''
                # Remove existing shebang and add new setup
                lines = content.split('\n')
                if lines[0].startswith('#!'):
                    lines = lines[1:]
                
                content = python_setup + '\n'.join(lines)
            
            # Write fixed script
            with open(script_path, 'w') as f:
                f.write(content)
            
            logger.info(f"✅ Fixed TV script imports: {script_path}")
            
        except Exception as e:
            logger.error(f"❌ Failed to fix TV script imports: {e}")
    
    def fix_tbs_strategy(self) -> bool:
        """Fix TBS strategy completely"""
        logger.info("🔧 Fixing TBS Strategy...")
        
        try:
            # Fix TBS Excel file
            tbs_file = os.path.join(self.input_sheets_dir, self.strategy_files['TBS']['main'])
            if not os.path.exists(tbs_file):
                logger.error(f"TBS file not found: {tbs_file}")
                return False
            
            self.create_backup(tbs_file)
            
            # Read and fix TBS Excel
            excel_data = pd.read_excel(tbs_file, sheet_name=None)
            
            # Fix PortfolioSetting sheet
            if 'PortfolioSetting' in excel_data:
                portfolio_df = excel_data['PortfolioSetting']
                if 'Enabled' in portfolio_df.columns:
                    portfolio_df['Enabled'] = 'YES'
                excel_data['PortfolioSetting'] = portfolio_df
            
            # Fix StrategySetting sheet - fix file paths
            if 'StrategySetting' in excel_data:
                strategy_df = excel_data['StrategySetting']
                
                # Fix file paths to use Linux format
                if 'StrategyExcelFilePath' in strategy_df.columns:
                    strategy_df['StrategyExcelFilePath'] = strategy_df['StrategyExcelFilePath'].str.replace(
                        r'D:\\.*\\', f'{self.input_sheets_dir}/tbs/', regex=True
                    )
                    strategy_df['StrategyExcelFilePath'] = strategy_df['StrategyExcelFilePath'].str.replace(
                        '\\\\', '/', regex=False
                    )
                
                # Ensure at least one strategy is enabled
                if 'Enabled' in strategy_df.columns:
                    strategy_df.loc[0, 'Enabled'] = 'YES'  # Enable first strategy
                
                excel_data['StrategySetting'] = strategy_df
            
            # Save updated file
            with pd.ExcelWriter(tbs_file, engine='openpyxl') as writer:
                for sheet_name, df in excel_data.items():
                    df.to_excel(writer, sheet_name=sheet_name, index=False)
            
            logger.info("✅ TBS Strategy fixed successfully")
            self.fix_log.append("TBS Strategy: Excel file paths and enabled flags fixed")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to fix TBS strategy: {e}")
            return False
    
    def fix_oi_strategy(self) -> bool:
        """Fix OI strategy completely"""
        logger.info("🔧 Fixing OI Strategy...")
        
        try:
            # Fix OI main Excel file
            oi_file = os.path.join(self.input_sheets_dir, self.strategy_files['OI']['main'])
            if not os.path.exists(oi_file):
                logger.error(f"OI file not found: {oi_file}")
                return False
            
            self.create_backup(oi_file)
            
            # Read and fix OI Excel
            excel_data = pd.read_excel(oi_file, sheet_name=None)
            
            # Ensure all sheets have proper 'Enabled' column with 'YES' value
            for sheet_name, df in excel_data.items():
                if 'Enabled' in df.columns:
                    df['Enabled'] = 'YES'
                elif 'enabled' in df.columns:
                    df['enabled'] = 'YES'
                    # Also add uppercase version
                    df['Enabled'] = 'YES'
                excel_data[sheet_name] = df
            
            # Fix strategy file path in StrategySetting
            if 'StrategySetting' in excel_data:
                strategy_df = excel_data['StrategySetting']
                if 'StrategyExcelFilePath' in strategy_df.columns:
                    strategy_df['StrategyExcelFilePath'] = os.path.join(
                        self.input_sheets_dir, 'oi/input_maxoi.xlsx'
                    )
                excel_data['StrategySetting'] = strategy_df
            
            # Save updated file
            with pd.ExcelWriter(oi_file, engine='openpyxl') as writer:
                for sheet_name, df in excel_data.items():
                    df.to_excel(writer, sheet_name=sheet_name, index=False)
            
            # Fix OI strategy Excel file if it exists
            oi_strategy_file = os.path.join(self.input_sheets_dir, self.strategy_files['OI']['strategy'])
            if os.path.exists(oi_strategy_file):
                self.fix_oi_strategy_file(oi_strategy_file)
            
            logger.info("✅ OI Strategy fixed successfully")
            self.fix_log.append("OI Strategy: Excel enabled flags and file paths fixed")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to fix OI strategy: {e}")
            return False
    
    def fix_oi_strategy_file(self, strategy_file: str):
        """Fix OI strategy Excel file structure"""
        try:
            self.create_backup(strategy_file)
            
            # Check if file has required sheets
            try:
                excel_data = pd.read_excel(strategy_file, sheet_name=None)
            except:
                # Create minimal OI strategy file
                excel_data = {}
            
            # Ensure GeneralParameter sheet exists
            if 'GeneralParameter' not in excel_data:
                general_df = pd.DataFrame({
                    'StrategyName': ['MAXOI_1'],
                    'Underlying': ['NIFTY'],
                    'Index': ['NIFTY'],
                    'Weekdays': ['1,2,3,4,5'],
                    'DTE': [0],
                    'StartTime': ['091600'],
                    'EndTime': ['152900'],
                    'OIThreshold': [800000],
                    'Enabled': ['YES']
                })
                excel_data['GeneralParameter'] = general_df
            
            # Ensure LegParameter sheet exists
            if 'LegParameter' not in excel_data:
                leg_df = pd.DataFrame({
                    'StrategyName': ['MAXOI_1'],
                    'LegID': [1],
                    'Instrument': ['OPTIDX'],
                    'Transaction': ['SELL'],
                    'Expiry': ['CURRENT'],
                    'StrikeMethod': ['ATM'],
                    'StrikeValue': [0],
                    'Lots': [1],
                    'Enabled': ['YES']
                })
                excel_data['LegParameter'] = leg_df
            
            # Save updated file
            with pd.ExcelWriter(strategy_file, engine='openpyxl') as writer:
                for sheet_name, df in excel_data.items():
                    df.to_excel(writer, sheet_name=sheet_name, index=False)
            
            logger.info(f"✅ Fixed OI strategy file: {strategy_file}")
            
        except Exception as e:
            logger.error(f"❌ Failed to fix OI strategy file: {e}")
    
    def fix_orb_strategy(self) -> bool:
        """Fix ORB strategy completely"""
        logger.info("🔧 Fixing ORB Strategy...")
        
        try:
            # Fix ORB Excel file
            orb_file = os.path.join(self.input_sheets_dir, self.strategy_files['ORB']['main'])
            if not os.path.exists(orb_file):
                logger.error(f"ORB file not found: {orb_file}")
                return False
            
            self.create_backup(orb_file)
            
            # Read and fix ORB Excel
            excel_data = pd.read_excel(orb_file, sheet_name=None)
            
            # Add Enabled column to GeneralParameter if missing
            if 'GeneralParameter' in excel_data:
                general_df = excel_data['GeneralParameter']
                if 'Enabled' not in general_df.columns:
                    general_df['Enabled'] = 'YES'
                else:
                    general_df['Enabled'] = 'YES'  # Ensure all are enabled
                excel_data['GeneralParameter'] = general_df
            
            # Add Enabled column to LegParameter if missing
            if 'LegParameter' in excel_data:
                leg_df = excel_data['LegParameter']
                if 'Enabled' not in leg_df.columns:
                    leg_df['Enabled'] = 'YES'
                else:
                    leg_df['Enabled'] = 'YES'  # Ensure all are enabled
                excel_data['LegParameter'] = leg_df
            
            # Save updated file
            with pd.ExcelWriter(orb_file, engine='openpyxl') as writer:
                for sheet_name, df in excel_data.items():
                    df.to_excel(writer, sheet_name=sheet_name, index=False)
            
            logger.info("✅ ORB Strategy fixed successfully")
            self.fix_log.append("ORB Strategy: Added Enabled columns and set to YES")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to fix ORB strategy: {e}")
            return False
    
    def validate_all_strategies(self) -> Dict[str, Any]:
        """Validate all strategies after fixes"""
        logger.info("🔍 Validating all strategies...")
        
        validation_results = {
            'TV': self.validate_tv_strategy(),
            'TBS': self.validate_tbs_strategy(),
            'OI': self.validate_oi_strategy(),
            'ORB': self.validate_orb_strategy()
        }
        
        return validation_results
    
    def validate_tv_strategy(self) -> Dict[str, Any]:
        """Validate TV strategy"""
        try:
            tv_file = os.path.join(self.input_sheets_dir, self.strategy_files['TV']['main'])
            excel_data = pd.read_excel(tv_file, sheet_name=None)
            
            issues = []
            
            # Check required sheets
            required_sheets = ['Setting', 'PortfolioSetting', 'StrategySetting']
            for sheet in required_sheets:
                if sheet not in excel_data:
                    issues.append(f"Missing sheet: {sheet}")
            
            # Check Enabled columns
            if 'Setting' in excel_data and 'Enabled' in excel_data['Setting'].columns:
                enabled_val = excel_data['Setting']['Enabled'].iloc[0]
                if enabled_val not in ['YES', 'yes', True]:
                    issues.append(f"Setting.Enabled = {enabled_val}, should be YES")
            
            return {
                'valid': len(issues) == 0,
                'issues': issues,
                'sheets': list(excel_data.keys()) if excel_data else []
            }
            
        except Exception as e:
            return {'valid': False, 'issues': [str(e)], 'sheets': []}
    
    def validate_tbs_strategy(self) -> Dict[str, Any]:
        """Validate TBS strategy"""
        try:
            tbs_file = os.path.join(self.input_sheets_dir, self.strategy_files['TBS']['main'])
            excel_data = pd.read_excel(tbs_file, sheet_name=None)
            
            issues = []
            
            # Check required sheets
            required_sheets = ['PortfolioSetting', 'StrategySetting']
            for sheet in required_sheets:
                if sheet not in excel_data:
                    issues.append(f"Missing sheet: {sheet}")
            
            # Check file paths
            if 'StrategySetting' in excel_data and 'StrategyExcelFilePath' in excel_data['StrategySetting'].columns:
                file_paths = excel_data['StrategySetting']['StrategyExcelFilePath']
                for path in file_paths:
                    if 'D:\\' in str(path):
                        issues.append(f"Windows path found: {path}")
            
            return {
                'valid': len(issues) == 0,
                'issues': issues,
                'sheets': list(excel_data.keys()) if excel_data else []
            }
            
        except Exception as e:
            return {'valid': False, 'issues': [str(e)], 'sheets': []}
    
    def validate_oi_strategy(self) -> Dict[str, Any]:
        """Validate OI strategy"""
        try:
            oi_file = os.path.join(self.input_sheets_dir, self.strategy_files['OI']['main'])
            excel_data = pd.read_excel(oi_file, sheet_name=None)
            
            issues = []
            
            # Check for Enabled columns in all sheets
            for sheet_name, df in excel_data.items():
                if 'Enabled' not in df.columns and 'enabled' not in df.columns:
                    issues.append(f"Sheet {sheet_name} missing Enabled column")
            
            return {
                'valid': len(issues) == 0,
                'issues': issues,
                'sheets': list(excel_data.keys()) if excel_data else []
            }
            
        except Exception as e:
            return {'valid': False, 'issues': [str(e)], 'sheets': []}
    
    def validate_orb_strategy(self) -> Dict[str, Any]:
        """Validate ORB strategy"""
        try:
            orb_file = os.path.join(self.input_sheets_dir, self.strategy_files['ORB']['main'])
            excel_data = pd.read_excel(orb_file, sheet_name=None)
            
            issues = []
            
            # Check required sheets
            required_sheets = ['GeneralParameter', 'LegParameter']
            for sheet in required_sheets:
                if sheet not in excel_data:
                    issues.append(f"Missing sheet: {sheet}")
                elif 'Enabled' not in excel_data[sheet].columns:
                    issues.append(f"Sheet {sheet} missing Enabled column")
            
            return {
                'valid': len(issues) == 0,
                'issues': issues,
                'sheets': list(excel_data.keys()) if excel_data else []
            }
            
        except Exception as e:
            return {'valid': False, 'issues': [str(e)], 'sheets': []}
    
    def fix_all_strategies(self) -> Dict[str, Any]:
        """Fix all strategies comprehensively"""
        logger.info("🚀 Starting comprehensive strategy fixes...")
        
        results = {
            'TV': self.fix_tv_strategy(),
            'TBS': self.fix_tbs_strategy(),
            'OI': self.fix_oi_strategy(),
            'ORB': self.fix_orb_strategy()
        }
        
        # Validate after fixes
        validation_results = self.validate_all_strategies()
        
        # Generate comprehensive report
        self.generate_comprehensive_report(results, validation_results)
        
        return {
            'fix_results': results,
            'validation_results': validation_results,
            'fix_log': self.fix_log
        }
    
    def generate_comprehensive_report(self, fix_results: Dict, validation_results: Dict):
        """Generate comprehensive fix and validation report"""
        report_file = f"/tmp/comprehensive_strategy_fix_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
        
        with open(report_file, 'w') as f:
            f.write("COMPREHENSIVE STRATEGY FIX REPORT\n")
            f.write("="*60 + "\n")
            f.write(f"Date: {datetime.now().isoformat()}\n\n")
            
            f.write("FIX RESULTS:\n")
            f.write("-" * 30 + "\n")
            for strategy, success in fix_results.items():
                status = "✅ SUCCESS" if success else "❌ FAILED"
                f.write(f"{strategy}: {status}\n")
            
            f.write("\nVALIDATION RESULTS:\n")
            f.write("-" * 30 + "\n")
            for strategy, result in validation_results.items():
                status = "✅ VALID" if result['valid'] else "❌ INVALID"
                f.write(f"{strategy}: {status}\n")
                if result['issues']:
                    for issue in result['issues']:
                        f.write(f"  - {issue}\n")
            
            f.write("\nFIX LOG:\n")
            f.write("-" * 30 + "\n")
            for log_entry in self.fix_log:
                f.write(f"- {log_entry}\n")
        
        logger.info(f"📄 Comprehensive report saved to: {report_file}")


def main():
    """Main fixer runner"""
    fixer = ComprehensiveStrategyFixer()
    results = fixer.fix_all_strategies()
    
    logger.info(f"\n{'='*60}")
    logger.info("COMPREHENSIVE STRATEGY FIX SUMMARY")
    logger.info(f"{'='*60}")
    
    # Fix results
    fix_results = results['fix_results']
    successful_fixes = sum(fix_results.values())
    total_strategies = len(fix_results)
    
    logger.info(f"Strategies Fixed: {successful_fixes}/{total_strategies}")
    for strategy, success in fix_results.items():
        status = "✅" if success else "❌"
        logger.info(f"  {strategy}: {status}")
    
    # Validation results
    validation_results = results['validation_results']
    valid_strategies = sum(1 for r in validation_results.values() if r['valid'])
    
    logger.info(f"\nValidation Results: {valid_strategies}/{total_strategies} valid")
    for strategy, result in validation_results.items():
        status = "✅" if result['valid'] else "❌"
        logger.info(f"  {strategy}: {status}")
        if result['issues']:
            for issue in result['issues']:
                logger.info(f"    - {issue}")
    
    # Overall success
    overall_success = successful_fixes == total_strategies and valid_strategies == total_strategies
    
    if overall_success:
        logger.info(f"\n🎉 ALL STRATEGIES FIXED AND VALIDATED!")
        logger.info("✅ Ready for production testing")
    else:
        logger.info(f"\n⚠️ SOME ISSUES REMAIN")
        logger.info("🔧 Review the report for details")


if __name__ == "__main__":
    main()
