#!/usr/bin/env python3
"""
TBS Golden Format Integration Test

This script tests the TBS multi-strategy golden format integration:
1. TBS multi-strategy golden format generator (167 sheets)
2. BTRunPortfolio_GPU.py integration
3. H1-H80 strategy pattern
4. 32-column transaction format
5. Day-wise results format
"""

import os
import sys
import logging
import pandas as pd
from datetime import datetime
import tempfile

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s [%(levelname)s] %(name)s: %(message)s"
)
logger = logging.getLogger("TBS_Golden_Format_Test")

# Add BTRUN to path
script_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, script_dir)

def test_tbs_golden_format_generator():
    """Test the TBS multi-strategy golden format generator"""
    logger.info("🧪 Testing TBS Multi-Strategy Golden Format Generator")
    
    try:
        # Import golden format generator
        sys.path.append(os.path.join(script_dir, 'utils'))
        from golden_format_excel_generator import GoldenFormatExcelGenerator
        
        generator = GoldenFormatExcelGenerator()
        logger.info("✅ TBS golden format generator imported successfully")
        
        # Create test data
        portfolio_trans_df = pd.DataFrame({
            'entry_date': ['2024-01-03'] * 100,
            'exit_date': ['2024-01-03'] * 100,
            'pnl': [1000.0, -500.0] * 50,
            'symbol': ['NIFTY'] * 100,
            'strategy': ['H1', 'H2'] * 50
        })
        
        strategy_data = {
            'H1': {
                'transactions': portfolio_trans_df[:50],
                'total_trades': 50,
                'total_pnl': 25000.0,
                'strategy_type': 'TBS'
            },
            'H2': {
                'transactions': portfolio_trans_df[50:],
                'total_trades': 50,
                'total_pnl': 25000.0,
                'strategy_type': 'TBS'
            }
        }
        
        tbs_settings = {
            'portfolio_file': 'test_portfolio.xlsx',
            'strategy_type': 'TBS',
            'symbol': 'NIFTY',
            'total_portfolios': 2
        }
        
        # Test TBS multi-strategy golden format generation
        with tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False) as tmp:
            output_path = tmp.name
        
        success = generator.generate_tbs_multi_strategy_golden_format(
            portfolio_trans_df=portfolio_trans_df,
            strategy_data=strategy_data,
            tbs_settings=tbs_settings,
            output_path=output_path
        )
        
        if success and os.path.exists(output_path):
            # Verify the file structure
            xl = pd.ExcelFile(output_path)
            sheet_names = xl.sheet_names
            
            logger.info(f"✅ TBS golden format generated with {len(sheet_names)} sheets")
            
            # Check for required base sheets
            base_sheets = ['PortfolioParameter', 'GeneralParameter', 'LegParameter', 'Metrics', 'Max Profit and Loss']
            base_sheets_found = all(sheet in sheet_names for sheet in base_sheets)
            
            # Check for portfolio sheets
            portfolio_sheets = ['PORTFOLIO Trans', 'PORTFOLIO Results']
            portfolio_sheets_found = all(sheet in sheet_names for sheet in portfolio_sheets)
            
            # Check for strategy sheets
            strategy_sheets_found = any(sheet.startswith('H') and 'Trans' in sheet for sheet in sheet_names)
            
            if base_sheets_found and portfolio_sheets_found and strategy_sheets_found:
                logger.info("✅ TBS multi-strategy golden format test passed")
                os.unlink(output_path)
                return True
            else:
                logger.error(f"❌ Missing required sheets. Found: {sheet_names}")
                os.unlink(output_path)
                return False
        else:
            logger.error("❌ TBS golden format generation test failed")
            return False
            
    except Exception as e:
        logger.error(f"❌ TBS golden format generator test failed: {e}")
        return False

def test_tbs_btrun_functions():
    """Test BTRunPortfolio_GPU.py TBS golden format functions"""
    logger.info("🧪 Testing BTRunPortfolio_GPU.py TBS Functions")
    
    try:
        # Test function imports
        from BTRunPortfolio_GPU import _prepare_tbs_settings_from_result, _prepare_tbs_strategy_data_from_result
        logger.info("✅ BTRunPortfolio_GPU.py TBS functions imported successfully")
        
        # Test data preparation functions
        portfolio_excel_path = "test_portfolio.xlsx"
        parsed_portfolio_models = {
            'Portfolio1': {'name': 'Portfolio1'},
            'Portfolio2': {'name': 'Portfolio2'}
        }
        
        settings = _prepare_tbs_settings_from_result(portfolio_excel_path, parsed_portfolio_models)
        
        combined_result = {
            'transaction_dfs': {
                'portfolio': pd.DataFrame({
                    'pnl': [1000.0, -500.0],
                    'strategy': ['H1', 'H2']
                })
            }
        }
        
        transaction_dfs = combined_result['transaction_dfs']
        strategy_data = _prepare_tbs_strategy_data_from_result(combined_result, transaction_dfs)
        
        if len(settings) > 0 and len(strategy_data) >= 0:
            logger.info("✅ BTRunPortfolio_GPU.py TBS functions test passed")
            return True
        else:
            logger.error("❌ BTRunPortfolio_GPU.py TBS functions test failed")
            return False
            
    except Exception as e:
        logger.error(f"❌ BTRunPortfolio_GPU.py TBS functions test failed: {e}")
        return False

def test_tbs_32_column_format():
    """Test TBS 32-column transaction format"""
    logger.info("🧪 Testing TBS 32-Column Transaction Format")
    
    try:
        # Import golden format generator
        sys.path.append(os.path.join(script_dir, 'utils'))
        from golden_format_excel_generator import GoldenFormatExcelGenerator
        
        generator = GoldenFormatExcelGenerator()
        
        # Create test transaction data
        test_trans_df = pd.DataFrame({
            'entry_date': ['2024-01-03'],
            'exit_date': ['2024-01-03'],
            'pnl': [1000.0],
            'symbol': ['NIFTY'],
            'strike': [22000],
            'option_type': ['CE']
        })
        
        # Test 32-column conversion
        golden_trans_df = generator._convert_to_32_column_format(test_trans_df)
        
        if len(golden_trans_df.columns) >= 32:
            logger.info(f"✅ TBS 32-column format test passed ({len(golden_trans_df.columns)} columns)")
            return True
        else:
            logger.error(f"❌ TBS 32-column format test failed ({len(golden_trans_df.columns)} columns)")
            return False
            
    except Exception as e:
        logger.error(f"❌ TBS 32-column format test failed: {e}")
        return False

def test_tbs_day_wise_results():
    """Test TBS day-wise results format"""
    logger.info("🧪 Testing TBS Day-wise Results Format")
    
    try:
        # Import golden format generator
        sys.path.append(os.path.join(script_dir, 'utils'))
        from golden_format_excel_generator import GoldenFormatExcelGenerator
        
        generator = GoldenFormatExcelGenerator()
        
        # Create test data with different weekdays
        test_data = pd.DataFrame({
            'entry_date': ['2024-01-01', '2024-01-02', '2024-01-03', '2024-01-04', '2024-01-05'],  # Mon-Fri
            'pnl': [1000, -500, 750, -250, 500]
        })
        
        # Test day-wise results creation
        results_df = generator._create_tbs_day_wise_results(test_data, 'TEST')
        
        # Check if it has the required columns
        required_columns = ['Year', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Total']
        has_required_columns = all(col in results_df.columns for col in required_columns)
        
        if has_required_columns and len(results_df) > 0:
            logger.info("✅ TBS day-wise results format test passed")
            return True
        else:
            logger.error(f"❌ TBS day-wise results format test failed. Columns: {list(results_df.columns)}")
            return False
            
    except Exception as e:
        logger.error(f"❌ TBS day-wise results format test failed: {e}")
        return False

def main():
    """Run all TBS golden format tests"""
    logger.info("🚀 Starting TBS Golden Format Integration Tests")
    logger.info("="*60)
    
    tests = [
        ("TBS Golden Format Generator", test_tbs_golden_format_generator),
        ("BTRunPortfolio_GPU TBS Functions", test_tbs_btrun_functions),
        ("TBS 32-Column Format", test_tbs_32_column_format),
        ("TBS Day-wise Results", test_tbs_day_wise_results)
    ]
    
    results = []
    for test_name, test_func in tests:
        logger.info(f"\n📋 Running: {test_name}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            logger.error(f"💥 {test_name} crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    logger.info("\n" + "="*60)
    logger.info("📊 TBS GOLDEN FORMAT TEST RESULTS SUMMARY")
    logger.info("="*60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        logger.info(f"{status} - {test_name}")
        if result:
            passed += 1
    
    logger.info(f"\n🎯 Overall Result: {passed}/{total} tests passed")
    
    if passed == total:
        logger.info("🎉 ALL TBS GOLDEN FORMAT TESTS PASSED!")
        logger.info("✅ TBS Golden Format Integration Phase 2 is ready for deployment")
        return 0
    else:
        logger.error("❌ Some tests failed. Please review and fix issues before deployment.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
