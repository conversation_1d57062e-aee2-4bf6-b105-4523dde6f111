#!/usr/bin/env python3
"""
Final Integration Test - Phase 4 Completion
Tests all fixes and validates production readiness
"""
import os
import sys
import time
import json
import logging
from datetime import datetime
from typing import Dict, List, Any

# Setup Python path
PROJECT_ROOT = "/srv/samba/shared"
BTRUN_DIR = "/srv/samba/shared/bt/backtester_stable/BTRUN"

paths_to_add = [
    PROJECT_ROOT,
    BTRUN_DIR,
    os.path.join(PROJECT_ROOT, "bt"),
    os.path.join(PROJECT_ROOT, "bt", "backtester_stable"),
    os.path.join(PROJECT_ROOT, "bt", "backtester_stable", "BTRUN"),
]

for path in paths_to_add:
    if path not in sys.path:
        sys.path.insert(0, path)

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(message)s'
)
logger = logging.getLogger(__name__)

class FinalIntegrationTester:
    """Final integration test for Phase 4 completion"""
    
    def __init__(self):
        self.results = {}
        self.start_time = time.time()
        
    def test_database_connection(self) -> bool:
        """Test HeavyDB connection"""
        try:
            logger.info("🔍 Testing HeavyDB connection...")
            from heavyai import connect
            
            conn = connect(
                host='127.0.0.1',
                port=6274,
                user='admin',
                password='HyperInteractive',
                dbname='heavyai'
            )
            
            cursor = conn.cursor()
            cursor.execute("SELECT COUNT(*) FROM nifty_option_chain")
            count = cursor.fetchone()[0]
            
            logger.info(f"✅ HeavyDB connected - {count:,} rows available")
            cursor.close()
            conn.close()
            return True
            
        except Exception as e:
            logger.error(f"❌ HeavyDB connection failed: {e}")
            return False
    
    def test_input_file_migration(self) -> bool:
        """Test input file migration results"""
        try:
            logger.info("🔍 Testing input file migration...")
            
            # Check if migration backup directory exists
            backup_dir = "/srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/migration_backup"
            if not os.path.exists(backup_dir):
                logger.error("❌ Migration backup directory not found")
                return False
            
            # Count backup files
            backup_files = [f for f in os.listdir(backup_dir) if f.endswith('.backup_20250612')]
            logger.info(f"✓ Found {len(backup_files)} backup files from migration")
            
            # Test OI Excel file structure
            oi_file = "/srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/oi/bt_setting.xlsx"
            if os.path.exists(oi_file):
                import pandas as pd
                try:
                    portfolio_df = pd.read_excel(oi_file, sheet_name="PortfolioSetting")
                    if 'Enabled' in portfolio_df.columns:
                        logger.info("✓ OI Excel file has proper 'Enabled' column")
                        return True
                    else:
                        logger.error("❌ OI Excel file missing 'Enabled' column")
                        return False
                except Exception as e:
                    logger.error(f"❌ Error reading OI Excel file: {e}")
                    return False
            else:
                logger.error("❌ OI Excel file not found")
                return False
                
        except Exception as e:
            logger.error(f"❌ Input file migration test failed: {e}")
            return False
    
    def test_module_import_resolution(self) -> bool:
        """Test module import resolution"""
        try:
            logger.info("🔍 Testing module import resolution...")
            
            # Check if launchers directory exists
            launchers_dir = "/srv/samba/shared/bt/backtester_stable/BTRUN/launchers"
            if not os.path.exists(launchers_dir):
                logger.error("❌ Launchers directory not found")
                return False
            
            # Count launcher files
            launcher_files = [f for f in os.listdir(launchers_dir) if f.startswith('launch_') and f.endswith('.py')]
            logger.info(f"✓ Found {len(launcher_files)} strategy launchers")
            
            # Test basic imports
            try:
                from core import config
                logger.info("✓ Core config import successful")
            except ImportError as e:
                logger.error(f"❌ Core config import failed: {e}")
                return False
            
            try:
                from dal.heavydb_conn import get_conn
                logger.info("✓ HeavyDB connection import successful")
            except ImportError as e:
                logger.warning(f"⚠️ HeavyDB connection import failed: {e}")
                # This is not critical for the test
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Module import resolution test failed: {e}")
            return False
    
    def test_strategy_execution_readiness(self) -> Dict[str, Any]:
        """Test strategy execution readiness"""
        logger.info("🔍 Testing strategy execution readiness...")
        
        results = {
            "tv_ready": False,
            "tbs_ready": False,
            "oi_ready": False,
            "orb_ready": False
        }
        
        # Test TV strategy readiness
        try:
            tv_script = "/srv/samba/shared/bt/backtester_stable/BTRUN/BT_TV_GPU_aggregated_v4.py"
            if os.path.exists(tv_script):
                # Check if the script has proper imports
                with open(tv_script, 'r') as f:
                    content = f.read()
                    if 'PROJECT_ROOT = "/srv/samba/shared"' in content:
                        results["tv_ready"] = True
                        logger.info("✓ TV strategy script has proper imports")
                    else:
                        logger.warning("⚠️ TV strategy script missing proper imports")
            else:
                logger.error("❌ TV strategy script not found")
        except Exception as e:
            logger.error(f"❌ TV strategy test failed: {e}")
        
        # Test TBS strategy readiness
        try:
            tbs_script = "/srv/samba/shared/bt/backtester_stable/BTRUN/BTRunPortfolio_GPU.py"
            if os.path.exists(tbs_script):
                results["tbs_ready"] = True
                logger.info("✓ TBS strategy script exists")
            else:
                logger.error("❌ TBS strategy script not found")
        except Exception as e:
            logger.error(f"❌ TBS strategy test failed: {e}")
        
        # Test OI strategy readiness
        try:
            oi_script = "/srv/samba/shared/bt/backtester_stable/BTRUN/BT_OI_GPU.py"
            oi_excel = "/srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/oi/bt_setting.xlsx"
            if os.path.exists(oi_script) and os.path.exists(oi_excel):
                results["oi_ready"] = True
                logger.info("✓ OI strategy script and Excel file exist")
            else:
                logger.error("❌ OI strategy files not found")
        except Exception as e:
            logger.error(f"❌ OI strategy test failed: {e}")
        
        # Test ORB strategy readiness
        try:
            orb_script = "/srv/samba/shared/bt/backtester_stable/BTRUN/BT_ORB_GPU.py"
            if os.path.exists(orb_script):
                results["orb_ready"] = True
                logger.info("✓ ORB strategy script exists")
            else:
                logger.warning("⚠️ ORB strategy script not found")
        except Exception as e:
            logger.error(f"❌ ORB strategy test failed: {e}")
        
        return results
    
    def test_golden_format_integration(self) -> bool:
        """Test golden format integration"""
        try:
            logger.info("🔍 Testing golden format integration...")
            
            # Check if golden format utilities exist
            utils_dir = "/srv/samba/shared/bt/backtester_stable/BTRUN/utils"
            if os.path.exists(utils_dir):
                golden_format_files = [f for f in os.listdir(utils_dir) if 'golden_format' in f.lower()]
                if golden_format_files:
                    logger.info(f"✓ Found {len(golden_format_files)} golden format files")
                    return True
                else:
                    logger.warning("⚠️ No golden format files found")
                    return False
            else:
                logger.warning("⚠️ Utils directory not found")
                return False
                
        except Exception as e:
            logger.error(f"❌ Golden format integration test failed: {e}")
            return False
    
    def run_comprehensive_test(self):
        """Run comprehensive final integration test"""
        logger.info("🚀 Starting Final Integration Test - Phase 4 Completion")
        logger.info("="*70)
        
        # Test 1: Database Connection
        db_result = self.test_database_connection()
        self.results["database_connection"] = db_result
        
        # Test 2: Input File Migration
        migration_result = self.test_input_file_migration()
        self.results["input_file_migration"] = migration_result
        
        # Test 3: Module Import Resolution
        import_result = self.test_module_import_resolution()
        self.results["module_import_resolution"] = import_result
        
        # Test 4: Strategy Execution Readiness
        strategy_results = self.test_strategy_execution_readiness()
        self.results["strategy_execution"] = strategy_results
        
        # Test 5: Golden Format Integration
        golden_format_result = self.test_golden_format_integration()
        self.results["golden_format_integration"] = golden_format_result
        
        # Generate final report
        self.generate_final_report()
    
    def generate_final_report(self):
        """Generate final integration test report"""
        total_time = time.time() - self.start_time
        
        logger.info(f"\n{'='*70}")
        logger.info("🎯 FINAL INTEGRATION TEST RESULTS")
        logger.info(f"{'='*70}")
        logger.info(f"Total execution time: {total_time:.2f}s")
        logger.info(f"Test date: {datetime.now().isoformat()}")
        
        # Core Infrastructure Tests
        logger.info(f"\n📊 CORE INFRASTRUCTURE TESTS:")
        logger.info(f"  Database Connection: {'✅ PASS' if self.results.get('database_connection') else '❌ FAIL'}")
        logger.info(f"  Input File Migration: {'✅ PASS' if self.results.get('input_file_migration') else '❌ FAIL'}")
        logger.info(f"  Module Import Resolution: {'✅ PASS' if self.results.get('module_import_resolution') else '❌ FAIL'}")
        logger.info(f"  Golden Format Integration: {'✅ PASS' if self.results.get('golden_format_integration') else '❌ FAIL'}")
        
        # Strategy Readiness Tests
        strategy_results = self.results.get("strategy_execution", {})
        logger.info(f"\n🎯 STRATEGY READINESS TESTS:")
        logger.info(f"  TV Strategy: {'✅ READY' if strategy_results.get('tv_ready') else '❌ NOT READY'}")
        logger.info(f"  TBS Strategy: {'✅ READY' if strategy_results.get('tbs_ready') else '❌ NOT READY'}")
        logger.info(f"  OI Strategy: {'✅ READY' if strategy_results.get('oi_ready') else '❌ NOT READY'}")
        logger.info(f"  ORB Strategy: {'✅ READY' if strategy_results.get('orb_ready') else '❌ NOT READY'}")
        
        # Calculate overall success rate
        core_tests = [
            self.results.get('database_connection', False),
            self.results.get('input_file_migration', False),
            self.results.get('module_import_resolution', False),
            self.results.get('golden_format_integration', False)
        ]
        
        strategy_tests = list(strategy_results.values())
        all_tests = core_tests + strategy_tests
        
        success_count = sum(all_tests)
        total_tests = len(all_tests)
        success_rate = (success_count / total_tests * 100) if total_tests > 0 else 0
        
        logger.info(f"\n📈 OVERALL RESULTS:")
        logger.info(f"  Tests Passed: {success_count}/{total_tests}")
        logger.info(f"  Success Rate: {success_rate:.1f}%")
        
        # Production readiness assessment
        if success_rate >= 90:
            logger.info(f"\n🎉 PRODUCTION READY!")
            logger.info("✅ System is ready for production deployment")
            logger.info("✅ All critical components are functional")
        elif success_rate >= 75:
            logger.info(f"\n⚠️ MOSTLY READY")
            logger.info("🔧 Minor fixes needed before production")
            logger.info("✅ Core infrastructure is solid")
        else:
            logger.info(f"\n❌ NOT READY")
            logger.info("🔧 Significant fixes needed before production")
            logger.info("⚠️ Review failed tests and implement fixes")
        
        # Save detailed results
        report_file = f"/tmp/final_integration_test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w') as f:
            json.dump({
                "test_date": datetime.now().isoformat(),
                "phase": "Phase 4 - Final Integration Test",
                "total_time": total_time,
                "success_rate": success_rate,
                "tests_passed": success_count,
                "total_tests": total_tests,
                "results": self.results
            }, f, indent=2)
        
        logger.info(f"\n📄 Detailed report saved to: {report_file}")


def main():
    """Main test runner"""
    tester = FinalIntegrationTester()
    tester.run_comprehensive_test()


if __name__ == "__main__":
    main()
