#!/usr/bin/env python3
"""
Real Archive Files Analysis

This script analyzes the actual archive output files to understand the exact
sheet generation patterns for different strategy types.
"""

import os
import sys
import logging
import pandas as pd
from pathlib import Path

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s [%(levelname)s] %(name)s: %(message)s"
)
logger = logging.getLogger("Real_Archive_Files_Analyzer")

def analyze_tbs_multi_file():
    """Analyze the actual TBS multi-strategy file"""
    logger.info("🔍 Analyzing Real TBS Multi-Strategy File")
    
    tbs_file = "/srv/samba/shared/enhanced-market-regime-optimizer-final-package-updated/data/input/Python_Multi_Zone_Files/Python multi.xlsx"
    
    if not os.path.exists(tbs_file):
        logger.error(f"TBS file not found: {tbs_file}")
        return None
    
    try:
        xl = pd.ExcelFile(tbs_file)
        sheet_names = xl.sheet_names
        
        logger.info(f"📊 Total Sheets: {len(sheet_names)}")
        
        # Analyze sheet patterns
        analysis = {
            'total_sheets': len(sheet_names),
            'sheet_names': sheet_names,
            'base_sheets': [],
            'trans_sheets': [],
            'results_sheets': [],
            'other_sheets': [],
            'strategy_names': set()
        }
        
        # Categorize sheets
        for sheet in sheet_names:
            if sheet in ['PortfolioParameter', 'GeneralParameter', 'LegParameter', 'Metrics', 'Max Profit and Loss']:
                analysis['base_sheets'].append(sheet)
            elif ' Trans' in sheet:
                analysis['trans_sheets'].append(sheet)
                strategy_name = sheet.replace(' Trans', '')
                analysis['strategy_names'].add(strategy_name)
            elif ' Results' in sheet:
                analysis['results_sheets'].append(sheet)
                strategy_name = sheet.replace(' Results', '')
                analysis['strategy_names'].add(strategy_name)
            else:
                analysis['other_sheets'].append(sheet)
        
        logger.info(f"📋 Base Sheets ({len(analysis['base_sheets'])}): {analysis['base_sheets']}")
        logger.info(f"📈 Transaction Sheets ({len(analysis['trans_sheets'])}): {analysis['trans_sheets'][:5]}...")
        logger.info(f"📊 Results Sheets ({len(analysis['results_sheets'])}): {analysis['results_sheets'][:5]}...")
        logger.info(f"📄 Other Sheets ({len(analysis['other_sheets'])}): {analysis['other_sheets']}")
        logger.info(f"🎯 Unique Strategies ({len(analysis['strategy_names'])}): {sorted(list(analysis['strategy_names']))[:10]}...")
        
        # Analyze strategy naming pattern
        h_strategies = [s for s in analysis['strategy_names'] if s.startswith('H') and s[1:].isdigit()]
        if h_strategies:
            h_numbers = [int(s[1:]) for s in h_strategies]
            logger.info(f"🔤 H-Pattern Range: H{min(h_numbers)} to H{max(h_numbers)} ({len(h_strategies)} strategies)")
        
        # Check for PORTFOLIO
        has_portfolio = 'PORTFOLIO' in analysis['strategy_names']
        logger.info(f"📁 Has PORTFOLIO: {has_portfolio}")
        
        # Verify the pattern
        expected_sheets = len(analysis['base_sheets']) + 2 * len(analysis['strategy_names'])
        actual_sheets = len(sheet_names)
        
        logger.info(f"🧮 Sheet Count Verification:")
        logger.info(f"   Expected: {len(analysis['base_sheets'])} base + 2×{len(analysis['strategy_names'])} strategies = {expected_sheets}")
        logger.info(f"   Actual: {actual_sheets}")
        logger.info(f"   Match: {'✅' if expected_sheets == actual_sheets else '❌'}")
        
        return analysis
        
    except Exception as e:
        logger.error(f"Error analyzing TBS file: {e}")
        return None

def analyze_tv_file():
    """Analyze the TV file"""
    logger.info("🔍 Analyzing Real TV File")
    
    tv_file = "/srv/samba/shared/enhanced-market-regime-optimizer-final-package-updated/data/input/Python_Multi_Zone_Files/Python (for Tv).xlsx"
    
    if not os.path.exists(tv_file):
        logger.error(f"TV file not found: {tv_file}")
        return None
    
    try:
        xl = pd.ExcelFile(tv_file)
        sheet_names = xl.sheet_names
        
        logger.info(f"📊 Total Sheets: {len(sheet_names)}")
        logger.info(f"📋 Sheet Names: {sheet_names}")
        
        # Analyze TV-specific patterns
        analysis = {
            'total_sheets': len(sheet_names),
            'sheet_names': sheet_names,
            'tv_specific_sheets': [],
            'strategy_sheets': [],
            'base_sheets': []
        }
        
        for sheet in sheet_names:
            if 'Tv' in sheet or 'TV' in sheet:
                analysis['tv_specific_sheets'].append(sheet)
            elif sheet in ['PortfolioParameter', 'GeneralParameter', 'LegParameter', 'Metrics', 'Max Profit and Loss']:
                analysis['base_sheets'].append(sheet)
            else:
                analysis['strategy_sheets'].append(sheet)
        
        logger.info(f"📺 TV-Specific Sheets: {analysis['tv_specific_sheets']}")
        logger.info(f"📋 Base Sheets: {analysis['base_sheets']}")
        logger.info(f"🎯 Strategy Sheets: {analysis['strategy_sheets']}")
        
        return analysis
        
    except Exception as e:
        logger.error(f"Error analyzing TV file: {e}")
        return None

def find_archive_output_files():
    """Find actual archive output files for analysis"""
    logger.info("🔍 Searching for Archive Output Files")
    
    search_paths = [
        "/srv/samba/shared/Trades",
        "/srv/samba/shared/test_results",
        "/srv/samba/shared/archive_outputs",
        "/srv/samba/shared/bt/archive/backtester_stable/BTRUN/Trades"
    ]
    
    archive_files = []
    
    for search_path in search_paths:
        if os.path.exists(search_path):
            for file in os.listdir(search_path):
                if file.endswith('.xlsx'):
                    full_path = os.path.join(search_path, file)
                    archive_files.append(full_path)
    
    logger.info(f"📁 Found {len(archive_files)} archive output files")
    
    if archive_files:
        # Analyze the most recent one
        latest_file = max(archive_files, key=os.path.getmtime)
        logger.info(f"📊 Analyzing Latest Archive Output: {os.path.basename(latest_file)}")
        
        try:
            xl = pd.ExcelFile(latest_file)
            sheet_names = xl.sheet_names
            
            logger.info(f"📋 Archive Output Sheets ({len(sheet_names)}): {sheet_names}")
            
            return {
                'file_path': latest_file,
                'sheet_names': sheet_names,
                'total_sheets': len(sheet_names)
            }
            
        except Exception as e:
            logger.warning(f"Could not analyze archive output: {e}")
    
    return None

def create_corrected_golden_format_structure():
    """Create corrected golden format structure based on real analysis"""
    logger.info("🎯 Creating Corrected Golden Format Structure")
    
    corrected_structure = {
        'universal_pattern': {
            'base_sheets': [
                'PortfolioParameter',
                'GeneralParameter', 
                'LegParameter',
                'Metrics',
                'Max Profit and Loss'
            ],
            'strategy_pattern': '[STRATEGY_NAME] Trans + [STRATEGY_NAME] Results',
            'formula': 'Total = Base(5) + Special + 2×(enabled_strategies)'
        },
        'strategy_specific': {
            'TV': {
                'special_sheets': ['Tv Setting', 'Tv Signals'],
                'strategy_naming': 'LONG_L, SHORT_S, or custom from signals',
                'typical_count': '5 base + 2 special + 2×2 strategies = 11 sheets (expandable to 16)'
            },
            'TBS': {
                'special_sheets': [],
                'strategy_naming': 'H1, H2, ..., HN + PORTFOLIO',
                'typical_count': '5 base + 2×(N+1) strategies = 5 + 2×81 = 167 sheets'
            },
            'OI': {
                'special_sheets': [],
                'strategy_naming': 'MAXOI, MAXCOI, etc.',
                'typical_count': '5 base + 2×(OI_strategies)'
            },
            'ORB': {
                'special_sheets': [],
                'strategy_naming': 'ORB_15MIN, ORB_30MIN, etc.',
                'typical_count': '5 base + 2×(ORB_timeframes)'
            }
        }
    }
    
    logger.info("🏗️ Corrected Golden Format Structure:")
    logger.info(f"   Universal Formula: {corrected_structure['universal_pattern']['formula']}")
    
    for strategy_type, config in corrected_structure['strategy_specific'].items():
        logger.info(f"   {strategy_type}: {config['typical_count']}")
    
    return corrected_structure

def main():
    """Run real archive files analysis"""
    logger.info("🚀 Starting Real Archive Files Analysis")
    logger.info("="*60)
    
    # Analyze real TBS multi-strategy file
    tbs_analysis = analyze_tbs_multi_file()
    
    # Analyze real TV file
    tv_analysis = analyze_tv_file()
    
    # Find and analyze archive outputs
    archive_analysis = find_archive_output_files()
    
    # Create corrected structure
    corrected_structure = create_corrected_golden_format_structure()
    
    # Summary
    logger.info("\n" + "="*60)
    logger.info("📊 REAL ARCHIVE FILES ANALYSIS COMPLETE")
    logger.info("="*60)
    
    if tbs_analysis:
        logger.info(f"✅ TBS Analysis: {tbs_analysis['total_sheets']} sheets with {len(tbs_analysis['strategy_names'])} strategies")
    
    if tv_analysis:
        logger.info(f"✅ TV Analysis: {tv_analysis['total_sheets']} sheets")
    
    if archive_analysis:
        logger.info(f"✅ Archive Output: {archive_analysis['total_sheets']} sheets")
    
    logger.info("\n🎯 CORRECTED UNDERSTANDING:")
    logger.info("1. Sheet count is DYNAMIC based on actual enabled strategies")
    logger.info("2. TBS: H1-H80 + PORTFOLIO = 81 strategies × 2 sheets + 5 base = 167 sheets")
    logger.info("3. TV: Variable based on signal directions + special sheets")
    logger.info("4. OI/ORB: Follow same pattern with strategy-specific naming")
    
    logger.info("\n✅ READY TO UPDATE GOLDEN FORMAT GENERATORS WITH REAL PATTERNS")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
