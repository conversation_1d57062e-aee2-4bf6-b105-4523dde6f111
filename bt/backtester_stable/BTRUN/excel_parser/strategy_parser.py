from __future__ import annotations

import os
from pathlib import Path
from typing import List
import datetime

import pandas as pd

from ..models import StrategyModel, LegModel, OptionType, TransactionType, StrikeRule, ExpiryRule
from ..models.common import hhmmss_to_time
from ..models.risk import RiskRule, RiskRuleType, NumberType
from ..models.indicator import IndicatorStrategyModel

# Re-export for integration compatibility
from .strategy import load_general_parameter, load_leg_parameter
from .to_models import build_strategy

LEG_STRIKE_MAP = {
    "ATM": StrikeRule.ATM,
    "OTM": StrikeRule.OTM,
    "ITM": StrikeRule.ITM,
    "FIXED": StrikeRule.FIXED,
    "DELTA": StrikeRule.DELTA,
    "PREMIUM": StrikeRule.PREMIUM,
    "DELTA_TARGET": StrikeRule.DELTA_TARGET,
    "PREMIUM_EQ": StrikeRule.PREMIUM_EQ,
    "ATM_WIDTH": StrikeRule.ATM_WIDTH,
    "STRADDLE_WIDTH": StrikeRule.STRADDLE_WIDTH,
    "ATM_MATCH": StrikeRule.ATM_MATCH,
    "ATM_DIFF": StrikeRule.ATM_DIFF,
    "PREMIUM_VARIABLE2": StrikeRule.PREMIUM_VARIABLE2,
    "BY_ATM_STRIKE": StrikeRule.BY_ATM_STRIKE,
    "BY_ATM_STRADDLE": StrikeRule.BY_ATM_STRADDLE,
    "BY_MATCH_PREMIUM": StrikeRule.BY_MATCH_PREMIUM,
    "BY_MIN_STRIKE_DIFF": StrikeRule.BY_MIN_STRIKE_DIFF,
    "BY_CLOSEST_PREMIUM": StrikeRule.BY_CLOSEST_PREMIUM,
    "BY_CLOSEST_DELTA": StrikeRule.BY_CLOSEST_DELTA,
    "BY_GE_PREMIUM": StrikeRule.BY_GE_PREMIUM,
    "BY_GE_DELTA": StrikeRule.BY_GE_DELTA,
    "BY_LE_PREMIUM": StrikeRule.BY_LE_PREMIUM,
    "BY_LE_DELTA": StrikeRule.BY_LE_DELTA,
    "BY_DYNAMIC_FACTOR": StrikeRule.BY_DYNAMIC_FACTOR,
    "BY_VALUE": StrikeRule.BY_VALUE,
}

EXPIRY_MAP = {
    # Current week variations
    "CURRENT": ExpiryRule.CURRENT_WEEK,
    "CW": ExpiryRule.CURRENT_WEEK,
    "CURRENT_WEEK": ExpiryRule.CURRENT_WEEK,
    "CURRENTWEEK": ExpiryRule.CURRENT_WEEK,
    # Next week variations
    "NEXT": ExpiryRule.NEXT_WEEK,
    "NW": ExpiryRule.NEXT_WEEK,
    "NEXT_WEEK": ExpiryRule.NEXT_WEEK,
    "NEXTWEEK": ExpiryRule.NEXT_WEEK,
    # Monthly variations
    "MONTHLY": ExpiryRule.MONTHLY,
    "MONTH": ExpiryRule.MONTHLY,
    "M": ExpiryRule.MONTHLY,
}

# Legacy mapping for W&Type and related fields (see archive Util.py:TGT_SL_ALIAS)
TGT_SL_ALIAS = {
    "POINT": "POINT", "POINTS": "POINT", "PERCENT": "PERCENTAGE", "PERCENTAGE": "PERCENTAGE", "INDEX POINT": "INDEX_POINTS",
    "INDEX PERCENTAGE": "INDEX_PERCENTAGE", "ABSOLUTE": "PREMIUM", "DELTA": "ABSOLUTE_DELTA"
}

def parse_strategy_excel(path: str | os.PathLike, multiplier: float = 1.0) -> List[StrategyModel]:
    """Parse GP/LP sheets of one strategy Excel, return StrategyModel list (1)."""
    path = Path(path)
    if not path.exists():
        raise FileNotFoundError(path)

    gp_df = pd.read_excel(path, sheet_name="GeneralParameter")
    lp_df = pd.read_excel(path, sheet_name="LegParameter")

    # --- Mapping enforcement: ensure all columns are mapped or explicitly ignored ---
    # Reference: docs/column_mapping.md
    allowed_gp_cols = {
        # All mapped or explicitly ignored columns from docs/column_mapping.md (GeneralParameter)
        "StrategyName", "MoveSlToCost", "Underlying", "Index", "Weekdays", "DTE", "StrikeSelectionTime", "StartTime", "LastEntryTime", "EndTime",
        "StrategyProfit", "StrategyLoss", "StrategyProfitReExecuteNo", "StrategyLossReExecuteNo", "StrategyTrailingType", "PnLCalTime", "LockPercent",
        "TrailPercent", "SqOff1Time", "SqOff1Percent", "SqOff2Time", "SqOff2Percent", "ProfitReaches", "LockMinProfitAt", "IncreaseInProfit",
        "TrailMinProfitBy", "TgtTrackingFrom", "TgtRegisterPriceFrom", "SlTrackingFrom", "SlRegisterPriceFrom", "PnLCalculationFrom",
        "ConsiderHedgePnLForStgyPnL", "StoplossCheckingInterval", "TargetCheckingInterval", "ReEntryCheckingInterval", "OnExpiryDayTradeNextExpiry",
        "Enabled", "PortfolioName", "StrategyType", "StrategyExcelFilePath", "isTickBt",
        # ORB-specific columns
        "OrbRangeStart", "OrbRangeEnd",
        # OI-specific columns
        "MaxOpenPositions",
        # Indicator-specific columns
        "Timeframe", "IndicatorBasedReEntry", "ChangeStrikeForIndicatorBasedReEntry", "EntryCombination", "ExitCombination",
        "ConsiderVwapForEntry", "VwapEntryCondition", "ConsiderVwapForExit", "VwapExitCondition",
        "ConsiderEMAForEntry", "EmaEntryCondition", "ConsiderEMAForExit", "EmaExitCondition", "EMAPeriod",
        "ConsiderSTForEntry", "StEntryCondition", "ConsiderSTForExit", "StExitCondition", "STPeriod", "STMultiplier",
        "ConsiderRSIForEntry", "RsiEntryCondition", "ConsiderRSIForExit", "RsiExitCondition", "RsiPeriod",
        "ConsiderVolSmaForEntry", "VolSmaEntryCondition", "ConsiderVolSmaForExit", "VolSmaExitCondition", "VolSMAPeriod"
    }
    allowed_lp_cols = {
        # All mapped or explicitly ignored columns from docs/column_mapping.md (LegParameter)
        "StrategyName", "IsIdle", "LegID", "Instrument", "Transaction", "Expiry", "W&Type", "W&TValue", "TrailW&T", "StrikeMethod", "MatchPremium",
        "StrikeValue", "StrikePremiumCondition", "SLType", "SLValue", "TGTType", "TGTValue", "TrailSLType", "SL_TrailAt", "SL_TrailBy", "Lots",
        "ReEntryType", "ReEnteriesCount", "OnEntry_OpenTradeOn", "OnEntry_SqOffTradeOff", "OnEntry_SqOffAllLegs", "OnEntry_OpenTradeDelay",
        "OnEntry_SqOffDelay", "OnExit_OpenTradeOn", "OnExit_SqOffTradeOff", "OnExit_SqOffAllLegs", "OnExit_OpenAllLegs", "OnExit_OpenTradeDelay",
        "OnExit_SqOffDelay", "OpenHedge", "HedgeStrikeMethod", "HedgeStrikeValue", "HedgeStrikePremiumCondition", "ConsiderEMAForEntry",
        "EmaEntryCondition", "ConsiderVwapForEntry", "VwapEntryCondition", "RsiEntryCondition", "EMAPeriod", "STPeriod", "StartTime", "EndTime",
        # Re-entry columns for ORB and other strategies
        "SL_ReEntryType", "SL_ReEntryNo", "TGT_ReEntryType", "TGT_ReEntryNo",
        # OI-specific columns
        "OiThreshold",
        # Some columns may appear in legacy or variant sheets
        "Index", "StrikeDistance",
        # Enabled column for leg-level filtering
        "Enabled"
    }
    # Check GeneralParameter columns
    for col in gp_df.columns:
        if col not in allowed_gp_cols:
            raise ValueError(f"Unmapped column in GeneralParameter: {col}. Please update docs/column_mapping.md and parser.")
    # Check LegParameter columns
    for col in lp_df.columns:
        if col not in allowed_lp_cols:
            raise ValueError(f"Unmapped column in LegParameter: {col}. Please update docs/column_mapping.md and parser.")

    # Legacy compatibility: if the GeneralParameter sheet lacks an "Enabled" column, assume all rows are enabled
    if "Enabled" not in gp_df.columns:
        gp_df["Enabled"] = "YES"
    gp_df = gp_df[gp_df["Enabled"].astype(str).str.upper() == "YES"].copy()
    if gp_df.empty:
        return []

    strategies: list[StrategyModel] = []
    for _, grow in gp_df.iterrows():
        sname = str(grow["StrategyName"])
        start_time = hhmmss_to_time(grow.get("StartTime", 0))
        end_time = hhmmss_to_time(grow.get("EndTime", 0))
        
        # Convert to string for passing to leg parser
        start_time_str = start_time.strftime("%H:%M:%S") if isinstance(start_time, datetime.time) else start_time
        end_time_str = end_time.strftime("%H:%M:%S") if isinstance(end_time, datetime.time) else end_time

        legs: list[LegModel] = []
        legs_df = lp_df[lp_df["StrategyName"] == sname]

        # Filter enabled legs if Enabled column exists
        if "Enabled" in legs_df.columns:
            legs_df = legs_df[legs_df["Enabled"].astype(str).str.upper() == "YES"].copy()

        for _, lrow in legs_df.iterrows():
            # Pass strategy-level times to leg parser
            leg = _parse_leg_row(lrow, multiplier, start_time_str, end_time_str)
            if leg:
                legs.append(leg)

        # capture extra columns not explicitly mapped, coerce all to str
        extra_params = {col: str(grow[col]) for col in grow.index if col not in (
            "StrategyName","StrategyType","Enabled","StartTime","EndTime","isTickBt"
        )}

        # Check if this is an indicator strategy and create IndicatorStrategyModel
        indicator_strategy = None
        evaluator = str(grow.get("StrategyType", "Tbs")).upper()
        if evaluator == "INDICATOR" or _has_indicator_columns(grow):
            try:
                indicator_strategy = IndicatorStrategyModel.from_excel_params(extra_params)
                extra_params["indicator_strategy"] = indicator_strategy.model_dump_json()
                logger.info(f"Created IndicatorStrategyModel for strategy {sname}: {indicator_strategy}")
            except Exception as e:
                logger.warning(f"Failed to create IndicatorStrategyModel for strategy {sname}: {e}")

        # Ensure strategy_excel_path is present
        strategy_excel_path = str(path)
        if "StrategyExcelFilePath" in grow and pd.notna(grow["StrategyExcelFilePath"]):
            strategy_excel_path = str(grow["StrategyExcelFilePath"])
        extra_params["strategy_excel_path"] = strategy_excel_path

        # Convert time objects to strings
        start_time_str = start_time.strftime("%H:%M:%S") if isinstance(start_time, datetime.time) else start_time
        end_time_str = end_time.strftime("%H:%M:%S") if isinstance(end_time, datetime.time) else end_time

        strategy = StrategyModel(
            strategy_name=sname,
            evaluator=str(grow.get("StrategyType", "Tbs")),
            is_tick_bt=str(grow.get("isTickBt", "NO")).upper() == "YES",
            entry_start=start_time_str,
            entry_end=end_time_str,
            legs=legs,
            extra_params=extra_params,
            strategy_excel_path=strategy_excel_path,
        )
        strategies.append(strategy)
    return strategies

def _to_hhmmss(val):
    """Convert various time formats to HH:MM:SS."""
    if pd.isna(val):
        return "00:00:00"
    
    if isinstance(val, str):
        # If already in HH:MM:SS format
        if ":" in val:
            return val
        # If string of digits, pad and format
        s = str(val).replace(" ", "").zfill(6)
        return f"{s[0:2]}:{s[2:4]}:{s[4:6]}"
    
    if isinstance(val, (int, float)):
        # Convert numeric HHMMSS to string
        s = str(int(val)).zfill(6)
        return f"{s[0:2]}:{s[2:4]}:{s[4:6]}"
    
    if isinstance(val, datetime.time):
        return val.strftime("%H:%M:%S")
    
    # For any other type, convert to string and try to format
    s = str(val).replace(" ", "").zfill(6)
    return f"{s[0:2]}:{s[2:4]}:{s[4:6]}"

def _parse_expiry_rule(expiry_raw: str) -> tuple[ExpiryRule | None, str | None]:
    """Parse expiry rule from raw string, handling case-insensitive variations.
    Returns (expiry_rule, expiry_bucket) tuple.
    """
    if pd.isna(expiry_raw):
        return ExpiryRule.CURRENT_WEEK, None
    
    expiry_str = str(expiry_raw).strip().upper()
    
    # Handle bucketed expiry first
    if expiry_str in ("CW", "NW", "CM", "NM"):
        # Map CW/NW to CURRENT_WEEK/NEXT_WEEK
        if expiry_str == "CW":
            return ExpiryRule.CURRENT_WEEK, expiry_str
        elif expiry_str == "NW":
            return ExpiryRule.NEXT_WEEK, expiry_str
        else:
            return None, expiry_str
    
    # Try direct enum lookup first (handles all variations defined in ExpiryRule)
    try:
        return ExpiryRule[expiry_str], None
    except KeyError:
        pass
    
    # Try common patterns
    if any(x in expiry_str for x in ["CURRENT", "CW"]):
        return ExpiryRule.CURRENT_WEEK, None
    elif any(x in expiry_str for x in ["NEXT", "NW"]):
        return ExpiryRule.NEXT_WEEK, None
    elif any(x in expiry_str for x in ["MONTH", "M"]):
        return ExpiryRule.MONTHLY, None
    
    # Default to CURRENT_WEEK
    print(f"Warning: Unknown expiry rule '{expiry_raw}', defaulting to CURRENT_WEEK")
    return ExpiryRule.CURRENT_WEEK, None

def _parse_leg_row(row, multiplier: float, default_start_time: str = "09:16:00", default_end_time: str = "15:15:00") -> LegModel | None:
    try:
        # Option type comes from Instrument, not W&Type
        instrument = str(row.get("Instrument", "")).strip().upper()
        if instrument in ("CE", "CALL"):
            option_type = OptionType.CALL
        elif instrument in ("PE", "PUT"):
            option_type = OptionType.PUT
        elif instrument in ("FUT", "FUTURE"):
            option_type = OptionType.FUT
        else:
            print(f"_parse_leg_row Warning: Unknown Instrument (option type): {instrument}. Skipping leg.\nRow: {row}")
            return None
        
        # Case-insensitive transaction type
        txn = TransactionType.BUY if str(row["Transaction"]).strip().upper() in ["BUY", "B"] else TransactionType.SELL
        
        # Case-insensitive strike method
        strike_method_raw = str(row.get("StrikeMethod", "ATM")).upper().strip()
        strike_distance = None
        strike_rule = None
        
        # --- Dynamic ITM/OTM Handling (archive convention: ITM1, OTM2, etc.) ---
        import re
        match = re.match(r"^(ITM|OTM)(\d+)$", strike_method_raw)
        if match:
            base, n = match.groups()
            strike_rule = LEG_STRIKE_MAP.get(base, None)
            if strike_rule is not None:
                strike_distance = int(n)
            else:
                strike_rule = StrikeRule.ATM
        else:
            match_legacy = re.match(r"^(\d+)(ITM|OTM)$", strike_method_raw)
            if match_legacy:
                n, base = match_legacy.groups()
                strike_rule = LEG_STRIKE_MAP.get(base, None)
                if strike_rule is not None:
                    strike_distance = int(n)
                else:
                    strike_rule = StrikeRule.ATM
            else:
                strike_rule = LEG_STRIKE_MAP.get(strike_method_raw, None)
                if strike_rule is None:
                    try:
                        strike_rule = StrikeRule[strike_method_raw]
                    except Exception:
                        strike_rule = StrikeRule.ATM
        
        # Case-insensitive expiry
        expiry_rule, expiry_bucket = _parse_expiry_rule(row.get("Expiry", "CURRENT"))
        
        lots = int(row.get("Lots", 1) * multiplier)
        
        # --- W&Type handling (legacy TGT_SL_ALIAS logic) ---
        wtype_raw = str(row.get("W&Type", "POINT")).strip().upper()
        number_type = TGT_SL_ALIAS.get(wtype_raw, "POINT")
        
        # Store number_type in extra_params for use in risk/SL/TGT logic
        # --- Indicator columns validation and condition parsing ---
        known_indicator_flags = {
            "ConsiderEMAForEntry", "ConsiderEMAForExit", "ConsiderVwapForEntry", "ConsiderVwapForExit",
            "ConsiderSTForEntry", "ConsiderSTForExit", "OpenHedge", "TrailW&T"
        }
        # Define all known condition columns to prevent warnings for them
        known_condition_columns = {
            "EmaEntryCondition", "EmaExitCondition", "VwapEntryCondition", "VwapExitCondition",
            "StEntryCondition", "StExitCondition", "RsiEntryCondition", "VolSmaEntryCondition", "VolSmaExitCondition",
            "StrikePremiumCondition", "HedgeStrikePremiumCondition"
        }
        indicator_params = ["EMAPeriod", "STPeriod"]
        parsed_indicator_conditions = {}
        extra_params = {"number_type": str(number_type)}  # Add number_type for downstream use, coerce to str
        
        for col in row.index:
            if col not in (
                "LegID","Index","W&Type","Instrument","Transaction","Lots","StrikeMethod","StrikeDistance","StrikeValue","Expiry","StartTime","EndTime"
            ):
                # Store known indicator flags and parameters directly
                if col in known_indicator_flags or col in indicator_params:
                    extra_params[col] = str(row[col])
                # Store known condition columns directly
                elif col in known_condition_columns:
                    condition_value = str(row[col]).strip().upper()
                    # For known conditions, including StrikePremiumCondition & HedgeStrikePremiumCondition, store them.
                    # Perform validation only for non-premium conditions if needed.
                    if col not in ["StrikePremiumCondition", "HedgeStrikePremiumCondition"]:
                        if condition_value not in ['ABOVE', 'BELOW', 'CROSSOVER_ABOVE', 'CROSSOVER_BELOW', 'CROSS_ABOVE', 'CROSS_BELOW', '<', '>', '=']:
                            if not (condition_value.startswith('<') or condition_value.startswith('>') or condition_value.startswith('=')):
                                print(f"Unsupported condition value '{condition_value}' for {col}. Will be stored as string.")
                    parsed_indicator_conditions[col] = condition_value
                    extra_params[col] = condition_value 
                # For any other column that looks like a condition or flag, store it silently
                elif col.startswith("Consider") or col.endswith("Condition"):
                    extra_params[col] = str(row[col])
                # All other columns are stored as extra_params
                else:
                    extra_params[col] = str(row[col])
        extra_params['_parsed_indicator_conditions'] = str(parsed_indicator_conditions) # Store parsed conditions as str for model
        if pd.notna(row.get("StrikeDistance", None)):
            strike_distance = int(row.get("StrikeDistance", 0))
        if option_type == OptionType.FUT:
            strike_rule = None
            strike_distance = None
            fixed_strike = None
            strike_value = None
        else:
            # For ATM strike method with an offset, store in extra_params for ATM offset logic
            if strike_rule == StrikeRule.ATM and pd.notna(row.get("StrikeValue", None)):
                # Store StrikeValue in extra_params for ATM offset
                extra_params["StrikeValue"] = str(row.get("StrikeValue", 0))
                fixed_strike = None
                strike_value = None
            elif strike_rule == StrikeRule.FIXED:
                # For FIXED strike method, use fixed_strike
                fixed_strike = float(row.get("StrikeValue", 0)) if pd.notna(row.get("StrikeValue", None)) else None
                strike_value = None
            else:
                # For other strike methods
                fixed_strike = None
                strike_value = float(row.get("StrikeValue", 0)) if pd.notna(row.get("StrikeValue", None)) else None
        risk_rules = []
        # Normalize SLType, TGTType, TrailSLType using TGT_SL_ALIAS (legacy logic)
        if pd.notna(row.get("SLType", None)) and pd.notna(row.get("SLValue", None)):
            sl_type_raw = str(row["SLType"]).strip().upper()
            sl_type_norm_value = TGT_SL_ALIAS.get(sl_type_raw, "POINT") # This is the target string VALUE
            sl_value = float(row["SLValue"])
            try:
                number_type_member = NumberType(sl_type_norm_value) # Get enum member by its value
                risk_rules.append(RiskRule(rule_type=RiskRuleType.STOPLOSS, number_type=number_type_member, value=sl_value))
            except ValueError:
                print(f"Unknown SLType value after normalization: '{sl_type_norm_value}'. Skipping SL risk rule.")
        if pd.notna(row.get("TGTType", None)) and pd.notna(row.get("TGTValue", None)):
            tgt_type_raw = str(row["TGTType"]).strip().upper()
            tgt_type_norm_value = TGT_SL_ALIAS.get(tgt_type_raw, "POINT") # This is the target string VALUE
            tgt_value = float(row["TGTValue"])
            try:
                number_type_member = NumberType(tgt_type_norm_value) # Get enum member by its value
                risk_rules.append(RiskRule(rule_type=RiskRuleType.TAKEPROFIT, number_type=number_type_member, value=tgt_value))
            except ValueError:
                print(f"Unknown TGTType value after normalization: '{tgt_type_norm_value}'. Skipping TGT risk rule.")
        if pd.notna(row.get("TrailSLType", None)) and pd.notna(row.get("SL_TrailAt", None)) and pd.notna(row.get("SL_TrailBy", None)):
            trail_type_raw = str(row["TrailSLType"]).strip().upper()
            trail_type_norm_value = TGT_SL_ALIAS.get(trail_type_raw, "POINT") # This is the target string VALUE
            trail_at = float(row["SL_TrailAt"])
            trail_by = float(row["SL_TrailBy"])
            try:
                number_type_member = NumberType(trail_type_norm_value) # Get enum member by its value
                risk_rules.append(RiskRule(rule_type=RiskRuleType.TRAIL, number_type=number_type_member, value=trail_at, params={"trail_by": trail_by}))
            except ValueError:
                print(f"Unknown TrailSLType value after normalization: '{trail_type_norm_value}'. Skipping TRAIL risk rule.")
        hedge_fields = [
            "OpenHedge", "HedgeStrikeMethod", "HedgeStrikeValue", "HedgeStrikePremiumCondition"
        ]
        reentry_fields = [
            "ReEntryType", "ReEnteriesCount"
        ]
        for field in hedge_fields + reentry_fields:
            if field in row.index and pd.notna(row[field]):
                val = row[field]
                extra_params[field] = str(val)
        
        # Use strategy-level times as defaults if StartTime or EndTime are missing/invalid
        entry_time_raw = row.get("StartTime")
        if pd.isna(entry_time_raw) or entry_time_raw == 0:
            entry_time_raw = default_start_time
        
        exit_time_raw = row.get("EndTime")
        if pd.isna(exit_time_raw) or exit_time_raw == 0:
            exit_time_raw = default_end_time
            
        entry_time = _to_hhmmss(entry_time_raw)
        exit_time = _to_hhmmss(exit_time_raw)
        
        # Force conversion if still not string (should not be needed if _to_hhmmss is robust)
        if not isinstance(entry_time, str) and hasattr(entry_time, 'strftime'):
            entry_time = entry_time.strftime("%H:%M:%S")
        if not isinstance(exit_time, str) and hasattr(exit_time, 'strftime'):
            exit_time = exit_time.strftime("%H:%M:%S")
        leg = LegModel(
            leg_id=str(row.get("LegID", "1")),
            index=str(row.get("Index", "NIFTY")).upper(),
            option_type=option_type,
            transaction=txn,
            lots=lots,
            strike_rule=strike_rule,
            strike_distance=strike_distance,
            strike_value=strike_value if 'strike_value' in locals() else None,
            fixed_strike=fixed_strike,
            expiry_rule=expiry_rule or ExpiryRule.CURRENT_WEEK,  # Default to CURRENT_WEEK if None
            entry_time=entry_time,
            exit_time=exit_time,
            risk_rules=risk_rules,
            extra_params=extra_params,
        )
        # Add expiry_bucket to extra_params if present
        if expiry_bucket:
            extra_params["expiry_bucket"] = expiry_bucket
        return leg
    except Exception as exc:
        print(f"_parse_leg_row Exception: {exc}\nRow: {row}")
        return None 

def _has_indicator_columns(row) -> bool:
    """Check if the row contains any indicator-related columns."""
    indicator_columns = [
        "ConsiderVwapForEntry", "ConsiderVwapForExit", "ConsiderEMAForEntry", "ConsiderEMAForExit",
        "ConsiderSTForEntry", "ConsiderSTForExit", "ConsiderRSIForEntry", "ConsiderRSIForExit",
        "ConsiderVolSmaForEntry", "ConsiderVolSmaForExit", "Timeframe", "EntryCombination", "ExitCombination"
    ]
    
    for col in indicator_columns:
        if col in row.index and pd.notna(row[col]) and str(row[col]).upper() == "YES":
            return True
    
    return False 