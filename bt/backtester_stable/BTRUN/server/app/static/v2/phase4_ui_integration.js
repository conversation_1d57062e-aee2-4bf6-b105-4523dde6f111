/**
 * Phase 4 UI Integration - Production Ready
 * Connects Phase 4 backend with existing UI frontend
 * Includes real-time WebSocket updates and comprehensive error handling
 */

class Phase4UIIntegration {
    constructor() {
        this.baseUrl = window.location.origin;
        this.websocket = null;
        this.currentJobId = null;
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 5;
        this.reconnectDelay = 3000;
        
        // Strategy endpoints mapping
        this.endpoints = {
            tv: '/api/v2/strategies/tv/backtest',
            tbs: '/api/v2/strategies/tbs/backtest', 
            oi: '/api/v2/strategies/oi/backtest',
            orb: '/api/v2/strategies/orb/backtest',
            pos: '/api/v2/strategies/pos/backtest',
            ml: '/api/v2/strategies/ml/backtest'
        };
        
        this.init();
    }
    
    init() {
        console.log('🚀 Phase 4 UI Integration initialized');
        this.setupEventListeners();
        this.initializeWebSocket();
        this.updateUIStatus('ready', 'Phase 4 Integration Ready');
    }
    
    setupEventListeners() {
        // Override existing form submissions
        const forms = document.querySelectorAll('form[data-strategy]');
        forms.forEach(form => {
            form.addEventListener('submit', (e) => {
                e.preventDefault();
                this.handleFormSubmission(form);
            });
        });
        
        // Add strategy type buttons if they exist
        const strategyButtons = document.querySelectorAll('[data-strategy-type]');
        strategyButtons.forEach(button => {
            button.addEventListener('click', (e) => {
                const strategyType = e.target.getAttribute('data-strategy-type');
                this.selectStrategy(strategyType);
            });
        });
    }
    
    async handleFormSubmission(form) {
        try {
            const strategyType = form.getAttribute('data-strategy') || this.detectStrategyType(form);
            
            if (!strategyType) {
                throw new Error('Strategy type not detected');
            }
            
            this.updateUIStatus('processing', `Submitting ${strategyType.toUpperCase()} backtest...`);
            
            // Prepare form data
            const formData = new FormData(form);
            const requestData = this.prepareRequestData(strategyType, formData);
            
            // Submit backtest
            const response = await this.submitBacktest(strategyType, requestData);
            
            if (response.success) {
                this.currentJobId = response.job_id;
                this.subscribeToJobUpdates(response.job_id);
                this.updateUIStatus('submitted', `Backtest submitted: ${response.job_id}`);
                this.showProgressBar(true);
            } else {
                throw new Error(response.message || 'Backtest submission failed');
            }
            
        } catch (error) {
            console.error('Form submission error:', error);
            this.updateUIStatus('error', `Error: ${error.message}`);
            this.showErrorMessage(error.message);
        }
    }
    
    prepareRequestData(strategyType, formData) {
        const data = {
            strategy_type: strategyType,
            test_mode: true, // Use test mode for Phase 4
            files: {},
            parameters: {}
        };
        
        // Extract files
        for (let [key, value] of formData.entries()) {
            if (value instanceof File) {
                data.files[key] = value;
            } else {
                data.parameters[key] = value;
            }
        }
        
        // Add default date range for testing
        if (!data.parameters.start_date) {
            data.parameters.start_date = '2024-01-03';
        }
        if (!data.parameters.end_date) {
            data.parameters.end_date = '2024-01-03';
        }
        
        return data;
    }
    
    async submitBacktest(strategyType, requestData) {
        const endpoint = this.endpoints[strategyType];
        if (!endpoint) {
            throw new Error(`Unknown strategy type: ${strategyType}`);
        }
        
        // Convert to FormData for file upload
        const formData = new FormData();
        
        // Add files
        for (let [key, file] of Object.entries(requestData.files)) {
            formData.append(key, file);
        }
        
        // Add parameters as JSON
        formData.append('parameters', JSON.stringify({
            strategy_type: requestData.strategy_type,
            test_mode: requestData.test_mode,
            ...requestData.parameters
        }));
        
        const response = await fetch(`${this.baseUrl}${endpoint}`, {
            method: 'POST',
            body: formData
        });
        
        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.message || `HTTP ${response.status}`);
        }
        
        return await response.json();
    }
    
    initializeWebSocket() {
        if (this.websocket) {
            this.websocket.close();
        }
        
        const wsUrl = `${window.location.protocol === 'https:' ? 'wss:' : 'ws:'}//${window.location.host}/api/v2/ws`;
        
        try {
            this.websocket = new WebSocket(wsUrl);
            
            this.websocket.onopen = () => {
                console.log('✅ WebSocket connected');
                this.reconnectAttempts = 0;
                this.updateConnectionStatus(true);
            };
            
            this.websocket.onmessage = (event) => {
                this.handleWebSocketMessage(JSON.parse(event.data));
            };
            
            this.websocket.onclose = () => {
                console.log('❌ WebSocket disconnected');
                this.updateConnectionStatus(false);
                this.attemptReconnect();
            };
            
            this.websocket.onerror = (error) => {
                console.error('WebSocket error:', error);
                this.updateConnectionStatus(false);
            };
            
        } catch (error) {
            console.error('WebSocket initialization error:', error);
            this.updateConnectionStatus(false);
        }
    }
    
    handleWebSocketMessage(data) {
        console.log('📨 WebSocket message:', data);
        
        switch (data.type) {
            case 'progress':
                this.updateProgress(data.data.progress, data.data.message);
                break;
                
            case 'status_update':
                this.updateJobStatus(data.status, data.message);
                break;
                
            case 'result_ready':
                this.handleResultReady(data.data);
                break;
                
            case 'error':
                this.handleJobError(data.message);
                break;
                
            case 'heartbeat':
                // Respond to heartbeat
                if (this.websocket.readyState === WebSocket.OPEN) {
                    this.websocket.send(JSON.stringify({ type: 'pong' }));
                }
                break;
        }
    }
    
    subscribeToJobUpdates(jobId) {
        if (this.websocket && this.websocket.readyState === WebSocket.OPEN) {
            this.websocket.send(JSON.stringify({
                type: 'subscribe_job',
                job_id: jobId
            }));
        }
    }
    
    updateProgress(percentage, message) {
        // Update progress bar
        const progressBar = document.querySelector('.progress-bar');
        if (progressBar) {
            progressBar.style.width = `${percentage}%`;
            progressBar.textContent = `${Math.round(percentage)}%`;
        }
        
        // Update progress message
        const progressMessage = document.querySelector('.progress-message');
        if (progressMessage) {
            progressMessage.textContent = message;
        }
        
        console.log(`📊 Progress: ${percentage}% - ${message}`);
    }
    
    updateJobStatus(status, message) {
        this.updateUIStatus(status, message);
        
        if (status === 'completed') {
            this.showProgressBar(false);
            this.updateUIStatus('success', 'Backtest completed successfully!');
        } else if (status === 'failed') {
            this.showProgressBar(false);
            this.updateUIStatus('error', `Backtest failed: ${message}`);
        }
    }
    
    handleResultReady(resultData) {
        console.log('🎉 Results ready:', resultData);
        
        // Show download button
        this.showDownloadButton(resultData.download_url);
        
        // Display results summary
        if (resultData.summary) {
            this.displayResultsSummary(resultData.summary);
        }
        
        this.updateUIStatus('completed', 'Results are ready for download');
    }
    
    handleJobError(errorMessage) {
        console.error('❌ Job error:', errorMessage);
        this.updateUIStatus('error', errorMessage);
        this.showProgressBar(false);
        this.showErrorMessage(errorMessage);
    }
    
    updateUIStatus(status, message) {
        // Update status indicator
        const statusElement = document.querySelector('.status-indicator');
        if (statusElement) {
            statusElement.className = `status-indicator status-${status}`;
            statusElement.textContent = message;
        }
        
        // Update main status message
        const statusMessage = document.querySelector('.status-message');
        if (statusMessage) {
            statusMessage.textContent = message;
        }
        
        console.log(`🔄 Status: ${status} - ${message}`);
    }
    
    updateConnectionStatus(connected) {
        const connectionIndicator = document.querySelector('.connection-indicator');
        if (connectionIndicator) {
            connectionIndicator.className = `connection-indicator ${connected ? 'connected' : 'disconnected'}`;
            connectionIndicator.textContent = connected ? 'Connected' : 'Disconnected';
        }
    }
    
    showProgressBar(show) {
        const progressContainer = document.querySelector('.progress-container');
        if (progressContainer) {
            progressContainer.style.display = show ? 'block' : 'none';
        }
    }
    
    showDownloadButton(downloadUrl) {
        const downloadContainer = document.querySelector('.download-container');
        if (downloadContainer) {
            downloadContainer.innerHTML = `
                <a href="${downloadUrl}" class="btn btn-success" download>
                    📥 Download Results
                </a>
            `;
            downloadContainer.style.display = 'block';
        }
    }
    
    showErrorMessage(message) {
        const errorContainer = document.querySelector('.error-container');
        if (errorContainer) {
            errorContainer.innerHTML = `
                <div class="alert alert-danger">
                    <strong>Error:</strong> ${message}
                </div>
            `;
            errorContainer.style.display = 'block';
        }
    }
    
    displayResultsSummary(summary) {
        const summaryContainer = document.querySelector('.results-summary');
        if (summaryContainer) {
            summaryContainer.innerHTML = `
                <div class="card">
                    <div class="card-header">
                        <h5>📊 Backtest Results Summary</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <p><strong>Total Trades:</strong> ${summary.total_trades || 'N/A'}</p>
                                <p><strong>Win Rate:</strong> ${summary.win_rate || 'N/A'}%</p>
                                <p><strong>Total PnL:</strong> ₹${summary.total_pnl || 'N/A'}</p>
                            </div>
                            <div class="col-md-6">
                                <p><strong>Max Drawdown:</strong> ${summary.max_drawdown || 'N/A'}%</p>
                                <p><strong>Sharpe Ratio:</strong> ${summary.sharpe_ratio || 'N/A'}</p>
                                <p><strong>Execution Time:</strong> ${summary.execution_time || 'N/A'}s</p>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            summaryContainer.style.display = 'block';
        }
    }
    
    detectStrategyType(form) {
        // Try to detect strategy type from form elements
        const strategyInput = form.querySelector('[name="strategy_type"]');
        if (strategyInput) {
            return strategyInput.value;
        }
        
        // Check form class or ID
        const formClass = form.className.toLowerCase();
        const formId = form.id.toLowerCase();
        
        for (let strategy of Object.keys(this.endpoints)) {
            if (formClass.includes(strategy) || formId.includes(strategy)) {
                return strategy;
            }
        }
        
        return null;
    }
    
    selectStrategy(strategyType) {
        console.log(`🎯 Selected strategy: ${strategyType}`);
        
        // Update UI to show selected strategy
        const strategyButtons = document.querySelectorAll('[data-strategy-type]');
        strategyButtons.forEach(button => {
            button.classList.remove('active');
            if (button.getAttribute('data-strategy-type') === strategyType) {
                button.classList.add('active');
            }
        });
        
        // Show relevant form
        const forms = document.querySelectorAll('form[data-strategy]');
        forms.forEach(form => {
            form.style.display = form.getAttribute('data-strategy') === strategyType ? 'block' : 'none';
        });
    }
    
    attemptReconnect() {
        if (this.reconnectAttempts < this.maxReconnectAttempts) {
            this.reconnectAttempts++;
            console.log(`🔄 Attempting to reconnect (${this.reconnectAttempts}/${this.maxReconnectAttempts})...`);
            
            setTimeout(() => {
                this.initializeWebSocket();
            }, this.reconnectDelay);
        } else {
            console.error('❌ Max reconnection attempts reached');
            this.updateUIStatus('error', 'Connection lost. Please refresh the page.');
        }
    }
    
    // Public API methods
    async testConnection() {
        try {
            const response = await fetch(`${this.baseUrl}/api/v2/health`);
            return response.ok;
        } catch (error) {
            return false;
        }
    }
    
    getCurrentJobId() {
        return this.currentJobId;
    }
    
    isConnected() {
        return this.websocket && this.websocket.readyState === WebSocket.OPEN;
    }
}

// Initialize Phase 4 UI Integration when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    window.phase4UI = new Phase4UIIntegration();
    
    // Add global error handler
    window.addEventListener('error', (event) => {
        console.error('Global error:', event.error);
        if (window.phase4UI) {
            window.phase4UI.updateUIStatus('error', 'An unexpected error occurred');
        }
    });
    
    console.log('🎉 Phase 4 UI Integration loaded successfully');
});

// Export for module usage
if (typeof module !== 'undefined' && module.exports) {
    module.exports = Phase4UIIntegration;
}
