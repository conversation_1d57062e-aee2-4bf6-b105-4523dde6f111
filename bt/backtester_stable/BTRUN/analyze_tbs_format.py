#!/usr/bin/env python3
"""
Analyze TBS Multi-Strategy Format

This script analyzes the Python multi.xlsx file to understand the exact 
TBS golden format requirements for 120+ sheet structure.
"""

import os
import sys
import pandas as pd
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s [%(levelname)s] %(name)s: %(message)s"
)
logger = logging.getLogger("TBS_Format_Analyzer")

def analyze_tbs_multi_format():
    """Analyze the TBS multi-strategy format from Python multi.xlsx"""
    
    # Path to the TBS multi-strategy file
    tbs_file = "/srv/samba/shared/enhanced-market-regime-optimizer-final-package-updated/data/input/Python_Multi_Zone_Files/Python multi.xlsx"
    
    if not os.path.exists(tbs_file):
        logger.error(f"TBS file not found: {tbs_file}")
        return None
    
    logger.info(f"📊 Analyzing TBS Multi-Strategy Format: {tbs_file}")
    
    try:
        # Read Excel file
        xl = pd.ExcelFile(tbs_file)
        sheet_names = xl.sheet_names
        
        logger.info(f"📋 Total Sheets: {len(sheet_names)}")
        
        # Analyze sheet structure
        sheet_analysis = {
            'total_sheets': len(sheet_names),
            'sheet_names': sheet_names,
            'trans_sheets': [],
            'results_sheets': [],
            'other_sheets': [],
            'strategy_pattern': {}
        }
        
        # Categorize sheets
        for sheet in sheet_names:
            if 'Trans' in sheet:
                sheet_analysis['trans_sheets'].append(sheet)
            elif 'Results' in sheet:
                sheet_analysis['results_sheets'].append(sheet)
            else:
                sheet_analysis['other_sheets'].append(sheet)
        
        logger.info(f"📈 Transaction Sheets: {len(sheet_analysis['trans_sheets'])}")
        logger.info(f"📊 Results Sheets: {len(sheet_analysis['results_sheets'])}")
        logger.info(f"📄 Other Sheets: {len(sheet_analysis['other_sheets'])}")
        
        # Analyze strategy pattern
        strategies = set()
        for sheet in sheet_analysis['trans_sheets']:
            if ' Trans' in sheet:
                strategy = sheet.replace(' Trans', '')
                strategies.add(strategy)
        
        logger.info(f"🎯 Unique Strategies: {len(strategies)}")
        logger.info(f"📝 Strategy Names: {sorted(list(strategies))}")
        
        # Analyze sample transaction sheet structure
        if sheet_analysis['trans_sheets']:
            sample_trans_sheet = sheet_analysis['trans_sheets'][0]
            logger.info(f"🔍 Analyzing Sample Transaction Sheet: {sample_trans_sheet}")
            
            try:
                sample_df = pd.read_excel(tbs_file, sheet_name=sample_trans_sheet)
                logger.info(f"📊 Sample Sheet Columns ({len(sample_df.columns)}): {list(sample_df.columns)}")
                logger.info(f"📈 Sample Sheet Rows: {len(sample_df)}")
                
                # Check if it matches the 32-column format
                if len(sample_df.columns) >= 32:
                    logger.info("✅ Matches 32+ column format requirement")
                else:
                    logger.warning(f"⚠️ Only {len(sample_df.columns)} columns (expected 32+)")
                    
            except Exception as e:
                logger.warning(f"Could not read sample sheet: {e}")
        
        # Analyze sample results sheet structure
        if sheet_analysis['results_sheets']:
            sample_results_sheet = sheet_analysis['results_sheets'][0]
            logger.info(f"🔍 Analyzing Sample Results Sheet: {sample_results_sheet}")
            
            try:
                sample_df = pd.read_excel(tbs_file, sheet_name=sample_results_sheet)
                logger.info(f"📊 Sample Results Columns ({len(sample_df.columns)}): {list(sample_df.columns)}")
                logger.info(f"📈 Sample Results Rows: {len(sample_df)}")
                    
            except Exception as e:
                logger.warning(f"Could not read sample results sheet: {e}")
        
        return sheet_analysis
        
    except Exception as e:
        logger.error(f"Error analyzing TBS format: {e}")
        return None

def analyze_current_tbs_output():
    """Analyze current TBS output format from BTRunPortfolio_GPU.py"""
    
    logger.info("🔍 Analyzing Current TBS Output Format")
    
    # Check if there are any existing TBS output files
    output_dirs = [
        "/srv/samba/shared/Trades",
        "/srv/samba/shared/test_results"
    ]
    
    tbs_files = []
    for output_dir in output_dirs:
        if os.path.exists(output_dir):
            for file in os.listdir(output_dir):
                if file.endswith('.xlsx') and ('portfolio' in file.lower() or 'tbs' in file.lower()):
                    tbs_files.append(os.path.join(output_dir, file))
    
    if tbs_files:
        logger.info(f"📁 Found {len(tbs_files)} existing TBS output files")
        
        # Analyze the most recent one
        latest_file = max(tbs_files, key=os.path.getmtime)
        logger.info(f"📊 Analyzing Latest TBS Output: {latest_file}")
        
        try:
            xl = pd.ExcelFile(latest_file)
            sheet_names = xl.sheet_names
            
            logger.info(f"📋 Current TBS Output Sheets ({len(sheet_names)}): {sheet_names}")
            
            # Check if it has the golden format structure
            expected_sheets = ['PortfolioParameter', 'GeneralParameter', 'LegParameter', 'Metrics']
            has_golden_format = all(sheet in sheet_names for sheet in expected_sheets)
            
            if has_golden_format:
                logger.info("✅ Current output has golden format base structure")
            else:
                logger.warning("⚠️ Current output missing golden format base structure")
                
        except Exception as e:
            logger.warning(f"Could not analyze current TBS output: {e}")
    else:
        logger.info("📁 No existing TBS output files found")

def main():
    """Main analysis function"""
    logger.info("🚀 Starting TBS Format Analysis")
    logger.info("="*60)
    
    # Analyze the target TBS multi-strategy format
    tbs_analysis = analyze_tbs_multi_format()
    
    if tbs_analysis:
        logger.info("\n" + "="*60)
        logger.info("📊 TBS MULTI-STRATEGY FORMAT ANALYSIS")
        logger.info("="*60)
        
        logger.info(f"🎯 Total Sheets Required: {tbs_analysis['total_sheets']}")
        logger.info(f"📈 Transaction Sheets: {len(tbs_analysis['trans_sheets'])}")
        logger.info(f"📊 Results Sheets: {len(tbs_analysis['results_sheets'])}")
        logger.info(f"📄 Other Sheets: {len(tbs_analysis['other_sheets'])}")
        
        # Extract strategy pattern
        strategies = set()
        for sheet in tbs_analysis['trans_sheets']:
            if ' Trans' in sheet:
                strategy = sheet.replace(' Trans', '')
                strategies.add(strategy)
        
        logger.info(f"\n🎯 STRATEGY PATTERN ANALYSIS:")
        logger.info(f"   📝 Unique Strategies: {len(strategies)}")
        logger.info(f"   📋 Strategy Names: {sorted(list(strategies))}")
        
        # Determine the pattern
        if len(strategies) > 0:
            sample_strategies = sorted(list(strategies))[:5]  # Show first 5
            logger.info(f"   📌 Sample Strategies: {sample_strategies}")
            
            # Check for H-pattern (H1, H2, H3, etc.)
            h_strategies = [s for s in strategies if s.startswith('H') and s[1:].isdigit()]
            if h_strategies:
                logger.info(f"   🔤 H-Pattern Strategies: {len(h_strategies)} (e.g., {sorted(h_strategies)[:5]})")
        
        logger.info(f"\n📋 GOLDEN FORMAT REQUIREMENTS:")
        logger.info(f"   🎯 Each strategy needs 2 sheets: [Strategy] Trans + [Strategy] Results")
        logger.info(f"   📈 Transaction sheets: 32+ column format")
        logger.info(f"   📊 Results sheets: Day-wise breakdown")
        logger.info(f"   📄 Additional sheets: {tbs_analysis['other_sheets']}")
    
    # Analyze current TBS output
    logger.info("\n" + "="*60)
    analyze_current_tbs_output()
    
    logger.info("\n" + "="*60)
    logger.info("🎉 TBS FORMAT ANALYSIS COMPLETE")
    logger.info("="*60)
    
    if tbs_analysis:
        logger.info("✅ TBS multi-strategy format requirements identified")
        logger.info("✅ Ready to implement TBS golden format generator")
        return 0
    else:
        logger.error("❌ Could not analyze TBS format")
        return 1

if __name__ == "__main__":
    sys.exit(main())
