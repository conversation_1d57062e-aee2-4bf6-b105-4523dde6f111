#!/usr/bin/env python3
import sys
import os
import pandas as pd
from datetime import datetime
import heavydb

# Add paths
sys.path.append('/srv/samba/shared')
sys.path.append('/srv/samba/shared/bt/backtester_stable')

def run_gpu_backtest():
    """Run GPU backtest with HeavyDB data."""
    
    print("Connecting to HeavyDB...")
    conn = heavydb.connect(
        host='localhost',
        port=6274,
        user='admin',
        password='HyperInteractive',
        dbname='heavyai'
    )
    cursor = conn.cursor()
    
    # Query for April 1-5, 2024 data
    query = """
    SELECT 
        trade_date,
        trade_time,
        expiry_date,
        strike,
        ce_close,
        pe_close,
        spot,
        ce_symbol,
        pe_symbol
    FROM nifty_option_chain
    WHERE trade_date >= '2024-04-01'
    AND trade_date <= '2024-04-05'
    AND expiry_date = '2024-04-04'
    AND expiry_bucket = 'CW'
    AND trade_time IN ('09:15:00', '15:25:00')
    ORDER BY trade_date, trade_time, strike
    """
    
    cursor.execute(query)
    results = cursor.fetchall()
    
    print(f"Found {len(results)} records")
    
    # Process trades
    trades = []
    current_date = None
    entry_data = {}
    
    for row in results:
        date = row[0]
        time = row[1]
        strike = row[3]
        
        if time.hour == 9 and time.minute == 15:  # Entry
            # Find ATM strike
            spot = row[6]
            if date != current_date:
                current_date = date
                # Find best ATM for this date
                atm_strike = round(spot / 50) * 50
                entry_data[date] = {
                    'atm_strike': atm_strike,
                    'ce_price': None,
                    'pe_price': None,
                    'ce_symbol': None,
                    'pe_symbol': None
                }
                
            if strike == entry_data[date]['atm_strike']:
                entry_data[date]['ce_price'] = row[4]
                entry_data[date]['pe_price'] = row[5]
                entry_data[date]['ce_symbol'] = row[7]
                entry_data[date]['pe_symbol'] = row[8]
                
        elif time.hour == 15 and time.minute == 25:  # Exit
            if date in entry_data and strike == entry_data[date]['atm_strike']:
                # CE trade
                if entry_data[date]['ce_price']:
                    trades.append({
                        'Date': date.strftime('%d_%m_%Y'),
                        'Time': 91500,
                        'Strategy': 'ATM_STRADDLE',
                        'Transaction': 'SELL',
                        'Symbol': entry_data[date]['ce_symbol'],
                        'Quantity': 100,
                        'Entry Price': float(entry_data[date]['ce_price']),
                        'Exit Date': date.strftime('%d_%m_%Y'),
                        'Exit Time': 152500,
                        'Exit Price': float(row[4]),
                        'P&L': (float(entry_data[date]['ce_price']) - float(row[4])) * 100,
                        'P&L %': ((float(entry_data[date]['ce_price']) - float(row[4])) / float(entry_data[date]['ce_price'])) * 100,
                        'Leg': 1
                    })
                
                # PE trade
                if entry_data[date]['pe_price']:
                    trades.append({
                        'Date': date.strftime('%d_%m_%Y'),
                        'Time': 91500,
                        'Strategy': 'ATM_STRADDLE',
                        'Transaction': 'SELL',
                        'Symbol': entry_data[date]['pe_symbol'],
                        'Quantity': 100,
                        'Entry Price': float(entry_data[date]['pe_price']),
                        'Exit Date': date.strftime('%d_%m_%Y'),
                        'Exit Time': 152500,
                        'Exit Price': float(row[5]),
                        'P&L': (float(entry_data[date]['pe_price']) - float(row[5])) * 100,
                        'P&L %': ((float(entry_data[date]['pe_price']) - float(row[5])) / float(entry_data[date]['pe_price'])) * 100,
                        'Leg': 2
                    })
    
    conn.close()
    
    # Save output
    output_file = '/srv/samba/shared/test_results/golden_output_gpu/gpu_golden_output.xlsx'
    
    if trades:
        with pd.ExcelWriter(output_file) as writer:
            pd.DataFrame(trades).to_excel(writer, sheet_name='PORTFOLIO Trans', index=False)
            
            # Add summary
            total_pnl = sum(t['P&L'] for t in trades)
            summary = pd.DataFrame([{
                'Total Trades': len(trades),
                'Total P&L': total_pnl,
                'Win Rate': sum(1 for t in trades if t['P&L'] > 0) / len(trades) * 100,
                'Average P&L': total_pnl / len(trades)
            }])
            summary.to_excel(writer, sheet_name='Summary', index=False)
            
        print(f"\nGenerated {len(trades)} trades")
        print(f"Total P&L: {total_pnl}")
        print(f"Output saved to: {output_file}")
    else:
        print("No trades generated")
        
    return output_file

if __name__ == "__main__":
    run_gpu_backtest()
