CONFIG.PY: Attempting to load config.py...
CONFIG.PY: Finished loading config.py.
CONFIG.PY: Attempting to load config.py...
CONFIG.PY: Finished loading config.py.
2025-05-28 13:44:12,042 - __main__ - DEBUG - Starting BTRunPortfolio_GPU.py – HeavyDB version
2025-05-28 13:44:12,042 - __main__ - DEBUG - CWD: /srv/samba/shared
2025-05-28 13:44:12,042 - __main__ - DEBUG - Python path: ['/srv/samba/shared/bt', '/srv/samba/shared/bt/backtester_stable/BTRUN', '/srv/samba/shared/bt/backtester_stable/BTRUN/BTRUN', '/srv/samba/shared/bt/backtester_stable/BTRUN/strategies/../models', '/srv/samba/shared', '/srv/samba/shared', '/usr/lib/python310.zip', '/usr/lib/python3.10', '/usr/lib/python3.10/lib-dynload', '/home/<USER>/.local/lib/python3.10/site-packages', '/usr/local/lib/python3.10/dist-packages', '/srv/samba/shared/excel-mcp-server/src', '/usr/lib/python3/dist-packages', '/srv/samba/shared']
2025-05-28 13:44:12,042 - __main__ - DEBUG - HeavyDB host: 127.0.0.1, DB: heavyai
2025-05-28 13:44:12,042 - __main__ - DEBUG - GPU enabled: False
2025-05-28 13:44:12,044 - __main__ - INFO - GPU acceleration enabled: False
2025-05-28 13:44:12,044 - __main__ - INFO - HeavyDB integration available (Host: 127.0.0.1)
2025-05-28 13:44:12,263 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-28 13:44:12,263 - __main__ - INFO - Successfully connected to HeavyDB
2025-05-28 13:44:12,264 - __main__ - INFO - Runtime flags – workers: 1, retry_cpu: False
2025-05-28 13:44:12,264 - backtester_stable.BTRUN.core.utils - INFO - Running necessary functions before starting BT...
2025-05-28 13:44:12,264 - backtester_stable.BTRUN.core.config - INFO - Found fixture 'LOTSIZE.csv' at /srv/samba/shared/LOTSIZE.csv
2025-05-28 13:44:12,264 - backtester_stable.BTRUN.core.utils - INFO - Loading lot sizes from: /srv/samba/shared/LOTSIZE.csv
2025-05-28 13:44:12,269 - backtester_stable.BTRUN.core.utils - INFO - Successfully populated config.LOT_SIZE: 50 entries.
2025-05-28 13:44:12,270 - backtester_stable.BTRUN.core.utils - INFO - Populating margin info using data_fetchers.get_margin_symbol_info...
2025-05-28 13:44:12,270 - backtester_stable.BTRUN.core.data_fetchers - DEBUG - Margin symbol info cache file path: bt/backtester_stable/BTRUN/input_sheets/margin_symbol_info.json
2025-05-28 13:44:12,270 - backtester_stable.BTRUN.core.data_fetchers - INFO - Loading margin symbol info from cache: bt/backtester_stable/BTRUN/input_sheets/margin_symbol_info.json
2025-05-28 13:44:12,270 - backtester_stable.BTRUN.core.data_fetchers - INFO - Successfully loaded margin info from cache for 16 underlyings.
2025-05-28 13:44:12,270 - backtester_stable.BTRUN.core.utils - INFO - Successfully populated MARGIN_INFO using data_fetchers for 16 underlyings.
2025-05-28 13:44:12,270 - backtester_stable.BTRUN.core.utils - INFO - Pre-BT functions complete.
2025-05-28 13:44:12,270 - __main__ - INFO - Modern utils.run_necessary_functions_before_starting_bt() called.
2025-05-28 13:44:12,270 - __main__ - INFO - Using modern Excel parsing via excel_parser.portfolio_parser
2025-05-28 13:44:12,270 - __main__ - INFO - Using portfolio Excel from CLI argument: /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/input_portfolio.xlsx
2025-05-28 13:44:12,270 - __main__ - INFO - Set config.INPUT_FILE_FOLDER to: /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets
2025-05-28 13:44:12,270 - __main__ - INFO - Parsing main portfolio Excel: /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/input_portfolio.xlsx using modern parser. INPUT_FILE_FOLDER is currently: /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets
2025-05-28 13:44:12,393 - __main__ - INFO - Successfully parsed 1 portfolio(s) using modern parser.
2025-05-28 13:44:12,393 - __main__ - INFO - Queueing portfolio: NIF0DTE (from modern_portfolio_parser)
2025-05-28 13:44:12,393 - __main__ - INFO - Executing 1 portfolios sequentially (multiprocessing temporarily disabled for debugging)
2025-05-28 13:44:12,394 - __main__ - INFO - Processing payload 1/1: NIF0DTE
[DEBUG] run_full_backtest bt_params keys: ['portfolio_model', 'portfolio_name', 'start_date', 'end_date']
2025-05-28 13:44:12,394 - backtester_stable.BTRUN.core.runtime - INFO - Fetching trades from local HeavyDB for portfolio 'NIF0DTE'
2025-05-28 13:44:12,614 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Successfully connected to HeavyDB using pyheavydb.
2025-05-28 13:44:12,649 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.034s, returned 563 rows. Query: SELECT SUBSTRING('SELECT DISTINCT trade_date FROM nifty_option_chain', 1, 200) ...
2025-05-28 13:44:12,690 - backtester_stable.BTRUN.core.heavydb_data_access - INFO - Cached 563 distinct trade dates
2025-05-28 13:44:12,690 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] portfolio_model_dict keys: ['portfolio_name', 'start_date', 'end_date', 'slippage_percent', 'margin_multiplier', 'is_tick_bt', 'strategies', 'extra_params']
2025-05-28 13:44:12,691 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Starting to process leg 1 for strategy RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL
2025-05-28 13:44:12,691 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Entry SQL for leg 1:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2024-01-03' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike AND oc.ce_symbol IS NOT NULL AND oc.trade_time >= TIME '09:16:00'
ORDER BY oc.trade_time ASC
LIMIT 1
2025-05-28 13:44:12,691 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Exit SQL for leg 1:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2024-01-03' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike AND oc.ce_symbol IS NOT NULL AND oc.trade_time <= TIME '12:00:00'
ORDER BY oc.trade_time DESC
LIMIT 1
2025-05-28 13:44:12,731 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.040s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2024-01-03'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike AND oc.ce_symbol IS NOT NULL AND oc.trade_time >= TIME ''09:16', 1, 200) ...
2025-05-28 13:44:12,769 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.038s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2024-01-03'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike AND oc.ce_symbol IS NOT NULL AND oc.trade_time <= TIME ''12:00', 1, 200) ...
2025-05-28 13:44:12,769 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 1 - Query results: entry_df size=1, exit_df size=1
2025-05-28 13:44:12,770 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 1 - Got entry and exit data, proceeding...
2025-05-28 13:44:12,771 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 1 - Entry DF (ent):
trade_date                   2024-01-03
trade_time                     09:16:00
expiry_date                  2024-01-04
index_name                        NIFTY
spot                           21615.75
atm_strike                      21600.0
strike                          21600.0
dte                                   1
expiry_bucket                        CW
zone_id                               1
zone_name                          OPEN
call_strike_type                    ATM
put_strike_type                     ATM
ce_symbol           NIFTY04JAN2421600CE
ce_open                            88.5
ce_high                           89.95
ce_low                             87.0
ce_close                           89.7
ce_volume                       1328300
ce_oi                           3474200
ce_coi                                0
ce_iv                             13.19
ce_delta                           0.55
ce_gamma                         0.0021
ce_theta                         -26.25
ce_vega                            5.73
ce_rho                          53.6298
pe_symbol           NIFTY04JAN2421600PE
pe_open                            77.2
pe_high                            77.2
pe_low                             74.0
pe_close                          74.95
pe_volume                       2019050
pe_oi                           5942000
pe_coi                                0
pe_iv                             15.06
pe_delta                          -0.45
pe_gamma                         0.0018
pe_theta                          -23.6
pe_vega                            5.75
pe_rho                         -44.3187
future_open                     21706.1
future_high                     21710.0
future_low                     21705.05
future_close                    21708.0
future_volume                     47300
future_oi                      11836050
future_coi                            0
2025-05-28 13:44:12,771 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 1 - Exit DF (exi):
trade_date                   2024-01-03
trade_time                     12:00:00
expiry_date                  2024-01-04
index_name                        NIFTY
spot                            21572.3
atm_strike                      21600.0
strike                          21600.0
dte                                   1
expiry_bucket                        CW
zone_id                               2
zone_name                      MID_MORN
call_strike_type                    ATM
put_strike_type                     ATM
ce_symbol           NIFTY04JAN2421600CE
ce_open                           66.84
ce_high                           67.09
ce_low                            64.55
ce_close                          66.25
ce_volume                        751150
ce_oi                          11196100
ce_coi                                0
ce_iv                             12.96
ce_delta                           0.45
ce_gamma                         0.0021
ce_theta                         -25.39
ce_vega                            5.75
ce_rho                           44.766
pe_symbol           NIFTY04JAN2421600PE
pe_open                           66.95
pe_high                            68.9
pe_low                             66.3
pe_close                          66.84
pe_volume                        728900
pe_oi                          10438850
pe_coi                                0
pe_iv                              9.93
pe_delta                          -0.55
pe_gamma                         0.0028
pe_theta                         -14.06
pe_vega                            5.73
pe_rho                         -53.6056
future_open                     21680.0
future_high                     21683.0
future_low                      21677.3
future_close                   21679.05
future_volume                      4400
future_oi                      11893500
future_coi                         3700
2025-05-28 13:44:12,771 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 1 - ent.get('datetime'): None, ent.get('trade_time'): 09:16:00
2025-05-28 13:44:12,771 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 1 - exi.get('datetime'): None, exi.get('trade_time'): 12:00:00
2025-05-28 13:44:12,772 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 1 - Checking risk rules: has_rules=True, entry_empty=False, exit_empty=False
2025-05-28 13:44:12,772 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 1 - Entering risk rules block
2025-05-28 13:44:12,772 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Called for Symbol: NIFTY, LegInfo provided: True
2025-05-28 13:44:12,772 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: LegInfo OptionType raw: CALL, Type: <enum 'OptionType'>, ID: 140369525607328
2025-05-28 13:44:12,772 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Imported OptionType.CALL ID: 140369525607328, OptionType.PUT ID: 140369525607440
2025-05-28 13:44:12,772 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: leg_info.option_type in [OptionType.CALL, OptionType.PUT]? True
2025-05-28 13:44:12,772 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Determined: is_option_leg=True, option_type_str='ce'
2025-05-28 13:44:12,772 - backtester_stable.BTRUN.core.heavydb_data_access - INFO - GET_PRICE_DATA: Fetching OPTION data for NIFTY Type: CE
2025-05-28 13:44:12,774 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Executing Query: SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, ce_open AS option_open, ce_high AS option_high, ce_low AS option_low, ce_close AS option_close, COALESCE(ce_volume, 0) AS option_volume, ce_iv AS ce_iv, ce_delta AS ce_delta FROM nifty_option_chain
                       WHERE index_name = 'NIFTY'
                         AND trade_date >= '2024-01-03' AND trade_date <= '2024-01-03'
                       ORDER BY trade_date_alias, trade_time_alias, strike_alias
2025-05-28 13:44:13,099 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.325s, returned 23496 rows. Query: SELECT SUBSTRING('SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, ce_open AS option_open, ce_high AS option_high, ce_low AS option_low, ce_close AS option_close, COALESCE(', 1, 200) ...
2025-05-28 13:44:13,101 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Fetched 23496 rows. Columns: ['trade_date_alias', 'trade_time_alias', 'strike_alias', 'option_open', 'option_high', 'option_low', 'option_close', 'option_volume', 'ce_iv', 'ce_delta']
2025-05-28 13:44:13,102 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Columns after rename: ['date', 'time', 'strike', 'open', 'high', 'low', 'close', 'volume', 'ce_iv', 'ce_delta']
2025-05-28 13:44:13,171 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: DataFrame after drop_duplicates, before resampling: 375 rows.
2025-05-28 13:44:13,174 - debug_detail - WARNING - TRADE_PROC_DETAIL: Leg 1 - Tick data for specific strike 21600.0 is EMPTY after filtering windowed data.
2025-05-28 13:44:13,174 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 1 - Starting risk rules loop, has 3 rules
2025-05-28 13:44:13,174 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 1 - Processing risk rule 1 of 3
2025-05-28 13:44:13,175 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 1 - About to call evaluate_risk_rule for rule type STOPLOSS
2025-05-28 13:44:13,178 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 1 - evaluate_risk_rule returned: triggered=True
2025-05-28 13:44:13,180 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 1 - Breaking out of risk rules loop due to triggered rule
2025-05-28 13:44:13,180 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 1 - After risk rules block (or skipped it)
2025-05-28 13:44:13,180 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Starting to process leg 2 for strategy RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL
2025-05-28 13:44:13,180 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Entry SQL for leg 2:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2024-01-03' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike AND oc.pe_symbol IS NOT NULL AND oc.trade_time >= TIME '09:16:00'
ORDER BY oc.trade_time ASC
LIMIT 1
2025-05-28 13:44:13,180 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Exit SQL for leg 2:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2024-01-03' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike AND oc.pe_symbol IS NOT NULL AND oc.trade_time <= TIME '12:00:00'
ORDER BY oc.trade_time DESC
LIMIT 1
2025-05-28 13:44:13,203 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.023s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2024-01-03'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike AND oc.pe_symbol IS NOT NULL AND oc.trade_time >= TIME ''09:16', 1, 200) ...
2025-05-28 13:44:13,224 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.021s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2024-01-03'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike AND oc.pe_symbol IS NOT NULL AND oc.trade_time <= TIME ''12:00', 1, 200) ...
2025-05-28 13:44:13,225 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - Query results: entry_df size=1, exit_df size=1
2025-05-28 13:44:13,225 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - Got entry and exit data, proceeding...
2025-05-28 13:44:13,226 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 2 - Entry DF (ent):
trade_date                   2024-01-03
trade_time                     09:16:00
expiry_date                  2024-01-04
index_name                        NIFTY
spot                           21615.75
atm_strike                      21600.0
strike                          21600.0
dte                                   1
expiry_bucket                        CW
zone_id                               1
zone_name                          OPEN
call_strike_type                    ATM
put_strike_type                     ATM
ce_symbol           NIFTY04JAN2421600CE
ce_open                            88.5
ce_high                           89.95
ce_low                             87.0
ce_close                           89.7
ce_volume                       1328300
ce_oi                           3474200
ce_coi                                0
ce_iv                             13.19
ce_delta                           0.55
ce_gamma                         0.0021
ce_theta                         -26.25
ce_vega                            5.73
ce_rho                          53.6298
pe_symbol           NIFTY04JAN2421600PE
pe_open                            77.2
pe_high                            77.2
pe_low                             74.0
pe_close                          74.95
pe_volume                       2019050
pe_oi                           5942000
pe_coi                                0
pe_iv                             15.06
pe_delta                          -0.45
pe_gamma                         0.0018
pe_theta                          -23.6
pe_vega                            5.75
pe_rho                         -44.3187
future_open                     21706.1
future_high                     21710.0
future_low                     21705.05
future_close                    21708.0
future_volume                     47300
future_oi                      11836050
future_coi                            0
2025-05-28 13:44:13,226 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 2 - Exit DF (exi):
trade_date                   2024-01-03
trade_time                     12:00:00
expiry_date                  2024-01-04
index_name                        NIFTY
spot                            21572.3
atm_strike                      21600.0
strike                          21600.0
dte                                   1
expiry_bucket                        CW
zone_id                               2
zone_name                      MID_MORN
call_strike_type                    ATM
put_strike_type                     ATM
ce_symbol           NIFTY04JAN2421600CE
ce_open                           66.84
ce_high                           67.09
ce_low                            64.55
ce_close                          66.25
ce_volume                        751150
ce_oi                          11196100
ce_coi                                0
ce_iv                             12.96
ce_delta                           0.45
ce_gamma                         0.0021
ce_theta                         -25.39
ce_vega                            5.75
ce_rho                           44.766
pe_symbol           NIFTY04JAN2421600PE
pe_open                           66.95
pe_high                            68.9
pe_low                             66.3
pe_close                          66.84
pe_volume                        728900
pe_oi                          10438850
pe_coi                                0
pe_iv                              9.93
pe_delta                          -0.55
pe_gamma                         0.0028
pe_theta                         -14.06
pe_vega                            5.73
pe_rho                         -53.6056
future_open                     21680.0
future_high                     21683.0
future_low                      21677.3
future_close                   21679.05
future_volume                      4400
future_oi                      11893500
future_coi                         3700
2025-05-28 13:44:13,226 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 2 - ent.get('datetime'): None, ent.get('trade_time'): 09:16:00
2025-05-28 13:44:13,227 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 2 - exi.get('datetime'): None, exi.get('trade_time'): 12:00:00
2025-05-28 13:44:13,227 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - Checking risk rules: has_rules=True, entry_empty=False, exit_empty=False
2025-05-28 13:44:13,227 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - Entering risk rules block
2025-05-28 13:44:13,227 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Called for Symbol: NIFTY, LegInfo provided: True
2025-05-28 13:44:13,227 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: LegInfo OptionType raw: PUT, Type: <enum 'OptionType'>, ID: 140369525607440
2025-05-28 13:44:13,227 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Imported OptionType.CALL ID: 140369525607328, OptionType.PUT ID: 140369525607440
2025-05-28 13:44:13,227 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: leg_info.option_type in [OptionType.CALL, OptionType.PUT]? True
2025-05-28 13:44:13,227 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Determined: is_option_leg=True, option_type_str='pe'
2025-05-28 13:44:13,227 - backtester_stable.BTRUN.core.heavydb_data_access - INFO - GET_PRICE_DATA: Fetching OPTION data for NIFTY Type: PE
2025-05-28 13:44:13,229 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Executing Query: SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, pe_open AS option_open, pe_high AS option_high, pe_low AS option_low, pe_close AS option_close, COALESCE(pe_volume, 0) AS option_volume, pe_iv AS pe_iv, pe_delta AS pe_delta FROM nifty_option_chain
                       WHERE index_name = 'NIFTY'
                         AND trade_date >= '2024-01-03' AND trade_date <= '2024-01-03'
                       ORDER BY trade_date_alias, trade_time_alias, strike_alias
2025-05-28 13:44:13,515 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.286s, returned 23496 rows. Query: SELECT SUBSTRING('SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, pe_open AS option_open, pe_high AS option_high, pe_low AS option_low, pe_close AS option_close, COALESCE(', 1, 200) ...
2025-05-28 13:44:13,517 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Fetched 23496 rows. Columns: ['trade_date_alias', 'trade_time_alias', 'strike_alias', 'option_open', 'option_high', 'option_low', 'option_close', 'option_volume', 'pe_iv', 'pe_delta']
2025-05-28 13:44:13,518 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Columns after rename: ['date', 'time', 'strike', 'open', 'high', 'low', 'close', 'volume', 'pe_iv', 'pe_delta']
2025-05-28 13:44:13,585 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: DataFrame after drop_duplicates, before resampling: 375 rows.
2025-05-28 13:44:13,589 - debug_detail - WARNING - TRADE_PROC_DETAIL: Leg 2 - Tick data for specific strike 21600.0 is EMPTY after filtering windowed data.
2025-05-28 13:44:13,589 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - Starting risk rules loop, has 3 rules
2025-05-28 13:44:13,589 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - Processing risk rule 1 of 3
2025-05-28 13:44:13,589 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - About to call evaluate_risk_rule for rule type STOPLOSS
2025-05-28 13:44:13,600 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - evaluate_risk_rule returned: triggered=False
2025-05-28 13:44:13,600 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - Finished processing risk rule 1
2025-05-28 13:44:13,600 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - Processing risk rule 2 of 3
2025-05-28 13:44:13,600 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - About to call evaluate_risk_rule for rule type TAKEPROFIT
2025-05-28 13:44:13,610 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - evaluate_risk_rule returned: triggered=False
2025-05-28 13:44:13,610 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - Finished processing risk rule 2
2025-05-28 13:44:13,610 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - Processing risk rule 3 of 3
2025-05-28 13:44:13,611 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - About to call evaluate_risk_rule for rule type TRAIL
2025-05-28 13:44:13,613 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - evaluate_risk_rule returned: triggered=False
2025-05-28 13:44:13,613 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - Finished processing risk rule 3
2025-05-28 13:44:13,613 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 2 - After risk rules block (or skipped it)
2025-05-28 13:44:13,614 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Starting to process leg 3 for strategy RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL
2025-05-28 13:44:13,614 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Entry SQL for leg 3:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2024-01-03' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike + (2.0 * 50) AND oc.ce_symbol IS NOT NULL AND oc.trade_time >= TIME '09:16:00'
ORDER BY oc.trade_time ASC
LIMIT 1
2025-05-28 13:44:13,614 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Exit SQL for leg 3:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2024-01-03' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike + (2.0 * 50) AND oc.ce_symbol IS NOT NULL AND oc.trade_time <= TIME '12:00:00'
ORDER BY oc.trade_time DESC
LIMIT 1
2025-05-28 13:44:13,655 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.041s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2024-01-03'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike + (2.0 * 50) AND oc.ce_symbol IS NOT NULL AND oc.trade_time >=', 1, 200) ...
2025-05-28 13:44:13,678 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.023s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2024-01-03'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike + (2.0 * 50) AND oc.ce_symbol IS NOT NULL AND oc.trade_time <=', 1, 200) ...
2025-05-28 13:44:13,679 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 3 - Query results: entry_df size=1, exit_df size=1
2025-05-28 13:44:13,679 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 3 - Got entry and exit data, proceeding...
2025-05-28 13:44:13,680 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 3 - Entry DF (ent):
trade_date                   2024-01-03
trade_time                     09:16:00
expiry_date                  2024-01-04
index_name                        NIFTY
spot                           21615.75
atm_strike                      21600.0
strike                          21700.0
dte                                   1
expiry_bucket                        CW
zone_id                               1
zone_name                          OPEN
call_strike_type                   OTM2
put_strike_type                    ITM2
ce_symbol           NIFTY04JAN2421700CE
ce_open                            44.5
ce_high                           45.45
ce_low                            43.05
ce_close                           45.0
ce_volume                       1292500
ce_oi                           9159300
ce_coi                                0
ce_iv                              13.2
ce_delta                           0.37
ce_gamma                         0.0019
ce_theta                         -23.64
ce_vega                            5.38
ce_rho                          33.9451
pe_symbol           NIFTY04JAN2421700PE
pe_open                           133.0
pe_high                          133.19
pe_low                           129.19
pe_close                          130.4
pe_volume                       1135600
pe_oi                           4799150
pe_coi                                0
pe_iv                             15.21
pe_delta                          -0.63
pe_gamma                         0.0017
pe_theta                         -21.57
pe_vega                            5.48
pe_rho                          -61.946
future_open                     21706.1
future_high                     21710.0
future_low                     21705.05
future_close                    21708.0
future_volume                     47300
future_oi                      11836050
future_coi                            0
2025-05-28 13:44:13,680 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 3 - Exit DF (exi):
trade_date                   2024-01-03
trade_time                     12:00:00
expiry_date                  2024-01-04
index_name                        NIFTY
spot                            21572.3
atm_strike                      21600.0
strike                          21700.0
dte                                   1
expiry_bucket                        CW
zone_id                               2
zone_name                      MID_MORN
call_strike_type                   OTM2
put_strike_type                    ITM2
ce_symbol           NIFTY04JAN2421700CE
ce_open                            26.7
ce_high                           26.85
ce_low                             25.7
ce_close                           26.2
ce_volume                        478850
ce_oi                          13555800
ce_coi                                0
ce_iv                             12.07
ce_delta                           0.16
ce_gamma                         0.0018
ce_theta                         -18.42
ce_vega                            4.62
ce_rho                          24.3548
pe_symbol           NIFTY04JAN2421700PE
pe_open                          127.55
pe_high                           130.0
pe_low                            126.3
pe_close                         127.45
pe_volume                        215500
pe_oi                           3183950
pe_coi                                0
pe_iv                              8.04
pe_delta                          -0.84
pe_gamma                         0.0021
pe_theta                          -3.47
pe_vega                            3.48
pe_rho                         -82.5441
future_open                     21680.0
future_high                     21683.0
future_low                      21677.3
future_close                   21679.05
future_volume                      4400
future_oi                      11893500
future_coi                         3700
2025-05-28 13:44:13,680 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 3 - ent.get('datetime'): None, ent.get('trade_time'): 09:16:00
2025-05-28 13:44:13,680 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 3 - exi.get('datetime'): None, exi.get('trade_time'): 12:00:00
2025-05-28 13:44:13,681 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 3 - Checking risk rules: has_rules=True, entry_empty=False, exit_empty=False
2025-05-28 13:44:13,681 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 3 - Entering risk rules block
2025-05-28 13:44:13,681 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Called for Symbol: NIFTY, LegInfo provided: True
2025-05-28 13:44:13,681 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: LegInfo OptionType raw: CALL, Type: <enum 'OptionType'>, ID: 140369525607328
2025-05-28 13:44:13,681 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Imported OptionType.CALL ID: 140369525607328, OptionType.PUT ID: 140369525607440
2025-05-28 13:44:13,681 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: leg_info.option_type in [OptionType.CALL, OptionType.PUT]? True
2025-05-28 13:44:13,681 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Determined: is_option_leg=True, option_type_str='ce'
2025-05-28 13:44:13,681 - backtester_stable.BTRUN.core.heavydb_data_access - INFO - GET_PRICE_DATA: Fetching OPTION data for NIFTY Type: CE
2025-05-28 13:44:13,682 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Executing Query: SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, ce_open AS option_open, ce_high AS option_high, ce_low AS option_low, ce_close AS option_close, COALESCE(ce_volume, 0) AS option_volume, ce_iv AS ce_iv, ce_delta AS ce_delta FROM nifty_option_chain
                       WHERE index_name = 'NIFTY'
                         AND trade_date >= '2024-01-03' AND trade_date <= '2024-01-03'
                       ORDER BY trade_date_alias, trade_time_alias, strike_alias
2025-05-28 13:44:14,012 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.329s, returned 23496 rows. Query: SELECT SUBSTRING('SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, ce_open AS option_open, ce_high AS option_high, ce_low AS option_low, ce_close AS option_close, COALESCE(', 1, 200) ...
2025-05-28 13:44:14,014 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Fetched 23496 rows. Columns: ['trade_date_alias', 'trade_time_alias', 'strike_alias', 'option_open', 'option_high', 'option_low', 'option_close', 'option_volume', 'ce_iv', 'ce_delta']
2025-05-28 13:44:14,014 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Columns after rename: ['date', 'time', 'strike', 'open', 'high', 'low', 'close', 'volume', 'ce_iv', 'ce_delta']
2025-05-28 13:44:14,083 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: DataFrame after drop_duplicates, before resampling: 375 rows.
2025-05-28 13:44:14,086 - debug_detail - WARNING - TRADE_PROC_DETAIL: Leg 3 - Tick data for specific strike 21700.0 is EMPTY after filtering windowed data.
2025-05-28 13:44:14,086 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 3 - Starting risk rules loop, has 3 rules
2025-05-28 13:44:14,086 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 3 - Processing risk rule 1 of 3
2025-05-28 13:44:14,086 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 3 - About to call evaluate_risk_rule for rule type STOPLOSS
2025-05-28 13:44:14,097 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 3 - evaluate_risk_rule returned: triggered=False
2025-05-28 13:44:14,097 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 3 - Finished processing risk rule 1
2025-05-28 13:44:14,097 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 3 - Processing risk rule 2 of 3
2025-05-28 13:44:14,097 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 3 - About to call evaluate_risk_rule for rule type TAKEPROFIT
2025-05-28 13:44:14,100 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 3 - evaluate_risk_rule returned: triggered=True
2025-05-28 13:44:14,102 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 3 - Breaking out of risk rules loop due to triggered rule
2025-05-28 13:44:14,102 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 3 - After risk rules block (or skipped it)
2025-05-28 13:44:14,102 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Starting to process leg 4 for strategy RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL
2025-05-28 13:44:14,102 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Entry SQL for leg 4:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2024-01-03' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike - (2.0 * 50) AND oc.pe_symbol IS NOT NULL AND oc.trade_time >= TIME '09:16:00'
ORDER BY oc.trade_time ASC
LIMIT 1
2025-05-28 13:44:14,102 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Exit SQL for leg 4:
SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE '2024-01-03' AND oc.expiry_bucket = 'CW' AND oc.strike = oc.atm_strike - (2.0 * 50) AND oc.pe_symbol IS NOT NULL AND oc.trade_time <= TIME '12:00:00'
ORDER BY oc.trade_time DESC
LIMIT 1
2025-05-28 13:44:14,127 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.024s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2024-01-03'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike - (2.0 * 50) AND oc.pe_symbol IS NOT NULL AND oc.trade_time >=', 1, 200) ...
2025-05-28 13:44:14,150 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.023s, returned 1 rows. Query: SELECT SUBSTRING('SELECT * FROM nifty_option_chain oc
WHERE oc.trade_date = DATE ''2024-01-03'' AND oc.expiry_bucket = ''CW'' AND oc.strike = oc.atm_strike - (2.0 * 50) AND oc.pe_symbol IS NOT NULL AND oc.trade_time <=', 1, 200) ...
2025-05-28 13:44:14,150 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 4 - Query results: entry_df size=1, exit_df size=1
2025-05-28 13:44:14,150 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 4 - Got entry and exit data, proceeding...
2025-05-28 13:44:14,151 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 4 - Entry DF (ent):
trade_date                   2024-01-03
trade_time                     09:16:00
expiry_date                  2024-01-04
index_name                        NIFTY
spot                           21615.75
atm_strike                      21600.0
strike                          21500.0
dte                                   1
expiry_bucket                        CW
zone_id                               1
zone_name                          OPEN
call_strike_type                   ITM2
put_strike_type                    OTM2
ce_symbol           NIFTY04JAN2421500CE
ce_open                           152.6
ce_high                           155.3
ce_low                            151.8
ce_close                         154.35
ce_volume                        159000
ce_oi                           1485800
ce_coi                                0
ce_iv                             13.11
ce_delta                           0.72
ce_gamma                         0.0017
ce_theta                         -22.87
ce_vega                            4.64
ce_rho                          72.0855
pe_symbol           NIFTY04JAN2421500PE
pe_open                           41.75
pe_high                           41.75
pe_low                            39.15
pe_close                          39.75
pe_volume                        988650
pe_oi                           6403900
pe_coi                                0
pe_iv                             15.37
pe_delta                          -0.28
pe_gamma                         0.0015
pe_theta                         -21.31
pe_vega                            4.92
pe_rho                         -27.9001
future_open                     21706.1
future_high                     21710.0
future_low                     21705.05
future_close                    21708.0
future_volume                     47300
future_oi                      11836050
future_coi                            0
2025-05-28 13:44:14,152 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 4 - Exit DF (exi):
trade_date                   2024-01-03
trade_time                     12:00:00
expiry_date                  2024-01-04
index_name                        NIFTY
spot                            21572.3
atm_strike                      21600.0
strike                          21500.0
dte                                   1
expiry_bucket                        CW
zone_id                               2
zone_name                      MID_MORN
call_strike_type                   ITM2
put_strike_type                    OTM2
ce_symbol           NIFTY04JAN2421500CE
ce_open                          132.85
ce_high                          133.19
ce_low                           129.65
ce_close                         132.15
ce_volume                        203150
ce_oi                           3395250
ce_coi                                0
ce_iv                             14.73
ce_delta                           0.69
ce_gamma                         0.0017
ce_theta                         -27.79
ce_vega                            5.35
ce_rho                          62.7576
pe_symbol           NIFTY04JAN2421500PE
pe_open                            33.2
pe_high                            34.1
pe_low                            32.75
pe_close                          33.15
pe_volume                        387450
pe_oi                           9387350
pe_coi                                0
pe_iv                             11.49
pe_delta                          -0.31
pe_gamma                         0.0021
pe_theta                         -15.98
pe_vega                             5.1
pe_rho                         -30.2188
future_open                     21680.0
future_high                     21683.0
future_low                      21677.3
future_close                   21679.05
future_volume                      4400
future_oi                      11893500
future_coi                         3700
2025-05-28 13:44:14,152 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 4 - ent.get('datetime'): None, ent.get('trade_time'): 09:16:00
2025-05-28 13:44:14,152 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Leg 4 - exi.get('datetime'): None, exi.get('trade_time'): 12:00:00
2025-05-28 13:44:14,152 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 4 - Checking risk rules: has_rules=True, entry_empty=False, exit_empty=False
2025-05-28 13:44:14,152 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 4 - Entering risk rules block
2025-05-28 13:44:14,153 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Called for Symbol: NIFTY, LegInfo provided: True
2025-05-28 13:44:14,153 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: LegInfo OptionType raw: PUT, Type: <enum 'OptionType'>, ID: 140369525607440
2025-05-28 13:44:14,153 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Imported OptionType.CALL ID: 140369525607328, OptionType.PUT ID: 140369525607440
2025-05-28 13:44:14,153 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: leg_info.option_type in [OptionType.CALL, OptionType.PUT]? True
2025-05-28 13:44:14,153 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Determined: is_option_leg=True, option_type_str='pe'
2025-05-28 13:44:14,153 - backtester_stable.BTRUN.core.heavydb_data_access - INFO - GET_PRICE_DATA: Fetching OPTION data for NIFTY Type: PE
2025-05-28 13:44:14,154 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Executing Query: SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, pe_open AS option_open, pe_high AS option_high, pe_low AS option_low, pe_close AS option_close, COALESCE(pe_volume, 0) AS option_volume, pe_iv AS pe_iv, pe_delta AS pe_delta FROM nifty_option_chain
                       WHERE index_name = 'NIFTY'
                         AND trade_date >= '2024-01-03' AND trade_date <= '2024-01-03'
                       ORDER BY trade_date_alias, trade_time_alias, strike_alias
2025-05-28 13:44:14,450 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.296s, returned 23496 rows. Query: SELECT SUBSTRING('SELECT trade_date AS trade_date_alias, trade_time AS trade_time_alias, strike AS strike_alias, pe_open AS option_open, pe_high AS option_high, pe_low AS option_low, pe_close AS option_close, COALESCE(', 1, 200) ...
2025-05-28 13:44:14,452 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Fetched 23496 rows. Columns: ['trade_date_alias', 'trade_time_alias', 'strike_alias', 'option_open', 'option_high', 'option_low', 'option_close', 'option_volume', 'pe_iv', 'pe_delta']
2025-05-28 13:44:14,452 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: Columns after rename: ['date', 'time', 'strike', 'open', 'high', 'low', 'close', 'volume', 'pe_iv', 'pe_delta']
2025-05-28 13:44:14,520 - backtester_stable.BTRUN.core.heavydb_data_access - DEBUG - GET_PRICE_DATA: DataFrame after drop_duplicates, before resampling: 375 rows.
2025-05-28 13:44:14,524 - debug_detail - WARNING - TRADE_PROC_DETAIL: Leg 4 - Tick data for specific strike 21500.0 is EMPTY after filtering windowed data.
2025-05-28 13:44:14,524 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 4 - Starting risk rules loop, has 3 rules
2025-05-28 13:44:14,524 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 4 - Processing risk rule 1 of 3
2025-05-28 13:44:14,524 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 4 - About to call evaluate_risk_rule for rule type STOPLOSS
2025-05-28 13:44:14,527 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 4 - evaluate_risk_rule returned: triggered=True
2025-05-28 13:44:14,529 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 4 - Breaking out of risk rules loop due to triggered rule
2025-05-28 13:44:14,529 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 4 - After risk rules block (or skipped it)
2025-05-28 13:44:14,529 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Leg 4 - Reached trade building section (outside all conditionals)
2025-05-28 13:44:14,529 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: About to build trade record for leg 4
2025-05-28 13:44:14,529 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Building trade for Leg: 4, Portfolio: NIF0DTE, Strategy: RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL
2025-05-28 13:44:14,529 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Entry Row: {'trade_date': datetime.date(2024, 1, 3), 'trade_time': datetime.time(9, 16), 'expiry_date': datetime.date(2024, 1, 4), 'index_name': 'NIFTY', 'spot': 21615.75, 'atm_strike': 21600.0, 'strike': 21500.0, 'dte': 1, 'expiry_bucket': 'CW', 'zone_id': 1, 'zone_name': 'OPEN', 'call_strike_type': 'ITM2', 'put_strike_type': 'OTM2', 'ce_symbol': 'NIFTY04JAN2421500CE', 'ce_open': 152.6, 'ce_high': 155.3, 'ce_low': 151.8, 'ce_close': 154.35, 'ce_volume': 159000, 'ce_oi': 1485800, 'ce_coi': 0, 'ce_iv': 13.11, 'ce_delta': 0.72, 'ce_gamma': 0.0017, 'ce_theta': -22.87, 'ce_vega': 4.64, 'ce_rho': 72.0855, 'pe_symbol': 'NIFTY04JAN2421500PE', 'pe_open': 41.75, 'pe_high': 41.75, 'pe_low': 39.15, 'pe_close': 39.75, 'pe_volume': 988650, 'pe_oi': 6403900, 'pe_coi': 0, 'pe_iv': 15.37, 'pe_delta': -0.28, 'pe_gamma': 0.0015, 'pe_theta': -21.31, 'pe_vega': 4.92, 'pe_rho': -27.9001, 'future_open': 21706.1, 'future_high': 21710.0, 'future_low': 21705.05, 'future_close': 21708.0, 'future_volume': 47300, 'future_oi': 11836050, 'future_coi': 0}
2025-05-28 13:44:14,530 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Exit Row: {'trade_date': datetime.date(2024, 1, 3), 'trade_time': 120000, 'expiry_date': datetime.date(2024, 1, 4), 'index_name': 'NIFTY', 'spot': 21572.3, 'atm_strike': 21600.0, 'strike': 21500.0, 'dte': 1, 'expiry_bucket': 'CW', 'zone_id': 2, 'zone_name': 'MID_MORN', 'call_strike_type': 'ITM2', 'put_strike_type': 'OTM2', 'ce_symbol': 'NIFTY04JAN2421500CE', 'ce_open': 132.85, 'ce_high': 133.19, 'ce_low': 129.65, 'ce_close': 132.15, 'ce_volume': 203150, 'ce_oi': 3395250, 'ce_coi': 0, 'ce_iv': 14.73, 'ce_delta': 0.69, 'ce_gamma': 0.0017, 'ce_theta': -27.79, 'ce_vega': 5.35, 'ce_rho': 62.7576, 'pe_symbol': 'NIFTY04JAN2421500PE', 'pe_open': 33.2, 'pe_high': 34.1, 'pe_low': 32.75, 'pe_close': 33.15, 'pe_volume': 387450, 'pe_oi': 9387350, 'pe_coi': 0, 'pe_iv': 11.49, 'pe_delta': -0.31, 'pe_gamma': 0.0021, 'pe_theta': -15.98, 'pe_vega': 5.1, 'pe_rho': -30.2188, 'future_open': 21680.0, 'future_high': 21683.0, 'future_low': 21677.3, 'future_close': 21679.05, 'future_volume': 4400, 'future_oi': 11893500, 'future_coi': 3700, 'close': 3.25, 'datetime': Timestamp('2024-01-03 12:00:00'), 'exit_reason': 'Exit Time Hit'}
2025-05-28 13:44:14,530 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Leg Details: OptionType=PUT, Transaction=BUY, Lots=1
2025-05-28 13:44:14,530 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Using leg's exit time: 12:00:00
2025-05-28 13:44:14,530 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Formatted strategy exit time: 12:00:00
2025-05-28 13:44:14,530 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Final exit time format override: 12:00:00
2025-05-28 13:44:14,530 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Calculated PnL: -330.00000000000006, PnL (Slippage): -330.00000000000006, Net PnL: -330.00000000000006
2025-05-28 13:44:14,530 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Final Exit Reason: Exit Time Hit
2025-05-28 13:44:14,530 - backtester_stable.BTRUN.strategies.trade_builder - DEBUG - BUILD_TRADE_RECORD: Final Record: {'portfolio_name': 'NIF0DTE', 'strategy': 'RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL', 'leg_id': '4', 'entry_date': '2024-01-03', 'entry_time': '09:16:00', 'entry_day': 'Wednesday', 'exit_date': '2024-01-03', 'exit_time': '12:00:00', 'exit_day': 'Wednesday', 'symbol': 'NIFTY', 'expiry': '2024-01-04', 'strike': 21500, 'instrument_type': 'PE', 'side': 'BUY', 'filled_quantity': 50.0, 'entry_price': 39.75, 'exit_price': 33.15, 'points': -6.600000000000001, 'pointsAfterSlippage': -6.600000000000001, 'pnl': -330.00000000000006, 'pnlAfterSlippage': -330.00000000000006, 'expenses': 0.0, 'netPnlAfterExpenses': -330.00000000000006, 're_entry_no': 0, 'stop_loss_entry_number': 0, 'take_profit_entry_number': 0, 'reason': 'Exit Time Hit', 'strategy_entry_number': 0, 'index_entry_price': 21615.75, 'index_exit_price': 21572.3, 'max_profit': 0, 'max_loss': 0, 'entry_datetime': '2024-01-03 09:16:00', 'exit_datetime': '2024-01-03 12:00:00'}
2025-05-28 13:44:14,530 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Built trade record for leg 4, about to append. Total records before: 0
2025-05-28 13:44:14,530 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG: Appended trade record for leg 4. Total records now: 1
2025-05-28 13:44:14,531 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - [DEBUG] Number of trade records generated: 1
2025-05-28 13:44:14,531 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - Generated 1 trade records via model-driven path
2025-05-28 13:44:14,531 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - Calculating tick-by-tick P&L for Max Profit/Loss tracking
2025-05-28 13:44:14,531 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG Tick P&L: First trade fields: ['portfolio_name', 'strategy', 'leg_id', 'entry_date', 'entry_time', 'entry_day', 'exit_date', 'exit_time', 'exit_day', 'symbol', 'expiry', 'strike', 'instrument_type', 'side', 'filled_quantity', 'entry_price', 'exit_price', 'points', 'pointsAfterSlippage', 'pnl', 'pnlAfterSlippage', 'expenses', 'netPnlAfterExpenses', 're_entry_no', 'stop_loss_entry_number', 'take_profit_entry_number', 'reason', 'strategy_entry_number', 'index_entry_price', 'index_exit_price', 'max_profit', 'max_loss', 'entry_datetime', 'exit_datetime']
2025-05-28 13:44:14,531 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - DEBUG Tick P&L: First trade sample: {'portfolio_name': 'NIF0DTE', 'strategy': 'RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL', 'leg_id': '4', 'entry_date': '2024-01-03', 'entry_time': '09:16:00', 'entry_day': 'Wednesday', 'exit_date': '2024-01-03', 'exit_time': '12:00:00', 'exit_day': 'Wednesday', 'symbol': 'NIFTY', 'expiry': '2024-01-04', 'strike': 21500, 'instrument_type': 'PE', 'side': 'BUY', 'filled_quantity': 50.0, 'entry_price': 39.75, 'exit_price': 33.15, 'points': -6.600000000000001, 'pointsAfterSlippage': -6.600000000000001, 'pnl': -330.00000000000006, 'pnlAfterSlippage': -330.00000000000006, 'expenses': 0.0, 'netPnlAfterExpenses': -330.00000000000006, 're_entry_no': 0, 'stop_loss_entry_number': 0, 'take_profit_entry_number': 0, 'reason': 'Exit Time Hit', 'strategy_entry_number': 0, 'index_entry_price': 21615.75, 'index_exit_price': 21572.3, 'max_profit': 0, 'max_loss': 0, 'entry_datetime': '2024-01-03 09:16:00', 'exit_datetime': '2024-01-03 12:00:00'}
2025-05-28 13:44:14,550 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 375 rows. Query: SELECT SUBSTRING('SELECT DISTINCT EXTRACT(HOUR FROM trade_time) * 10000 + 
                                       EXTRACT(MINUTE FROM trade_time) * 100 + 
                                       EXTRACT(SECOND FROM trad', 1, 200) ...
2025-05-28 13:44:14,567 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.016s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 13:44:14,580 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.013s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 13:44:14,596 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.016s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 13:44:14,609 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.013s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 13:44:14,623 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.013s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 13:44:14,640 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.017s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 13:44:14,654 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.013s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 13:44:14,668 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.013s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 13:44:14,682 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.013s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 13:44:14,696 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.013s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 13:44:14,713 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.016s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 13:44:14,730 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.016s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 13:44:14,747 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.016s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 13:44:14,764 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.017s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 13:44:14,781 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.017s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 13:44:14,799 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.017s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 13:44:14,819 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 13:44:14,839 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 13:44:14,858 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 13:44:14,877 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 13:44:14,896 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 13:44:14,915 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 13:44:14,934 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 13:44:14,954 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 13:44:14,973 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 13:44:14,992 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 13:44:15,011 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 13:44:15,032 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 13:44:15,051 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 13:44:15,070 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 13:44:15,090 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 13:44:15,109 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 13:44:15,129 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 13:44:15,148 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 13:44:15,167 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 13:44:15,187 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 13:44:15,206 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 13:44:15,225 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 13:44:15,245 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 13:44:15,264 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 13:44:15,283 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 13:44:15,303 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 13:44:15,322 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 13:44:15,342 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 13:44:15,361 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 13:44:15,381 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 13:44:15,400 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 13:44:15,420 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 13:44:15,439 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 13:44:15,459 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 13:44:15,477 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 13:44:15,497 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 13:44:15,515 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 13:44:15,535 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 13:44:15,553 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 13:44:15,573 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 13:44:15,592 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 13:44:15,611 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 13:44:15,631 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 13:44:15,649 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 13:44:15,669 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 13:44:15,688 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 13:44:15,707 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 13:44:15,726 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 13:44:15,745 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 13:44:15,765 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 13:44:15,784 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 13:44:15,804 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 13:44:15,823 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 13:44:15,843 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 13:44:15,862 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 13:44:15,882 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 13:44:15,901 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 13:44:15,920 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 13:44:15,939 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 13:44:15,960 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 13:44:15,977 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.017s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 13:44:15,996 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 13:44:16,015 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 13:44:16,034 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 13:44:16,053 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 13:44:16,073 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 13:44:16,092 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 13:44:16,111 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 13:44:16,130 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 13:44:16,149 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 13:44:16,169 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 13:44:16,188 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 13:44:16,207 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 13:44:16,226 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 13:44:16,245 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 13:44:16,264 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 13:44:16,283 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 13:44:16,303 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 13:44:16,322 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 13:44:16,341 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 13:44:16,360 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 13:44:16,380 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 13:44:16,399 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 13:44:16,418 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 13:44:16,437 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 13:44:16,456 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 13:44:16,476 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 13:44:16,495 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 13:44:16,515 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 13:44:16,534 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 13:44:16,553 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 13:44:16,573 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 13:44:16,592 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 13:44:16,611 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 13:44:16,631 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 13:44:16,650 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 13:44:16,669 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 13:44:16,689 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 13:44:16,708 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 13:44:16,727 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 13:44:16,747 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 13:44:16,766 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 13:44:16,785 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 13:44:16,804 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 13:44:16,823 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 13:44:16,842 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 13:44:16,862 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 13:44:16,881 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 13:44:16,900 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 13:44:16,920 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 13:44:16,939 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 13:44:16,958 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 13:44:16,977 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 13:44:16,996 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 13:44:17,016 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 13:44:17,035 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 13:44:17,090 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.054s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 13:44:17,109 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.018s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 13:44:17,129 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 13:44:17,148 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 13:44:17,168 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 13:44:17,187 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 13:44:17,207 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 13:44:17,226 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 13:44:17,246 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 13:44:17,265 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 13:44:17,284 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 13:44:17,304 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 13:44:17,323 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 13:44:17,343 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 13:44:17,362 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 13:44:17,382 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 13:44:17,401 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 13:44:17,420 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 13:44:17,440 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 13:44:17,459 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 13:44:17,478 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 13:44:17,497 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 13:44:17,518 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 13:44:17,537 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 13:44:17,557 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 13:44:17,576 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 13:44:17,595 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 13:44:17,615 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 13:44:17,634 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 13:44:17,654 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 13:44:17,677 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.023s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 13:44:17,697 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.019s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 13:44:17,717 - backtester_stable.BTRUN.core.heavydb_connection - INFO - Query executed in 0.020s, returned 1 rows. Query: SELECT SUBSTRING('SELECT pe_close as current_price
                                            FROM nifty_option_chain
                                            WHERE trade_date = ''2024-01-03''
                     ', 1, 200) ...
2025-05-28 13:44:17,738 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - INFO - Calculated tick P&L for date 240103: 375 timestamps
2025-05-28 13:44:17,739 - backtester_stable.BTRUN.strategies.heavydb_trade_processing - DEBUG - Closed reusable HeavyDB connection from get_trades_for_portfolio
2025-05-28 13:44:17,773 - backtester_stable.BTRUN.builders - WARNING - utils.MARGIN_INFO not found or empty - margin requirements will be 0. Attempting to populate.
2025-05-28 13:44:17,773 - backtester_stable.BTRUN.core.config - INFO - Found fixture 'margin_symbol_info.json' at /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/margin_symbol_info.json
2025-05-28 13:44:17,773 - backtester_stable.BTRUN.core.utils - INFO - Loading margin symbol info from: /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/margin_symbol_info.json (Broker: angel)
2025-05-28 13:44:17,774 - backtester_stable.BTRUN.core.utils - INFO - Successfully populated config.MARGIN_INFO for angel.
2025-05-28 13:44:17,774 - backtester_stable.BTRUN.builders - ERROR - utils.MARGIN_INFO still empty after populate attempt. Margin req will be 0.
2025-05-28 13:44:17,777 - backtester_stable.BTRUN.builders - DEBUG - Strategy RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL margin requirement: 0.00
2025-05-28 13:44:17,777 - backtester_stable.BTRUN.builders - INFO - Total portfolio margin requirement: 0.00
2025-05-28 13:44:17,777 - backtester_stable.BTRUN.builders - DEBUG - Converting tick PnL to daywise DataFrame.
2025-05-28 13:44:17,779 - backtester_stable.BTRUN.builders - INFO - Backtest response parsed.
2025-05-28 13:44:17,818 - backtester_stable.BTRUN.core.runtime - INFO - Generated metrics_df with 50 records for strategies: ['Combined' 'RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL']
2025-05-28 13:44:17,895 - backtester_stable.BTRUN.core.runtime - INFO - Saving Excel output to comparison_results_20250528_134330/gpu_output.xlsx
2025-05-28 13:44:17,895 - backtester_stable.BTRUN.core.runtime - DEBUG - [DEBUG save_backtest_results] save_excel=True, metrics_df is empty: False
2025-05-28 13:44:17,895 - backtester_stable.BTRUN.core.io - INFO - 2025-05-28 13:44:17.895779, Started writing stats to excel file: comparison_results_20250528_134330/gpu_output.xlsx
2025-05-28 13:44:17,895 - backtester_stable.BTRUN.core.gpu_helpers - DEBUG - Temporarily switching to CPU-only mode.
2025-05-28 13:44:17,909 - backtester_stable.BTRUN.core.io - INFO - Added PortfolioParameter sheet in legacy 2x2 format
2025-05-28 13:44:17,942 - backtester_stable.BTRUN.core.io - INFO - Added GeneralParameter sheet from input files
2025-05-28 13:44:17,955 - backtester_stable.BTRUN.core.io - INFO - Added LegParameter sheet from input files
2025-05-28 13:44:17,955 - backtester_stable.BTRUN.core.io - INFO - Filtered metrics to show only 'Combined' entries: 25 rows
2025-05-28 13:44:18,002 - backtester_stable.BTRUN.core.io - INFO - Added PORTFOLIO Results sheet in legacy format
2025-05-28 13:44:18,055 - backtester_stable.BTRUN.core.io - INFO - 2025-05-28 13:44:18.055431, Excel file prepared successfully, Time taken: 0.16s
2025-05-28 13:44:18,055 - backtester_stable.BTRUN.core.gpu_helpers - DEBUG - Restored GPU_ENABLED to: False
2025-05-28 13:44:18,055 - backtester_stable.BTRUN.core.runtime - INFO - Saving JSON output to comparison_results_20250528_134330/gpu_output.json
2025-05-28 13:44:18,080 - backtester_stable.BTRUN.core.io - INFO - --- JSON DUMP DEBUG START ---
2025-05-28 13:44:18,081 - backtester_stable.BTRUN.core.io - INFO - --- JSON DUMP DEBUG END ---
2025-05-28 13:44:18,081 - backtester_stable.BTRUN.core.io - INFO - Successfully wrote JSON output to comparison_results_20250528_134330/gpu_output.json
2025-05-28 13:44:18,086 - backtester_stable.BTRUN.core.io - ERROR - Text-to-speech failed: This means you probably do not have eSpeak or eSpeak-ng installed!
