# ML ORB Input Sheet Column Mapping and Valid Options

This document provides a comprehensive reference for all columns in the Machine Learning Opening Range Breakout (ORB) strategy input sheets, including their valid options, descriptions, and usage patterns.

## GeneralParameter Sheet

| Column | Description | Valid Options | Notes |
|--------|-------------|--------------|-------|
| StrategyName | Unique identifier for the strategy | Any string | Used for grouping and identification |
| Underlying | Underlying security | SPOT, FUT | The underlying security to trade |
| Index | Index name | NIFTY, BANKNIFTY | Used for index selection |
| Weekdays | Trading days filter | 1,2,3,4,5 | 1=Monday, 5=Friday; Comma-separated list |
| DTE | Days to expiry | Integer (0,1,2...) | Days Till Expiry; 0 for same-day expiry |
| OrbRangeStart | Opening range start time | HHMMSS format (92000 = 09:20:00) | Beginning of opening range candle formation |
| OrbRangeEnd | Opening range end time | HHMMSS format (102000 = 10:20:00) | End of opening range candle formation |
| LastEntryTime | Last entry time | HHMMSS format (150000 = 15:00:00) | No new entries after this time |
| EndTime | Exit time | HHMMSS format (150000 = 15:00:00) | Time to exit all positions |
| StrategyProfit | Strategy profit target | Number | Target profit for the entire strategy |
| StrategyLoss | Strategy stop loss | Number | Maximum loss for the entire strategy |
| StrategyProfitReExecuteNo | Re-entries after profit | Integer | Number of times to re-enter after hitting profit target |
| StrategyLossReExecuteNo | Re-entries after loss | Integer | Number of times to re-enter after hitting stop loss |
| StrategyTrailingType | Strategy trailing type | Lock Minimum Profit, Lock & Trail Profits | Method for trailing strategy profits |
| ProfitReaches | Profit target threshold | Number | When to start trailing/locking profit |
| LockMinProfitAt | Minimum profit to lock | Number | Profit level to secure |
| IncreaseInProfit | Profit increase step | Number | Amount to increase trailing stop by |
| TrailMinProfitBy | Minimum profit to trail | Number | Minimum profit for trailing |
| TgtTrackingFrom | Target tracking mode | close, open, high/low | Price source for target tracking |
| TgtRegisterPriceFrom | Target price mode | tick, tracking | How to register target price |
| SlTrackingFrom | SL tracking mode | close, open, high/low | Price source for stop-loss tracking |
| SlRegisterPriceFrom | SL price mode | tick, tracking | How to register stop-loss price |
| PnLCalculationFrom | P&L calculation mode | close, open, high/low | Price source for P&L calculation |
| ConsiderHedgePnLForStgyPnL | Include hedge in P&L | yes/no | Whether to include hedge position P&L in strategy P&L |
| StoplossCheckingInterval | SL check interval | Integer (seconds) | How often to check for stop-loss trigger |
| TargetCheckingInterval | Target check interval | Integer (seconds) | How often to check for target trigger |
| ReEntryCheckingInterval | Re-entry check interval | Integer (seconds) | How often to check for re-entry conditions |
| OnExpiryDayTradeNextExpiry | Trade next expiry on expiry day | yes/no | Whether to use next expiry contracts on expiry day |

## LegParameter Sheet

| Column | Description | Valid Options | Notes |
|--------|-------------|--------------|-------|
| StrategyName | Strategy identifier | Must match GeneralParameter | Links leg to strategy |
| LegID | Leg identifier | Any unique identifier (a1, a2, etc.) | Used to reference specific legs |
| Instrument | Option type | call, put, FUT | CE/CALL → CALL, PE/PUT → PUT, FUT for futures |
| Transaction | Transaction type | buy, sell | Trade direction |
| Expiry | Expiry selection | current, next, monthly | CURRENT/CW → CURRENT_WEEK, NEXT/NW → NEXT_WEEK, MONTHLY for monthly contracts |
| W&Type | Risk type | percentage, point, index point, index percentage, absolute, delta | Type of risk measurement |
| W&TValue | Risk value | Number | Used with W&Type |
| MatchPremium | Premium matching | high, low | For premium-based strike selection |
| StrikeMethod | Strike selection | ATM, ITM1, ITM2, OTM1, OTM2, etc. | Method to select strike price (see detailed explanation below) |
| StrikeValue | Fixed strike value | Number | Used with FIXED strike method and special cases |
| StrikePremiumCondition | Premium condition | =, <, >, <= | Comparison operator for premium |
| SLType | Stop loss type | percentage, point, index point, index percentage, absolute, delta | Type of stop-loss |
| SLValue | Stop loss value | Number | Amount for stop loss (recommended: 500 for SELL, 50 for BUY) |
| TGTType | Target type | percentage, point, index point, index percentage, absolute, delta | Type of target |
| TGTValue | Target value | Number | Amount for target (recommended: 100) |
| TrailSLType | Trailing SL type | percentage, point, index point, index percentage, absolute, delta | Type of trailing stop-loss |
| SL_TrailAt | When to start trailing | Number | Profit level to start trailing |
| SL_TrailBy | Trail step | Number | Amount to trail by |
| Lots | Number of lots | Integer | Position size in lots |
| SL_ReEntryType | Stop loss re-entry | cost, original, instant new strike, instant same strike | How to re-enter after stop loss |
| SL_ReEntryNo | SL re-entries | Integer | Number of re-entries after stop loss |
| TGT_ReEntryType | Target re-entry | cost, original, instant new strike, instant same strike | How to re-enter after hitting target |
| TGT_ReEntryNo | Target re-entries | Integer | Number of re-entries after hitting target |
| OpenHedge | Use hedging | Yes/No | Whether to use automatic hedging |
| HedgeStrikeMethod | Hedge strike method | atm, premium, atm width, delta | Method to select hedge strike (see detailed explanation below) |
| HedgeStrikeValue | Hedge strike value | Number | Used with specific hedge strike methods |
| HedgeStrikePremiumCondition | Hedge premium condition | =, <, >, <= | Comparison operator for hedge premium |

## Detailed Strike Selection Logic

The strike selection mechanism works differently depending on the `StrikeMethod` value:

### Basic Strike Methods

| StrikeMethod | Description | StrikeValue Usage | Notes |
|--------------|-------------|-------------------|-------|
| `ATM` | At-The-Money | Ignored (or 0) | Selects the strike closest to the current underlying price |
| `ITM1`, `ITM2`, etc. | In-The-Money | Ignored | The number indicates steps away from ATM (ITM1 = 1 step, ITM2 = 2 steps, etc.) |
| `OTM1`, `OTM2`, etc. | Out-Of-The-Money | Ignored | The number indicates steps away from ATM (OTM1 = 1 step, OTM2 = 2 steps, etc.) |
| `FIXED` | Fixed Strike | Required | Uses StrikeValue directly as the strike price |

### Advanced Strike Methods

| StrikeMethod | Description | StrikeValue Usage | Notes |
|--------------|-------------|-------------------|-------|
| `PREMIUM` | Premium-based | Required | Select strike based on premium value, uses StrikePremiumCondition (=, <, >) |
| `ATM WIDTH` | ATM Width | Required | Offset from ATM based on ATM straddle premium * StrikeValue |
| `STRADDLE WIDTH` | Straddle Width | Required | Similar to ATM WIDTH, uses straddle premium to calculate offset |
| `ATM MATCH` | ATM Match | Required | Match premium of the opposite option type |
| `ATM DIFF` | ATM Difference | Required | Find strike with minimum CE-PE premium difference |
| `DELTA` | Delta-based | Required | Select strike based on delta value |

### Strike Steps Calculation

The system uses different step intervals for different indices, based on exchange standards:

| Index | Strike Step Size | Notes |
|-------|-----------------|-------|
| NIFTY | 50 | Fixed step size regardless of index level |
| BANKNIFTY | 100 | Fixed step size regardless of index level |
| FINNIFTY | 50 | Fixed step size regardless of index level |
| MIDCPNIFTY | 25 | Fixed step size regardless of index level |
| SENSEX | 100 | Fixed step size regardless of index level |
| BANKEX | 100 | Fixed step size regardless of index level |

These step sizes align with the official NSE/BSE specifications and are used to calculate exact ITM and OTM strikes.

For example:
- If NIFTY is at 19525, ATM would be 19525 (rounded to nearest 50 if needed)
  - ITM1 for CALL would be 19475
  - OTM1 for CALL would be 19575
  - ITM1 for PUT would be 19575
  - OTM1 for PUT would be 19475

- If BANKNIFTY is at 46825, ATM would be 46825 (rounded to nearest 100 if needed)
  - ITM1 for CALL would be 46725
  - OTM1 for CALL would be 46925
  - ITM1 for PUT would be 46925
  - OTM1 for PUT would be 46725

### Option Type Considerations

Strike selection also varies by option type (CALL vs PUT):
- For CALL options: ITM = lower strikes, OTM = higher strikes
- For PUT options: ITM = higher strikes, OTM = lower strikes

## Detailed Hedge Strike Selection Logic

Similar to the primary strike selection, hedge strikes also follow a structured selection process based on the `HedgeStrikeMethod` value:

### Basic Hedge Strike Methods

| HedgeStrikeMethod | Description | HedgeStrikeValue Usage | Notes |
|--------------|-------------|-------------------|-------|
| `ATM` | At-The-Money | Ignored (or 0) | Selects the hedge strike closest to the current underlying price |
| `ITM1`, `ITM2`, etc. | In-The-Money | Ignored | The number indicates steps away from ATM (ITM1 = 1 step, ITM2 = 2 steps, etc.) |
| `OTM1`, `OTM2`, etc. | Out-Of-The-Money | Ignored | The number indicates steps away from ATM (OTM1 = 1 step, OTM2 = 2 steps, etc.) |
| `FIXED` | Fixed Strike | Required | Uses HedgeStrikeValue directly as the hedge strike price |

### Advanced Hedge Strike Methods

| HedgeStrikeMethod | Description | HedgeStrikeValue Usage | Notes |
|--------------|-------------|-------------------|-------|
| `PREMIUM` | Premium-based | Required | Select hedge strike based on premium value, uses HedgeStrikePremiumCondition (=, <, >) |
| `ATM WIDTH` | ATM Width | Required | Offset from ATM based on ATM straddle premium * HedgeStrikeValue |
| `STRADDLE WIDTH` | Straddle Width | Required | Similar to ATM WIDTH, uses straddle premium to calculate offset |
| `DELTA` | Delta-based | Required | Select hedge strike based on delta value |

The hedge strike calculation follows the same strike step sizes and option type considerations as the main strike selection.

## Expiry Options

| Code | Description | Database Mapping | Notes |
|------|-------------|-----------------|-------|
| `current` / `CW` | Current Week | CW | Weekly expiry that's nearest to current date |
| `next` / `NW` | Next Week | NW | Second-nearest weekly expiry |
| `monthly` / `CM` | Current Month | CM | Current month's expiry (typically last Thursday) |
| `NM` | Next Month | NM | Next month's expiry |

These expiry codes align with the expiry bucket nomenclature used in the `nifty_option_chain` table and other HeavyDB database objects.

## Opening Range Breakout (ORB) Strategy Logic

The ORB strategy operates on the principle that the price movement during the market's opening period can set the tone for the entire trading day. The key components of the ORB strategy are:

### Opening Range Calculation

1. The system observes price action during the specified opening range:
   - Range start is defined by `OrbRangeStart` (e.g., 09:20:00)
   - Range end is defined by `OrbRangeEnd` (e.g., 10:20:00)
   
2. During this period, the system records:
   - The high price of the range
   - The low price of the range

### Breakout Entry Criteria

After the opening range is established, trade entries can occur based on:

1. **Bullish Breakout**: Price moves above the opening range high
   - Entry signal for buy calls or sell puts

2. **Bearish Breakout**: Price moves below the opening range low
   - Entry signal for buy puts or sell calls

3. Entries are allowed any time after the `OrbRangeEnd` and before the `LastEntryTime`

### Common ORB Strategy Variations

1. **Classic ORB**: Enter on the first breakout of either the high or low of the opening range
2. **Directional Bias ORB**: Only take breakouts in the direction of the prevailing trend
3. **Contrarian ORB**: Take counter-trend trades if the breakout fails to continue
4. **Multiple Time Frame ORB**: Compare breakouts across different opening range time frames

## Strategy Trailing Types

| Type | Description | Implementation Logic |
|------|-------------|----------------------|
| Lock Minimum Profit | Locks a minimum profit once target is reached | **Logic**: If ProfitReaches=10000, LockMinProfitAt=5000, when profit hits 10000, system locks minimum profit at 5000. All positions exit when MTM decreases to 5000. |
| Lock & Trail Profits | Continuously trails the profit as it increases | **Logic**: Uses TrailPercent to determine the trailing amount. As profit grows, stop level is moved up accordingly, maintaining the TrailPercent distance. |

### "Lock Minimum Profit" Example
If the "ProfitReaches" parameter is set to 10000 and the "LockMinProfitAt" parameter is set to 5000:
1. When the strategy's profit reaches 10000, the system activates profit locking
2. A minimum profit of 5000 is locked in
3. All positions will be exited when the Mark-to-Market (MTM) decreases to 5000
4. This provides a way to secure a portion of profits while allowing for continued upside

## Risk Management Parameters

### Stop Loss & Take Profit Recommendations

| Leg Type | SLType | SLValue | TGTType | TGTValue | Notes |
|----------|--------|---------|---------|----------|-------|
| Short Call/Put | percentage | 500 | percentage | 100 | For sell legs |
| Long Call/Put | percentage | 50 | percentage | 100 | For buy legs |

These values are large enough to avoid premature triggers but still provide protection against extreme moves.

### Common Exit Time Issues

**Problem**: Trades exit at entry time instead of EndTime

**Causes**:
1. **Tight SL/TP**: Values that trigger immediately
   - **Fix**: Increase SLValue (e.g., from 100% to 500% for sell legs)
   - **Fix**: Increase TGTValue (e.g., from 0% to 100%)

2. **EndTime equals entry time**
   - **Fix**: Ensure EndTime > StartTime (for ORB, EndTime > OrbRangeEnd)

## Price Reference Sources

For SL/TP and other price-based triggers, the system can use different price references:

### TgtTrackingFrom / SlTrackingFrom / PnLCalculationFrom
- `close`: Use the closing price
- `open`: Use the opening price
- `high/low`: Use the high or low price (whichever is more relevant for the condition)

### TgtRegisterPriceFrom / SlRegisterPriceFrom
- `tick`: Update on every price tick
- `tracking`: Update based on tracking logic

## Example ORB Strategy Configuration

A typical ORB strategy might be configured as:

1. **Opening Range**: 09:20:00 - 10:20:00 (first hour of trading)
2. **Entry Trigger**: Break above/below the opening range high/low
3. **Trade Direction**:
   - Buy calls when price breaks above opening range high
   - Buy puts when price breaks below opening range low
4. **Stop Loss**: Place stop loss at the opposite end of the opening range
5. **Take Profit**: Target 2x the opening range size
6. **Time Exit**: Close all positions by 15:30:00 regardless of profit/loss 

## HeavyDB Column Mapping and Query Patterns

This section provides comprehensive documentation on how Opening Range Breakout (ORB) Strategy fields map to HeavyDB SQL queries, transformations, and implementation patterns.

### GeneralParameter Sheet Field Mappings

#### Core Strategy Fields

| Excel Column | Model Field | HeavyDB Treatment | SQL Pattern/Transformation |
|--------------|-------------|-------------------|----------------------------|
| StrategyName | strategy_name | Used for identification | Referenced in query comments and filtering conditions (`-- Strategy: {strategy_name}`) |
| Underlying | extra_params['Underlying'] | Mapped to `spot` column | Spot/Future selection logic: `CASE WHEN extra_params->>'Underlying' = 'SPOT' THEN use_spot ELSE use_future END` |
| Index | extra_params['Index'] | Determines `{index}_option_chain` table | Used in resolver: `TableMap.get(index.upper(), "nifty_option_chain")` |
| DTE | dte_filter | Direct filter on `dte` column | `WHERE dte = {dte_filter}` |
| Weekdays | extra_params['Weekdays'] | Applied as day of week filter | `WHERE EXTRACT(DOW FROM trade_date) IN ({comma_separated_days})` |

#### ORB-Specific Time Window Fields

| Excel Column | Model Field | HeavyDB Treatment | SQL Pattern/Transformation |
|--------------|-------------|-------------------|----------------------------|
| OrbRangeStart | extra_params['OrbRangeStart'] | Converted to HH:MM:SS TIME | `92000` → `WHERE trade_time >= TIME '09:20:00'` in range calculation |
| OrbRangeEnd | extra_params['OrbRangeEnd'] | Converted to HH:MM:SS TIME | `102000` → `WHERE trade_time <= TIME '10:20:00'` in range calculation |
| LastEntryTime | extra_params['LastEntryTime'] | Converted to HH:MM:SS TIME | `150000` → `WHERE trade_time <= TIME '15:00:00'` |
| EndTime | entry_end | Converted to HH:MM:SS TIME | `152000` → `WHERE trade_time <= TIME '15:20:00'` |

#### Risk Management Fields

| Excel Column | Model Field | HeavyDB Treatment | SQL Pattern/Transformation |
|--------------|-------------|-------------------|----------------------------|
| StrategyProfit | extra_params['StrategyProfit'] | Direct numeric value | Used in risk rule evaluation: `CASE WHEN strategy_pnl >= CAST(extra_params->>'StrategyProfit' AS DOUBLE) THEN exit_strategy` |
| StrategyLoss | extra_params['StrategyLoss'] | Direct numeric value | Used in risk rule evaluation: `CASE WHEN strategy_pnl <= CAST(extra_params->>'StrategyLoss' AS DOUBLE) THEN exit_strategy` |
| StrategyProfitReExecuteNo | extra_params['StrategyProfitReExecuteNo'] | Integer conversion | Used in re-entry logic: `WHERE re_entry_count < CAST(extra_params->>'StrategyProfitReExecuteNo' AS INT)` |
| StrategyLossReExecuteNo | extra_params['StrategyLossReExecuteNo'] | Integer conversion | Used in re-entry logic: `WHERE re_entry_count < CAST(extra_params->>'StrategyLossReExecuteNo' AS INT)` |
| ProfitReaches | extra_params['ProfitReaches'] | Direct numeric value | Profit threshold: `IF strategy_pnl >= CAST(extra_params->>'ProfitReaches' AS DOUBLE) THEN activate_trailing` |
| LockMinProfitAt | extra_params['LockMinProfitAt'] | Direct numeric value | Minimum profit lock: `min_profit = CAST(extra_params->>'LockMinProfitAt' AS DOUBLE)` |
| IncreaseInProfit | extra_params['IncreaseInProfit'] | Direct numeric value | Used in trailing stop increments |
| TrailMinProfitBy | extra_params['TrailMinProfitBy'] | Direct numeric value | Minimum profit amount to trail by |

#### Tracking Fields

| Excel Column | Model Field | HeavyDB Treatment | SQL Pattern/Transformation |
|--------------|-------------|-------------------|----------------------------|
| StrategyTrailingType | extra_params['StrategyTrailingType'] | String enum mapping | `'Lock Minimum Profit'` or `'Lock & Trail Profits'` determine trailing logic |
| TgtTrackingFrom | extra_params['TgtTrackingFrom'] | Price source selection | Maps to column selection: `'close'` → `{symbol}_close`, `'open'` → `{symbol}_open` |
| TgtRegisterPriceFrom | extra_params['TgtRegisterPriceFrom'] | Price update policy | `'tick'` → update on every tick, `'tracking'` → update based on tracking rules |
| SlTrackingFrom | extra_params['SlTrackingFrom'] | Price source selection | Maps to column selection: `'close'` → `{symbol}_close`, `'open'` → `{symbol}_open` |
| SlRegisterPriceFrom | extra_params['SlRegisterPriceFrom'] | Price update policy | `'tick'` → update on every tick, `'tracking'` → update based on tracking rules |
| PnLCalculationFrom | extra_params['PnLCalculationFrom'] | Price source selection | Maps to column selection for P&L calculations |

#### Boolean Fields

| Excel Column | Model Field | HeavyDB Treatment | SQL Pattern/Transformation |
|--------------|-------------|-------------------|----------------------------|
| OnExpiryDayTradeNextExpiry | extra_params['OnExpiryDayTradeNextExpiry'] | Converted to boolean | `YES/yes` → `true`, `NO/no` → `false`, used in expiry selection logic |
| ConsiderHedgePnLForStgyPnL | extra_params['ConsiderHedgePnLForStgyPnL'] | Converted to boolean | Used in PnL calculation: `IF extra_params->>'ConsiderHedgePnLForStgyPnL' = 'true' THEN include_hedge_pnl` |

#### Time Interval Fields

| Excel Column | Model Field | HeavyDB Treatment | SQL Pattern/Transformation |
|--------------|-------------|-------------------|----------------------------|
| StoplossCheckingInterval | extra_params['StoplossCheckingInterval'] | Second conversion | Controls check frequency: `EXTRACT(EPOCH FROM (current_time - last_check)) >= value` |
| TargetCheckingInterval | extra_params['TargetCheckingInterval'] | Second conversion | Controls check frequency for targets |
| ReEntryCheckingInterval | extra_params['ReEntryCheckingInterval'] | Second conversion | Controls delay between re-entry attempts |

### LegParameter Sheet Field Mappings

#### Core Leg Fields

| Excel Column | Model Field | HeavyDB Treatment | SQL Pattern/Transformation |
|--------------|-------------|-------------------|----------------------------|
| StrategyName | strategy_name | Used for leg grouping | Must match GeneralParameter.StrategyName |
| LegID | leg_id | Used as identifier | Used in CTE names and query comments: `-- LegID: {leg_id}` |
| Instrument | option_type | Maps to option type enum | `'call'`/`'CE'` → `OptionType.CALL`, `'put'`/`'PE'` → `OptionType.PUT`, `'FUT'` → `OptionType.FUT` |
| Transaction | transaction | Maps to transaction enum | `'buy'` → `TransactionType.BUY`, `'sell'` → `TransactionType.SELL` |
| Expiry | expiry_rule | Maps to expiry bucket | `'current'`/`'CW'` → `WHERE expiry_bucket = 'CW'`, `'next'`/`'NW'` → `WHERE expiry_bucket = 'NW'`, etc. |
| Lots | lots | Integer conversion | Used for position sizing: `position_size = lot_size * CAST(lots AS INT)` |

#### Strike Selection Fields

| Excel Column | Model Field | HeavyDB Treatment | SQL Pattern/Transformation |
|--------------|-------------|-------------------|----------------------------|
| StrikeMethod | strike_rule | Maps to strike rule enum | Determines SQL filter pattern. Examples: <br>- `'ATM'` → `WHERE strike = atm_strike` <br>- `'ITM1'` → `WHERE call_strike_type = 'ITM1'` or `WHERE put_strike_type = 'ITM1'` <br>- `'OTM2'` → `WHERE call_strike_type = 'OTM2'` or `WHERE put_strike_type = 'OTM2'` <br>- `'FIXED'` → `WHERE strike = {fixed_strike}` |
| StrikeValue | fixed_strike | Direct numeric value | Used with `FIXED` strike rule: `WHERE strike = CAST(fixed_strike AS DOUBLE)` |
| MatchPremium | extra_params['MatchPremium'] | Premium selection | `'high'`/`'low'` determines premium selection logic |
| StrikePremiumCondition | extra_params['StrikePremiumCondition'] | SQL operator | Converted to SQL operator: `'='` → `=`, `'<'` → `<`, `'>'` → `>`, `'<='` → `<=`, etc. |

#### Risk Fields

| Excel Column | Model Field | HeavyDB Treatment | SQL Pattern/Transformation |
|--------------|-------------|-------------------|----------------------------|
| W&Type | extra_params['W&Type'] | Risk type selection | Determines risk calculation method: `'percentage'`, `'point'`, `'index point'`, etc. |
| W&TValue | extra_params['W&TValue'] | Direct numeric value | Used with W&Type to calculate risk |
| SLType | extra_params['SLType'] | Stop loss type selection | Determines SL calculation: `'percentage'`, `'point'`, `'index point'`, etc. |
| SLValue | extra_params['SLValue'] | Direct numeric value | Used with SLType to calculate stop loss level |
| TGTType | extra_params['TGTType'] | Target type selection | Determines target calculation: `'percentage'`, `'point'`, `'index point'`, etc. |
| TGTValue | extra_params['TGTValue'] | Direct numeric value | Used with TGTType to calculate target level |
| TrailSLType | extra_params['TrailSLType'] | Trailing SL type selection | Determines trailing SL calculation method |
| SL_TrailAt | extra_params['SL_TrailAt'] | Direct numeric value | Profit level to start trailing SL |
| SL_TrailBy | extra_params['SL_TrailBy'] | Direct numeric value | Amount to trail SL by |

#### Re-entry Fields

| Excel Column | Model Field | HeavyDB Treatment | SQL Pattern/Transformation |
|--------------|-------------|-------------------|----------------------------|
| SL_ReEntryType | extra_params['SL_ReEntryType'] | Re-entry mode selection | Determines re-entry logic after SL hit: `'cost'`, `'original'`, `'instant new strike'`, `'instant same strike'` |
| SL_ReEntryNo | extra_params['SL_ReEntryNo'] | Integer conversion | Max SL re-entries: `WHERE sl_reentry_count < CAST(extra_params->>'SL_ReEntryNo' AS INT)` |
| TGT_ReEntryType | extra_params['TGT_ReEntryType'] | Re-entry mode selection | Determines re-entry logic after target hit |
| TGT_ReEntryNo | extra_params['TGT_ReEntryNo'] | Integer conversion | Max target re-entries: `WHERE tgt_reentry_count < CAST(extra_params->>'TGT_ReEntryNo' AS INT)` |

#### Hedge Fields

| Excel Column | Model Field | HeavyDB Treatment | SQL Pattern/Transformation |
|--------------|-------------|-------------------|----------------------------|
| OpenHedge | extra_params['OpenHedge'] | Boolean conversion | Controls hedge creation: `CASE WHEN UPPER(extra_params->>'OpenHedge') = 'YES' THEN create_hedge` |
| HedgeStrikeMethod | extra_params['HedgeStrikeMethod'] | Strike rule for hedge | Same mapping as StrikeMethod but applied to hedge leg |
| HedgeStrikeValue | extra_params['HedgeStrikeValue'] | Direct numeric value | Used with HedgeStrikeMethod |
| HedgeStrikePremiumCondition | extra_params['HedgeStrikePremiumCondition'] | SQL operator | Same mapping as StrikePremiumCondition but for hedge |

### ORB-Specific SQL Query Patterns

#### Opening Range Calculation

```sql
-- Calculate opening range high and low for the day
WITH opening_range AS (
  SELECT 
    MAX(underlying_price) AS range_high,
    MIN(underlying_price) AS range_low,
    MAX(underlying_price) - MIN(underlying_price) AS range_size
  FROM nifty_option_chain
  WHERE trade_date = DATE '2025-04-01'
    -- Range period from GeneralParameter.OrbRangeStart to OrbRangeEnd
    AND trade_time BETWEEN TIME '09:20:00' AND TIME '10:20:00'
)
SELECT * FROM opening_range
```

#### Detecting Breakout Point

```sql
-- Find first tick where price breaks above opening range high
WITH opening_range AS (
  SELECT 
    MAX(underlying_price) AS range_high,
    MIN(underlying_price) AS range_low
  FROM nifty_option_chain
  WHERE trade_date = DATE '2025-04-01'
    -- Range period from GeneralParameter.OrbRangeStart to OrbRangeEnd
    AND trade_time BETWEEN TIME '09:20:00' AND TIME '10:20:00'
),
breakout_point AS (
  SELECT
    MIN(trade_time) AS breakout_time
  FROM nifty_option_chain oc, opening_range r
  WHERE oc.trade_date = DATE '2025-04-01'
    -- Look for breakout after the range period
    AND oc.trade_time > TIME '10:20:00'
    -- Breakout condition - price exceeds the high of the opening range
    AND oc.underlying_price > r.range_high
)
SELECT * FROM breakout_point
```

#### ORB Entry Signal

```sql
-- Generate entry signal based on breakout
WITH opening_range AS (
  -- Calculate high/low from OrbRangeStart to OrbRangeEnd
  SELECT 
    MAX(underlying_price) AS range_high,
    MIN(underlying_price) AS range_low,
    MAX(underlying_price) - MIN(underlying_price) AS range_size
  FROM nifty_option_chain
  WHERE trade_date = DATE '2025-04-01'
    AND trade_time BETWEEN TIME '09:20:00' AND TIME '10:20:00'
),
breakout_signal AS (
  -- Find first breakout tick meeting conditions
  SELECT
    oc.trade_time,
    oc.underlying_price,
    CASE
      -- Bullish breakout (for call entry or put exit)
      WHEN oc.underlying_price > r.range_high THEN 'BULLISH'
      -- Bearish breakout (for put entry or call exit)
      WHEN oc.underlying_price < r.range_low THEN 'BEARISH'
      ELSE NULL
    END AS breakout_type,
    r.range_size,
    -- Calculate breakout strength as % of range
    ABS(oc.underlying_price - 
      CASE 
        WHEN oc.underlying_price > r.range_high THEN r.range_high
        ELSE r.range_low
      END) / NULLIF(r.range_size, 0) * 100 AS breakout_strength
  FROM nifty_option_chain oc, opening_range r
  WHERE oc.trade_date = DATE '2025-04-01'
    -- Only consider breakouts after the range period
    AND oc.trade_time > TIME '10:20:00'
    -- Only until LastEntryTime
    AND oc.trade_time <= TIME '15:00:00'
    -- Breakout conditions
    AND (oc.underlying_price > r.range_high OR oc.underlying_price < r.range_low)
  ORDER BY oc.trade_time
  LIMIT 1  -- First instance of breakout
)
-- Get options to trade based on breakout
SELECT oc.*
FROM nifty_option_chain oc
JOIN breakout_signal bs ON oc.trade_date = DATE '2025-04-01' AND oc.trade_time = bs.trade_time
WHERE oc.expiry_bucket = 'CW'
  -- For bullish breakout, get ATM calls; for bearish, ATM puts
  AND ((bs.breakout_type = 'BULLISH' AND oc.strike = oc.atm_strike AND oc.ce_symbol IS NOT NULL)
       OR 
       (bs.breakout_type = 'BEARISH' AND oc.strike = oc.atm_strike AND oc.pe_symbol IS NOT NULL))
LIMIT 1
```

#### ORB Target and Stop-Loss Calculation

```sql
-- Calculate targets and stops based on range size
WITH opening_range AS (
  SELECT 
    MAX(underlying_price) AS range_high,
    MIN(underlying_price) AS range_low,
    MAX(underlying_price) - MIN(underlying_price) AS range_size
  FROM nifty_option_chain
  WHERE trade_date = DATE '2025-04-01'
    AND trade_time BETWEEN TIME '09:20:00' AND TIME '10:20:00'
),
breakout_signal AS (
  -- First breakout tick after range period
  SELECT
    oc.trade_time,
    oc.underlying_price AS breakout_price,
    oc.atm_strike,
    CASE
      WHEN oc.underlying_price > r.range_high THEN 'BULLISH'
      ELSE 'BEARISH'
    END AS breakout_type,
    r.range_high,
    r.range_low,
    r.range_size
  FROM nifty_option_chain oc, opening_range r
  WHERE oc.trade_date = DATE '2025-04-01'
    AND oc.trade_time > TIME '10:20:00'
    AND (oc.underlying_price > r.range_high OR oc.underlying_price < r.range_low)
  ORDER BY oc.trade_time
  LIMIT 1
),
target_levels AS (
  -- Calculate dynamic target/stop levels
  SELECT
    breakout_price,
    breakout_type,
    atm_strike,
    -- For bullish breakout: entry price + range_size * X
    -- For bearish breakout: entry price - range_size * X
    CASE 
      WHEN breakout_type = 'BULLISH' THEN breakout_price + range_size * 2  -- 2x range size
      ELSE breakout_price - range_size * 2
    END AS target_price,
    -- Stop is typically at the other end of the range
    CASE 
      WHEN breakout_type = 'BULLISH' THEN range_low
      ELSE range_high
    END AS stop_price
  FROM breakout_signal
)
SELECT * FROM target_levels
```

#### Time Transformation Function

```python
def _ensure_hhmmss(value: str | int | time) -> int:
    """Convert Excel time format to HeavyDB TIME string.
    
    91600 → '09:16:00'
    """
    if isinstance(value, time):
        return value.hour * 10000 + value.minute * 100 + value.second

    s = str(value).strip()

    # Handle colon-separated inputs
    if ":" in s:
        parts = s.split(":")
        hh = int(parts[0])
        mm = int(parts[1]) if len(parts) > 1 else 0
        ss = int(parts[2]) if len(parts) > 2 else 0
        return hh * 10000 + mm * 100 + ss

    # Handle numeric inputs
    try:
        v_int = int(s)
        
        # Six digits like 091600 or 152000
        if len(s) == 6 and v_int <= 235959:
            return v_int
            
        # Seconds from midnight (≤ 86399)
        if v_int <= 86399:
            hh = v_int // 3600
            mm = (v_int % 3600) // 60
            ss = v_int % 60
            return hh * 10000 + mm * 100 + ss
            
        # Already in HHMMSS form
        return v_int
    except ValueError:
        raise ValueError(f"Invalid time format: '{value}'")
```

### Performance Considerations for ORB Strategies

1. **Opening Range Calculation Optimization**:
   - The opening range calculation is critical to ORB strategy performance
   - Create materialized view for frequently used date ranges
   - Use indexed window functions for high-performance range calculation:
   ```sql
   -- Fast opening range calculation with WINDOW functions
   SELECT 
     trade_date,
     MAX(underlying_price) OVER(PARTITION BY trade_date) AS range_high,
     MIN(underlying_price) OVER(PARTITION BY trade_date) AS range_low
   FROM nifty_option_chain
   WHERE trade_date = DATE '2025-04-01'
     AND trade_time BETWEEN TIME '09:20:00' AND TIME '10:20:00'
   QUALIFY ROW_NUMBER() OVER(PARTITION BY trade_date ORDER BY trade_time DESC) = 1
   ```

2. **Breakout Detection Optimization**:
   - Breakout detection can be computationally expensive
   - Use date/time filters aggressively
   - Consider pre-computed breakout signals for common parameters:
   ```sql
   -- Pre-compute breakout signals
   CREATE TABLE orb_breakout_signals AS
   SELECT
     trade_date,
     MIN(CASE WHEN underlying_price > range_high THEN trade_time END) AS bullish_breakout_time,
     MIN(CASE WHEN underlying_price < range_low THEN trade_time END) AS bearish_breakout_time
   FROM nifty_option_chain
   JOIN opening_ranges ON trade_date = range_date
   WHERE trade_time > range_end_time
   GROUP BY trade_date
   ```

3. **Time Window Optimizations**:
   - The ORB strategy involves three distinct time windows:
     1. Range calculation period (`OrbRangeStart` to `OrbRangeEnd`)
     2. Breakout detection period (`OrbRangeEnd` to `LastEntryTime`)
     3. Position management period (`Breakout` to `EndTime`)
   - Apply separate optimizations for each window
   - Use time-of-day indexing for efficient filtering

4. **GPU Acceleration**:
   - Use `/*+ gpu_enable(true) */` hint for window functions
   - Example: `SELECT /*+ gpu_enable(true) */ MAX(underlying_price) OVER(...)`
   - Especially effective for range calculations and first/last value retrieval

### Symbol Adaptation

ORB strategies work with any symbol table following the pattern `{symbol}_option_chain`:

```python
# Table selection logic
TABLE_MAP = {
    "NIFTY": "nifty_option_chain",
    "BANKNIFTY": "banknifty_option_chain",
    "FINNIFTY": "finnifty_option_chain",
}

def table_for_index(index: str) -> str:
    idx = index.upper()
    return TABLE_MAP.get(idx, "nifty_option_chain")
```

For ORB strategies, different symbols require adjustments to breakout parameters:

| Index | Typical Range Size | Recommended Adjustments |
|-------|-------------------|-------------------------|
| NIFTY | 50-150 points | Standard parameters |
| BANKNIFTY | 150-400 points | 1.5-2x larger targets, wider SL |
| FINNIFTY | 75-200 points | 1.2-1.5x standard parameters |
| MIDCPNIFTY | 50-100 points | 0.8-1x standard parameters |
| SENSEX | 150-300 points | 2x standard parameters |

Adjust target calculations and range size multipliers based on the underlying index's volatility characteristics. 