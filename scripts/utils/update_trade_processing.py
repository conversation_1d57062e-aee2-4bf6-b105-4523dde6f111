#!/usr/bin/env python3
"""
Update Trade Processing Script

This script updates the heavydb_trade_processing.py module to properly pass
the strategy's start time to the risk evaluation function.
"""

import os
import shutil
import re
import sys
import logging
from datetime import datetime

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler(f'update_trade_processing_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log')
    ]
)
logger = logging.getLogger('update_trade_processing')

# Path to the heavydb_trade_processing.py file
TRADE_PROC_PATH = '/srv/samba/shared/bt/backtester_stable/BTRUN/heavydb_trade_processing.py'
BACKUP_PATH = f'{TRADE_PROC_PATH}.bak.{datetime.now().strftime("%Y%m%d_%H%M%S")}'

def backup_file():
    """Create a backup of the original file"""
    try:
        shutil.copy2(TRADE_PROC_PATH, BACKUP_PATH)
        logger.info(f"Created backup at: {BACKUP_PATH}")
        return True
    except Exception as e:
        logger.error(f"Failed to create backup: {e}")
        return False

def update_trade_processing():
    """Update the heavydb_trade_processing.py module to pass start time to risk evaluation"""
    if not os.path.exists(TRADE_PROC_PATH):
        logger.error(f"Trade processing module not found at: {TRADE_PROC_PATH}")
        return False
    
    # Make a backup first
    if not backup_file():
        return False
    
    try:
        # Read the file content
        with open(TRADE_PROC_PATH, 'r') as f:
            content = f.read()
        
        # Restore from backup - safer approach
        shutil.copy2(BACKUP_PATH, TRADE_PROC_PATH)
        logger.info(f"Restoring from backup for safer editing")
        
        # Read the file content again
        with open(TRADE_PROC_PATH, 'r') as f:
            lines = f.readlines()
            
        # Find where to add the start_time parameter
        for i, line in enumerate(lines):
            if "risk_rule_obj = RiskRule.model_validate(risk_rule_item_dict)" in line:
                # Add comment before the change
                indent = len(line) - len(line.lstrip())
                spaces = ' ' * indent
                
                # Insert lines before the risk rule validation
                lines[i:i+1] = [
                    f"{spaces}# Create a copy of the risk rule to avoid modifying the original\n",
                    f"{spaces}risk_rule_dict = risk_rule_item_dict.copy()\n",
                    f"{spaces}\n",
                    f"{spaces}# Add the strategy start time to the risk rule params\n",
                    f"{spaces}if 'params' not in risk_rule_dict:\n",
                    f"{spaces}    risk_rule_dict['params'] = {{}}\n",
                    f"{spaces}\n",
                    f"{spaces}# Include start_time in the risk rule params from strategy.entry_start\n",
                    f"{spaces}if strategy_entry_time_int:\n",
                    f"{spaces}    risk_rule_dict['params']['start_time'] = strategy_entry_time_int\n",
                    f"{spaces}    trade_proc_debug_logger.info(f\"TRADE_PROC_DETAIL: Added start_time={{strategy_entry_time_int}} to risk rule params\")\n",
                    f"{spaces}\n",
                    f"{spaces}risk_rule_obj = RiskRule.model_validate(risk_rule_dict)\n",
                ]
                
                # Remove the original line
                lines.pop(i + 13)
                
                logger.info(f"Added start_time parameter at line {i}")
                break
        else:
            logger.warning("Could not find where to add the start_time parameter")
            return False
        
        # Write the updated content back
        with open(TRADE_PROC_PATH, 'w') as f:
            f.writelines(lines)
        
        logger.info(f"Successfully updated: {TRADE_PROC_PATH}")
        return True
    
    except Exception as e:
        logger.error(f"Error updating trade processing module: {e}")
        # Restore from backup if there was an error
        try:
            shutil.copy2(BACKUP_PATH, TRADE_PROC_PATH)
            logger.info(f"Restored from backup after error")
        except Exception as restore_error:
            logger.error(f"Error restoring from backup: {restore_error}")
        return False

def main():
    """Main function"""
    logger.info("Starting trade processing module update...")
    
    if update_trade_processing():
        logger.info("Update completed successfully")
        return 0
    else:
        logger.error("Update failed")
        return 1

if __name__ == "__main__":
    sys.exit(main()) 