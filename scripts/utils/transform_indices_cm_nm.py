#!/usr/bin/env python3
"""
Transform BANKNIFTY and MIDCAPNIFTY data to match the nifty_option_chain schema
Focus on current month (CM) and next month (NM) expiries only
Optimized version with faster processing
"""
import pandas as pd
import numpy as np
from datetime import datetime
import os

def get_expiry_type(trade_date, expiry_date):
    """Get expiry type - CM or NM only"""
    trade_month = trade_date.month
    trade_year = trade_date.year
    expiry_month = expiry_date.month
    expiry_year = expiry_date.year
    
    month_diff = (expiry_year - trade_year) * 12 + (expiry_month - trade_month)
    
    if month_diff == 0:
        return "CM"
    elif month_diff == 1:
        return "NM"
    else:
        return None  # Skip other expiries

def process_sample_data(input_file, index_name, output_dir, strike_increment, sample_dates=None):
    """Process only a sample of data for testing"""
    print(f"\nProcessing {input_file}...")
    
    # Read only required columns
    required_cols = [
        'date', 'time', 'strike', 'expiry', 'underlying_price', 'ATM',
        'CE_symbol', 'CE_open', 'CE_high', 'CE_low', 'CE_close', 'CE_volume', 'CE_oi',
        'PE_symbol', 'PE_open', 'PE_high', 'PE_low', 'PE_close', 'PE_volume', 'PE_oi',
        'call_implied_volatility', 'put_implied_volatility'
    ]
    
    # Read data
    df = pd.read_csv(input_file, usecols=lambda x: x in required_cols)
    print(f"Loaded {len(df)} rows")
    
    # Convert dates
    df['date'] = pd.to_datetime(df['date'], format='%y%m%d')
    df['expiry'] = pd.to_datetime(df['expiry'], format='%y%m%d')
    
    # Filter for CM and NM expiries only
    df['expiry_type'] = df.apply(lambda row: get_expiry_type(row['date'], row['expiry']), axis=1)
    df = df[df['expiry_type'].isin(['CM', 'NM'])]
    print(f"After CM/NM filter: {len(df)} rows")
    
    # If sample_dates provided, filter to those dates only
    if sample_dates:
        df = df[df['date'].dt.strftime('%Y-%m-%d').isin(sample_dates)]
        print(f"After date filter: {len(df)} rows")
    
    # Convert time
    if ':' in str(df['time'].iloc[0]):
        df['trade_time'] = df['time'].astype(str) + ':00'
    else:
        df['trade_time'] = pd.to_datetime(df['time'].astype(str).str.zfill(4), format='%H%M').dt.strftime('%H:%M:%S')
    
    # Calculate DTE
    df['dte'] = (df['expiry'] - df['date']).dt.days
    
    # Use existing ATM or calculate
    df['atm_strike'] = df['ATM'].fillna(
        (df['underlying_price'] / strike_increment).round() * strike_increment
    )
    
    # Calculate zone (simplified)
    df['strike_diff'] = (df['strike'] - df['atm_strike']).abs()
    df['zone_id'] = pd.cut(
        df['strike_diff'] / strike_increment,
        bins=[-np.inf, 0.5, 5, 10, 20, np.inf],
        labels=[0, 1, 2, 3, 4]
    ).astype(int)
    
    zone_map = {0: 'ATM', 1: 'OPEN', 2: 'NEAR', 3: 'MID', 4: 'FAR'}
    df['zone_name'] = df['zone_id'].map(zone_map)
    
    # Create output dataframe
    output_df = pd.DataFrame({
        'trade_date': df['date'].dt.strftime('%Y-%m-%d'),
        'trade_time': df['trade_time'],
        'expiry_date': df['expiry'].dt.strftime('%Y-%m-%d'),
        'index_name': index_name,
        'spot': df['underlying_price'],
        'atm_strike': df['atm_strike'],
        'strike': df['strike'],
        'dte': df['dte'],
        'expiry_bucket': df['expiry_type'],
        'zone_id': df['zone_id'],
        'zone_name': df['zone_name'],
        'call_strike_type': 'OTM',  # Simplified
        'put_strike_type': 'OTM',   # Simplified
        
        # Option data
        'ce_symbol': df['CE_symbol'].fillna(''),
        'ce_open': df['CE_open'],
        'ce_high': df['CE_high'],
        'ce_low': df['CE_low'],
        'ce_close': df['CE_close'],
        'ce_volume': df['CE_volume'].fillna(0),
        'ce_oi': df['CE_oi'].fillna(0),
        'ce_coi': 0,
        'ce_iv': df['call_implied_volatility'],
        'ce_delta': np.nan,
        'ce_gamma': np.nan,
        'ce_theta': np.nan,
        'ce_vega': np.nan,
        'ce_rho': np.nan,
        
        'pe_symbol': df['PE_symbol'].fillna(''),
        'pe_open': df['PE_open'],
        'pe_high': df['PE_high'],
        'pe_low': df['PE_low'],
        'pe_close': df['PE_close'],
        'pe_volume': df['PE_volume'].fillna(0),
        'pe_oi': df['PE_oi'].fillna(0),
        'pe_coi': 0,
        'pe_iv': df['put_implied_volatility'],
        'pe_delta': np.nan,
        'pe_gamma': np.nan,
        'pe_theta': np.nan,
        'pe_vega': np.nan,
        'pe_rho': np.nan,
        
        # Future data (not available)
        'future_open': np.nan,
        'future_high': np.nan,
        'future_low': np.nan,
        'future_close': np.nan,
        'future_volume': 0,
        'future_oi': 0,
        'future_coi': 0
    })
    
    # Sort and save
    output_df = output_df.sort_values(['trade_date', 'trade_time', 'strike'])
    
    # Create filename
    base_name = os.path.basename(input_file).replace('.csv', '')
    output_file = os.path.join(output_dir, f"{index_name.lower()}_{base_name}_cm_nm.csv")
    output_df.to_csv(output_file, index=False)
    
    print(f"\nSaved to: {output_file}")
    print(f"Total rows: {len(output_df)}")
    print(f"Date range: {output_df['trade_date'].min()} to {output_df['trade_date'].max()}")
    
    # Show summary
    expiry_summary = output_df.groupby(['expiry_bucket', 'expiry_date']).size().reset_index(name='count')
    print("\nExpiry Summary:")
    print(expiry_summary)
    
    return output_file

def main():
    """Process sample data for BANKNIFTY and MIDCAPNIFTY"""
    
    # Create output directory
    output_dir = "/srv/samba/shared/indices_cm_nm_data"
    os.makedirs(output_dir, exist_ok=True)
    
    # Process BANKNIFTY - sample dates only to avoid timeout
    print("="*60)
    print("PROCESSING BANKNIFTY DATA (Sample)")
    print("="*60)
    
    banknifty_file = "/tmp/banknifty_2025/filtered_processed_2025_jan_final_output.csv"
    if os.path.exists(banknifty_file):
        # Process only first few days as sample
        sample_dates = ['2025-01-01', '2025-01-02', '2025-01-03']
        process_sample_data(banknifty_file, "BANKNIFTY", output_dir, 100, sample_dates)
    
    # Process MIDCAPNIFTY - sample dates
    print("\n" + "="*60)
    print("PROCESSING MIDCAPNIFTY DATA (Sample)")
    print("="*60)
    
    midcap_file = "/srv/samba/shared/temp_extract/midcpnifty/2025_midcpnifty/filtered_processed_2025_midcpnifty_jan.csv"
    if os.path.exists(midcap_file):
        # Process only first few days as sample
        sample_dates = ['2025-01-01', '2025-01-02', '2025-01-03']
        process_sample_data(midcap_file, "MIDCAPNIFTY", output_dir, 25, sample_dates)
    
    print("\n" + "="*60)
    print("SAMPLE TRANSFORMATION COMPLETE")
    print("="*60)
    print(f"\nOutput directory: {output_dir}")
    print("\nNext steps:")
    print("1. Review the sample transformed data")
    print("2. Run full transformation if sample looks good")
    print("3. Load into HeavyDB using COPY FROM")

if __name__ == "__main__":
    main()