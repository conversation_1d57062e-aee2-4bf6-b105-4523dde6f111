#!/usr/bin/env python3
"""
Comprehensive fix for all metric calculation issues in backtester output
"""

import pandas as pd
import numpy as np
from datetime import datetime
import sys
import os

# Add path for imports
sys.path.append('/srv/samba/shared/bt/backtester_stable/BTRUN')

def fix_max_profit_loss_sheet(trans_df):
    """
    Properly calculate Max Profit and Loss sheet from transaction data
    """
    if trans_df.empty or 'Entry Date' not in trans_df.columns:
        return pd.DataFrame(columns=['Date', 'Max Profit', 'Max Loss', 'Max Profit Time', 'Max Loss Time'])
    
    print("Calculating Max Profit and Loss sheet...")
    
    # Ensure we have the required columns
    pnl_col = 'Net PNL' if 'Net PNL' in trans_df.columns else 'netPnlAfterExpenses'
    if pnl_col not in trans_df.columns:
        print("❌ No P&L column found")
        return pd.DataFrame()
    
    # Add time columns if missing
    if 'Exit at' not in trans_df.columns and 'exit_time' in trans_df.columns:
        trans_df['Exit at'] = trans_df['exit_time']
    
    daily_results = []
    
    # Group by date
    trans_df['Date'] = pd.to_datetime(trans_df['Entry Date']).dt.date
    
    for date, day_trades in trans_df.groupby('Date'):
        # Sort by exit time
        if 'Exit at' in day_trades.columns:
            day_trades = day_trades.sort_values('Exit at')
        
        # Calculate cumulative P&L throughout the day
        day_trades['Cumulative_PNL'] = day_trades[pnl_col].cumsum()
        
        # Track high water mark and drawdowns
        cumulative_values = day_trades['Cumulative_PNL'].values
        
        # Max profit is the highest point reached
        max_profit = cumulative_values.max() if len(cumulative_values) > 0 else 0
        
        # Calculate intraday drawdown
        if len(cumulative_values) > 0:
            # Start from 0 (beginning of day)
            values_with_start = np.concatenate([[0], cumulative_values])
            
            # Find the maximum drawdown
            running_max = np.maximum.accumulate(values_with_start)
            drawdowns = values_with_start - running_max
            max_drawdown = drawdowns.min()
            
            # Max loss is the worst point reached (could be negative cumulative or drawdown)
            min_cumulative = cumulative_values.min()
            max_loss = min(max_drawdown, min_cumulative, 0)  # Ensure it's negative or 0
        else:
            max_loss = 0
        
        # Find times
        if max_profit > 0:
            max_profit_idx = day_trades['Cumulative_PNL'].idxmax()
            max_profit_time = day_trades.loc[max_profit_idx, 'Exit at'] if 'Exit at' in day_trades.columns else '15:30:00'
            # Format time properly
            if pd.notna(max_profit_time):
                if isinstance(max_profit_time, str) and ':' not in str(max_profit_time):
                    # Convert HHMMSS to HH:MM:SS
                    time_str = str(max_profit_time).zfill(6)
                    max_profit_time = f"{time_str[:2]}:{time_str[2:4]}:{time_str[4:6]}"
        else:
            max_profit_time = pd.NaT
        
        if max_loss < 0:
            # Find when the worst point occurred
            if len(values_with_start) > 1:
                worst_idx = np.argmin(values_with_start[1:])  # Skip the 0 start
                if worst_idx < len(day_trades):
                    max_loss_idx = day_trades.index[worst_idx]
                    max_loss_time = day_trades.loc[max_loss_idx, 'Exit at'] if 'Exit at' in day_trades.columns else '09:16:00'
                    # Format time
                    if pd.notna(max_loss_time) and isinstance(max_loss_time, str) and ':' not in str(max_loss_time):
                        time_str = str(max_loss_time).zfill(6)
                        max_loss_time = f"{time_str[:2]}:{time_str[2:4]}:{time_str[4:6]}"
                else:
                    max_loss_time = '09:16:00'
            else:
                max_loss_time = '09:16:00'
        else:
            max_loss_time = pd.NaT
        
        daily_results.append({
            'Date': date,
            'Max Profit': round(max_profit, 2),
            'Max Loss': round(max_loss, 2),
            'Max Profit Time': max_profit_time,
            'Max Loss Time': max_loss_time
        })
    
    result_df = pd.DataFrame(daily_results)
    print(f"✓ Calculated {len(result_df)} days of Max P&L data")
    return result_df

def calculate_comprehensive_metrics(trans_df, portfolio_name='PORTFOLIO'):
    """
    Calculate comprehensive metrics for the Metrics sheet
    """
    metrics_list = []
    
    if trans_df.empty:
        return pd.DataFrame()
    
    # Ensure we have required columns
    pnl_col = 'Net PNL' if 'Net PNL' in trans_df.columns else 'netPnlAfterExpenses'
    
    # Basic metrics
    total_trades = len(trans_df)
    
    if pnl_col in trans_df.columns:
        total_pnl = trans_df[pnl_col].sum()
        avg_pnl = trans_df[pnl_col].mean()
        
        # Win/Loss metrics
        winning_trades = trans_df[trans_df[pnl_col] > 0]
        losing_trades = trans_df[trans_df[pnl_col] < 0]
        
        win_count = len(winning_trades)
        loss_count = len(losing_trades)
        win_rate = (win_count / total_trades * 100) if total_trades > 0 else 0
        
        # Average win/loss
        avg_win = winning_trades[pnl_col].mean() if len(winning_trades) > 0 else 0
        avg_loss = losing_trades[pnl_col].mean() if len(losing_trades) > 0 else 0
        
        # Risk reward ratio
        risk_reward = abs(avg_win / avg_loss) if avg_loss != 0 else 0
        
        # Max win/loss trades
        max_win_trade = trans_df[pnl_col].max()
        max_loss_trade = trans_df[pnl_col].min()
        
        # Calculate returns and Sharpe
        if 'Entry Date' in trans_df.columns:
            trans_df['Date'] = pd.to_datetime(trans_df['Entry Date']).dt.date
            daily_pnl = trans_df.groupby('Date')[pnl_col].sum()
            
            if len(daily_pnl) > 1:
                # Assuming starting capital of 100000
                starting_capital = 100000
                daily_returns = daily_pnl / starting_capital
                
                # Sharpe ratio (annualized)
                if daily_returns.std() > 0:
                    sharpe = np.sqrt(252) * daily_returns.mean() / daily_returns.std()
                else:
                    sharpe = 0
                
                # Max drawdown
                cumulative_returns = (1 + daily_returns).cumprod()
                running_max = cumulative_returns.cummax()
                drawdown = (cumulative_returns - running_max) / running_max
                max_dd = drawdown.min() * 100
                
                # Calmar ratio
                annual_return = daily_returns.mean() * 252 * 100
                calmar = annual_return / abs(max_dd) if max_dd != 0 else 0
            else:
                sharpe = 0
                max_dd = 0
                calmar = 0
        else:
            sharpe = 0
            max_dd = 0
            calmar = 0
        
        # Create metrics list
        metrics_data = [
            ('Total Trades', total_trades),
            ('Total P&L', f'{total_pnl:.2f}'),
            ('Average P&L per Trade', f'{avg_pnl:.2f}'),
            ('Win Rate', f'{win_rate:.2f}%'),
            ('Winning Trades', win_count),
            ('Losing Trades', loss_count),
            ('Average Win', f'{avg_win:.2f}'),
            ('Average Loss', f'{avg_loss:.2f}'),
            ('Risk Reward Ratio', f'{risk_reward:.2f}'),
            ('Max Win Trade', f'{max_win_trade:.2f}'),
            ('Max Loss Trade', f'{max_loss_trade:.2f}'),
            ('Sharpe Ratio', f'{sharpe:.3f}'),
            ('Max Drawdown', f'{max_dd:.2f}%'),
            ('Calmar Ratio', f'{calmar:.3f}')
        ]
        
        for metric, value in metrics_data:
            metrics_list.append({
                'Metric': metric,
                portfolio_name: value
            })
    
    return pd.DataFrame(metrics_list)

def fix_all_metrics(input_file, output_file):
    """
    Fix all metric issues in the output file
    """
    print("FIXING ALL METRICS IN OUTPUT FILE")
    print("="*80)
    
    # Read all sheets
    with pd.ExcelFile(input_file) as xl:
        sheets = {}
        for sheet_name in xl.sheet_names:
            sheets[sheet_name] = pd.read_excel(xl, sheet_name=sheet_name)
    
    # Get transaction data
    trans_df = sheets.get('PORTFOLIO Trans', pd.DataFrame())
    
    if not trans_df.empty:
        # 1. Recalculate Max Profit and Loss sheet
        print("\n1. Recalculating Max Profit and Loss sheet...")
        max_pl_df = fix_max_profit_loss_sheet(trans_df)
        sheets['Max Profit and Loss'] = max_pl_df
        
        # 2. Recalculate Metrics sheet
        print("\n2. Recalculating Metrics sheet...")
        metrics_df = calculate_comprehensive_metrics(trans_df)
        if not metrics_df.empty:
            sheets['Metrics'] = metrics_df
    
    # Write updated file
    print(f"\n3. Writing updated file to: {output_file}")
    with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
        for sheet_name, df in sheets.items():
            df.to_excel(writer, sheet_name=sheet_name, index=False)
    
    print("\n✓ All metrics fixed successfully!")
    
    # Show summary
    if 'Max Profit and Loss' in sheets:
        max_pl_df = sheets['Max Profit and Loss']
        print(f"\nMax Profit and Loss summary:")
        print(f"  Days with data: {len(max_pl_df)}")
        if 'Max Loss' in max_pl_df.columns:
            non_zero_loss = (max_pl_df['Max Loss'] < 0).sum()
            print(f"  Days with losses: {non_zero_loss}")
            if non_zero_loss > 0:
                print(f"  Max Loss range: {max_pl_df['Max Loss'].min():.2f} to {max_pl_df['Max Loss'].max():.2f}")

if __name__ == "__main__":
    input_file = "/srv/samba/shared/test_metrics_fixed.xlsx"
    output_file = "/srv/samba/shared/test_metrics_fully_fixed.xlsx"
    
    fix_all_metrics(input_file, output_file)