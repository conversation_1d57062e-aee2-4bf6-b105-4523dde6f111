#!/usr/bin/env python3
"""
ETL Performance Monitoring System
Tracks loading speeds, memory usage, GPU utilization, and query performance
"""

import os
import sys
import time
import json
import psutil
import logging
import subprocess
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
import pandas as pd
from heavydb import connect
import numpy as np

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('/srv/samba/shared/logs/etl_performance.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class ETLPerformanceMonitor:
    """Monitor and track ETL performance metrics"""
    
    def __init__(self):
        self.conn = None
        self.metrics_table = "etl_performance_metrics"
        self.connect_db()
        self.ensure_metrics_table()
        
    def connect_db(self):
        """Connect to HeavyDB"""
        try:
            self.conn = connect(
                host='localhost',
                port=6274,
                user='admin',
                password='HyperInteractive',
                dbname='heavyai'
            )
            logger.info("Connected to HeavyDB")
        except Exception as e:
            logger.error(f"Failed to connect to HeavyDB: {e}")
            raise
            
    def ensure_metrics_table(self):
        """Create metrics table if it doesn't exist"""
        try:
            cursor = self.conn.cursor()
            cursor.execute(f"""
                CREATE TABLE IF NOT EXISTS {self.metrics_table} (
                    metric_time TIMESTAMP,
                    index_name TEXT ENCODING DICT(32),
                    operation TEXT ENCODING DICT(32),
                    rows_processed BIGINT,
                    duration_seconds DOUBLE,
                    rows_per_second DOUBLE,
                    cpu_percent DOUBLE,
                    memory_mb DOUBLE,
                    gpu_utilization DOUBLE,
                    gpu_memory_mb DOUBLE,
                    disk_read_mb DOUBLE,
                    disk_write_mb DOUBLE,
                    query_count INTEGER,
                    error_count INTEGER,
                    status TEXT ENCODING DICT(32)
                ) WITH (fragment_size=1000000);
            """)
            logger.info("Metrics table ready")
        except Exception as e:
            if "already exists" not in str(e):
                logger.error(f"Failed to create metrics table: {e}")
                
    def get_gpu_metrics(self) -> Dict[str, float]:
        """Get GPU utilization and memory usage"""
        try:
            result = subprocess.run([
                'nvidia-smi', 
                '--query-gpu=utilization.gpu,memory.used',
                '--format=csv,noheader,nounits'
            ], capture_output=True, text=True)
            
            if result.returncode == 0:
                values = result.stdout.strip().split(', ')
                return {
                    'gpu_utilization': float(values[0]),
                    'gpu_memory_mb': float(values[1])
                }
        except Exception as e:
            logger.warning(f"Could not get GPU metrics: {e}")
            
        return {'gpu_utilization': 0.0, 'gpu_memory_mb': 0.0}
        
    def get_system_metrics(self) -> Dict[str, float]:
        """Get system CPU and memory metrics"""
        return {
            'cpu_percent': psutil.cpu_percent(interval=1),
            'memory_mb': psutil.virtual_memory().used / (1024 * 1024),
            'disk_read_mb': psutil.disk_io_counters().read_bytes / (1024 * 1024),
            'disk_write_mb': psutil.disk_io_counters().write_bytes / (1024 * 1024)
        }
        
    def monitor_etl_operation(self, index_name: str, operation: str, 
                            rows_processed: int, duration: float,
                            status: str = "success", error_count: int = 0):
        """Record ETL operation metrics"""
        try:
            # Get system and GPU metrics
            sys_metrics = self.get_system_metrics()
            gpu_metrics = self.get_gpu_metrics()
            
            # Calculate rows per second
            rows_per_second = rows_processed / duration if duration > 0 else 0
            
            # Insert metrics
            cursor = self.conn.cursor()
            cursor.execute(f"""
                INSERT INTO {self.metrics_table} VALUES (
                    NOW(),
                    '{index_name}',
                    '{operation}',
                    {rows_processed},
                    {duration},
                    {rows_per_second},
                    {sys_metrics['cpu_percent']},
                    {sys_metrics['memory_mb']},
                    {gpu_metrics['gpu_utilization']},
                    {gpu_metrics['gpu_memory_mb']},
                    {sys_metrics['disk_read_mb']},
                    {sys_metrics['disk_write_mb']},
                    0,  -- query_count
                    {error_count},
                    '{status}'
                )
            """)
            
            logger.info(f"Recorded metrics for {index_name} {operation}: "
                       f"{rows_per_second:.0f} rows/sec")
                       
        except Exception as e:
            logger.error(f"Failed to record metrics: {e}")
            
    def get_performance_summary(self, hours: int = 24) -> pd.DataFrame:
        """Get performance summary for the last N hours"""
        try:
            query = f"""
                SELECT 
                    index_name,
                    operation,
                    COUNT(*) as operation_count,
                    SUM(rows_processed) as total_rows,
                    AVG(rows_per_second) as avg_rows_per_sec,
                    MAX(rows_per_second) as max_rows_per_sec,
                    MIN(rows_per_second) as min_rows_per_sec,
                    AVG(duration_seconds) as avg_duration,
                    AVG(cpu_percent) as avg_cpu,
                    AVG(memory_mb) as avg_memory_mb,
                    AVG(gpu_utilization) as avg_gpu_util,
                    SUM(error_count) as total_errors
                FROM {self.metrics_table}
                WHERE metric_time >= NOW() - INTERVAL '{hours}' HOUR
                GROUP BY index_name, operation
                ORDER BY index_name, operation
            """
            
            df = pd.read_sql(query, self.conn)
            return df
            
        except Exception as e:
            logger.error(f"Failed to get performance summary: {e}")
            return pd.DataFrame()
            
    def get_data_freshness(self) -> pd.DataFrame:
        """Check data freshness for all indices"""
        try:
            indices = ['NIFTY', 'BANKNIFTY', 'MIDCAPNIFTY', 'SENSEX', 'FINNIFTY', 'BANKEX']
            freshness_data = []
            
            # Optimized: Get all indices data in a single query
            try:
                cursor = self.conn.cursor()
                
                # Get all indices data at once using GROUP BY
                cursor.execute("""
                    SELECT 
                        index_name,
                        COUNT(*) as row_count,
                        MAX(trade_date) as latest_date,
                        MIN(trade_date) as earliest_date,
                        COUNT(DISTINCT trade_date) as trading_days
                    FROM nifty_option_chain
                    WHERE index_name IN ('NIFTY', 'BANKNIFTY', 'MIDCAPNIFTY', 'SENSEX', 'FINNIFTY', 'BANKEX')
                    GROUP BY index_name
                """)
                
                results = cursor.fetchall()
                results_dict = {row[0]: row for row in results}
                
                # Process results for each index
                for index in indices:
                    if index in results_dict:
                        row = results_dict[index]
                        days_behind = (datetime.now().date() - row[2]).days if row[2] else -1
                        freshness_data.append({
                            'index_name': index,
                            'row_count': row[1],
                            'latest_date': row[2],
                            'earliest_date': row[3],
                            'trading_days': row[4],
                            'days_behind': days_behind,
                            'status': 'Current' if days_behind <= 1 else 
                                     'Recent' if days_behind <= 7 else 
                                     'Stale' if days_behind <= 30 else 'Outdated'
                        })
                    else:
                        freshness_data.append({
                            'index_name': index,
                            'row_count': 0,
                            'latest_date': None,
                            'earliest_date': None,
                            'trading_days': 0,
                            'days_behind': -1,
                            'status': 'No Data'
                        })
                        
            except Exception as e:
                logger.error(f"Error in optimized freshness query: {e}")
                # Fallback to individual queries with timeout
                for index in indices:
                    try:
                        cursor = self.conn.cursor()
                        cursor.execute(f"""
                            SELECT 
                                COUNT(*) as row_count,
                                MAX(trade_date) as latest_date,
                                MIN(trade_date) as earliest_date,
                                COUNT(DISTINCT trade_date) as trading_days
                            FROM nifty_option_chain
                            WHERE index_name = '{index}'
                        """)
                    
                        result = cursor.fetchone()
                        if result and result[0] > 0:
                            days_behind = (datetime.now().date() - result[1]).days
                            freshness_data.append({
                                'index_name': index,
                                'row_count': result[0],
                                'latest_date': result[1],
                                'earliest_date': result[2],
                                'trading_days': result[3],
                                'days_behind': days_behind,
                                'status': 'Current' if days_behind <= 1 else 
                                         'Recent' if days_behind <= 7 else 
                                         'Stale' if days_behind <= 30 else 'Outdated'
                            })
                        else:
                            freshness_data.append({
                                'index_name': index,
                                'row_count': 0,
                                'latest_date': None,
                                'earliest_date': None,
                                'trading_days': 0,
                                'days_behind': -1,
                                'status': 'No Data'
                            })
                    except Exception as e:
                        logger.warning(f"Could not check freshness for {index}: {e}")
                        freshness_data.append({
                            'index_name': index,
                            'row_count': 0,
                            'latest_date': None,
                            'earliest_date': None,
                            'trading_days': 0,
                            'days_behind': -1,
                            'status': 'Error'
                        })
                    
            return pd.DataFrame(freshness_data)
            
        except Exception as e:
            logger.error(f"Failed to check data freshness: {e}")
            return pd.DataFrame()
            
    def analyze_performance_trends(self, days: int = 7) -> Dict[str, any]:
        """Analyze performance trends over time"""
        try:
            # Get hourly performance trends
            query = f"""
                SELECT 
                    CAST(metric_time AS DATE) as day, EXTRACT(HOUR FROM metric_time) as hour_of_day,
                    AVG(rows_per_second) as avg_speed,
                    AVG(cpu_percent) as avg_cpu,
                    AVG(gpu_utilization) as avg_gpu,
                    SUM(rows_processed) as total_rows,
                    COUNT(*) as operations
                FROM {self.metrics_table}
                WHERE metric_time >= NOW() - INTERVAL '{days}' DAY
                    AND status = 'success'
                GROUP BY day, hour_of_day
ORDER BY day, hour_of_day
            """
            
            df = pd.read_sql(query, self.conn)
            
            # Calculate trends
            if len(df) > 1:
                speed_trend = np.polyfit(range(len(df)), df['avg_speed'], 1)[0]
                cpu_trend = np.polyfit(range(len(df)), df['avg_cpu'], 1)[0]
                gpu_trend = np.polyfit(range(len(df)), df['avg_gpu'], 1)[0]
                
                return {
                    'speed_trend': 'improving' if speed_trend > 0 else 'degrading',
                    'speed_change_per_hour': speed_trend,
                    'cpu_trend': 'increasing' if cpu_trend > 0 else 'decreasing',
                    'gpu_trend': 'increasing' if gpu_trend > 0 else 'decreasing',
                    'avg_operations_per_hour': df['operations'].mean(),
                    'peak_hour': df.loc[df['avg_speed'].idxmax()]['hour'] if len(df) > 0 else None,
                    'recommendations': self._generate_recommendations(df, speed_trend)
                }
            else:
                return {'status': 'insufficient_data'}
                
        except Exception as e:
            logger.error(f"Failed to analyze trends: {e}")
            return {'status': 'error', 'message': str(e)}
            
    def _generate_recommendations(self, df: pd.DataFrame, speed_trend: float) -> List[str]:
        """Generate performance recommendations"""
        recommendations = []
        
        # Check if performance is degrading
        if speed_trend < -10:  # Losing more than 10 rows/sec per hour
            recommendations.append("Performance is degrading significantly. Consider:")
            recommendations.append("- Checking for table fragmentation")
            recommendations.append("- Reviewing concurrent query load")
            recommendations.append("- Increasing GPU memory allocation")
            
        # Check CPU usage
        avg_cpu = df['avg_cpu'].mean()
        if avg_cpu > 80:
            recommendations.append(f"High CPU usage ({avg_cpu:.1f}%). Consider:")
            recommendations.append("- Enabling GPU acceleration if not already")
            recommendations.append("- Reducing concurrent operations")
            
        # Check GPU usage
        avg_gpu = df['avg_gpu'].mean()
        if avg_gpu < 20 and len(df) > 10:
            recommendations.append(f"Low GPU utilization ({avg_gpu:.1f}%). Consider:")
            recommendations.append("- Increasing batch sizes")
            recommendations.append("- Enabling more GPU workers")
        elif avg_gpu > 90:
            recommendations.append(f"High GPU utilization ({avg_gpu:.1f}%). Consider:")
            recommendations.append("- Distributing load across time")
            recommendations.append("- Optimizing queries")
            
        # Check operation frequency
        ops_per_hour = df['operations'].mean()
        if ops_per_hour > 100:
            recommendations.append(f"High operation frequency ({ops_per_hour:.0f}/hour). Consider:")
            recommendations.append("- Batching smaller operations")
            recommendations.append("- Implementing operation queuing")
            
        if not recommendations:
            recommendations.append("Performance is optimal. No recommendations at this time.")
            
        return recommendations
        
    def generate_performance_report(self) -> str:
        """Generate a comprehensive performance report"""
        try:
            report = []
            report.append("=" * 80)
            report.append(f"ETL PERFORMANCE REPORT - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            report.append("=" * 80)
            
            # Data freshness
            report.append("\n## DATA FRESHNESS STATUS")
            report.append("-" * 40)
            freshness_df = self.get_data_freshness()
            if not freshness_df.empty:
                for _, row in freshness_df.iterrows():
                    status_emoji = "✅" if row['status'] == 'Current' else \
                                  "⚠️" if row['status'] in ['Recent', 'Stale'] else "❌"
                    report.append(f"{status_emoji} {row['index_name']}: {row['status']} "
                                f"({row['days_behind']} days behind)")
                    if row['row_count'] > 0:
                        report.append(f"   - Rows: {row['row_count']:,}")
                        report.append(f"   - Latest: {row['latest_date']}")
                        
            # Performance summary (last 24 hours)
            report.append("\n## PERFORMANCE SUMMARY (Last 24 Hours)")
            report.append("-" * 40)
            perf_df = self.get_performance_summary(24)
            if not perf_df.empty:
                for index in perf_df['index_name'].unique():
                    index_df = perf_df[perf_df['index_name'] == index]
                    report.append(f"\n{index}:")
                    for _, row in index_df.iterrows():
                        report.append(f"  {row['operation']}:")
                        report.append(f"    - Operations: {row['operation_count']}")
                        report.append(f"    - Avg Speed: {row['avg_rows_per_sec']:,.0f} rows/sec")
                        report.append(f"    - Total Rows: {row['total_rows']:,}")
                        if row['total_errors'] > 0:
                            report.append(f"    - ⚠️ Errors: {row['total_errors']}")
                            
            # Performance trends
            report.append("\n## PERFORMANCE TRENDS (Last 7 Days)")
            report.append("-" * 40)
            trends = self.analyze_performance_trends(7)
            if 'speed_trend' in trends:
                report.append(f"Speed Trend: {trends['speed_trend'].upper()}")
                report.append(f"CPU Usage Trend: {trends['cpu_trend']}")
                report.append(f"GPU Usage Trend: {trends['gpu_trend']}")
                if trends.get('peak_hour'):
                    report.append(f"Peak Performance Hour: {trends['peak_hour']}")
                    
                report.append("\n### Recommendations:")
                for rec in trends.get('recommendations', []):
                    report.append(f"  • {rec}")
                    
            # System resources
            report.append("\n## CURRENT SYSTEM RESOURCES")
            report.append("-" * 40)
            sys_metrics = self.get_system_metrics()
            gpu_metrics = self.get_gpu_metrics()
            report.append(f"CPU Usage: {sys_metrics['cpu_percent']:.1f}%")
            report.append(f"Memory Usage: {sys_metrics['memory_mb']:,.0f} MB")
            report.append(f"GPU Utilization: {gpu_metrics['gpu_utilization']:.1f}%")
            report.append(f"GPU Memory: {gpu_metrics['gpu_memory_mb']:,.0f} MB")
            
            report.append("\n" + "=" * 80)
            
            return "\n".join(report)
            
        except Exception as e:
            logger.error(f"Failed to generate report: {e}")
            return f"Error generating report: {e}"
            
    def check_alerts(self) -> List[Dict[str, any]]:
        """Check for performance alerts"""
        alerts = []
        
        try:
            # Check for recent failures
            cursor = self.conn.cursor()
            cursor.execute(f"""
                SELECT index_name, operation, COUNT(*) as failure_count
                FROM {self.metrics_table}
                WHERE metric_time >= NOW() - INTERVAL '1' HOUR
                    AND status != 'success'
                GROUP BY index_name, operation
                HAVING COUNT(*) > 2
            """)
            
            for row in cursor.fetchall():
                alerts.append({
                    'level': 'error',
                    'type': 'repeated_failures',
                    'message': f"{row[0]} {row[1]} failed {row[2]} times in last hour",
                    'index': row[0],
                    'operation': row[1]
                })
                
            # Check for performance degradation
            cursor.execute(f"""
                SELECT 
                    index_name,
                    AVG(CASE WHEN metric_time >= NOW() - INTERVAL '1' HOUR 
                        THEN rows_per_second END) as recent_speed,
                    AVG(CASE WHEN metric_time >= NOW() - INTERVAL '25' HOUR 
                        AND metric_time < NOW() - INTERVAL '24' HOUR 
                        THEN rows_per_second END) as yesterday_speed
                FROM {self.metrics_table}
                WHERE metric_time >= NOW() - INTERVAL '25' HOUR
                    AND status = 'success'
                GROUP BY index_name
                HAVING recent_speed < yesterday_speed * 0.7
            """)
            
            for row in cursor.fetchall():
                if row[1] and row[2]:
                    degradation = ((row[2] - row[1]) / row[2]) * 100
                    alerts.append({
                        'level': 'warning',
                        'type': 'performance_degradation',
                        'message': f"{row[0]} performance degraded by {degradation:.1f}%",
                        'index': row[0],
                        'current_speed': row[1],
                        'previous_speed': row[2]
                    })
                    
            # Check for stale data
            freshness_df = self.get_data_freshness()
            for _, row in freshness_df.iterrows():
                if row['status'] == 'Outdated' and row['row_count'] > 0:
                    alerts.append({
                        'level': 'warning',
                        'type': 'stale_data',
                        'message': f"{row['index_name']} data is {row['days_behind']} days old",
                        'index': row['index_name'],
                        'latest_date': str(row['latest_date'])
                    })
                    
        except Exception as e:
            logger.error(f"Failed to check alerts: {e}")
            alerts.append({
                'level': 'error',
                'type': 'monitoring_error',
                'message': f"Failed to check alerts: {str(e)}"
            })
            
        return alerts

def main():
    """Run performance monitoring"""
    monitor = ETLPerformanceMonitor()
    
    # Generate and print report
    report = monitor.generate_performance_report()
    print(report)
    
    # Check for alerts
    alerts = monitor.check_alerts()
    if alerts:
        print("\n## ALERTS")
        print("-" * 40)
        for alert in alerts:
            emoji = "🔴" if alert['level'] == 'error' else "🟡"
            print(f"{emoji} {alert['message']}")
            
    # Save report
    report_path = f"/srv/samba/shared/logs/etl_performance_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
    with open(report_path, 'w') as f:
        f.write(report)
    print(f"\nReport saved to: {report_path}")

if __name__ == "__main__":
    main()