"""Generate SQL fragment for legs with full TBS column support."""
from __future__ import annotations

import logging
from datetime import datetime, time as datetime_time
from typing import Tuple, List, Optional, Union

from ..models.leg import LegModel
from ..models.common import OptionType, StrikeRule
from .resolver import table_for_index, expiry_predicate, strike_order_clause

logger = logging.getLogger(__name__)

def _convert_time_format(value: Union[str, int, datetime_time]) -> str:
    """Convert various time formats to HH:MM:SS.
    
    Examples:
        91600 -> "09:16:00"
        "91600" -> "09:16:00"
        "9:16:00" -> "09:16:00"
        92000 -> "09:20:00"
    """
    if isinstance(value, datetime_time):
        return value.strftime("%H:%M:%S")
    
    s = str(value).strip()
    
    # Already in HH:MM:SS format
    if ":" in s:
        # Ensure proper formatting with leading zeros
        parts = s.split(":")
        hh = parts[0].zfill(2)
        mm = parts[1].zfill(2) if len(parts) > 1 else "00"
        ss = parts[2].zfill(2) if len(parts) > 2 else "00"
        return f"{hh}:{mm}:{ss}"
    
    # HHMMSS format
    if len(s) == 5:
        s = "0" + s  # Add leading zero
    
    if len(s) == 6:
        hh = s[0:2]
        mm = s[2:4]
        ss = s[4:6]
        return f"{hh}:{mm}:{ss}"
    
    # Default
    return "09:16:00"

def _get_strike_offset_filter(leg: LegModel, option_type: OptionType, index: str) -> Optional[str]:
    """Generate strike offset filter for ATM+/-N selections.
    
    Handles the standardized offset logic from column_mapping_ml_tbs.md:
    - Positive StrikeValue = OTM for both CE and PE
    - Negative StrikeValue = ITM for both CE and PE
    """
    strike_rule = getattr(leg, 'strike_rule', None)
    strike_value = getattr(leg, 'strike_value', 0)
    
    if strike_rule != StrikeRule.ATM or strike_value == 0:
        return None
    
    # Determine step size based on index
    step_sizes = {
        'NIFTY': 50,
        'BANKNIFTY': 100,
        'FINNIFTY': 50,
        'MIDCPNIFTY': 25,
        'SENSEX': 100,
        'BANKEX': 100
    }
    step_size = step_sizes.get(index.upper(), 50)
    
    # Calculate offset
    offset = strike_value * step_size
    
    # For CE: positive = OTM (higher), negative = ITM (lower)
    # For PE: positive = OTM (lower), negative = ITM (higher)
    if option_type == OptionType.CALL:
        return f"oc.strike = oc.atm_strike + {offset}"
    else:  # PUT
        return f"oc.strike = oc.atm_strike - {offset}"

def build_leg_sql(
    leg: LegModel, 
    trade_date_iso: str, 
    alias: str = "oc",
    entry_time: Optional[str] = None,
    strategy_extra_params: Optional[dict] = None
) -> str:
    """Build SQL to fetch option chain data for a single leg with full TBS support.
    
    Parameters
    ----------
    leg : LegModel
        The leg configuration
    trade_date_iso : str
        Trade date in ISO format (YYYY-MM-DD)
    alias : str
        Table alias (default: "oc")
    entry_time : str, optional
        Entry time (will be overridden by StrikeSelectionTime if present)
    strategy_extra_params : dict, optional
        Strategy-level extra parameters (GeneralParameter columns)
    
    Returns
    -------
    str
        SQL query string
    """
    table = table_for_index(leg.index)
    extra_params = getattr(leg, 'extra_params', {})
    
    # Merge strategy extra params with leg extra params
    all_params = {}
    if strategy_extra_params:
        all_params.update(strategy_extra_params)
    if extra_params:
        all_params.update(extra_params)
    
    # Initialize WHERE parts
    where_parts = [f"trade_date = date '{trade_date_iso}'"]
    
    # 1. Handle StrikeSelectionTime
    strike_time = entry_time or "09:16:00"
    if 'StrikeSelectionTime' in all_params:
        strike_time_raw = all_params['StrikeSelectionTime']
        strike_time = _convert_time_format(strike_time_raw)
        logger.info(f"Using StrikeSelectionTime: {strike_time} (from {strike_time_raw})")
    
    where_parts.append(f"trade_time = time '{strike_time}'")
    
    # 2. Add DTE filtering
    dte_filter = all_params.get('DTE')
    if dte_filter is not None:
        try:
            dte_value = int(dte_filter)
            if dte_value == 0:
                # For DTE=0, only select rows where trade_date equals expiry_date
                where_parts.append("oc.trade_date = oc.expiry_date")
                logger.info("DTE=0: Filtering for expiry day only trades")
            else:
                # For other DTE values, use the dte column
                where_parts.append(f"oc.dte = {dte_value}")
                logger.info(f"DTE={dte_value}: Filtering for specific days till expiry")
        except (ValueError, TypeError) as e:
            logger.warning(f"Invalid DTE value: {dte_filter}, error: {e}")
    
    # 3. Add weekday filtering
    weekdays_str = all_params.get('Weekdays')
    if weekdays_str and weekdays_str != "1,2,3,4,5":  # Only filter if not all weekdays
        try:
            weekday_list = [int(d.strip()) for d in weekdays_str.split(',')]
            if weekday_list:
                # HeavyDB DOW: 0=Sun, 1=Mon, ..., 6=Sat
                # Excel: 1=Mon, 2=Tue, ..., 5=Fri
                # No conversion needed as HeavyDB matches Excel for Mon-Fri
                where_parts.append(f"EXTRACT(DOW FROM oc.trade_date) IN ({','.join(map(str, weekday_list))})")
                logger.info(f"Weekday filter: {weekdays_str}")
        except (ValueError, TypeError) as e:
            logger.warning(f"Invalid weekday format: {weekdays_str}, error: {e}")
    
    # 4. Handle expiry selection
    table_name = table_for_index(leg.index)
    expiry_bucket = extra_params.get('expiry_bucket')
    
    # Check for OnExpiryDayTradeNextExpiry
    if all_params.get('OnExpiryDayTradeNextExpiry', '').upper() == 'YES':
        # If today is expiry day and this flag is set, use next expiry
        where_parts.append("""
            CASE 
                WHEN oc.trade_date = oc.expiry_date THEN oc.expiry_bucket = 'NW'
                ELSE oc.expiry_bucket = 'CW'
            END
        """)
    elif expiry_bucket:
        where_parts.append(f"expiry_bucket = '{expiry_bucket}'")
    else:
        where_parts.append(expiry_predicate(leg.expiry_rule, trade_date_iso, alias, table_name))
    
    # 5. Handle option type and strike selection
    moneyness_filter = None
    symbol_col = None
    price_col = None
    iv_col = None
    delta_col = None
    
    if leg.option_type == OptionType.CALL:
        symbol_col = "ce_symbol"
        where_parts.append(f"ce_symbol IS NOT NULL")
        price_col = "ce_close"
        iv_col = "ce_iv"
        delta_col = "ce_delta"
        
        # Handle strike selection
        if getattr(leg, 'strike_rule', None) == StrikeRule.ATM:
            # Check for ATM offset
            offset_filter = _get_strike_offset_filter(leg, OptionType.CALL, leg.index)
            if offset_filter:
                where_parts.append(offset_filter)
            else:
                moneyness_filter = "call_strike_type = 'ATM'"
        elif getattr(leg, 'strike_rule', None) == StrikeRule.ITM:
            # Handle ITM1, ITM2, etc.
            strike_distance = getattr(leg, 'strike_distance', 1)
            moneyness_filter = f"call_strike_type = 'ITM{strike_distance}'"
        elif getattr(leg, 'strike_rule', None) == StrikeRule.OTM:
            # Handle OTM1, OTM2, etc.
            strike_distance = getattr(leg, 'strike_distance', 1)
            moneyness_filter = f"call_strike_type = 'OTM{strike_distance}'"
            
    elif leg.option_type == OptionType.PUT:
        symbol_col = "pe_symbol"
        where_parts.append(f"pe_symbol IS NOT NULL")
        price_col = "pe_close"
        iv_col = "pe_iv"
        delta_col = "pe_delta"
        
        # Handle strike selection
        if getattr(leg, 'strike_rule', None) == StrikeRule.ATM:
            # Check for ATM offset
            offset_filter = _get_strike_offset_filter(leg, OptionType.PUT, leg.index)
            if offset_filter:
                where_parts.append(offset_filter)
            else:
                moneyness_filter = "put_strike_type = 'ATM'"
        elif getattr(leg, 'strike_rule', None) == StrikeRule.ITM:
            strike_distance = getattr(leg, 'strike_distance', 1)
            moneyness_filter = f"put_strike_type = 'ITM{strike_distance}'"
        elif getattr(leg, 'strike_rule', None) == StrikeRule.OTM:
            strike_distance = getattr(leg, 'strike_distance', 1)
            moneyness_filter = f"put_strike_type = 'OTM{strike_distance}'"
    else:
        # FUT or default
        symbol_col = "ce_symbol"
        price_col = "underlying_price"
        iv_col = "NULL"
        delta_col = "NULL"
    
    # Handle FIXED strike rule
    if getattr(leg, 'strike_rule', None) == StrikeRule.FIXED and leg.fixed_strike is not None:
        where_parts.append(f"strike = {leg.fixed_strike}")
        moneyness_filter = None
    elif moneyness_filter:
        where_parts.append(moneyness_filter)
    
    # Handle PREMIUM-based strike selection
    if getattr(leg, 'strike_rule', None) == StrikeRule.PREMIUM:
        premium_value = getattr(leg, 'strike_value', 100)
        premium_condition = extra_params.get('StrikePremiumCondition', '<=')
        where_parts.append(f"oc.{price_col} {premium_condition} {premium_value}")
    
    # Handle DELTA-based strike selection
    if getattr(leg, 'strike_rule', None) == StrikeRule.DELTA:
        delta_value = getattr(leg, 'strike_value', 0.5)
        # Find strike closest to target delta
        order_by = f"ORDER BY ABS(oc.{delta_col} - {delta_value})"
    else:
        order_by = ""
    
    # 6. Add indicator filters if present
    indicator_filters = _build_indicator_sql_filters(all_params, "oc")
    if indicator_filters:
        where_parts.extend(indicator_filters)
    
    # Ensure all WHERE clause filters use oc.<column>
    where_parts = [w.replace(f"{alias}.", "oc.") if f"{alias}." in w else w for w in where_parts]
    where_clause = " AND ".join(where_parts)
    
    # Build final query
    select_cols = f"oc.*, oc.{price_col} AS price, oc.{iv_col} AS iv, oc.{delta_col} AS delta"
    
    sql = (
        f"SELECT {select_cols}\n"
        f"FROM {table} oc\n"
        f"WHERE {where_clause}\n"
    )
    
    if order_by:
        sql += f"{order_by}\n"
    
    sql += "LIMIT 1\n"
    
    logger.debug(f"Generated SQL for leg {leg.leg_id}:\n{sql}")
    
    return sql

def _build_indicator_sql_filters(extra_params: dict, alias: str = "oc") -> List[str]:
    """Build SQL WHERE clauses from indicator conditions."""
    filters = []
    
    if not extra_params:
        return filters
    
    conditions = extra_params.get('_parsed_indicator_conditions', {})
    
    # EMA filtering
    if extra_params.get('ConsiderEMAForEntry', 'NO').upper() == 'YES' and conditions.get('EmaEntryCondition'):
        cond_val = conditions['EmaEntryCondition']
        if cond_val in ['ABOVE', 'CROSSOVER_ABOVE', 'CROSS_ABOVE', '>']:
            filters.append(f"{alias}.underlying_price > {alias}.emaN")
        elif cond_val in ['BELOW', 'CROSSOVER_BELOW', 'CROSS_BELOW', '<']:
            filters.append(f"{alias}.underlying_price < {alias}.emaN")
    
    # VWAP filtering
    if extra_params.get('ConsiderVwapForEntry', 'NO').upper() == 'YES' and conditions.get('VwapEntryCondition'):
        cond_val = conditions['VwapEntryCondition']
        if cond_val in ['ABOVE', 'CROSSOVER_ABOVE', 'CROSS_ABOVE', '>']:
            filters.append(f"{alias}.underlying_price > {alias}.vwap")
        elif cond_val in ['BELOW', 'CROSSOVER_BELOW', 'CROSS_BELOW', '<']:
            filters.append(f"{alias}.underlying_price < {alias}.vwap")
    
    # RSI filtering
    if conditions.get('RsiEntryCondition'):
        cond_str = conditions['RsiEntryCondition']
        try:
            if '<' in cond_str and '=' not in cond_str.replace('<=',''):
                value = float(cond_str.split('<')[-1].strip())
                filters.append(f"{alias}.rsiN < {value}")
            elif '>' in cond_str and '=' not in cond_str.replace('>=',''):
                value = float(cond_str.split('>')[-1].strip())
                filters.append(f"{alias}.rsiN > {value}")
            elif '<=' in cond_str:
                value = float(cond_str.split('<=')[-1].strip())
                filters.append(f"{alias}.rsiN <= {value}")
            elif '>=' in cond_str:
                value = float(cond_str.split('>=')[-1].strip())
                filters.append(f"{alias}.rsiN >= {value}")
            elif '=' in cond_str:
                value = float(cond_str.split('=')[-1].strip())
                filters.append(f"{alias}.rsiN = {value}")
        except ValueError:
            logger.warning(f"Could not parse RSI condition: '{cond_str}'")
    
    return filters
