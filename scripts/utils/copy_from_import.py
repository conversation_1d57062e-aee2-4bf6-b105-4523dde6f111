#!/usr/bin/env python3
import os
import sys
import glob
import subprocess
import argparse
import time
from datetime import datetime

# Configuration
CSV_DIR = "/srv/samba/shared/market_data/nifty/oc_with_futures/"
TEMP_DIR = "/tmp/heavydb_import/"

def parse_args():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser(description='Fast CSV Importer for Nifty Option Chain')
    parser.add_argument('--test', action='store_true', help='Run in test mode with first file only')
    parser.add_argument('--file', type=str, help='Process only a specific file')
    return parser.parse_args()

def run_sql_command(sql, timeout=300):
    """Run SQL command via heavysql"""
    cmd = ["/opt/heavyai/bin/heavysql", "-s", "127.0.0.1", "--port", "6274", 
           "-u", "admin", "-p", "HyperInteractive", "-d", "heavyai"]
    
    try:
        process = subprocess.Popen(cmd, stdin=subprocess.PIPE, 
                                stdout=subprocess.PIPE, stderr=subprocess.PIPE,
                                text=True)
        
        stdout, stderr = process.communicate(input=sql, timeout=timeout)
        
        if stderr and "Error" in stderr:
            print(f"SQL Error: {stderr}")
            return False, stderr
        
        return True, stdout
    except subprocess.TimeoutExpired:
        print(f"SQL command timed out after {timeout} seconds")
        process.kill()
        return False, "Timeout"
    except Exception as e:
        print(f"Unexpected error: {e}")
        return False, str(e)

def get_csv_files():
    """Get a list of all CSV files to process"""
    return sorted(glob.glob(os.path.join(CSV_DIR, "*.csv")))

def ensure_table_exists():
    """Ensure the target table exists with correct schema"""
    print("Creating table nifty_option_chain...")
    create_table_sql = """
    DROP TABLE IF EXISTS nifty_option_chain;
    CREATE TABLE nifty_option_chain (
        trade_date       DATE,
        trade_time       TIME,
        expiry_date      DATE,
        index_name       TEXT,
        spot             DOUBLE,
        atm_strike       DOUBLE,
        strike           DOUBLE,
        dte              INT,
        expiry_bucket    TEXT,
        zone_id          SMALLINT,
        zone_name        TEXT,
        call_strike_type TEXT,
        put_strike_type  TEXT,
        ce_symbol        TEXT,
        ce_open          DOUBLE,
        ce_high          DOUBLE,
        ce_low           DOUBLE,
        ce_close         DOUBLE,
        ce_volume        BIGINT,
        ce_oi            BIGINT,
        ce_coi           BIGINT,
        ce_iv            DOUBLE,
        ce_delta         DOUBLE,
        ce_gamma         DOUBLE,
        ce_theta         DOUBLE,
        ce_vega          DOUBLE,
        ce_rho           DOUBLE,
        pe_symbol        TEXT,
        pe_open          DOUBLE,
        pe_high          DOUBLE,
        pe_low           DOUBLE,
        pe_close         DOUBLE,
        pe_volume        BIGINT,
        pe_oi            BIGINT,
        pe_coi           BIGINT,
        pe_iv            DOUBLE,
        pe_delta         DOUBLE,
        pe_gamma         DOUBLE,
        pe_theta         DOUBLE,
        pe_vega          DOUBLE,
        pe_rho           DOUBLE,
        future_open      DOUBLE,
        future_high      DOUBLE,
        future_low       DOUBLE,
        future_close     DOUBLE,
        future_volume    BIGINT,
        future_oi        BIGINT,
        future_coi       BIGINT
    );
    """
    
    success, output = run_sql_command(create_table_sql, timeout=120)
    if success:
        print("Table created successfully")
        return True
    else:
        print(f"Failed to create table: {output}")
        return False

def attempt_direct_copy(csv_file):
    """Try to import using COPY FROM direct method"""
    file_name = os.path.basename(csv_file)
    print(f"Attempting direct COPY FROM for {file_name}...")
    
    # Make sure temp dir exists
    os.makedirs(TEMP_DIR, exist_ok=True)
    
    # First, check if the server has access to the file location
    copy_sql = f"""
    COPY nifty_option_chain FROM '{csv_file}' 
    WITH (header='true', delimiter=',');
    """
    
    start_time = time.time()
    success, output = run_sql_command(copy_sql, timeout=1800)  # 30 min timeout
    end_time = time.time()
    
    if success:
        print(f"Direct COPY FROM succeeded in {end_time - start_time:.2f} seconds")
        return True
    
    print(f"Direct COPY FROM failed: {output}")
    return False

def count_lines(file_path):
    """Count lines in a file"""
    with open(file_path, 'r') as f:
        return sum(1 for _ in f)

def create_import_table(csv_file):
    """Create a temporary table for this file's structure"""
    file_name = os.path.basename(csv_file)
    table_name = f"temp_import_{file_name.replace('.', '_')}"
    
    create_sql = f"""
    DROP TABLE IF EXISTS {table_name};
    CREATE TABLE {table_name} (
        trade_date       DATE,
        trade_time       TIME,
        expiry_date      DATE,
        index_name       TEXT,
        spot             DOUBLE,
        atm_strike       DOUBLE,
        strike           DOUBLE,
        dte              INT,
        expiry_bucket    TEXT,
        zone_id          SMALLINT,
        zone_name        TEXT,
        call_strike_type TEXT,
        put_strike_type  TEXT,
        ce_symbol        TEXT,
        ce_open          DOUBLE,
        ce_high          DOUBLE,
        ce_low           DOUBLE,
        ce_close         DOUBLE,
        ce_volume        BIGINT,
        ce_oi            BIGINT,
        ce_coi           BIGINT,
        ce_iv            DOUBLE,
        ce_delta         DOUBLE,
        ce_gamma         DOUBLE,
        ce_theta         DOUBLE,
        ce_vega          DOUBLE,
        ce_rho           DOUBLE,
        pe_symbol        TEXT,
        pe_open          DOUBLE,
        pe_high          DOUBLE,
        pe_low           DOUBLE,
        pe_close         DOUBLE,
        pe_volume        BIGINT,
        pe_oi            BIGINT,
        pe_coi           BIGINT,
        pe_iv            DOUBLE,
        pe_delta         DOUBLE,
        pe_gamma         DOUBLE,
        pe_theta         DOUBLE,
        pe_vega          DOUBLE,
        pe_rho           DOUBLE,
        future_open      DOUBLE,
        future_high      DOUBLE,
        future_low       DOUBLE,
        future_close     DOUBLE,
        future_volume    BIGINT,
        future_oi        BIGINT,
        future_coi       BIGINT
    );
    """
    
    success, output = run_sql_command(create_sql)
    if not success:
        print(f"Failed to create temp table: {output}")
        return None
    
    return table_name

def import_file_batch(csv_file, batch_size=100000):
    """Import a file in batches using temporary staging table + INSERT INTO"""
    file_name = os.path.basename(csv_file)
    print(f"Importing {file_name} in batches...")
    
    # First, create a temporary copy with properly formatted CSV
    temp_file = os.path.join(TEMP_DIR, f"import_{file_name}")
    print(f"Creating temp file: {temp_file}")
    
    # Count total lines for progress reporting
    total_lines = count_lines(csv_file)
    total_rows = total_lines - 1  # Excluding header
    print(f"File has {total_rows} data rows")
    
    # Create a temporary staging table
    table_name = create_import_table(csv_file)
    if not table_name:
        return 0
    
    # Try using the faster COPY FROM command
    copy_sql = f"""
    COPY {table_name} FROM '{csv_file}' 
    WITH (header='true', delimiter=',');
    """
    
    print("Attempting COPY FROM to staging table...")
    start_time = time.time()
    success, output = run_sql_command(copy_sql, timeout=1800)  # 30 min timeout
    end_time = time.time()
    
    if success:
        # If COPY succeeded, now transfer data to main table
        print(f"COPY FROM succeeded in {end_time - start_time:.2f} seconds")
        print("Transferring data to main table...")
        
        transfer_sql = f"""
        INSERT INTO nifty_option_chain
        SELECT * FROM {table_name};
        """
        
        transfer_start = time.time()
        success, output = run_sql_command(transfer_sql, timeout=1800)
        transfer_end = time.time()
        
        if success:
            print(f"Data transfer succeeded in {transfer_end - transfer_start:.2f} seconds")
            
            # Verify row count
            count_sql = f"SELECT COUNT(*) FROM {table_name};"
            success, count_output = run_sql_command(count_sql)
            if success:
                count_line = [line for line in count_output.split('\n') if line and not line.startswith('User')]
                if count_line:
                    count = count_line[-1].strip()
                    print(f"Imported {count} rows")
                    
                    # Clean up
                    run_sql_command(f"DROP TABLE IF EXISTS {table_name};")
                    return int(count)
        else:
            print(f"Data transfer failed: {output}")
    else:
        print(f"COPY FROM failed: {output}")
    
    # Clean up temp table regardless of success
    run_sql_command(f"DROP TABLE IF EXISTS {table_name};")
    return 0

def verify_loaded_data():
    """Verify the data was loaded correctly"""
    # Get total count
    print("\nVerifying loaded data...")
    count_sql = "SELECT COUNT(*) FROM nifty_option_chain;"
    success, output = run_sql_command(count_sql)
    if success:
        count_line = [line for line in output.split('\n') if line and not line.startswith('User')]
        if count_line:
            count = count_line[-1].strip()
            print(f"Total rows in nifty_option_chain: {count}")
    else:
        print(f"Failed to verify data: {output}")

def main():
    args = parse_args()
    
    print("=== Fast CSV Import for Nifty Option Chain ===")
    print(f"Starting at: {datetime.now()}")
    
    # Ensure table exists
    if not ensure_table_exists():
        sys.exit(1)
    
    # Get list of files to process
    if args.file:
        csv_files = [os.path.join(CSV_DIR, args.file)]
        if not os.path.exists(csv_files[0]):
            print(f"Error: File {csv_files[0]} does not exist")
            sys.exit(1)
    else:
        csv_files = get_csv_files()
    
    print(f"Found {len(csv_files)} CSV files to process")
    
    # Process files
    start_time = datetime.now()
    total_imported = 0
    
    for i, csv_file in enumerate(csv_files):
        file_name = os.path.basename(csv_file)
        print(f"[{i+1}/{len(csv_files)}] Processing: {file_name}")
        
        # Try direct COPY FROM first (might fail due to permissions)
        if attempt_direct_copy(csv_file):
            # If direct copy works, we can't easily get the count, so estimate
            estimated_rows = count_lines(csv_file) - 1  # Exclude header
            total_imported += estimated_rows
            print(f"Estimated {estimated_rows} rows imported")
        else:
            # Fall back to batch import
            rows_imported = import_file_batch(csv_file)
            total_imported += rows_imported
            print(f"Batch import completed: {rows_imported} rows")
        
        # In test mode, only process first file
        if args.test and i == 0:
            print("Test mode: stopping after first file")
            break
    
    # Verify loaded data
    verify_loaded_data()
    
    # Calculate statistics
    end_time = datetime.now()
    duration = end_time - start_time
    rows_per_second = total_imported / max(1, duration.total_seconds())
    
    print("\n=== Import Summary ===")
    print(f"Start time: {start_time}")
    print(f"End time: {end_time}")
    print(f"Duration: {duration}")
    print(f"Files processed: {len(csv_files) if not args.test else 1}")
    print(f"Total rows imported: {total_imported}")
    print(f"Average throughput: {rows_per_second:.2f} rows/second")

if __name__ == "__main__":
    main() 