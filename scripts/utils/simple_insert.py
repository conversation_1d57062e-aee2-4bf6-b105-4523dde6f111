#!/usr/bin/env python3
import subprocess
import time

def run_sql_command(sql, timeout=300):
    """Run SQL command via heavysql with timeout"""
    cmd = ["/opt/heavyai/bin/heavysql", "-s", "127.0.0.1", "--port", "6274", 
           "-u", "admin", "-p", "HyperInteractive", "-d", "heavyai"]
    
    try:
        process = subprocess.Popen(cmd, stdin=subprocess.PIPE, 
                                stdout=subprocess.PIPE, stderr=subprocess.PIPE,
                                text=True)
        
        stdout, stderr = process.communicate(input=sql, timeout=timeout)
        
        if stderr and "Error" in stderr:
            print(f"SQL Error: {stderr}")
            return False, stderr
        
        return True, stdout
    except subprocess.TimeoutExpired:
        print(f"SQL command timed out after {timeout} seconds")
        # Kill the process
        process.kill()
        return False, f"Timeout after {timeout}s"
    except Exception as e:
        print(f"Error executing SQL: {e}")
        return False, str(e)

def main():
    print("=== Simple Insert Test ===")
    
    # Create table
    print("Creating table...")
    with open('create_simple_table.sql', 'r') as f:
        create_sql = f.read()
    
    success, result = run_sql_command(create_sql)
    if not success:
        print(f"Failed to create table: {result}")
        return False
    
    print("Table created successfully")
    
    # Insert test data
    print("\nInserting test data...")
    test_data = [
        ("2023-06-01", "09:15:00", "2023-06-29", 18000.0, 535.55),
        ("2023-06-01", "09:30:00", "2023-06-29", 18050.0, 495.20),
        ("2023-06-01", "09:45:00", "2023-06-29", 18100.0, 450.75)
    ]
    
    # Test single row insert
    print("Testing single row insert...")
    single_row = test_data[0]
    insert_sql = f"""
    INSERT INTO nifty_test_table VALUES (
        '{single_row[0]}', 
        '{single_row[1]}', 
        '{single_row[2]}', 
        {single_row[3]}, 
        {single_row[4]}
    )
    """
    
    success, result = run_sql_command(insert_sql)
    if not success:
        print(f"Failed to insert single row: {result}")
    else:
        print("Single row inserted successfully")
    
    # Test batch insert
    print("\nTesting batch insert...")
    batch_values = []
    for row in test_data[1:]:
        batch_values.append(f"('{row[0]}', '{row[1]}', '{row[2]}', {row[3]}, {row[4]})")
    
    batch_sql = f"INSERT INTO nifty_test_table VALUES {', '.join(batch_values)}"
    
    success, result = run_sql_command(batch_sql)
    if not success:
        print(f"Failed to insert batch: {result}")
    else:
        print("Batch inserted successfully")
    
    # Verify data
    print("\nVerifying data...")
    verify_sql = "SELECT * FROM nifty_test_table"
    
    success, result = run_sql_command(verify_sql)
    if not success:
        print(f"Failed to verify data: {result}")
    else:
        print("Data verification result:")
        print(result)
    
    return True

if __name__ == "__main__":
    main() 