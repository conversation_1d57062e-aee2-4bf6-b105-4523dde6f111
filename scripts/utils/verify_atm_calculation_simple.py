#!/usr/bin/env python3
"""
Simplified ATM Calculation Verification Script
"""

import pandas as pd
import numpy as np
from heavydb import connect
import json

def main():
    # Connect to HeavyDB
    conn = connect(
        host='localhost',
        port=6274,
        user='admin',
        password='HyperInteractive',
        dbname='heavyai'
    )
    
    # Test on a single date
    trade_date = '2025-05-19'
    expiry_date = '2025-05-22'
    
    query = f"""
    SELECT 
        trade_date,
        expiry_date,
        strike,
        ce_close,
        pe_close,
        spot as spot_price,
        atm_strike as table_atm_strike
    FROM nifty_option_chain
    WHERE trade_date = '{trade_date}'
    AND expiry_date = '{expiry_date}'
    ORDER BY strike
    LIMIT 100
    """
    
    df = pd.read_sql(query, conn)
    
    if df.empty:
        print("No data found")
        return
        
    spot_price = df['spot_price'].iloc[0]
    table_atm = df['table_atm_strike'].iloc[0]
    strike_increment = 100.0 if spot_price >= 10000 else 50.0
    
    # Method 1: Spot-based ATM (simple rounding)
    spot_based_atm = round(spot_price / strike_increment) * strike_increment
    
    # Method 2: Synthetic future-based ATM
    df['synthetic_future'] = df['strike'] + df['ce_close'] - df['pe_close']
    df['diff_from_spot'] = abs(df['synthetic_future'] - spot_price)
    synthetic_atm_row = df.loc[df['diff_from_spot'].idxmin()]
    
    print(f"\n{'='*60}")
    print(f"ATM CALCULATION VERIFICATION RESULTS")
    print(f"{'='*60}")
    print(f"\nTest Date: {trade_date} (Expiry: {expiry_date})")
    print(f"Spot Price: {spot_price:.2f}")
    print(f"Strike Increment: {strike_increment}")
    
    print(f"\n1. HeavyDB Table ATM Strike: {table_atm}")
    print(f"   (This is what's stored in the table)")
    
    print(f"\n2. Spot-based ATM (Simple Rounding): {spot_based_atm}")
    print(f"   Formula: round(spot_price / strike_increment) * strike_increment")
    print(f"   This matches HeavyDB table: {table_atm == spot_based_atm}")
    
    print(f"\n3. Synthetic Future-based ATM: {synthetic_atm_row['strike']}")
    print(f"   Formula: Find strike where (strike + CE - PE) is closest to spot")
    print(f"   Synthetic Future at this strike: {synthetic_atm_row['synthetic_future']:.2f}")
    print(f"   CE Close: {synthetic_atm_row['ce_close']:.2f}")
    print(f"   PE Close: {synthetic_atm_row['pe_close']:.2f}")
    
    print(f"\n4. DIFFERENCE ANALYSIS:")
    strike_diff = abs(spot_based_atm - synthetic_atm_row['strike'])
    print(f"   Strike Difference: {strike_diff}")
    print(f"   In Strike Increments: {strike_diff / strike_increment}")
    
    # Show strikes around both ATMs
    print(f"\n5. STRIKES AROUND BOTH ATMs:")
    print(f"{'Strike':<10} {'CE Close':<10} {'PE Close':<10} {'Synthetic':<12} {'Note'}")
    print("-" * 60)
    
    min_strike = min(spot_based_atm, synthetic_atm_row['strike']) - 2 * strike_increment
    max_strike = max(spot_based_atm, synthetic_atm_row['strike']) + 2 * strike_increment
    
    nearby_df = df[(df['strike'] >= min_strike) & (df['strike'] <= max_strike)].copy()
    
    for _, row in nearby_df.iterrows():
        note = ""
        if row['strike'] == table_atm:
            note += "[Table ATM] "
        if row['strike'] == spot_based_atm:
            note += "[Spot ATM] "
        if row['strike'] == synthetic_atm_row['strike']:
            note += "[Synthetic ATM]"
            
        print(f"{row['strike']:<10.0f} {row['ce_close']:<10.2f} {row['pe_close']:<10.2f} "
              f"{row['synthetic_future']:<12.2f} {note}")
    
    print(f"\n{'='*60}")
    print("CONCLUSION:")
    print(f"{'='*60}")
    print("\nThe HeavyDB table uses spot-based ATM calculation (simple rounding).")
    print("The strike_selection.py uses synthetic future-based ATM calculation.")
    print(f"These methods can differ by up to {strike_diff / strike_increment:.1f} strikes.")
    print("\nThis difference is EXPECTED and documented in the archive verification.")
    
    # Save findings
    findings = {
        "test_date": trade_date,
        "spot_price": float(spot_price),
        "table_atm": float(table_atm),
        "spot_based_atm": float(spot_based_atm),
        "synthetic_atm": float(synthetic_atm_row['strike']),
        "strike_difference": float(strike_diff),
        "strike_increments_diff": float(strike_diff / strike_increment),
        "conclusion": "HeavyDB uses spot-based ATM, strike_selection.py uses synthetic future-based ATM"
    }
    
    with open('/srv/samba/shared/atm_verification_findings.json', 'w') as f:
        json.dump(findings, f, indent=2)
    
    print("\nFindings saved to: /srv/samba/shared/atm_verification_findings.json")
    
    conn.close()

if __name__ == "__main__":
    main()