#!/usr/bin/env python3
"""
Comprehensive data integrity verification for multi-index option chain data
"""
import pandas as pd
import numpy as np
from heavydb import connect
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

class DataIntegrityChecker:
    def __init__(self):
        self.conn = connect(
            host='localhost',
            port=6274,
            user='admin',
            password='HyperInteractive',
            dbname='heavyai'
        )
        self.cursor = self.conn.cursor()
        self.issues = []
        
        # Index configurations
        self.index_config = {
            'NIFTY': {
                'strike_increment': 50,
                'expected_expiries': ['CW', 'NW', 'CM', 'NM'],
                'min_strikes_per_day': 20
            },
            'BANKNIFTY': {
                'strike_increment': 100,
                'expected_expiries': ['CM', 'NM'],
                'min_strikes_per_day': 20
            },
            'MIDCAPNIFTY': {
                'strike_increment': 25,
                'expected_expiries': ['CM', 'NM'],
                'min_strikes_per_day': 15
            },
            'SENSEX': {
                'strike_increment': 100,
                'expected_expiries': ['CW', 'NW', 'CM', 'NM'],
                'min_strikes_per_day': 20
            }
        }
    
    def check_basic_stats(self):
        """Check basic statistics for each index"""
        print("\n" + "="*80)
        print("1. BASIC STATISTICS CHECK")
        print("="*80)
        
        self.cursor.execute("""
            SELECT index_name, 
                   COUNT(*) as total_rows,
                   COUNT(DISTINCT trade_date) as trading_days,
                   COUNT(DISTINCT strike) as unique_strikes,
                   COUNT(DISTINCT expiry_date) as unique_expiries,
                   MIN(trade_date) as start_date,
                   MAX(trade_date) as end_date
            FROM nifty_option_chain
            GROUP BY index_name
            ORDER BY index_name
        """)
        
        results = self.cursor.fetchall()
        for row in results:
            index_name = row[0]
            print(f"\n{index_name}:")
            print(f"  Total rows: {row[1]:,}")
            print(f"  Trading days: {row[2]}")
            print(f"  Unique strikes: {row[3]}")
            print(f"  Unique expiries: {row[4]}")
            print(f"  Date range: {row[5]} to {row[6]}")
            
            # Check minimum data requirements
            if row[2] < 10:
                self.issues.append(f"{index_name}: Only {row[2]} trading days (expected more)")
    
    def check_expiry_types(self):
        """Verify expiry types match expected configuration"""
        print("\n" + "="*80)
        print("2. EXPIRY TYPE VALIDATION")
        print("="*80)
        
        for index_name, config in self.index_config.items():
            self.cursor.execute(f"""
                SELECT DISTINCT expiry_bucket
                FROM nifty_option_chain
                WHERE index_name = '{index_name}'
                ORDER BY expiry_bucket
            """)
            
            actual_expiries = [row[0] for row in self.cursor.fetchall()]
            expected_expiries = config['expected_expiries']
            
            print(f"\n{index_name}:")
            print(f"  Expected: {expected_expiries}")
            print(f"  Actual: {actual_expiries}")
            
            if set(actual_expiries) == set(expected_expiries):
                print(f"  ✅ Expiry types match")
            else:
                missing = set(expected_expiries) - set(actual_expiries)
                extra = set(actual_expiries) - set(expected_expiries)
                if missing:
                    print(f"  ❌ Missing expiry types: {missing}")
                    self.issues.append(f"{index_name}: Missing expiry types {missing}")
                if extra:
                    print(f"  ❌ Unexpected expiry types: {extra}")
                    self.issues.append(f"{index_name}: Unexpected expiry types {extra}")
    
    def check_strike_increments(self):
        """Verify strike increments are correct"""
        print("\n" + "="*80)
        print("3. STRIKE INCREMENT VALIDATION")
        print("="*80)
        
        for index_name, config in self.index_config.items():
            expected_increment = config['strike_increment']
            
            # Get sample strikes for a recent date
            self.cursor.execute(f"""
                SELECT DISTINCT strike
                FROM nifty_option_chain
                WHERE index_name = '{index_name}'
                AND trade_date = (
                    SELECT MAX(trade_date) 
                    FROM nifty_option_chain 
                    WHERE index_name = '{index_name}'
                )
                ORDER BY strike
                LIMIT 20
            """)
            
            strikes = [row[0] for row in self.cursor.fetchall()]
            
            if len(strikes) > 1:
                # Calculate actual increments
                increments = [strikes[i+1] - strikes[i] for i in range(len(strikes)-1)]
                unique_increments = set(increments)
                
                print(f"\n{index_name}:")
                print(f"  Expected increment: {expected_increment}")
                print(f"  Actual increments: {unique_increments}")
                
                if unique_increments == {expected_increment}:
                    print(f"  ✅ Strike increments correct")
                else:
                    print(f"  ❌ Incorrect strike increments found")
                    self.issues.append(f"{index_name}: Incorrect strike increments {unique_increments}")
    
    def check_dte_consistency(self):
        """Verify DTE calculations are correct"""
        print("\n" + "="*80)
        print("4. DTE (DAYS TO EXPIRY) VALIDATION")
        print("="*80)
        
        # Sample check for each index
        for index_name in self.index_config.keys():
            self.cursor.execute(f"""
                SELECT trade_date, expiry_date, dte, expiry_bucket
                FROM nifty_option_chain
                WHERE index_name = '{index_name}'
                ORDER BY RANDOM()
                LIMIT 100
            """)
            
            results = self.cursor.fetchall()
            incorrect_dte = 0
            
            for row in results:
                trade_date = pd.to_datetime(row[0])
                expiry_date = pd.to_datetime(row[1])
                actual_dte = row[2]
                expiry_type = row[3]
                
                # Calculate expected DTE
                expected_dte = (expiry_date - trade_date).days
                
                if actual_dte != expected_dte:
                    incorrect_dte += 1
            
            print(f"\n{index_name}:")
            print(f"  Checked 100 random rows")
            print(f"  Incorrect DTE calculations: {incorrect_dte}")
            
            if incorrect_dte == 0:
                print(f"  ✅ DTE calculations correct")
            else:
                print(f"  ❌ Found {incorrect_dte} incorrect DTE values")
                self.issues.append(f"{index_name}: {incorrect_dte} incorrect DTE calculations")
    
    def check_atm_strikes(self):
        """Verify ATM strike calculations"""
        print("\n" + "="*80)
        print("5. ATM STRIKE VALIDATION")
        print("="*80)
        
        for index_name, config in self.index_config.items():
            increment = config['strike_increment']
            
            # Check ATM calculation logic
            self.cursor.execute(f"""
                SELECT spot, atm_strike, strike, zone_name
                FROM nifty_option_chain
                WHERE index_name = '{index_name}'
                AND zone_name = 'ATM'
                ORDER BY RANDOM()
                LIMIT 50
            """)
            
            results = self.cursor.fetchall()
            incorrect_atm = 0
            
            for row in results:
                spot = row[0]
                atm_strike = row[1]
                strike = row[2]
                
                # Verify ATM strike is correctly rounded
                expected_atm = round(spot / increment) * increment
                
                if abs(atm_strike - expected_atm) > increment:
                    incorrect_atm += 1
                
                # For ATM zone, strike should equal atm_strike
                if strike != atm_strike:
                    incorrect_atm += 1
            
            print(f"\n{index_name}:")
            print(f"  Checked 50 ATM rows")
            print(f"  Incorrect ATM calculations: {incorrect_atm}")
            
            if incorrect_atm == 0:
                print(f"  ✅ ATM calculations correct")
            else:
                print(f"  ❌ Found {incorrect_atm} incorrect ATM values")
                self.issues.append(f"{index_name}: {incorrect_atm} incorrect ATM calculations")
    
    def check_zone_classifications(self):
        """Verify zone classifications are correct"""
        print("\n" + "="*80)
        print("6. ZONE CLASSIFICATION VALIDATION")
        print("="*80)
        
        for index_name, config in self.index_config.items():
            increment = config['strike_increment']
            
            # Check zone distribution
            self.cursor.execute(f"""
                SELECT zone_name, COUNT(*) as cnt,
                       AVG(ABS(strike - atm_strike) / {increment}) as avg_distance
                FROM nifty_option_chain
                WHERE index_name = '{index_name}'
                GROUP BY zone_name
                ORDER BY zone_name
            """)
            
            print(f"\n{index_name}:")
            print(f"  Zone | Count     | Avg Strike Distance")
            print(f"  -----|-----------|-------------------")
            
            results = self.cursor.fetchall()
            for row in results:
                zone = row[0]
                count = row[1]
                avg_dist = row[2] if row[2] else 0
                print(f"  {zone:4} | {count:>9,} | {avg_dist:>8.1f} strikes")
                
                # Verify zone boundaries
                expected_ranges = {
                    'ATM': (0, 0.5),
                    'OPEN': (0.5, 5),
                    'NEAR': (5, 10),
                    'MID': (10, 20),
                    'FAR': (20, float('inf'))
                }
                
                if zone in expected_ranges:
                    min_dist, max_dist = expected_ranges[zone]
                    if not (min_dist <= avg_dist < max_dist):
                        self.issues.append(f"{index_name}: Zone {zone} has incorrect average distance {avg_dist:.1f}")
    
    def check_data_completeness(self):
        """Check for missing data or gaps"""
        print("\n" + "="*80)
        print("7. DATA COMPLETENESS CHECK")
        print("="*80)
        
        for index_name, config in self.index_config.items():
            # Check strikes per day
            self.cursor.execute(f"""
                SELECT trade_date, COUNT(DISTINCT strike) as strike_count
                FROM nifty_option_chain
                WHERE index_name = '{index_name}'
                GROUP BY trade_date
                HAVING COUNT(DISTINCT strike) < {config['min_strikes_per_day']}
                ORDER BY trade_date
                LIMIT 10
            """)
            
            results = self.cursor.fetchall()
            
            print(f"\n{index_name}:")
            if results:
                print(f"  ❌ Found {len(results)} days with insufficient strikes:")
                for row in results[:5]:
                    print(f"    {row[0]}: only {row[1]} strikes")
                self.issues.append(f"{index_name}: {len(results)} days with < {config['min_strikes_per_day']} strikes")
            else:
                print(f"  ✅ All days have sufficient strikes (>= {config['min_strikes_per_day']})")
    
    def check_data_quality(self):
        """Check for data quality issues"""
        print("\n" + "="*80)
        print("8. DATA QUALITY CHECK")
        print("="*80)
        
        quality_checks = [
            ("NULL spot prices", "spot IS NULL"),
            ("Zero spot prices", "spot = 0"),
            ("NULL ATM strikes", "atm_strike IS NULL"),
            ("Negative strikes", "strike < 0"),
            ("NULL expiry buckets", "expiry_bucket IS NULL"),
            ("Invalid zone IDs", "zone_id NOT IN (0,1,2,3,4)"),
            ("Future trade dates", "trade_date > CURRENT_DATE"),
            ("Negative DTEs", "dte < 0")
        ]
        
        for index_name in self.index_config.keys():
            print(f"\n{index_name}:")
            has_issues = False
            
            for check_name, condition in quality_checks:
                self.cursor.execute(f"""
                    SELECT COUNT(*) 
                    FROM nifty_option_chain
                    WHERE index_name = '{index_name}'
                    AND {condition}
                """)
                
                count = self.cursor.fetchone()[0]
                if count > 0:
                    print(f"  ❌ {check_name}: {count:,} rows")
                    self.issues.append(f"{index_name}: {count} rows with {check_name}")
                    has_issues = True
            
            if not has_issues:
                print(f"  ✅ No data quality issues found")
    
    def check_expiry_consistency(self):
        """Check expiry date consistency"""
        print("\n" + "="*80)
        print("9. EXPIRY DATE CONSISTENCY CHECK")
        print("="*80)
        
        for index_name in self.index_config.keys():
            # Check if expiry dates are on expected days (usually Thursday/Friday)
            self.cursor.execute(f"""
                SELECT expiry_date, 
                       EXTRACT(DOW FROM expiry_date::date) as day_of_week,
                       COUNT(*) as cnt
                FROM nifty_option_chain
                WHERE index_name = '{index_name}'
                GROUP BY expiry_date, day_of_week
                ORDER BY cnt DESC
                LIMIT 10
            """)
            
            results = self.cursor.fetchall()
            
            print(f"\n{index_name} - Top expiry dates:")
            day_names = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat']
            
            for row in results[:5]:
                day_name = day_names[int(row[1])]
                print(f"  {row[0]} ({day_name}): {row[2]:,} rows")
            
            # Check for unusual expiry days
            unusual_days = [r for r in results if int(r[1]) not in [3, 4, 5]]  # Wed, Thu, Fri
            if unusual_days:
                print(f"  ⚠️  Found expiries on unusual days (not Wed/Thu/Fri)")
    
    def generate_summary(self):
        """Generate final summary report"""
        print("\n" + "="*80)
        print("DATA INTEGRITY SUMMARY")
        print("="*80)
        
        if not self.issues:
            print("\n✅ ALL CHECKS PASSED - Data integrity verified!")
        else:
            print(f"\n❌ FOUND {len(self.issues)} ISSUES:")
            for i, issue in enumerate(self.issues, 1):
                print(f"{i}. {issue}")
        
        # Additional statistics
        self.cursor.execute("""
            SELECT COUNT(*) as total_rows,
                   COUNT(DISTINCT index_name) as indices,
                   COUNT(DISTINCT trade_date) as total_days,
                   MIN(trade_date) as earliest,
                   MAX(trade_date) as latest
            FROM nifty_option_chain
        """)
        
        stats = self.cursor.fetchone()
        print(f"\nOVERALL STATISTICS:")
        print(f"  Total rows: {stats[0]:,}")
        print(f"  Indices: {stats[1]}")
        print(f"  Trading days: {stats[2]}")
        print(f"  Date range: {stats[3]} to {stats[4]}")
    
    def run_all_checks(self):
        """Run all integrity checks"""
        print("="*80)
        print("MULTI-INDEX DATA INTEGRITY VERIFICATION")
        print("="*80)
        print(f"Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        self.check_basic_stats()
        self.check_expiry_types()
        self.check_strike_increments()
        self.check_dte_consistency()
        self.check_atm_strikes()
        self.check_zone_classifications()
        self.check_data_completeness()
        self.check_data_quality()
        self.check_expiry_consistency()
        
        self.generate_summary()
        
        self.cursor.close()
        self.conn.close()

def main():
    checker = DataIntegrityChecker()
    checker.run_all_checks()

if __name__ == "__main__":
    main()