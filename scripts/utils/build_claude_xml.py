#!/usr/bin/env python3
"""Script for building Claude XML configuration files."""

def build_strike_condition(tag, logLevel, align, atol, src):
    """Build XML strike condition element.
    
    Args:
        tag: Tag name for the condition
        logLevel: Logging level
        align: Alignment value
        atol: Absolute tolerance
        src: Source path
    
    Returns:
        str: XML string for strike condition
    """
    # Construct the XML element
    xml = f'<strike_condition tag="{tag}" logLevel="{logLevel}" align="{align}" atol="{atol}" src="{src}"/>'
    return xml

def main():
    """Main function to demonstrate usage."""
    # Example usage
    condition = build_strike_condition(
        tag="example",
        logLevel="INFO",
        align="center",
        atol="0.01",
        src="/path/to/source"
    )
    
    print("Generated XML:")
    print(condition)
    
    # Create a complete Claude XML file with proper structure
    claude_xml = f"""<?xml version="1.0" encoding="UTF-8"?>
<claude>
    <config>
        {condition}
    </config>
</claude>"""
    
    print("\nComplete Claude XML:")
    print(claude_xml)
    
    # Optional: Save to file
    # with open('claude_config.xml', 'w') as f:
    #     f.write(claude_xml)

if __name__ == "__main__":
    main() 