#!/usr/bin/env python3
"""
Diagnose the Instrument column issue
"""

import pandas as pd
import os

def diagnose_instrument_values():
    """Check actual Instrument values in all strategy files"""
    
    base_dir = '/srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/comprehensive_tests'
    
    print("Checking all strategy files for Instrument values...")
    print("=" * 60)
    
    all_values = set()
    problem_files = []
    
    for root, dirs, files in os.walk(base_dir):
        for file in files:
            if file.endswith('.xlsx') and 'PORTFOLIO' not in file.upper():
                filepath = os.path.join(root, file)
                try:
                    # Read with different methods to check for issues
                    df = pd.read_excel(filepath, sheet_name='LegParameter')
                    
                    # Get unique values
                    unique_values = df['Instrument'].unique()
                    
                    # Check for problems
                    has_problem = False
                    for val in unique_values:
                        val_str = str(val).strip()
                        all_values.add(val_str)
                        
                        if val_str.upper() not in ['CALL', 'CE', 'PUT', 'PE']:
                            has_problem = True
                            problem_files.append((file, val_str))
                    
                    # Show details for specific file
                    if file == 'leg_reentry_all_types.xlsx':
                        print(f"\nDetailed check for {file}:")
                        print(f"  Shape: {df.shape}")
                        print(f"  Columns: {df.columns.tolist()}")
                        print(f"  Instrument values: {df['Instrument'].tolist()}")
                        print(f"  Raw values (repr): {[repr(x) for x in df['Instrument'].tolist()]}")
                        
                except Exception as e:
                    print(f"Error reading {file}: {e}")
    
    print(f"\n\nAll unique Instrument values found: {sorted(all_values)}")
    
    if problem_files:
        print(f"\n\nFiles with problematic values:")
        for file, val in problem_files:
            print(f"  {file}: '{val}'")
    else:
        print("\n\nNo problematic values found!")
        
    # Check if there's a hidden character issue
    print("\n\nChecking for hidden characters:")
    for val in all_values:
        print(f"  '{val}' -> bytes: {val.encode('utf-8')} -> len: {len(val)}")

if __name__ == "__main__":
    diagnose_instrument_values()