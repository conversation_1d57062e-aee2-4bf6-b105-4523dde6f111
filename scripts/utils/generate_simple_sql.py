#!/usr/bin/env python3
import os
import sys
import logging
import pandas as pd
import csv

# Configure logging
logging.basicConfig(level=logging.INFO, 
                   format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def create_test_data():
    """Creates a small test dataset with a few rows"""
    logger.info("Creating small test dataset")
    
    data = [
        {
            'trade_date': '2023-01-01',
            'trade_time': '09:15:00',
            'expiry_date': '2023-01-26',
            'index_name': 'NIFTY',
            'spot': 18000, 
            'atm_strike': 18000,
            'strike': 18000,
            'dte': 25,
            'expiry_bucket': 'CW',
            'zone_id': 1,
            'zone_name': 'OPEN',
            'call_strike_type': 'ATM',
            'put_strike_type': 'ATM',
            'ce_symbol': 'NIFTY26JAN2318000CE',
            'ce_open': 100, 'ce_high': 120, 'ce_low': 90, 'ce_close': 110,
            'ce_volume': 1000, 'ce_oi': 5000, 'ce_coi': 100, 'ce_iv': 15,
            'ce_delta': 0.5, 'ce_gamma': 0.05, 'ce_theta': -10, 'ce_vega': 10, 'ce_rho': 5,
            'pe_symbol': 'NIFTY26JAN2318000PE',
            'pe_open': 100, 'pe_high': 120, 'pe_low': 90, 'pe_close': 110,
            'pe_volume': 1000, 'pe_oi': 5000, 'pe_coi': 100, 'pe_iv': 15,
            'pe_delta': -0.5, 'pe_gamma': 0.05, 'pe_theta': -10, 'pe_vega': 10, 'pe_rho': -5,
            'future_open': 18050, 'future_high': 18100, 'future_low': 18000, 'future_close': 18075,
            'future_volume': 5000, 'future_oi': 10000, 'future_coi': 200
        },
        {
            'trade_date': '2023-01-01',
            'trade_time': '09:15:00',
            'expiry_date': '2023-01-26',
            'index_name': 'NIFTY',
            'spot': 18000, 
            'atm_strike': 18000,
            'strike': 18050,
            'dte': 25,
            'expiry_bucket': 'CW',
            'zone_id': 1,
            'zone_name': 'OPEN',
            'call_strike_type': 'OTM1',
            'put_strike_type': 'ITM1',
            'ce_symbol': 'NIFTY26JAN2318050CE',
            'ce_open': 90, 'ce_high': 110, 'ce_low': 80, 'ce_close': 100,
            'ce_volume': 900, 'ce_oi': 4500, 'ce_coi': 90, 'ce_iv': 16,
            'ce_delta': 0.45, 'ce_gamma': 0.06, 'ce_theta': -9, 'ce_vega': 11, 'ce_rho': 4,
            'pe_symbol': 'NIFTY26JAN2318050PE',
            'pe_open': 110, 'pe_high': 130, 'pe_low': 100, 'pe_close': 120,
            'pe_volume': 1100, 'pe_oi': 5500, 'pe_coi': 110, 'pe_iv': 14,
            'pe_delta': -0.55, 'pe_gamma': 0.04, 'pe_theta': -11, 'pe_vega': 9, 'pe_rho': -6,
            'future_open': 18050, 'future_high': 18100, 'future_low': 18000, 'future_close': 18075,
            'future_volume': 5000, 'future_oi': 10000, 'future_coi': 200
        }
    ]
    
    return data

def main():
    try:
        # Generate test data
        test_data = create_test_data()
        
        # Generate simple SQL script
        sql_file = "simple_insert.sql"
        with open(sql_file, 'w') as f:
            f.write("-- Simple insert statements\n\n")
            
            # Generate INSERT statements
            for row in test_data:
                # Start the INSERT statement
                f.write("INSERT INTO nifty_option_chain VALUES (\n")
                
                # Add values
                values = []
                for field in [
                    'trade_date', 'trade_time', 'expiry_date', 'index_name', 'spot', 
                    'atm_strike', 'strike', 'dte', 'expiry_bucket', 'zone_id', 
                    'zone_name', 'call_strike_type', 'put_strike_type', 'ce_symbol', 
                    'ce_open', 'ce_high', 'ce_low', 'ce_close', 'ce_volume', 
                    'ce_oi', 'ce_coi', 'ce_iv', 'ce_delta', 'ce_gamma', 
                    'ce_theta', 'ce_vega', 'ce_rho', 'pe_symbol', 'pe_open', 
                    'pe_high', 'pe_low', 'pe_close', 'pe_volume', 'pe_oi', 
                    'pe_coi', 'pe_iv', 'pe_delta', 'pe_gamma', 'pe_theta', 
                    'pe_vega', 'pe_rho', 'future_open', 'future_high', 'future_low', 
                    'future_close', 'future_volume', 'future_oi', 'future_coi'
                ]:
                    value = row[field]
                    if isinstance(value, str):
                        values.append(f"'{value}'")
                    else:
                        values.append(str(value))
                
                # Join values with commas
                f.write("    " + ",\n    ".join(values))
                
                # End the statement
                f.write("\n);\n\n")
            
            f.write("COMMIT;\n")
        
        logger.info(f"SQL INSERT statements written to {sql_file}")
        
        # Generate shell script to run the SQL
        with open("run_simple_insert.sh", "w") as f:
            f.write("#!/bin/bash\n\n")
            f.write(f"echo 'Executing {sql_file}...'\n")
            f.write(f"/opt/heavyai/bin/heavysql -s 127.0.0.1 --port 6274 -u admin -p HyperInteractive -d heavyai < {sql_file}\n")
            f.write("echo 'Insertion completed.'\n")
        
        os.chmod("run_simple_insert.sh", 0o755)  # Make executable
        logger.info("Generated run_simple_insert.sh script")
        logger.info("Run ./run_simple_insert.sh to insert the data")
            
    except Exception as e:
        logger.error(f"Error in main process: {str(e)}")

if __name__ == "__main__":
    main() 