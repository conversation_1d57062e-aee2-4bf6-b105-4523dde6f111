#!/usr/bin/env python3
import os
import sys
import glob
import subprocess
import time
import argparse
from datetime import datetime

# Configuration
CSV_DIR = "/srv/samba/shared/market_data/nifty/oc_with_futures/"
MAX_RETRIES = 3
TIMEOUT = 1800  # 30 minutes timeout for COPY

def parse_args():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser(description='Direct COPY FROM data loader for Nifty Option Chain')
    parser.add_argument('--test', action='store_true', help='Run in test mode with limited files')
    parser.add_argument('--files', type=str, help='Comma-separated list of specific files to process')
    parser.add_argument('--drop-table', action='store_true', help='Drop and recreate the table')
    parser.add_argument('--verify', action='store_true', help='Only verify existing data without loading')
    return parser.parse_args()

def run_sql_command(sql, timeout=TIMEOUT):
    """Run SQL command via heavysql with timeout"""
    cmd = ["/opt/heavyai/bin/heavysql", "-s", "127.0.0.1", "--port", "6274", 
           "-u", "admin", "-p", "HyperInteractive", "-d", "heavyai"]
    
    try:
        process = subprocess.Popen(cmd, stdin=subprocess.PIPE, 
                                stdout=subprocess.PIPE, stderr=subprocess.PIPE,
                                text=True)
        
        stdout, stderr = process.communicate(input=sql, timeout=timeout)
        
        if stderr and "Error" in stderr:
            print(f"SQL Error: {stderr}")
            return False, stderr
        
        return True, stdout
    except subprocess.TimeoutExpired:
        print(f"SQL command timed out after {timeout} seconds")
        # Kill the process
        process.kill()
        return False, f"Timeout after {timeout}s"
    except Exception as e:
        print(f"Error executing SQL: {e}")
        return False, str(e)

def get_row_count():
    """Get the current row count in the table"""
    success, result = run_sql_command("SELECT COUNT(*) FROM nifty_option_chain;")
    if success:
        try:
            # Extract the count from the output
            lines = result.strip().split('\n')
            for line in lines:
                if line.isdigit():
                    return int(line)
            return 0
        except Exception as e:
            print(f"Error parsing row count: {e}")
            return 0
    else:
        print("Failed to get row count")
        return 0

def count_csv_rows(csv_file):
    """Count rows in a CSV file (excluding header)"""
    try:
        with open(csv_file, 'r') as f:
            # Subtract 1 for header
            return sum(1 for _ in f) - 1
    except Exception as e:
        print(f"Error counting CSV rows: {e}")
        return 0

def process_file_via_copy(csv_file):
    """Process a file using COPY FROM command"""
    try:
        absolute_path = os.path.abspath(csv_file)
        csv_row_count = count_csv_rows(csv_file)
        print(f"Processing {csv_file} using COPY FROM (contains {csv_row_count} rows)")
        
        # Get current row count before loading
        before_count = get_row_count()
        
        copy_sql = f"""
        COPY nifty_option_chain FROM '{absolute_path}'
        WITH (header='true', delimiter=',');
        """
        
        success = False
        retries = 0
        
        while not success and retries < MAX_RETRIES:
            success, message = run_sql_command(copy_sql)
            if success:
                success = True
            else:
                retries += 1
                print(f"Retry {retries}/{MAX_RETRIES} for file {os.path.basename(csv_file)}")
                print(f"Error: {message}")
                time.sleep(2 ** retries)  # Exponential backoff
        
        # Get row count after loading
        after_count = get_row_count()
        rows_added = after_count - before_count
        
        if success:
            print(f"Successfully loaded {csv_file}")
            print(f"Rows in CSV: {csv_row_count}, Rows added to DB: {rows_added}")
            if rows_added < csv_row_count:
                print(f"WARNING: Not all rows loaded: {rows_added}/{csv_row_count} ({rows_added/csv_row_count*100:.2f}%)")
            return True, rows_added
        else:
            print(f"Failed to load {csv_file} after {MAX_RETRIES} retries")
            return False, 0
    except Exception as e:
        print(f"Error in COPY process for {csv_file}: {e}")
        return False, 0

def verify_data():
    """Verify the data in the database"""
    print("\n=== Data Verification ===")
    
    # Check row count
    row_count = get_row_count()
    print(f"Total rows in database: {row_count}")
    
    # Check date range
    success, result = run_sql_command("SELECT MIN(trade_date), MAX(trade_date), COUNT(DISTINCT trade_date) FROM nifty_option_chain;")
    if success:
        print(f"Date range: {result}")
    else:
        print("Failed to get date range")
    
    # Check sample data
    success, result = run_sql_command("SELECT * FROM nifty_option_chain LIMIT 5;")
    if success:
        print("\nSample data:")
        print(result)
    else:
        print("Failed to get sample data")
    
    # Check for any data issues
    success, result = run_sql_command("SELECT COUNT(*) FROM nifty_option_chain WHERE trade_date IS NULL OR expiry_date IS NULL;")
    if success:
        null_count = result.strip().split('\n')[-1]
        print(f"Rows with NULL dates: {null_count}")
    else:
        print("Failed to check for NULL dates")

def main():
    args = parse_args()
    
    print("=== Direct COPY FROM Nifty Option Chain Loader ===")
    print(f"Starting at: {datetime.now()}")
    print(f"Test mode: {args.test}")
    
    # Verify only mode
    if args.verify:
        verify_data()
        return
    
    # Create table if it doesn't exist or if drop-table is specified
    if args.drop_table:
        print("Dropping and recreating table nifty_option_chain...")
        try:
            with open('create_noc_table_simple.sql', 'r') as f:
                create_table_sql = f.read()
            
            # Split and execute each statement separately
            statements = create_table_sql.split(';')
            for stmt in statements:
                stmt = stmt.strip()
                if stmt:  # Skip empty statements
                    success, message = run_sql_command(stmt + ';')
                    if not success:
                        print(f"Failed to execute SQL statement: {stmt}")
                        print(f"Error: {message}")
                        return
            
            print("Table created successfully")
        except Exception as e:
            print(f"Error handling SQL file: {e}")
            return
    
    # Get list of CSV files to process
    if args.files:
        csv_files = [os.path.join(CSV_DIR, f.strip()) for f in args.files.split(',')]
    else:
        csv_files = glob.glob(os.path.join(CSV_DIR, "*.csv"))
    
    # Make sure all files exist
    csv_files = [f for f in csv_files if os.path.isfile(f)]
    
    if not csv_files:
        print("No CSV files found!")
        return
    
    if args.test:
        # Only process first 2 files in test mode
        csv_files = csv_files[:2]
    
    print(f"Found {len(csv_files)} CSV files to process")
    
    # Calculate total rows to process
    total_csv_rows = sum(count_csv_rows(f) for f in csv_files)
    print(f"Total rows to process: {total_csv_rows}")
    
    # Process each file sequentially
    success_count = 0
    fail_count = 0
    total_rows_loaded = 0
    start_time = time.time()
    
    for i, csv_file in enumerate(csv_files):
        print(f"\nProcessing file {i+1}/{len(csv_files)}: {os.path.basename(csv_file)}")
        success, rows_loaded = process_file_via_copy(csv_file)
        if success:
            success_count += 1
            total_rows_loaded += rows_loaded
        else:
            fail_count += 1
        
        # Calculate progress and ETA
        elapsed = time.time() - start_time
        progress = total_rows_loaded / total_csv_rows if total_csv_rows > 0 else 0
        if progress > 0:
            eta_seconds = elapsed / progress - elapsed
            eta = datetime.fromtimestamp(time.time() + eta_seconds).strftime('%Y-%m-%d %H:%M:%S')
            print(f"Progress: {progress*100:.2f}% - ETA: {eta}")
    
    print("\n=== Loading Results ===")
    print(f"Total files: {len(csv_files)}")
    print(f"Successfully loaded: {success_count}")
    print(f"Failed: {fail_count}")
    print(f"Total rows loaded: {total_rows_loaded}")
    print(f"Total time: {time.time() - start_time:.2f} seconds")
    
    # Final verification
    verify_data()
    
    print(f"Finished at: {datetime.now()}")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\nProcess interrupted by user")
        sys.exit(1) 