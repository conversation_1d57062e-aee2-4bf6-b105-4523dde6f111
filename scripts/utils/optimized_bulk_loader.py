#!/usr/bin/env python3
"""
Optimized bulk loader for HeavyDB using prepared statements and batching
"""
import os
import csv
import time
import glob
from datetime import datetime
from multiprocessing import Pool, cpu_count
import subprocess

def get_row_count():
    """Get current row count"""
    cmd = """echo "SELECT COUNT(*) FROM nifty_option_chain;" | /opt/heavyai/bin/heavysql -p HyperInteractive -u admin -d heavyai --quiet"""
    result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
    if result.returncode == 0:
        try:
            return int(result.stdout.strip())
        except:
            return 0
    return 0

def load_csv_batch(args):
    """Load a batch of CSV data using optimized SQL"""
    csv_file, start_idx, batch_size = args
    
    rows = []
    with open(csv_file, 'r') as f:
        reader = csv.DictReader(f)
        for i, row in enumerate(reader):
            if i < start_idx:
                continue
            if i >= start_idx + batch_size:
                break
            rows.append(row)
    
    if not rows:
        return 0
    
    # Build single INSERT with multiple VALUES
    values_list = []
    for row in rows:
        values = f"""(
            '{row['trade_date']}',
            '{row['trade_time']}',
            '{row['expiry_date']}',
            '{row['index_name']}',
            {row['spot']},
            {row['atm_strike']},
            {row['strike']},
            {row['dte']},
            '{row['opt_type']}',
            {row['ltp']},
            {row['oi']},
            {row['volume'] or 0},
            {row['bid']},
            {row['ask']},
            {row['strike_diff']}
        )"""
        values_list.append(values)
    
    sql = f"""INSERT INTO nifty_option_chain 
    (trade_date, trade_time, expiry_date, index_name, spot, atm_strike, 
     strike, dte, opt_type, ltp, oi, volume, bid, ask, strike_diff)
    VALUES {','.join(values_list)};"""
    
    # Write to temp file and execute
    temp_file = f"/tmp/batch_{os.getpid()}_{time.time()}.sql"
    with open(temp_file, 'w') as f:
        f.write(sql)
    
    try:
        cmd = f'/opt/heavyai/bin/heavysql -p HyperInteractive -u admin -d heavyai < {temp_file} 2>&1'
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
        if result.returncode == 0:
            return len(rows)
        else:
            print(f"Error: {result.stderr}")
            return 0
    finally:
        if os.path.exists(temp_file):
            os.remove(temp_file)

def process_file(csv_file):
    """Process a single CSV file in batches"""
    print(f"\nProcessing: {os.path.basename(csv_file)}")
    
    # Count rows in file
    with open(csv_file, 'r') as f:
        total_rows = sum(1 for line in f) - 1  # Subtract header
    
    print(f"Total rows: {total_rows:,}")
    
    # Process in larger batches for speed
    batch_size = 5000  # Increased batch size
    batches = []
    
    for start_idx in range(0, total_rows, batch_size):
        batches.append((csv_file, start_idx, batch_size))
    
    # Process batches in parallel
    loaded = 0
    with Pool(processes=min(8, cpu_count())) as pool:
        for result in pool.imap(load_csv_batch, batches):
            loaded += result
            if loaded % 10000 == 0:
                print(f"  Loaded {loaded:,} / {total_rows:,} rows", end='\r')
    
    print(f"  Loaded {loaded:,} / {total_rows:,} rows - Complete")
    return loaded

def main():
    data_dir = "/srv/samba/shared/market_data/nifty/oc_with_futures"
    
    print("=== Optimized Bulk Loader ===")
    print(f"Starting at: {datetime.now()}")
    
    initial_count = get_row_count()
    print(f"Initial row count: {initial_count:,}")
    
    # Get all CSV files
    csv_files = sorted(glob.glob(os.path.join(data_dir, "*.csv")))
    print(f"Found {len(csv_files)} CSV files")
    
    # Process each file
    total_loaded = 0
    start_time = time.time()
    
    for csv_file in csv_files:
        file_start = time.time()
        loaded = process_file(csv_file)
        file_time = time.time() - file_start
        total_loaded += loaded
        
        # Calculate performance
        rows_per_sec = loaded / file_time if file_time > 0 else 0
        print(f"  Time: {file_time:.1f}s, Speed: {rows_per_sec:.0f} rows/sec")
        
        # Show overall progress
        elapsed = time.time() - start_time
        overall_speed = total_loaded / elapsed if elapsed > 0 else 0
        eta_seconds = (11500000 - total_loaded) / overall_speed if overall_speed > 0 else 0
        eta_hours = eta_seconds / 3600
        
        print(f"  Total loaded: {total_loaded:,}, Overall speed: {overall_speed:.0f} rows/sec, ETA: {eta_hours:.1f} hours\n")
    
    final_count = get_row_count()
    total_time = time.time() - start_time
    
    print("\n=== Summary ===")
    print(f"Files processed: {len(csv_files)}")
    print(f"Rows loaded: {total_loaded:,}")
    print(f"Final row count: {final_count:,}")
    print(f"Total time: {total_time/60:.1f} minutes")
    print(f"Average speed: {total_loaded/total_time:.0f} rows/sec")
    print(f"Completed at: {datetime.now()}")

if __name__ == "__main__":
    main()