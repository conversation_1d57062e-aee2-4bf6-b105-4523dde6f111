#!/usr/bin/env python3
"""
Debug sheet creation issue
"""
import pandas as pd

# Test sheet name truncation
strategy_name = 'RS,916-1200,ATM-SELL,OTM2-BUY WITH 100%SL'

trans_name = f"{strategy_name} Trans"[:31]
results_name = f"{strategy_name} Results"[:31]

print(f"Original strategy name: {strategy_name}")
print(f"Trans sheet name: '{trans_name}' (length: {len(trans_name)})")
print(f"Results sheet name: '{results_name}' (length: {len(results_name)})")

# Create a test Excel file with both sheets
output_path = "/srv/samba/shared/test_outputs/debug_sheets.xlsx"

with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
    # Create dummy data
    df1 = pd.DataFrame({'A': [1, 2, 3]})
    df2 = pd.DataFrame({'B': [4, 5, 6]})
    
    # Write sheets
    df1.to_excel(writer, sheet_name=trans_name, index=False)
    df2.to_excel(writer, sheet_name=results_name, index=False)
    
print(f"\nCreated test file: {output_path}")

# Read back to verify
xl = pd.ExcelFile(output_path)
print(f"Sheets in file: {xl.sheet_names}")
print(f"Number of sheets: {len(xl.sheet_names)}")