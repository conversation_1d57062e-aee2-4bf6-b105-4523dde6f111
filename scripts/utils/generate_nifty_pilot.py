#!/usr/bin/env python3
"""
Generate a 5-row sample Nifty option-chain CSV (with futures join) that follows the
`nifty_option_chain_shema.md` specification.

Input files
-----------
Options : /srv/samba/shared/market_data/nifty/IV_2025_apr_nifty_cleaned.csv
Futures : /srv/samba/shared/market_data/nifty/nifty_future.csv

Output
------
/srv/samba/shared/market_data/nifty/oc_with_futures/sample_IV_2025_apr_with_futures.csv
"""
import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

OPTIONS_FILE = "/srv/samba/shared/market_data/nifty/IV_2025_apr_nifty_cleaned.csv"
FUTURES_FILE = "/srv/samba/shared/market_data/nifty/nifty_future.csv"
OUTPUT_DIR = "/srv/samba/shared/market_data/nifty/oc_with_futures"
OUTPUT_FILE = os.path.join(OUTPUT_DIR, "sample_IV_2025_apr_with_futures.csv")

# ---------------------------------------------------------------------------
# Utility helpers
# ---------------------------------------------------------------------------
NSE_HOLIDAYS = {
    # 2025    (keep in sync with processing code)
    "2025-02-26", "2025-03-14", "2025-03-31", "2025-04-10", "2025-04-14",
    "2025-04-18", "2025-05-01", "2025-08-15", "2025-08-27", "2025-10-02",
    "2025-10-21", "2025-10-22", "2025-11-05", "2025-12-25",
    # 2024
    "2024-01-26", "2024-03-08", "2024-03-25", "2024-03-29", "2024-04-11",
    "2024-04-17", "2024-05-01", "2024-06-17", "2024-08-15", "2024-10-02",
    "2024-11-01", "2024-12-25",
}

def is_trading_day(day: datetime.date) -> bool:
    if day.weekday() >= 5:
        return False
    return day.strftime("%Y-%m-%d") not in NSE_HOLIDAYS

def trading_dte(trade_date: str, expiry_date: str) -> int:
    td = datetime.strptime(trade_date, "%Y-%m-%d").date()
    ed = datetime.strptime(expiry_date, "%Y-%m-%d").date()
    if td >= ed:
        return 0
    cur = td + timedelta(days=1)
    cnt = 0
    while cur <= ed:
        if is_trading_day(cur):
            cnt += 1
        cur += timedelta(days=1)
    return cnt

def to_full_date(ymd: str) -> str:
    ymd = str(ymd).zfill(6)
    year = int(ymd[:2]) + 2000
    return f"{year:04d}-{int(ymd[2:4]):02d}-{int(ymd[4:6]):02d}"

def to_full_time(hm: str) -> str:
    if ":" in hm:
        parts = hm.split(":")
        return f"{parts[0].zfill(2)}:{parts[1].zfill(2)}:00"
    hm = str(hm).zfill(4)
    return f"{hm[:2]}:{hm[2:]}:00"

def zone_info(time_str: str):
    t = datetime.strptime(time_str, "%H:%M:%S").time()
    if t >= datetime.strptime("09:15:00", "%H:%M:%S").time() and t <= datetime.strptime("10:30:00", "%H:%M:%S").time():
        return 1, "OPEN"
    if t <= datetime.strptime("12:00:00", "%H:%M:%S").time():
        return 2, "MID_MORN"
    if t <= datetime.strptime("13:30:00", "%H:%M:%S").time():
        return 3, "LUNCH"
    if t <= datetime.strptime("15:00:00", "%H:%M:%S").time():
        return 4, "AFTERNOON"
    if t <= datetime.strptime("15:30:00", "%H:%M:%S").time():
        return 5, "CLOSE"
    return 1, "OPEN"

# ---------------------------------------------------------------------------
# Load data
# ---------------------------------------------------------------------------
print("Reading source CSVs ...")
opt_df = pd.read_csv(OPTIONS_FILE)
fut_df = pd.read_csv(FUTURES_FILE)

# Normalize key columns
opt_df["trade_date"] = opt_df["trade_date"].apply(to_full_date)
opt_df["expiry_date"] = opt_df["expiry_date"].apply(to_full_date)
opt_df["trade_time"] = opt_df["trade_time"].apply(to_full_time)

opt_df["index_name"] = "NIFTY"
opt_df["spot"] = opt_df["underlying_price"]

print("Calculating ATM per tick ...")
# Identify group keys
grp_cols = ["trade_date", "trade_time", "expiry_date"]

# Create a function to calculate synthetic futures and find ATM for each group
def find_atm_strike(group):
    # Calculate synthetic futures for all rows where both CE and PE have valid prices
    valid_rows = group[(group["ce_close"] > 0) & (group["pe_close"] > 0)].copy()
    
    if len(valid_rows) > 0:
        # Step 1: Calculate synthetic futures for each valid strike
        valid_rows["synthetic_future"] = valid_rows["strike"] + valid_rows["ce_close"] - valid_rows["pe_close"]
        
        # Step 2: Calculate average synthetic future
        avg_synthetic = valid_rows["synthetic_future"].mean()
        
        # Step 3: Find the strike closest to the average synthetic future
        valid_strikes = sorted(valid_rows["strike"].unique())
        atm_strike = min(valid_strikes, key=lambda x: abs(x - avg_synthetic))
        atm_method = "SYNTHETIC_TICK"
        
        # Debug output
        spot_value = group["spot"].iloc[0]
        print(f"Group: {group['trade_date'].iloc[0]} {group['trade_time'].iloc[0]} {group['expiry_date'].iloc[0]}")
        print(f"  Spot: {spot_value}")
        print(f"  Average synthetic future: {avg_synthetic}")
        print(f"  Selected ATM strike: {atm_strike} (closest to average synthetic future)")
    else:
        # Fallback to rounded spot if no valid pairs
        spot_value = group["spot"].iloc[0]
        atm_strike = round(spot_value / 50) * 50
        atm_method = "SPOT_TICK"
        print(f"Group: {group['trade_date'].iloc[0]} {group['trade_time'].iloc[0]} {group['expiry_date'].iloc[0]}")
        print(f"  Fallback to ATM strike: {atm_strike} with method: {atm_method}")
    
    # Set the same ATM strike for all rows in this group
    group["atm_strike"] = atm_strike
    group["atm_method"] = atm_method
    return group

# Apply the function to each group
opt_df = opt_df.groupby(grp_cols, group_keys=False).apply(find_atm_strike)

# DTE calculation
print("Calculating DTE ...")
opt_df["dte"] = opt_df.apply(lambda r: trading_dte(r["trade_date"], r["expiry_date"]), axis=1)

# Expiry buckets
print("Assigning expiry buckets ...")
opt_df["expiry_bucket"] = "OTHER"
for td, sub in opt_df.groupby("trade_date"):
    exps = sorted(sub["expiry_date"].unique())
    if exps:
        opt_df.loc[opt_df["trade_date"] == td, "expiry_bucket"] = opt_df.loc[opt_df["trade_date"] == td].apply(
            lambda r: "CW" if r["expiry_date"] == exps[0] else 
                     ("NW" if len(exps) > 1 and r["expiry_date"] == exps[1] else 
                      r["expiry_bucket"]), axis=1)
    
    # Monthly expiry detection  
    for ed in exps:
        ed_dt = datetime.strptime(ed, "%Y-%m-%d")
        if ed_dt.weekday() == 3 and (ed_dt + timedelta(days=7)).month != ed_dt.month:  # Last Thursday
            # Mark as CM if not already CW/NW, or NM if already some other monthly
            monthly_mask = (opt_df["trade_date"] == td) & (opt_df["expiry_date"] == ed)
            bucket_mask = ~opt_df["expiry_bucket"].isin(["CW", "NW"])
            if any(monthly_mask & bucket_mask):
                cm_mask = monthly_mask & bucket_mask & ~opt_df["expiry_bucket"].isin(["CM"])
                if any(cm_mask):
                    opt_df.loc[cm_mask, "expiry_bucket"] = "CM"
                else:
                    opt_df.loc[monthly_mask & bucket_mask, "expiry_bucket"] = "NM"

# Zones
opt_df[["zone_id", "zone_name"]] = opt_df["trade_time"].apply(lambda x: pd.Series(zone_info(x)))

# Strike classifications
step = 50
opt_df["call_strike_type"] = opt_df.apply(
    lambda r: "ATM" if r["strike"] == r["atm_strike"] else (
        f"ITM{int(np.ceil(abs(r['strike'] - r['atm_strike']) / step))}" if r["strike"] < r["atm_strike"] else 
        f"OTM{int(np.ceil(abs(r['strike'] - r['atm_strike']) / step))}"
    ), axis=1)
opt_df["put_strike_type"] = opt_df.apply(
    lambda r: "ATM" if r["strike"] == r["atm_strike"] else (
        f"ITM{int(np.ceil(abs(r['strike'] - r['atm_strike']) / step))}" if r["strike"] > r["atm_strike"] else 
        f"OTM{int(np.ceil(abs(r['strike'] - r['atm_strike']) / step))}"
    ), axis=1)

# Futures join prep
print("Preparing futures data ...")
fut_df["date"] = pd.to_datetime(fut_df["date"]).dt.strftime("%Y-%m-%d")
fut_df["time"] = fut_df["time"].astype(str)
fut_df["time_full"] = fut_df["time"].apply(lambda t: t if len(t.split(":")) == 3 else f"{t}:00")
fut_df["time_int"] = fut_df["time_full"].str.replace(":", "").astype(int)

columns_map = {
    "open": "future_open",
    "high": "future_high",
    "low": "future_low",
    "close": "future_close",
    "volume": "future_volume",
    "oi": "future_oi",
    "coi": "future_coi",
}
for tgt in columns_map.values():
    opt_df[tgt] = np.nan

print("Joining futures ... (may take a moment)")
for td, sub in opt_df.groupby("trade_date"):
    fut_sub = fut_df[fut_df["date"] == td].copy()
    if fut_sub.empty:
        continue
    fut_sub["time_int"] = fut_sub["time_full"].str.replace(":", "").astype(int)
    fut_sub.sort_values("time_int", inplace=True)
    # Build numpy arrays for quick search
    fut_times = fut_sub["time_int"].values
    for idx in sub.index:
        opt_time_int = int(opt_df.at[idx, "trade_time"].replace(":", ""))
        # indices where fut_time <= opt_time
        earlier_idx = np.where(fut_times <= opt_time_int)[0]
        if earlier_idx.size > 0:
            chosen = fut_sub.iloc[earlier_idx[-1]]
        else:
            # pick earliest later tick
            chosen = fut_sub.iloc[0]
        for src, tgt in columns_map.items():
            opt_df.at[idx, tgt] = chosen[src]

# Select required columns in correct order
col_order = [
    "trade_date", "trade_time", "expiry_date", "index_name", "spot", "atm_strike", "strike", "dte", "expiry_bucket",
    "zone_id", "zone_name", "call_strike_type", "put_strike_type",
    "ce_symbol", "ce_open", "ce_high", "ce_low", "ce_close", "ce_volume", "ce_oi", "ce_coi", "ce_iv", "ce_delta", "ce_gamma", "ce_theta", "ce_vega", "ce_rho",
    "pe_symbol", "pe_open", "pe_high", "pe_low", "pe_close", "pe_volume", "pe_oi", "pe_coi", "pe_iv", "pe_delta", "pe_gamma", "pe_theta", "pe_vega", "pe_rho",
    "future_open", "future_high", "future_low", "future_close", "future_volume", "future_oi", "future_coi"
]

sample_df = opt_df[col_order].head(5)
print("Sample rows ready:")
print(sample_df.to_string(index=False))

os.makedirs(OUTPUT_DIR, exist_ok=True)
sample_df.to_csv(OUTPUT_FILE, index=False)
print(f"Saved 5-row sample to {OUTPUT_FILE}") 