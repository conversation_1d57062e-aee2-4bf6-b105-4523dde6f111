#!/usr/bin/env python3
import os
import glob
import subprocess
import logging
import sys
import csv
import tempfile
from datetime import datetime

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("nifty_insert.log"),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

# Source directory with CSV files
SOURCE_DIR = "/srv/samba/shared/market_data/nifty/oc_with_futures"

def run_command(cmd):
    """Run a shell command and log the result"""
    logger.info(f"Running command: {cmd[:100]}...")
    
    try:
        process = subprocess.Popen(
            cmd, 
            shell=True, 
            stdout=subprocess.PIPE, 
            stderr=subprocess.PIPE,
            universal_newlines=True
        )
        stdout, stderr = process.communicate()
        
        if process.returncode == 0:
            logger.info("Command succeeded")
            return True, stdout
        else:
            logger.error(f"Command failed with return code {process.returncode}")
            logger.error(f"Error: {stderr}")
            return False, stderr
    except Exception as e:
        logger.error(f"Exception running command: {e}")
        return False, str(e)

def create_table():
    """Create the nifty_option_chain table"""
    logger.info("Creating nifty_option_chain table...")
    
    # Create SQL file without comments at the beginning
    create_sql = """DROP TABLE IF EXISTS nifty_option_chain;
CREATE TABLE nifty_option_chain (
    trade_date DATE,
    trade_time TIME,
    expiry_date DATE,
    index_name TEXT,
    spot DOUBLE,
    atm_strike DOUBLE,
    strike DOUBLE,
    dte INT,
    expiry_bucket TEXT,
    zone_id SMALLINT,
    zone_name TEXT,
    call_strike_type TEXT,
    put_strike_type TEXT,
    ce_symbol TEXT,
    ce_open DOUBLE,
    ce_high DOUBLE,
    ce_low DOUBLE,
    ce_close DOUBLE,
    ce_volume BIGINT,
    ce_oi BIGINT,
    ce_coi BIGINT,
    ce_iv DOUBLE,
    ce_delta DOUBLE,
    ce_gamma DOUBLE,
    ce_theta DOUBLE,
    ce_vega DOUBLE,
    ce_rho DOUBLE,
    pe_symbol TEXT,
    pe_open DOUBLE,
    pe_high DOUBLE,
    pe_low DOUBLE,
    pe_close DOUBLE,
    pe_volume BIGINT,
    pe_oi BIGINT,
    pe_coi BIGINT,
    pe_iv DOUBLE,
    pe_delta DOUBLE,
    pe_gamma DOUBLE,
    pe_theta DOUBLE,
    pe_vega DOUBLE,
    pe_rho DOUBLE,
    future_open DOUBLE,
    future_high DOUBLE,
    future_low DOUBLE,
    future_close DOUBLE,
    future_volume BIGINT,
    future_oi BIGINT,
    future_coi BIGINT
);"""
    
    with open("create_table.sql", "w") as f:
        f.write(create_sql)
    
    success, output = run_command("/opt/heavyai/bin/heavysql -s 127.0.0.1 --port 6274 -u admin -p HyperInteractive -d heavyai < create_table.sql")
    if success:
        logger.info("Table created successfully")
        return True
    else:
        logger.error("Failed to create table")
        return False

def process_csv_in_batches(csv_file, batch_size=1000):
    """Process a CSV file in batches and generate SQL INSERT statements"""
    base_name = os.path.basename(csv_file)
    file_size = os.path.getsize(csv_file) / (1024 * 1024)  # Size in MB
    
    logger.info(f"Processing file: {base_name} (size: {file_size:.2f} MB)")
    
    # Count lines in the file
    with open(csv_file, 'r') as f:
        line_count = sum(1 for _ in f)
    
    logger.info(f"File has {line_count} lines including header")
    
    total_batches = (line_count - 1) // batch_size + 1
    success_count = 0
    
    # Open the CSV file
    with open(csv_file, 'r') as f:
        # Read the header
        reader = csv.reader(f)
        header = next(reader)
        
        # Process in batches
        for batch_num in range(1, total_batches + 1):
            logger.info(f"Processing batch {batch_num}/{total_batches}")
            
            # Create a temporary SQL file for this batch
            sql_file = f"batch_{batch_num}.sql"
            with open(sql_file, 'w') as sql_f:
                # Process rows in this batch
                for i in range(batch_size):
                    try:
                        row = next(reader)
                        
                        # Generate INSERT statement
                        insert_stmt = f"""INSERT INTO nifty_option_chain VALUES (
'{row[0]}', '{row[1]}', '{row[2]}', '{row[3]}', {row[4]}, {row[5]}, {row[6]}, {row[7]}, '{row[8]}', {row[9]}, '{row[10]}', '{row[11]}', '{row[12]}', '{row[13]}', {row[14]}, {row[15]}, {row[16]}, {row[17]}, {row[18]}, {row[19]}, {row[20]}, {row[21]}, {row[22]}, {row[23]}, {row[24]}, {row[25]}, {row[26]}, '{row[27]}', {row[28]}, {row[29]}, {row[30]}, {row[31]}, {row[32]}, {row[33]}, {row[34]}, {row[35]}, {row[36]}, {row[37]}, {row[38]}, {row[39]}, {row[40]}, {row[41]}, {row[42]}, {row[43]}, {row[44]}, {row[45]}, {row[46]}, {row[47]});
"""
                        sql_f.write(insert_stmt)
                    except StopIteration:
                        break
            
            # Execute the SQL file
            success, _ = run_command(f"/opt/heavyai/bin/heavysql -s 127.0.0.1 --port 6274 -u admin -p HyperInteractive -d heavyai < {sql_file}")
            
            # Clean up
            os.remove(sql_file)
            
            if success:
                success_count += 1
                logger.info(f"Batch {batch_num} loaded successfully")
            else:
                logger.error(f"Failed to load batch {batch_num}")
    
    success_rate = (success_count / total_batches) * 100
    logger.info(f"File processing complete: {success_count}/{total_batches} batches successful ({success_rate:.2f}%)")
    
    return success_count == total_batches

def main():
    # Create the table
    if not create_table():
        logger.error("Table creation failed. Exiting.")
        sys.exit(1)
    
    # Get the list of CSV files
    csv_files = glob.glob(f"{SOURCE_DIR}/*.csv")
    csv_files.sort()
    
    # Limit to a few files for testing (comment out for full run)
    csv_files = csv_files[:3]
    
    logger.info(f"Found {len(csv_files)} CSV files to process")
    
    # Track metrics
    total_files = len(csv_files)
    loaded_files = 0
    failed_files = 0
    
    # Process each file
    for i, csv_file in enumerate(csv_files, 1):
        logger.info(f"Processing file {i}/{total_files}: {os.path.basename(csv_file)}")
        
        if process_csv_in_batches(csv_file, batch_size=1000):
            loaded_files += 1
            logger.info(f"Successfully loaded {os.path.basename(csv_file)}")
        else:
            failed_files += 1
            logger.error(f"Failed to load {os.path.basename(csv_file)}")
    
    # Summary
    logger.info("=== Load Summary ===")
    logger.info(f"Total files: {total_files}")
    logger.info(f"Successfully loaded: {loaded_files}")
    logger.info(f"Failed to load: {failed_files}")
    
    # Verify data
    success, output = run_command("""echo "SELECT COUNT(*) FROM nifty_option_chain;" | /opt/heavyai/bin/heavysql -s 127.0.0.1 --port 6274 -u admin -p HyperInteractive -d heavyai""")
    if success:
        logger.info(f"Total rows in table: {output}")

if __name__ == "__main__":
    start_time = datetime.now()
    logger.info(f"Starting data insert at {start_time}")
    
    main()
    
    end_time = datetime.now()
    logger.info(f"Data insert completed at {end_time}")
    logger.info(f"Total duration: {end_time - start_time}") 