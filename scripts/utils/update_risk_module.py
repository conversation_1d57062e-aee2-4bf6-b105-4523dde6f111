#!/usr/bin/env python3
"""
Update Risk Module <PERSON>

This script updates the risk.py module to properly handle start time validation
in the SL/TP evaluation function. It ensures that the first candle check
properly respects the configured start time.
"""

import os
import shutil
import re
import sys
import logging
from datetime import datetime

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler(f'update_risk_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log')
    ]
)
logger = logging.getLogger('update_risk')

# Path to the risk.py file
RISK_MODULE_PATH = '/srv/samba/shared/bt/backtester_stable/BTRUN/models/risk.py'
BACKUP_PATH = f'{RISK_MODULE_PATH}.bak.{datetime.now().strftime("%Y%m%d_%H%M%S")}'

def backup_file():
    """Create a backup of the original file"""
    try:
        shutil.copy2(RISK_MODULE_PATH, BACKUP_PATH)
        logger.info(f"Created backup at: {BACKUP_PATH}")
        return True
    except Exception as e:
        logger.error(f"Failed to create backup: {e}")
        return False

def update_risk_module():
    """Update the risk.py module with improved start time handling"""
    if not os.path.exists(RISK_MODULE_PATH):
        logger.error(f"Risk module not found at: {RISK_MODULE_PATH}")
        return False
    
    # Make a backup first
    if not backup_file():
        return False
    
    try:
        # Read the file content
        with open(RISK_MODULE_PATH, 'r') as f:
            content = f.read()
        
        # Find the first candle skip logic pattern
        first_candle_pattern = re.compile(
            r'# MAJOR FIX: ALWAYS skip the first entry candle to prevent immediate SL/TP trigger\s+'
            r'# This prevents the issue where trades exit at entry time \(9:16\) instead of specified exit time \(12:00\)\s+'
            r'if len\(tick_df\) > 1:.*?tick_df = tick_df\.iloc\[1:\]\.reset_index\(drop=True\)',
            re.DOTALL
        )
        
        # Define the improved replacement code
        replacement = """# IMPROVED FIX: Skip ticks based on start time when available
    # This properly handles the case where we should skip candles before the start time (9:16)
    if len(tick_df) > 1:
        # Get start_time from params if available
        start_time_str = risk_rule.params.get('start_time')
        
        if start_time_str:
            # If we have a start_time in params, skip candles before that time
            try:
                # Convert start_time to a comparable format based on its type
                if isinstance(start_time_str, int):
                    # Integer HHMMSS format
                    time_str = str(start_time_str).zfill(6)
                    start_hour, start_minute = int(time_str[:2]), int(time_str[2:4])
                elif isinstance(start_time_str, str) and ':' in start_time_str:
                    # HH:MM:SS format
                    time_parts = start_time_str.split(':')
                    start_hour, start_minute = int(time_parts[0]), int(time_parts[1])
                else:
                    # Try to convert a string of digits to HHMMSS
                    time_str = str(start_time_str).zfill(6)
                    start_hour, start_minute = int(time_str[:2]), int(time_str[2:4])
                
                # Skip all candles before start time
                original_len = len(tick_df)
                filtered_tick_df = tick_df[
                    (tick_df['datetime'].dt.hour > start_hour) | 
                    ((tick_df['datetime'].dt.hour == start_hour) & (tick_df['datetime'].dt.minute >= start_minute))
                ].reset_index(drop=True)
                
                # If we have candles after filtering, use those
                if not filtered_tick_df.empty:
                    risk_debug_logger.info(f"RISK: Filtered {original_len - len(filtered_tick_df)} candles before start time {start_hour}:{start_minute}")
                    tick_df = filtered_tick_df
                else:
                    # If all candles were filtered out, fall back to skipping just the first candle
                    risk_debug_logger.warning(f"RISK: All candles were before start time {start_hour}:{start_minute}, using all but first candle")
                    tick_df = tick_df.iloc[1:].reset_index(drop=True)
            except Exception as e:
                risk_debug_logger.error(f"RISK: Error filtering by start time: {e}. Falling back to first candle skip.")
                # Fall back to skipping just the first candle
                tick_df = tick_df.iloc[1:].reset_index(drop=True)
        else:
            # No start_time in params, skip just the first candle
            risk_debug_logger.info("RISK: No start_time provided, skipping first candle only")
            tick_df = tick_df.iloc[1:].reset_index(drop=True)"""
        
        # Replace the pattern
        new_content = first_candle_pattern.sub(replacement, content)
        
        # Check if any changes were made
        if new_content == content:
            logger.warning("No changes made to the file - pattern not found")
            return False
        
        # Write the updated content
        with open(RISK_MODULE_PATH, 'w') as f:
            f.write(new_content)
        
        logger.info(f"Successfully updated: {RISK_MODULE_PATH}")
        return True
    
    except Exception as e:
        logger.error(f"Error updating risk module: {e}")
        return False

def main():
    """Main function"""
    logger.info("Starting risk module update...")
    
    if update_risk_module():
        logger.info("Update completed successfully")
        return 0
    else:
        logger.error("Update failed")
        return 1

if __name__ == "__main__":
    sys.exit(main()) 