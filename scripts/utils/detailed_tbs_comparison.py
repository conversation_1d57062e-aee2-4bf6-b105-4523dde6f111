#!/usr/bin/env python3
"""
Detailed TBS comparison between archive and new system outputs
Focuses on PnL variance and trade matching after ATM alignment
"""
import pandas as pd
import numpy as np
import json
from datetime import datetime
import os

class DetailedTBSComparison:
    def __init__(self):
        self.archive_file = "/srv/samba/shared/comparison_results/archive_TBS_2024-04-01.xlsx"
        self.new_file = "/srv/samba/shared/comparison_results/new_TBS_2024-04-01.xlsx"
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
    def analyze_metrics(self):
        """Compare metrics between systems"""
        print("\n=== METRICS COMPARISON ===")
        
        # Read metrics from both files
        archive_metrics = pd.read_excel(self.archive_file, sheet_name='Metrics')
        new_metrics = pd.read_excel(self.new_file, sheet_name='Metrics')
        
        # Extract key metrics
        metrics_to_compare = ['Gross P&L', 'Net P&L', 'Win Rate %', 'Profit Factor', 
                             'Total Trades', 'Winning Trades', 'Losing Trades']
        
        comparison = []
        
        for metric in metrics_to_compare:
            archive_row = archive_metrics[archive_metrics['Particulars'] == metric]
            new_row = new_metrics[new_metrics['Particulars'] == metric]
            
            if not archive_row.empty and not new_row.empty:
                archive_val = archive_row['Combined'].values[0]
                new_val = new_row['Combined'].values[0]
                
                # Calculate variance
                if isinstance(archive_val, (int, float)) and isinstance(new_val, (int, float)):
                    if archive_val != 0:
                        variance = abs((new_val - archive_val) / archive_val * 100)
                    else:
                        variance = 100 if new_val != 0 else 0
                else:
                    variance = None
                    
                comparison.append({
                    'Metric': metric,
                    'Archive': archive_val,
                    'New': new_val,
                    'Variance %': f"{variance:.2f}" if variance is not None else "N/A"
                })
        
        # Display comparison
        df = pd.DataFrame(comparison)
        print(df.to_string(index=False))
        
        return comparison
    
    def analyze_transactions(self):
        """Compare transactions between systems"""
        print("\n\n=== TRANSACTION COMPARISON ===")
        
        # Read portfolio transactions
        archive_trans = pd.read_excel(self.archive_file, sheet_name='PORTFOLIO Trans')
        new_trans = pd.read_excel(self.new_file, sheet_name='PORTFOLIO Trans')
        
        print(f"\nArchive Transactions: {len(archive_trans)}")
        print(f"New System Transactions: {len(new_trans)}")
        
        # Check column differences
        archive_cols = set(archive_trans.columns)
        new_cols = set(new_trans.columns)
        
        print(f"\nColumn count - Archive: {len(archive_cols)}, New: {len(new_cols)}")
        
        # If new system has transactions, analyze them
        if len(new_trans) > 0:
            print("\nNew System Transaction Details:")
            for idx, row in new_trans.iterrows():
                print(f"  Trade {idx+1}:")
                for col in ['Strategy Name', 'Instrument', 'Strike', 'Entry Time', 'Exit Time', 'PNL']:
                    if col in new_trans.columns:
                        print(f"    {col}: {row[col]}")
        
        # Check for strategy-specific sheets
        archive_xl = pd.ExcelFile(self.archive_file)
        new_xl = pd.ExcelFile(self.new_file)
        
        archive_strategy_sheets = [s for s in archive_xl.sheet_names if 'Trans' in s and s != 'PORTFOLIO Trans']
        new_strategy_sheets = [s for s in new_xl.sheet_names if 'Trans' in s and s != 'PORTFOLIO Trans']
        
        print(f"\nStrategy-specific transaction sheets:")
        print(f"  Archive: {archive_strategy_sheets}")
        print(f"  New: {new_strategy_sheets}")
        
        return archive_trans, new_trans
    
    def analyze_atm_strikes(self):
        """Check if ATM strikes are now aligned"""
        print("\n\n=== ATM STRIKE ANALYSIS ===")
        
        # Try to find ATM strikes used in both systems
        archive_trans = pd.read_excel(self.archive_file, sheet_name='PORTFOLIO Trans')
        
        if 'Strike' in archive_trans.columns:
            archive_strikes = archive_trans['Strike'].unique()
            print(f"\nArchive strikes used: {sorted(archive_strikes)}")
            
            # Check if these are ATM strikes
            atm_candidates = [s for s in archive_strikes if s % 100 == 0]  # Assuming ATM is round number
            print(f"Potential ATM strikes: {atm_candidates}")
        
        return None
    
    def generate_report(self):
        """Generate comprehensive comparison report"""
        print("\n" + "="*80)
        print("TBS COMPARISON REPORT - POST ATM ALIGNMENT")
        print("="*80)
        print(f"\nTimestamp: {datetime.now()}")
        print(f"Archive File: {self.archive_file}")
        print(f"New File: {self.new_file}")
        
        # Run all analyses
        metrics = self.analyze_metrics()
        trans_archive, trans_new = self.analyze_transactions()
        self.analyze_atm_strikes()
        
        # Summary
        print("\n\n=== SUMMARY ===")
        print("\nKey Findings:")
        
        # Check PnL variance
        pnl_metrics = [m for m in metrics if m['Metric'] in ['Gross P&L', 'Net P&L']]
        for m in pnl_metrics:
            if m['New'] == 0 and m['Archive'] != 0:
                print(f"⚠️  {m['Metric']}: New system shows 0, Archive shows {m['Archive']}")
            else:
                print(f"✓ {m['Metric']} - Archive: {m['Archive']}, New: {m['New']}, Variance: {m['Variance %']}%")
        
        # Check trade count
        print(f"\n⚠️  Trade Count Mismatch: Archive has {len(trans_archive)} trades, New has {len(trans_new)} trades")
        
        print("\n\nConclusion:")
        if len(trans_new) == 0 or (len(trans_new) == 1 and all(trans_new.iloc[0].isna())):
            print("❌ New system appears to have NO TRADES executed")
            print("   This suggests the ATM calculation or strategy logic needs review")
        else:
            print("✓ Both systems generated trades, need to analyze PnL variance")
        
        # Save report
        report = {
            "timestamp": self.timestamp,
            "files": {
                "archive": self.archive_file,
                "new": self.new_file
            },
            "metrics_comparison": metrics,
            "transaction_count": {
                "archive": len(trans_archive),
                "new": len(trans_new)
            },
            "key_findings": [
                "New system shows 0 PnL and minimal trades",
                "Possible issue with ATM calculation or strategy execution",
                "Need to verify synthetic future ATM implementation"
            ]
        }
        
        report_path = f"/srv/samba/shared/tbs_comparison_report_{self.timestamp}.json"
        with open(report_path, 'w') as f:
            json.dump(report, f, indent=2)
        
        print(f"\n\nReport saved to: {report_path}")
        
        return report

def main():
    comparator = DetailedTBSComparison()
    comparator.generate_report()

if __name__ == "__main__":
    main()