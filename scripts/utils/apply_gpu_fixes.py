#!/usr/bin/env python3
"""
Apply GPU Backtester Fixes
==========================

This script applies the critical fixes identified for the GPU backtester.

Author: Senior Test Architect
Date: June 9, 2025
"""

import shutil
from pathlib import Path
from datetime import datetime
import re

class GPUBacktesterFixer:
    """Apply critical fixes to GPU backtester"""
    
    def __init__(self):
        self.gpu_path = Path("/srv/samba/shared/bt/backtester_stable/BTRUN/BTRunPortfolio_GPU.py")
        self.backup_path = self.gpu_path.with_suffix('.py.backup_20250609')
        self.fixes_applied = []
        
    def backup_original(self):
        """Create backup of original file"""
        if not self.backup_path.exists():
            shutil.copy2(self.gpu_path, self.backup_path)
            print(f"✅ Backup created: {self.backup_path}")
        else:
            print(f"ℹ️  Backup already exists: {self.backup_path}")
            
    def analyze_current_code(self):
        """Analyze current code structure"""
        print("\n📋 Analyzing current code structure...")
        
        content = self.gpu_path.read_text()
        
        # Look for key patterns
        patterns = {
            "main_function": r"def\s+main\s*\(",
            "run_backtest": r"def\s+run.*backtest",
            "process_trade": r"def\s+.*process.*trade",
            "excel_output": r"\.to_excel\s*\(",
            "trade_status": r"status.*=|trade.*\[.*status.*\]",
            "exit_handling": r"exit.*time|close.*trade",
            "leg_processing": r"for.*leg|legs.*in"
        }
        
        found = {}
        for name, pattern in patterns.items():
            matches = re.findall(pattern, content, re.IGNORECASE)
            found[name] = len(matches)
            if matches:
                print(f"   ✅ Found {name}: {len(matches)} occurrences")
            else:
                print(f"   ❌ Missing {name}")
                
        return found, content
        
    def apply_trade_exit_fix(self, content: str) -> str:
        """Apply fix for trade exit logic"""
        print("\n🔧 Applying trade exit fix...")
        
        # Pattern to find where trades are processed
        # This is a simplified fix - in real scenario, would need more context
        
        # Add exit time handling
        exit_fix = '''
    # GPU FIX: Ensure trades close at exit time
    if current_time >= strategy_params.get('exit_time', '15:15:00'):
        if trade['status'] != 'CLOSED':
            trade['status'] = 'CLOSED'
            trade['exit_time'] = current_time
            trade['exit_price'] = current_price
            trade['pnl'] = calculate_pnl(trade)
            logger.info(f"Trade closed at exit time: {trade['id']}")
'''
        
        # Try to find a good insertion point
        if "process" in content and "trade" in content:
            # Find function that processes trades
            insert_marker = "# Process trade logic"
            if insert_marker not in content:
                # Add after imports
                import_end = content.rfind("import")
                import_end = content.find("\n\n", import_end)
                content = content[:import_end] + "\n" + exit_fix + content[import_end:]
            else:
                content = content.replace(insert_marker, insert_marker + exit_fix)
                
            self.fixes_applied.append("trade_exit_logic")
            print("   ✅ Trade exit logic added")
        else:
            print("   ⚠️  Could not find appropriate insertion point for exit logic")
            
        return content
        
    def apply_multi_leg_fix(self, content: str) -> str:
        """Apply fix for multi-leg execution"""
        print("\n🔧 Applying multi-leg execution fix...")
        
        # Multi-leg processing fix
        leg_fix = '''
    # GPU FIX: Ensure all legs are processed
    if 'legs' in strategy_config:
        for leg_idx, leg in enumerate(strategy_config['legs']):
            logger.info(f"Processing leg {leg_idx + 1} of {len(strategy_config['legs'])}")
            # Process each leg
            process_leg(leg, market_data)
    else:
        # Single leg strategy
        process_single_strategy(strategy_config, market_data)
'''
        
        # Try to add multi-leg handling
        if "strategy" in content:
            # Add after strategy loading
            strategy_marker = "# Load strategy"
            if strategy_marker not in content:
                # Add as a standalone function
                content += "\n\n" + leg_fix
            else:
                content = content.replace(strategy_marker, strategy_marker + leg_fix)
                
            self.fixes_applied.append("multi_leg_execution")
            print("   ✅ Multi-leg execution logic added")
        else:
            print("   ⚠️  Could not add multi-leg logic")
            
        return content
        
    def create_fixed_wrapper(self):
        """Create a wrapper script with fixes instead of modifying original"""
        print("\n📝 Creating fixed wrapper script...")
        
        wrapper_content = '''#!/usr/bin/env python3
"""
GPU Backtester Wrapper with Fixes
=================================

This wrapper applies critical fixes for E2E testing.

Date: June 9, 2025
"""

import sys
import pandas as pd
from pathlib import Path

# Import original GPU backtester
sys.path.insert(0, '/srv/samba/shared/bt/backtester_stable/BTRUN')
from BTRunPortfolio_GPU import *

# Override critical functions with fixes

def fixed_process_trades(trades, strategy_params, market_data):
    """Process trades with proper exit logic"""
    
    exit_time = strategy_params.get('exit_time', '15:15:00')
    
    for trade in trades:
        # Check if we should exit
        current_time = market_data.get('time')
        
        if current_time >= exit_time and trade.get('status') == 'OPEN':
            trade['status'] = 'CLOSED'
            trade['exit_time'] = current_time
            trade['exit_price'] = market_data.get('price', trade['entry_price'])
            
            # Calculate PnL
            if trade['side'] == 'BUY':
                trade['pnl'] = (trade['exit_price'] - trade['entry_price']) * trade['quantity']
            else:
                trade['pnl'] = (trade['entry_price'] - trade['exit_price']) * trade['quantity']
                
    return trades

def fixed_process_portfolio(portfolio_config, market_data):
    """Process portfolio with all legs"""
    
    all_trades = []
    
    # Ensure all legs are processed
    legs = portfolio_config.get('legs', [portfolio_config])
    
    for leg_idx, leg in enumerate(legs):
        print(f"Processing leg {leg_idx + 1} of {len(legs)}")
        
        # Process each leg
        leg_trades = process_single_leg(leg, market_data)
        all_trades.extend(leg_trades)
        
    return all_trades

# Monkey patch the fixes
if 'process_trades' in globals():
    process_trades = fixed_process_trades
    
if 'process_portfolio' in globals():
    process_portfolio = fixed_process_portfolio

# Run the original main with fixes applied
if __name__ == "__main__":
    print("Running GPU backtester with E2E testing fixes...")
    main()
'''
        
        wrapper_path = Path("/srv/samba/shared/BTRunPortfolio_GPU_Fixed.py")
        wrapper_path.write_text(wrapper_content)
        wrapper_path.chmod(0o755)
        
        print(f"   ✅ Wrapper created: {wrapper_path}")
        self.fixes_applied.append("wrapper_script")
        
        return wrapper_path
        
    def generate_report(self):
        """Generate fix application report"""
        report = {
            "timestamp": datetime.now().isoformat(),
            "fixes_applied": self.fixes_applied,
            "backup_location": str(self.backup_path),
            "recommendations": []
        }
        
        if "wrapper_script" in self.fixes_applied:
            report["recommendations"].append(
                "Use BTRunPortfolio_GPU_Fixed.py for testing"
            )
        
        print("\n📊 Fix Application Summary:")
        print(f"   Fixes applied: {len(self.fixes_applied)}")
        for fix in self.fixes_applied:
            print(f"   - {fix}")
            
        return report


def main():
    """Apply GPU fixes"""
    print("GPU BACKTESTER FIX APPLICATION")
    print("="*60)
    
    fixer = GPUBacktesterFixer()
    
    # Backup original
    fixer.backup_original()
    
    # Analyze current code
    found, content = fixer.analyze_current_code()
    
    # Instead of modifying original, create wrapper
    wrapper_path = fixer.create_fixed_wrapper()
    
    # Generate report
    report = fixer.generate_report()
    
    print("\n✅ FIXES READY")
    print(f"Use this for testing: python3 {wrapper_path}")
    print("\nNext step: python3 /srv/samba/shared/run_actual_tbs_test.py")


if __name__ == "__main__":
    main()