#!/usr/bin/env python3
"""
Enhanced Daily ETL Script for Multi-Index Option Chain Data
Handles all indices: NIFTY, BANKNIFTY, MIDCAPNIFTY, SENSEX
"""

import os
import sys
import logging
import json
import traceback
import smtplib
from datetime import datetime, timedelta
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
import glob
import zipfile
import pandas as pd
from heavydb import connect

# Configure logging
LOG_DIR = '/srv/samba/shared/logs/daily_etl'
os.makedirs(LOG_DIR, exist_ok=True)

today = datetime.now().strftime('%Y%m%d')
log_file = os.path.join(LOG_DIR, f'enhanced_etl_{today}.log')

# Setup logger
logger = logging.getLogger('enhanced_etl')
logger.setLevel(logging.DEBUG)

# File handler
fh = logging.FileHandler(log_file)
fh.setLevel(logging.DEBUG)

# Console handler
ch = logging.StreamHandler()
ch.setLevel(logging.INFO)

# Formatter
formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
fh.setFormatter(formatter)
ch.setFormatter(formatter)

logger.addHandler(fh)
logger.addHandler(ch)

# Configuration
class ETLConfig:
    # Database config
    DB_CONFIG = {
        'host': 'localhost',
        'port': 6274,
        'user': 'admin',
        'password': 'HyperInteractive',
        'dbname': 'heavyai'
    }
    
    # Index configurations
    INDEX_CONFIG = {
        'NIFTY': {
            'table_name': 'nifty_option_chain',
            'data_dir': '/srv/samba/shared/market_data/nifty/oc_with_futures',
            'file_pattern': 'IV_*_nifty_futures.csv',
            'zip_file': None,
            'active': True
        },
        'BANKNIFTY': {
            'table_name': 'banknifty_option_chain',
            'data_dir': '/srv/samba/shared/market_data/banknifty',
            'file_pattern': '*.csv',
            'zip_file': 'banknifty_*.zip',
            'active': True
        },
        'MIDCAPNIFTY': {
            'table_name': 'midcpnifty_option_chain',
            'data_dir': '/srv/samba/shared/market_data/midcapnifty',
            'file_pattern': '*.csv',
            'zip_file': 'midcpnifty.zip',
            'active': True
        },
        'SENSEX': {
            'table_name': 'sensex_option_chain',
            'data_dir': '/srv/samba/shared/market_data/sensex',
            'file_pattern': '*.csv',
            'zip_file': 'sensex_*.zip',
            'active': True
        }
    }
    
    # Email notification config (optional)
    EMAIL_CONFIG = {
        'enabled': False,  # Set to True to enable email notifications
        'smtp_server': 'smtp.gmail.com',
        'smtp_port': 587,
        'username': '',  # Your email
        'password': '',  # Your app password
        'from_address': '',
        'to_addresses': []  # List of recipient emails
    }
    
    # ETL settings
    BATCH_SIZE = 100000
    MAX_RETRIES = 3
    RETRY_DELAY = 60  # seconds

class ETLProcessor:
    def __init__(self):
        self.config = ETLConfig()
        self.conn = None
        self.stats = {
            'start_time': datetime.now(),
            'indices_processed': {},
            'total_rows_loaded': 0,
            'errors': [],
            'warnings': []
        }
    
    def connect_db(self):
        """Establish database connection"""
        try:
            self.conn = connect(**self.config.DB_CONFIG)
            logger.info("Connected to HeavyDB successfully")
            return True
        except Exception as e:
            logger.error(f"Failed to connect to database: {e}")
            self.stats['errors'].append(f"Database connection failed: {str(e)}")
            return False
    
    def close_db(self):
        """Close database connection"""
        if self.conn:
            try:
                self.conn.close()
                logger.info("Database connection closed")
            except:
                pass
    
    def check_table_exists(self, table_name):
        """Check if table exists in database"""
        try:
            cursor = self.conn.cursor()
            cursor.execute(f"SELECT COUNT(*) FROM {table_name} LIMIT 1")
            cursor.close()
            return True
        except:
            return False
    
    def create_table_if_not_exists(self, index_name, table_name):
        """Create table for index if it doesn't exist"""
        if self.check_table_exists(table_name):
            logger.info(f"Table {table_name} already exists")
            return True
        
        logger.info(f"Creating table {table_name} for {index_name}")
        
        # Table creation SQL based on NIFTY schema
        create_sql = f"""
        CREATE TABLE IF NOT EXISTS {table_name} (
            trade_date DATE,
            trade_time TEXT ENCODING DICT(32),
            expiry_date DATE,
            index_name TEXT ENCODING DICT(8),
            spot DOUBLE,
            atm_strike INTEGER,
            strike INTEGER,
            dte INTEGER,
            expiry_bucket TEXT ENCODING DICT(8),
            zone_id INTEGER,
            zone_name TEXT ENCODING DICT(16),
            call_strike_type TEXT ENCODING DICT(16),
            put_strike_type TEXT ENCODING DICT(16),
            ce_symbol TEXT ENCODING DICT(32),
            ce_open DOUBLE,
            ce_high DOUBLE,
            ce_low DOUBLE,
            ce_close DOUBLE,
            ce_volume BIGINT,
            ce_oi BIGINT,
            ce_coi BIGINT,
            ce_iv DOUBLE,
            ce_delta DOUBLE,
            ce_gamma DOUBLE,
            ce_theta DOUBLE,
            ce_vega DOUBLE,
            ce_rho DOUBLE,
            pe_symbol TEXT ENCODING DICT(32),
            pe_open DOUBLE,
            pe_high DOUBLE,
            pe_low DOUBLE,
            pe_close DOUBLE,
            pe_volume BIGINT,
            pe_oi BIGINT,
            pe_coi BIGINT,
            pe_iv DOUBLE,
            pe_delta DOUBLE,
            pe_gamma DOUBLE,
            pe_theta DOUBLE,
            pe_vega DOUBLE,
            pe_rho DOUBLE,
            future_open DOUBLE,
            future_high DOUBLE,
            future_low DOUBLE,
            future_close DOUBLE,
            future_volume BIGINT,
            future_oi BIGINT,
            future_coi BIGINT
        ) WITH (fragment_size = 32000000, sort_column = 'trade_date');
        """
        
        try:
            cursor = self.conn.cursor()
            cursor.execute(create_sql)
            self.conn.commit()
            cursor.close()
            logger.info(f"Table {table_name} created successfully")
            return True
        except Exception as e:
            logger.error(f"Failed to create table {table_name}: {e}")
            self.stats['errors'].append(f"Table creation failed for {table_name}: {str(e)}")
            return False
    
    def extract_files_from_zip(self, index_name, config):
        """Extract CSV files from zip if needed"""
        extracted_files = []
        
        if not config.get('zip_file'):
            return extracted_files
        
        zip_pattern = os.path.join(config['data_dir'], config['zip_file'])
        zip_files = glob.glob(zip_pattern)
        
        for zip_file in zip_files:
            if not os.path.exists(zip_file):
                continue
                
            logger.info(f"Extracting files from {zip_file}")
            
            try:
                with zipfile.ZipFile(zip_file, 'r') as zf:
                    # Extract only CSV files
                    csv_files = [f for f in zf.namelist() if f.endswith('.csv')]
                    
                    for csv_file in csv_files:
                        # Check if file was modified today
                        info = zf.getinfo(csv_file)
                        file_date = datetime(*info.date_time).date()
                        
                        if file_date == datetime.now().date():
                            extract_path = os.path.join(config['data_dir'], csv_file)
                            zf.extract(csv_file, config['data_dir'])
                            extracted_files.append(extract_path)
                            logger.info(f"Extracted {csv_file}")
                    
            except Exception as e:
                logger.error(f"Failed to extract from {zip_file}: {e}")
                self.stats['warnings'].append(f"Failed to extract {zip_file}: {str(e)}")
        
        return extracted_files
    
    def find_new_files(self, index_name, config, days_back=1):
        """Find new files to process"""
        new_files = []
        
        # Extract from zip if needed
        extracted_files = self.extract_files_from_zip(index_name, config)
        
        # Find CSV files
        pattern = os.path.join(config['data_dir'], config['file_pattern'])
        all_files = glob.glob(pattern)
        
        # Add extracted files
        all_files.extend(extracted_files)
        
        # Filter files modified within last N days
        cutoff_time = datetime.now() - timedelta(days=days_back)
        
        for file_path in all_files:
            if not os.path.exists(file_path):
                continue
                
            # Skip sorted files (already processed)
            if '_sorted.csv' in file_path:
                continue
            
            # Check modification time
            mtime = datetime.fromtimestamp(os.path.getmtime(file_path))
            if mtime > cutoff_time:
                new_files.append(file_path)
                logger.info(f"Found new file: {file_path}")
        
        return new_files
    
    def load_file_to_db(self, index_name, table_name, file_path):
        """Load a single CSV file to database"""
        logger.info(f"Loading {file_path} to {table_name}")
        
        try:
            # Check file size
            file_size = os.path.getsize(file_path) / (1024 * 1024)  # MB
            logger.info(f"File size: {file_size:.2f} MB")
            
            # For NIFTY data, use the optimized loader
            if index_name == 'NIFTY' and os.path.exists('/srv/samba/shared/load_all_nifty_data_optimized.py'):
                import subprocess
                result = subprocess.run([
                    'python3', '/srv/samba/shared/load_all_nifty_data_optimized.py',
                    '--data-dir', os.path.dirname(file_path),
                    '--pattern', os.path.basename(file_path)
                ], capture_output=True, text=True)
                
                if result.returncode == 0:
                    logger.info(f"Successfully loaded {file_path}")
                    # Parse rows loaded from output
                    for line in result.stdout.split('\n'):
                        if 'Total rows loaded:' in line:
                            rows = int(line.split(':')[1].strip().replace(',', ''))
                            return rows
                    return 0
                else:
                    raise Exception(result.stderr)
            
            # For other indices, use direct COPY FROM
            cursor = self.conn.cursor()
            
            # Use COPY FROM for fast loading
            copy_sql = f"""
            COPY {table_name} FROM '{file_path}' 
            WITH (header='true', delimiter=',', quoted='true')
            """
            
            cursor.execute(copy_sql)
            self.conn.commit()
            
            # Get row count
            cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
            row_count = cursor.fetchone()[0]
            cursor.close()
            
            logger.info(f"Loaded {row_count:,} rows from {file_path}")
            return row_count
            
        except Exception as e:
            logger.error(f"Failed to load {file_path}: {e}")
            self.stats['errors'].append(f"Failed to load {file_path}: {str(e)}")
            return 0
    
    def process_index(self, index_name):
        """Process ETL for a single index"""
        config = self.config.INDEX_CONFIG.get(index_name, {})
        
        if not config.get('active', False):
            logger.info(f"Skipping inactive index: {index_name}")
            return
        
        logger.info(f"Processing {index_name}")
        
        stats = {
            'files_processed': 0,
            'rows_loaded': 0,
            'start_time': datetime.now(),
            'errors': []
        }
        
        try:
            # Create table if needed
            table_name = config['table_name']
            if not self.create_table_if_not_exists(index_name, table_name):
                stats['errors'].append("Failed to create table")
                self.stats['indices_processed'][index_name] = stats
                return
            
            # Find new files
            new_files = self.find_new_files(index_name, config)
            
            if not new_files:
                logger.info(f"No new files found for {index_name}")
                stats['status'] = 'No new data'
            else:
                # Process each file
                for file_path in new_files:
                    rows = self.load_file_to_db(index_name, table_name, file_path)
                    if rows > 0:
                        stats['files_processed'] += 1
                        stats['rows_loaded'] += rows
                        self.stats['total_rows_loaded'] += rows
                
                stats['status'] = 'Success'
            
            # Verify data integrity
            self.verify_data_integrity(index_name, table_name, stats)
            
        except Exception as e:
            logger.error(f"Error processing {index_name}: {e}")
            stats['errors'].append(str(e))
            stats['status'] = 'Failed'
        
        stats['end_time'] = datetime.now()
        stats['duration'] = (stats['end_time'] - stats['start_time']).total_seconds()
        
        self.stats['indices_processed'][index_name] = stats
    
    def verify_data_integrity(self, index_name, table_name, stats):
        """Verify loaded data integrity"""
        try:
            cursor = self.conn.cursor()
            
            # Check today's data
            cursor.execute(f"""
                SELECT COUNT(*) as count,
                       MIN(trade_time) as first_trade,
                       MAX(trade_time) as last_trade,
                       COUNT(DISTINCT strike) as unique_strikes,
                       COUNT(DISTINCT expiry_date) as unique_expiries
                FROM {table_name}
                WHERE trade_date = CURRENT_DATE
            """)
            
            result = cursor.fetchone()
            
            if result and result[0] > 0:
                stats['verification'] = {
                    'today_records': result[0],
                    'first_trade': result[1],
                    'last_trade': result[2],
                    'unique_strikes': result[3],
                    'unique_expiries': result[4]
                }
                logger.info(f"{index_name} verification - Today's records: {result[0]:,}")
            
            cursor.close()
            
        except Exception as e:
            logger.error(f"Verification failed for {index_name}: {e}")
            stats['errors'].append(f"Verification failed: {str(e)}")
    
    def send_notification(self, subject, body):
        """Send email notification"""
        if not self.config.EMAIL_CONFIG.get('enabled', False):
            return
        
        try:
            msg = MIMEMultipart()
            msg['From'] = self.config.EMAIL_CONFIG['from_address']
            msg['To'] = ', '.join(self.config.EMAIL_CONFIG['to_addresses'])
            msg['Subject'] = subject
            
            msg.attach(MIMEText(body, 'plain'))
            
            server = smtplib.SMTP(
                self.config.EMAIL_CONFIG['smtp_server'],
                self.config.EMAIL_CONFIG['smtp_port']
            )
            server.starttls()
            server.login(
                self.config.EMAIL_CONFIG['username'],
                self.config.EMAIL_CONFIG['password']
            )
            
            text = msg.as_string()
            server.sendmail(
                self.config.EMAIL_CONFIG['from_address'],
                self.config.EMAIL_CONFIG['to_addresses'],
                text
            )
            server.quit()
            
            logger.info("Notification sent successfully")
            
        except Exception as e:
            logger.error(f"Failed to send notification: {e}")
    
    def generate_report(self):
        """Generate ETL summary report"""
        duration = (datetime.now() - self.stats['start_time']).total_seconds()
        
        report = f"""
Enhanced Daily ETL Report
========================
Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
Duration: {duration:.2f} seconds
Total Rows Loaded: {self.stats['total_rows_loaded']:,}

Index Summary:
--------------
"""
        
        for index, index_stats in self.stats['indices_processed'].items():
            report += f"\n{index}:"
            report += f"\n  Status: {index_stats.get('status', 'Unknown')}"
            report += f"\n  Files Processed: {index_stats.get('files_processed', 0)}"
            report += f"\n  Rows Loaded: {index_stats.get('rows_loaded', 0):,}"
            
            if 'verification' in index_stats:
                v = index_stats['verification']
                report += f"\n  Today's Records: {v.get('today_records', 0):,}"
                report += f"\n  Unique Strikes: {v.get('unique_strikes', 0)}"
                report += f"\n  Unique Expiries: {v.get('unique_expiries', 0)}"
            
            if index_stats.get('errors'):
                report += f"\n  Errors: {', '.join(index_stats['errors'])}"
        
        if self.stats['errors']:
            report += "\n\nGlobal Errors:\n"
            for error in self.stats['errors']:
                report += f"  - {error}\n"
        
        if self.stats['warnings']:
            report += "\n\nWarnings:\n"
            for warning in self.stats['warnings']:
                report += f"  - {warning}\n"
        
        return report
    
    def run(self):
        """Main ETL execution"""
        logger.info("Starting Enhanced Daily ETL Process")
        
        # Connect to database
        if not self.connect_db():
            logger.error("Failed to connect to database. Exiting.")
            return False
        
        try:
            # Process each index
            for index_name in self.config.INDEX_CONFIG.keys():
                self.process_index(index_name)
            
            # Generate report
            report = self.generate_report()
            logger.info("\n" + report)
            
            # Save report to file
            report_file = os.path.join(LOG_DIR, f'etl_report_{today}.txt')
            with open(report_file, 'w') as f:
                f.write(report)
            
            # Send notification
            if self.stats['errors']:
                subject = f"ETL Failed - {datetime.now().strftime('%Y-%m-%d')}"
            else:
                subject = f"ETL Success - {datetime.now().strftime('%Y-%m-%d')} - {self.stats['total_rows_loaded']:,} rows"
            
            self.send_notification(subject, report)
            
            return True
            
        except Exception as e:
            logger.error(f"ETL process failed: {e}")
            logger.error(traceback.format_exc())
            return False
        
        finally:
            self.close_db()

def main():
    """Main entry point"""
    processor = ETLProcessor()
    success = processor.run()
    
    if success:
        logger.info("ETL completed successfully")
        sys.exit(0)
    else:
        logger.error("ETL failed")
        sys.exit(1)

if __name__ == '__main__':
    main()