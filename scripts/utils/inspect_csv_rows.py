#!/usr/bin/env python3
import os
import sys
import csv
import argparse

def parse_args():
    parser = argparse.ArgumentParser(description='Inspect rows from a CSV file in the nifty dataset')
    parser.add_argument('--file', type=str, required=True, help='CSV file to inspect')
    parser.add_argument('--rows', type=int, default=3, help='Number of rows to inspect')
    parser.add_argument('--skip', type=int, default=0, help='Number of rows to skip (after header)')
    parser.add_argument('--check-value-types', action='store_true', help='Check data types of values')
    parser.add_argument('--validate-dates', action='store_true', help='Validate date formats')
    return parser.parse_args()

def format_csv_row_sql(row, header):
    """Format a CSV row as an SQL INSERT statement"""
    values = []
    for i, val in enumerate(row):
        col_name = header[i]
        # Format based on column type
        if col_name in ['trade_date', 'expiry_date']:
            values.append(f"'{val}'")  # DATE
        elif col_name == 'trade_time':
            values.append(f"'{val}'")  # TIME
        elif col_name in ['index_name', 'expiry_bucket', 'zone_name', 'call_strike_type', 'put_strike_type', 
                          'ce_symbol', 'pe_symbol']:
            values.append(f"'{val}'")  # TEXT
        elif col_name == 'zone_id':
            values.append(val)  # SMALLINT
        elif col_name == 'dte':
            values.append(val)  # INT
        elif col_name in ['ce_volume', 'ce_oi', 'ce_coi', 'pe_volume', 'pe_oi', 'pe_coi', 
                         'future_volume', 'future_oi', 'future_coi']:
            values.append(val)  # BIGINT
        else:
            values.append(val)  # DOUBLE
    
    return f"INSERT INTO nifty_option_chain VALUES ({', '.join(values)});"

def check_value_types(row, header):
    """Check and report potential data type issues in a row"""
    issues = []
    
    for i, val in enumerate(row):
        col_name = header[i]
        
        # Check common issues
        if not val:
            issues.append(f"{col_name}: Empty value")
            continue
            
        # Date columns
        if col_name in ['trade_date', 'expiry_date']:
            parts = val.split('-')
            if len(parts) != 3 or not all(part.isdigit() for part in parts):
                issues.append(f"{col_name}: Invalid date format '{val}'")
                
        # Time column
        elif col_name == 'trade_time':
            parts = val.split(':')
            if len(parts) != 3 or not all(part.isdigit() for part in parts):
                issues.append(f"{col_name}: Invalid time format '{val}'")
                
        # Integer columns
        elif col_name in ['zone_id', 'dte', 'ce_volume', 'ce_oi', 'ce_coi', 'pe_volume', 'pe_oi', 'pe_coi', 
                         'future_volume', 'future_oi', 'future_coi']:
            try:
                float_val = float(val)
                # Check if it's a whole number
                if float_val != int(float_val):
                    issues.append(f"{col_name}: Non-integer value '{val}'")
            except ValueError:
                issues.append(f"{col_name}: Non-numeric value '{val}'")
                
        # Double columns
        elif col_name in ['spot', 'atm_strike', 'strike', 'ce_open', 'ce_high', 'ce_low', 'ce_close', 
                         'ce_iv', 'ce_delta', 'ce_gamma', 'ce_theta', 'ce_vega', 'ce_rho',
                         'pe_open', 'pe_high', 'pe_low', 'pe_close', 'pe_iv', 'pe_delta', 
                         'pe_gamma', 'pe_theta', 'pe_vega', 'pe_rho',
                         'future_open', 'future_high', 'future_low', 'future_close']:
            try:
                float(val)
            except ValueError:
                issues.append(f"{col_name}: Non-numeric value '{val}'")
    
    return issues

def validate_dates(row, header):
    """Check if dates in the row are valid and make sense for the data"""
    issues = []
    
    # Get trade_date and expiry_date
    trade_date = None
    expiry_date = None
    for i, col in enumerate(header):
        if col == 'trade_date':
            trade_date = row[i]
        elif col == 'expiry_date':
            expiry_date = row[i]
    
    if trade_date and expiry_date:
        # Parse dates
        try:
            trade_year, trade_month, trade_day = map(int, trade_date.split('-'))
            expiry_year, expiry_month, expiry_day = map(int, expiry_date.split('-'))
            
            # Check if expiry is before trade date
            if (expiry_year < trade_year or 
                (expiry_year == trade_year and expiry_month < trade_month) or
                (expiry_year == trade_year and expiry_month == trade_month and expiry_day < trade_day)):
                issues.append(f"Expiry date {expiry_date} is before trade date {trade_date}")
                
            # Check if years are reasonable
            if trade_year < 2010 or trade_year > 2030:
                issues.append(f"Trade year {trade_year} seems unreasonable")
            if expiry_year < 2010 or expiry_year > 2030:
                issues.append(f"Expiry year {expiry_year} seems unreasonable")
                
        except ValueError:
            issues.append(f"Could not parse dates: trade_date={trade_date}, expiry_date={expiry_date}")
            
    return issues

def main():
    args = parse_args()
    
    csv_file = args.file
    if not os.path.exists(csv_file):
        print(f"Error: File {csv_file} does not exist")
        return 1
        
    try:
        with open(csv_file, 'r') as f:
            reader = csv.reader(f)
            header = next(reader)  # Read header row
            
            # Skip rows if specified
            for _ in range(args.skip):
                next(reader, None)
                
            # Process the specified number of rows
            for i, row in enumerate(reader):
                if i >= args.rows:
                    break
                    
                print(f"\n=== Row {i+1} ===")
                print(f"Column count: {len(row)}")
                
                # Print each column with its value
                for j, (col, val) in enumerate(zip(header, row)):
                    print(f"{j+1:2d}. {col:20s}: {val}")
                
                # Format as SQL INSERT statement
                print("\n-- SQL INSERT --")
                print(format_csv_row_sql(row, header))
                
                # Check value types if requested
                if args.check_value_types:
                    issues = check_value_types(row, header)
                    if issues:
                        print("\n-- Type Issues --")
                        for issue in issues:
                            print(f"  * {issue}")
                
                # Validate dates if requested
                if args.validate_dates:
                    date_issues = validate_dates(row, header)
                    if date_issues:
                        print("\n-- Date Issues --")
                        for issue in date_issues:
                            print(f"  * {issue}")
    
    except Exception as e:
        print(f"Error processing file: {e}")
        return 1
        
    return 0

if __name__ == "__main__":
    sys.exit(main()) 