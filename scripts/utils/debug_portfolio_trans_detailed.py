#!/usr/bin/env python3
"""
Comprehensive debug script for PORTFOLIO Trans sheet to verify all calculations
"""

import pandas as pd
import numpy as np
from datetime import datetime

def debug_portfolio_trans(file_path):
    """Debug PORTFOLIO Trans sheet in detail"""
    
    print("COMPREHENSIVE PORTFOLIO TRANS SHEET DEBUG")
    print("="*80)
    print(f"File: {file_path}\n")
    
    # Read the sheet
    trans_df = pd.read_excel(file_path, sheet_name='PORTFOLIO Trans')
    
    print(f"Sheet dimensions: {trans_df.shape[0]} rows × {trans_df.shape[1]} columns")
    print(f"Columns ({len(trans_df.columns)}): {list(trans_df.columns)}\n")
    
    # 1. Check for missing values
    print("1. MISSING VALUES CHECK")
    print("-"*60)
    missing_summary = []
    for col in trans_df.columns:
        missing_count = trans_df[col].isna().sum()
        if missing_count > 0:
            missing_summary.append((col, missing_count))
            print(f"   ❌ {col}: {missing_count} missing ({missing_count/len(trans_df)*100:.1f}%)")
    
    if not missing_summary:
        print("   ✓ No missing values found!")
    
    # 2. Data Type Check
    print("\n2. DATA TYPE CHECK")
    print("-"*60)
    expected_types = {
        'Portfolio Name': 'object',
        'Strategy Name': 'object',
        'ID': 'int64',
        'Entry Date': 'datetime64[ns]',
        'Enter On': 'object',
        'Entry Day': 'object',
        'Exit Date': 'datetime64[ns]',
        'Exit at': 'object',
        'Exit Day': 'object',
        'Index': 'object',
        'Expiry': 'datetime64[ns]',
        'Strike': 'int64',
        'CE/PE': 'object',
        'Trade': 'object',
        'Qty': 'int64',
        'Entry at': 'float64',
        'Exit at.1': 'float64',
        'Points': 'float64',
        'Points After Slippage': 'float64',
        'PNL': 'float64',
        'AfterSlippage': 'float64',
        'Taxes': 'float64',
        'Net PNL': 'float64',
        'MaxProfit': 'float64',
        'MaxLoss': 'float64'
    }
    
    type_issues = []
    for col, expected in expected_types.items():
        if col in trans_df.columns:
            actual = str(trans_df[col].dtype)
            if actual != expected:
                type_issues.append((col, expected, actual))
                print(f"   ❌ {col}: Expected {expected}, Got {actual}")
            else:
                print(f"   ✓ {col}: {actual}")
    
    # 3. Calculation Verification
    print("\n3. CALCULATION VERIFICATION")
    print("-"*60)
    
    # Check Points calculation
    print("\n   a) Points Calculation (Exit Price - Entry Price for SELL):")
    for idx, row in trans_df.iterrows():
        if row['Trade'] == 'SELL':
            expected_points = row['Entry at'] - row['Exit at.1']
        else:  # BUY
            expected_points = row['Exit at.1'] - row['Entry at']
        
        actual_points = row['Points']
        diff = abs(expected_points - actual_points)
        
        if diff > 0.01:
            print(f"      Row {idx}: ❌ Points mismatch - Expected: {expected_points:.2f}, Actual: {actual_points:.2f}")
        else:
            if idx == 0:  # Show first row as example
                print(f"      Row {idx}: ✓ Points = {actual_points:.2f} ({row['Trade']}: {row['Entry at']:.2f} → {row['Exit at.1']:.2f})")
    
    # Check Slippage calculation
    print("\n   b) Slippage Calculation (0.1% default):")
    slippage_percent = 0.1
    slippage_issues = []
    
    for idx, row in trans_df.iterrows():
        points = row['Points']
        points_after_slip = row['Points After Slippage']
        
        # For SELL trades
        if row['Trade'] == 'SELL':
            # Entry slippage: reduce entry price
            entry_with_slip = row['Entry at'] * (1 - slippage_percent/100)
            # Exit slippage: increase exit price
            exit_with_slip = row['Exit at.1'] * (1 + slippage_percent/100)
            expected_points_slip = entry_with_slip - exit_with_slip
        else:  # BUY
            # Entry slippage: increase entry price
            entry_with_slip = row['Entry at'] * (1 + slippage_percent/100)
            # Exit slippage: reduce exit price
            exit_with_slip = row['Exit at.1'] * (1 - slippage_percent/100)
            expected_points_slip = exit_with_slip - entry_with_slip
        
        diff = abs(expected_points_slip - points_after_slip)
        if diff > 0.01:
            slippage_issues.append((idx, expected_points_slip, points_after_slip))
            
    if slippage_issues:
        print(f"      ❌ Found {len(slippage_issues)} slippage calculation issues")
        for idx, expected, actual in slippage_issues[:3]:  # Show first 3
            print(f"         Row {idx}: Expected {expected:.2f}, Got {actual:.2f}")
    else:
        print(f"      ✓ All slippage calculations correct")
        print(f"         Sample: Points {trans_df.iloc[0]['Points']:.2f} → {trans_df.iloc[0]['Points After Slippage']:.2f}")
    
    # Check P&L calculations
    print("\n   c) P&L Calculations:")
    pnl_issues = []
    
    for idx, row in trans_df.iterrows():
        # PNL = Points * Qty
        expected_pnl = row['Points'] * row['Qty']
        actual_pnl = row['PNL']
        
        # AfterSlippage = Points After Slippage * Qty
        expected_after_slip = row['Points After Slippage'] * row['Qty']
        actual_after_slip = row['AfterSlippage']
        
        # Net PNL = AfterSlippage - Taxes
        expected_net_pnl = row['AfterSlippage'] - row['Taxes']
        actual_net_pnl = row['Net PNL']
        
        if abs(expected_pnl - actual_pnl) > 0.01:
            pnl_issues.append(f"Row {idx}: PNL mismatch")
        if abs(expected_after_slip - actual_after_slip) > 0.01:
            pnl_issues.append(f"Row {idx}: AfterSlippage mismatch")
        if abs(expected_net_pnl - actual_net_pnl) > 0.01:
            pnl_issues.append(f"Row {idx}: Net PNL mismatch")
    
    if pnl_issues:
        print(f"      ❌ Found {len(pnl_issues)} P&L calculation issues")
        for issue in pnl_issues[:3]:
            print(f"         {issue}")
    else:
        print("      ✓ All P&L calculations correct")
        sample = trans_df.iloc[0]
        print(f"         Sample: Points({sample['Points']:.2f}) × Qty({sample['Qty']}) = PNL({sample['PNL']:.2f})")
        print(f"         Net PNL = AfterSlippage({sample['AfterSlippage']:.2f}) - Taxes({sample['Taxes']:.2f}) = {sample['Net PNL']:.2f}")
    
    # 4. Summary Statistics
    print("\n4. SUMMARY STATISTICS")
    print("-"*60)
    
    total_trades = len(trans_df)
    winning_trades = len(trans_df[trans_df['Net PNL'] > 0])
    losing_trades = len(trans_df[trans_df['Net PNL'] < 0])
    
    print(f"   Total Trades: {total_trades}")
    print(f"   Winning Trades: {winning_trades} ({winning_trades/total_trades*100:.1f}%)")
    print(f"   Losing Trades: {losing_trades} ({losing_trades/total_trades*100:.1f}%)")
    print(f"   Total P&L: ₹{trans_df['Net PNL'].sum():.2f}")
    print(f"   Average P&L per Trade: ₹{trans_df['Net PNL'].mean():.2f}")
    print(f"   Max Win: ₹{trans_df['Net PNL'].max():.2f}")
    print(f"   Max Loss: ₹{trans_df['Net PNL'].min():.2f}")
    
    # 5. Expiry and Date Consistency
    print("\n5. DATE AND EXPIRY CONSISTENCY")
    print("-"*60)
    
    # Check if expiry dates are valid
    if 'Expiry' in trans_df.columns:
        unique_expiries = trans_df['Expiry'].dropna().unique()
        print(f"   Unique Expiry Dates: {len(unique_expiries)}")
        for expiry in unique_expiries[:3]:  # Show first 3
            # Check if it's a Thursday (typical expiry day)
            if hasattr(expiry, 'weekday'):
                weekday = expiry.weekday()
                day_name = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'][weekday]
                print(f"      {expiry.date()} ({day_name}) {'✓ Thursday' if weekday == 3 else '⚠️  Not Thursday'}")
    
    # Check entry/exit date consistency
    date_issues = []
    for idx, row in trans_df.iterrows():
        if pd.notna(row['Entry Date']) and pd.notna(row['Exit Date']):
            if row['Exit Date'] < row['Entry Date']:
                date_issues.append(f"Row {idx}: Exit before Entry!")
    
    if date_issues:
        print(f"\n   ❌ Date consistency issues found:")
        for issue in date_issues:
            print(f"      {issue}")
    else:
        print("\n   ✓ All entry/exit dates are consistent")
    
    # 6. MaxProfit/MaxLoss Check
    print("\n6. MAX PROFIT/LOSS CHECK")
    print("-"*60)
    
    if 'MaxProfit' in trans_df.columns and 'MaxLoss' in trans_df.columns:
        non_zero_profit = trans_df[trans_df['MaxProfit'] != 0]
        non_zero_loss = trans_df[trans_df['MaxLoss'] != 0]
        
        print(f"   MaxProfit non-zero: {len(non_zero_profit)} trades")
        print(f"   MaxLoss non-zero: {len(non_zero_loss)} trades")
        
        if len(non_zero_profit) > 0:
            print(f"   MaxProfit range: {non_zero_profit['MaxProfit'].min():.2f} to {non_zero_profit['MaxProfit'].max():.2f}")
        if len(non_zero_loss) > 0:
            print(f"   MaxLoss range: {non_zero_loss['MaxLoss'].min():.2f} to {non_zero_loss['MaxLoss'].max():.2f}")
    
    # 7. Strike and Option Type Analysis
    print("\n7. STRIKE AND OPTION TYPE ANALYSIS")
    print("-"*60)
    
    strike_summary = trans_df.groupby(['Strike', 'CE/PE']).agg({
        'Net PNL': 'sum',
        'Qty': 'sum'
    }).round(2)
    
    print("   Strike-wise P&L Summary:")
    print(strike_summary.to_string())
    
    # Final Summary
    print("\n" + "="*80)
    print("FINAL VERIFICATION SUMMARY")
    print("="*80)
    
    total_issues = len(missing_summary) + len(type_issues) + len(slippage_issues) + len(pnl_issues) + len(date_issues)
    
    if total_issues == 0:
        print("✅ ALL CALCULATIONS VERIFIED - PORTFOLIO Trans sheet is properly calculated!")
    else:
        print(f"⚠️  Found {total_issues} total issues that need attention")
        if missing_summary:
            print(f"   - {len(missing_summary)} columns with missing values")
        if type_issues:
            print(f"   - {len(type_issues)} data type mismatches")
        if slippage_issues:
            print(f"   - {len(slippage_issues)} slippage calculation issues")
        if pnl_issues:
            print(f"   - {len(pnl_issues)} P&L calculation issues")
        if date_issues:
            print(f"   - {len(date_issues)} date consistency issues")
    
    return trans_df

def compare_with_golden(output_file, golden_file):
    """Compare output with golden file"""
    print("\n\nCOMPARING WITH GOLDEN OUTPUT")
    print("="*80)
    
    output_df = pd.read_excel(output_file, sheet_name='PORTFOLIO Trans')
    golden_df = pd.read_excel(golden_file, sheet_name='PORTFOLIO Trans')
    
    print(f"Output rows: {len(output_df)}, Golden rows: {len(golden_df)}")
    
    # Compare a sample trade
    if len(output_df) > 0 and len(golden_df) > 0:
        print("\nSample Trade Comparison (Row 0):")
        
        compare_cols = ['Strike', 'CE/PE', 'Entry at', 'Exit at.1', 'Points', 
                       'Points After Slippage', 'PNL', 'Net PNL']
        
        for col in compare_cols:
            if col in output_df.columns and col in golden_df.columns:
                out_val = output_df.iloc[0][col]
                gold_val = golden_df.iloc[0][col]
                match = '✓' if str(out_val) == str(gold_val) else '❌'
                print(f"   {match} {col}: Output={out_val}, Golden={gold_val}")

if __name__ == "__main__":
    # Debug the new output
    output_file = "/srv/samba/shared/portfolio_trans_debug_test.xlsx"
    golden_file = "/srv/samba/shared/Nifty_Golden_Ouput.xlsx"
    
    trans_df = debug_portfolio_trans(output_file)
    
    # Compare with golden
    if golden_file:
        compare_with_golden(output_file, golden_file)