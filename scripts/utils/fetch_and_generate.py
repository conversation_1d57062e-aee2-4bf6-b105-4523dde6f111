#!/usr/bin/env python3
import os
import sys
import datetime
import subprocess
import tempfile

def fetch_data_for_date(date, limit=50, offset=0):
    """Fetch data from HeavyDB for a specific date."""
    # Create a temporary SQL file
    with tempfile.NamedTemporaryFile(mode='w', suffix='.sql', delete=False) as f:
        sql_file = f.name
        f.write(f"""
SELECT 
    trade_date, 
    trade_time, 
    expiry_date, 
    strike, 
    underlying_price,
    ce_symbol, ce_open, ce_high, ce_low, ce_close, 
    ce_volume, ce_oi, ce_coi, ce_iv, ce_delta, 
    ce_gamma, ce_theta, ce_vega, ce_rho,
    pe_symbol, pe_open, pe_high, pe_low, pe_close, 
    pe_volume, pe_oi, pe_coi, pe_iv, pe_delta, 
    pe_gamma, pe_theta, pe_vega, pe_rho
FROM nifty_greeks
WHERE trade_date = '{date}'
ORDER BY trade_time, expiry_date, strike
LIMIT {limit} OFFSET {offset};
""")
    
    # Execute the SQL and capture the output
    command = [
        "/opt/heavyai/bin/heavysql", 
        "-s", "127.0.0.1", 
        "--port", "6274", 
        "-u", "admin", 
        "-p", "HyperInteractive", 
        "-d", "heavyai", 
        "-q", sql_file
    ]
    
    result = subprocess.run(command, capture_output=True, text=True)
    
    # Clean up
    os.unlink(sql_file)
    
    # Process the output
    lines = result.stdout.strip().split('\n')
    data = []
    
    # Skip header and footer lines
    for line in lines:
        if ("User" in line or "connected" in line or "disconnected" in line or 
            "heavyai" in line or "trade_date" in line or not line.strip()):
            continue
        
        # Remove the 'heavyai' suffix from each field
        clean_line = line.replace('heavyai', '')
        
        # Split the line into fields
        fields = clean_line.split('|')
        
        if len(fields) >= 28:  # Expect at least 28 fields
            row = {
                "trade_date": fields[0].strip(),
                "trade_time": fields[1].strip(),
                "expiry_date": fields[2].strip(),
                "strike": float(fields[3].strip()),
                "underlying_price": float(fields[4].strip()),
                "ce_symbol": fields[5].strip(),
                "ce_open": float(fields[6].strip()),
                "ce_high": float(fields[7].strip()),
                "ce_low": float(fields[8].strip()),
                "ce_close": float(fields[9].strip()),
                "ce_volume": int(fields[10].strip()),
                "ce_oi": int(fields[11].strip()),
                "ce_coi": int(fields[12].strip()),
                "ce_iv": float(fields[13].strip()),
                "ce_delta": float(fields[14].strip()),
                "ce_gamma": float(fields[15].strip()),
                "ce_theta": float(fields[16].strip()),
                "ce_vega": float(fields[17].strip()),
                "ce_rho": float(fields[18].strip()),
                "pe_symbol": fields[19].strip(),
                "pe_open": float(fields[20].strip()),
                "pe_high": float(fields[21].strip()),
                "pe_low": float(fields[22].strip()),
                "pe_close": float(fields[23].strip()),
                "pe_volume": int(fields[24].strip()),
                "pe_oi": int(fields[25].strip()),
                "pe_coi": int(fields[26].strip()),
                "pe_iv": float(fields[27].strip())
            }
            
            # Handle the rest of the fields if available
            if len(fields) > 28:
                row["pe_delta"] = float(fields[28].strip())
            if len(fields) > 29:
                row["pe_gamma"] = float(fields[29].strip())
            if len(fields) > 30:
                row["pe_theta"] = float(fields[30].strip())
            if len(fields) > 31:
                row["pe_vega"] = float(fields[31].strip())
            if len(fields) > 32:
                row["pe_rho"] = float(fields[32].strip())
            
            data.append(row)
    
    return data

def generate_insert_statement(row):
    """Generate an SQL INSERT statement from a row of data."""
    # [Same as previous generate_insert_statement function]
    fields = [
        "trade_date", "trade_time", "expiry_date", "index_name", "underlying_price",
        "atm_strike", "strike", "dte", "expiry_bucket", "zone_id", "zone_name",
        "call_strike_type", "put_strike_type",
        "ce_symbol", "ce_open", "ce_high", "ce_low", "ce_close",
        "ce_volume", "ce_oi", "ce_coi", "ce_iv", "ce_delta", "ce_gamma", "ce_theta", "ce_vega", "ce_rho",
        "pe_symbol", "pe_open", "pe_high", "pe_low", "pe_close",
        "pe_volume", "pe_oi", "pe_coi", "pe_iv", "pe_delta", "pe_gamma", "pe_theta", "pe_vega", "pe_rho"
    ]
    
    # Extract values from the row
    trade_date = row["trade_date"]
    trade_time = row["trade_time"]
    expiry_date = row["expiry_date"]
    underlying_price = row["underlying_price"]
    strike = row["strike"]
    
    # Calculate derived fields
    if underlying_price < 10000:
        atm_strike = round(underlying_price / 50) * 50
    else:
        atm_strike = round(underlying_price / 100) * 100
    
    # Determine zone
    time_parts = [int(x) for x in trade_time.split(':')]
    time_obj = datetime.time(time_parts[0], time_parts[1], time_parts[2])
    
    if datetime.time(9, 15) <= time_obj <= datetime.time(10, 30):
        zone_id = 1
        zone_name = "OPEN"
    elif datetime.time(10, 30, 1) <= time_obj <= datetime.time(12, 0):
        zone_id = 2
        zone_name = "MID_MORN"
    elif datetime.time(12, 0, 1) <= time_obj <= datetime.time(13, 30):
        zone_id = 3
        zone_name = "LUNCH"
    elif datetime.time(13, 30, 1) <= time_obj <= datetime.time(15, 0):
        zone_id = 4
        zone_name = "AFTERNOON"
    elif datetime.time(15, 0, 1) <= time_obj <= datetime.time(15, 30):
        zone_id = 5
        zone_name = "CLOSE"
    else:
        zone_id = 1
        zone_name = "OPEN"
    
    # Simple DTE calculation (not considering holidays)
    start_date = datetime.datetime.strptime(trade_date, "%Y-%m-%d").date()
    end_date = datetime.datetime.strptime(expiry_date, "%Y-%m-%d").date()
    dte = (end_date - start_date).days
    
    # Moneyness classification (simplified)
    if strike == atm_strike:
        call_strike_type = "ATM"
        put_strike_type = "ATM"
    elif strike < atm_strike:
        call_strike_type = "ITM1"
        put_strike_type = "OTM1"
    else:
        call_strike_type = "OTM1"
        put_strike_type = "ITM1"
    
    # Build values list
    values = [
        f"'{trade_date}'", f"'{trade_time}'", f"'{expiry_date}'", "'NIFTY'", str(underlying_price),
        str(atm_strike), str(strike), str(dte), "'CW'", str(zone_id), f"'{zone_name}'",
        f"'{call_strike_type}'", f"'{put_strike_type}'",
        f"'{row['ce_symbol']}'", str(row['ce_open']), str(row['ce_high']), str(row['ce_low']), str(row['ce_close']),
        str(row['ce_volume']), str(row['ce_oi']), str(row['ce_coi']), str(row['ce_iv']), str(row['ce_delta']),
        str(row['ce_gamma']), str(row['ce_theta']), str(row['ce_vega']), str(row['ce_rho']),
        f"'{row['pe_symbol']}'", str(row['pe_open']), str(row['pe_high']), str(row['pe_low']), str(row['pe_close']),
        str(row['pe_volume']), str(row['pe_oi']), str(row['pe_coi']), str(row['pe_iv']), str(row['pe_delta']),
        str(row['pe_gamma']), str(row['pe_theta']), str(row['pe_vega']), str(row['pe_rho'])
    ]
    
    # Build and return the INSERT statement
    return f"INSERT INTO nifty_option_chain ({', '.join(fields)}) VALUES ({', '.join(values)});"

def process_date(date, batch_size=50):
    """Process a date in batches."""
    # Get total count first
    count_query = f"SELECT COUNT(*) FROM nifty_greeks WHERE trade_date = '{date}';"
    with tempfile.NamedTemporaryFile(mode='w', suffix='.sql', delete=False) as f:
        f.write(count_query)
        count_file = f.name
    
    command = [
        "/opt/heavyai/bin/heavysql", 
        "-s", "127.0.0.1", 
        "--port", "6274", 
        "-u", "admin", 
        "-p", "HyperInteractive", 
        "-d", "heavyai", 
        "-q", count_file
    ]
    
    result = subprocess.run(command, capture_output=True, text=True)
    
    # Clean up
    os.unlink(count_file)
    
    # Extract count
    count = 0
    for line in result.stdout.strip().split('\n'):
        if "EXPR" not in line and "User" not in line and "connected" not in line and line.strip():
            try:
                count = int(line.strip())
                break
            except ValueError:
                pass
    
    print(f"Total records for {date}: {count}")
    
    # Calculate number of batches
    num_batches = (count + batch_size - 1) // batch_size
    
    print(f"Will process {num_batches} batches")
    
    for batch in range(num_batches):
        offset = batch * batch_size
        print(f"Processing batch {batch+1}/{num_batches} (offset: {offset})")
        
        # Fetch data for this batch
        data = fetch_data_for_date(date, batch_size, offset)
        
        if not data:
            print(f"No data returned for batch {batch+1}. Skipping.")
            continue
        
        # Generate INSERT statements
        insert_file = f"inserts_{date}_batch_{batch+1}.sql"
        with open(insert_file, 'w') as f:
            for row in data:
                try:
                    insert_stmt = generate_insert_statement(row)
                    f.write(insert_stmt + "\n")
                except Exception as e:
                    print(f"Error generating INSERT for row: {e}")
                    continue
        
        print(f"Generated {len(data)} INSERT statements in {insert_file}")
        
        # Execute the INSERT statements
        print(f"Executing INSERT statements...")
        command = [
            "/opt/heavyai/bin/heavysql", 
            "-s", "127.0.0.1", 
            "--port", "6274", 
            "-u", "admin", 
            "-p", "HyperInteractive", 
            "-d", "heavyai", 
            "-q", insert_file
        ]
        
        result = subprocess.run(command, capture_output=True, text=True)
        
        if "Error" in result.stdout or "error" in result.stdout.lower():
            print(f"Error executing INSERTs: {result.stdout}")
        else:
            print(f"Successfully executed batch {batch+1}")
        
        # Don't remove the file for debugging purposes
        # os.unlink(insert_file)
    
    print(f"Completed processing for date {date}")

def main():
    """Main function."""
    if len(sys.argv) < 2:
        print("Usage: python fetch_and_generate.py YYYY-MM-DD [BATCH_SIZE]")
        sys.exit(1)
    
    date = sys.argv[1]
    batch_size = int(sys.argv[2]) if len(sys.argv) > 2 else 50
    
    process_date(date, batch_size)

if __name__ == "__main__":
    main() 