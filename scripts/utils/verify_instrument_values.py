#!/usr/bin/env python3
"""
Verify all strategy files have valid Instrument values
"""

import pandas as pd
import os

def verify_instrument_values():
    """Verify all strategy files have valid Instrument values"""
    
    base_dir = '/srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/comprehensive_tests'
    
    print("Verifying Instrument values in all strategy files...")
    print("=" * 80)
    
    valid_instruments = ['call', 'put', 'CE', 'PE', 'CALL', 'PUT']
    all_ok = True
    file_count = 0
    
    for root, dirs, files in os.walk(base_dir):
        for file in files:
            if file.endswith('.xlsx') and 'PORTFOLIO' not in file.upper():
                filepath = os.path.join(root, file)
                file_count += 1
                
                try:
                    # Read the LegParameter sheet
                    df = pd.read_excel(filepath, sheet_name='LegParameter')
                    
                    # Check each instrument value
                    invalid_found = False
                    for idx, row in df.iterrows():
                        instrument = str(row['Instrument']).strip()
                        
                        if instrument.upper() not in [v.upper() for v in valid_instruments]:
                            print(f"\n❌ INVALID in {file}:")
                            print(f"   Row {idx+1}, LegID {row['LegID']}: Instrument = '{instrument}'")
                            all_ok = False
                            invalid_found = True
                        
                        # Check if lowercase (preferred)
                        elif instrument not in ['call', 'put']:
                            print(f"\n⚠️  Non-standard case in {file}:")
                            print(f"   Row {idx+1}, LegID {row['LegID']}: Instrument = '{instrument}' (should be lowercase)")
                    
                    if not invalid_found:
                        # Verify we have the expected distribution
                        instrument_counts = df['Instrument'].value_counts()
                        print(f"✅ {file:40} - {dict(instrument_counts)}")
                        
                except Exception as e:
                    print(f"\n❌ ERROR reading {file}: {e}")
                    all_ok = False
    
    print(f"\n{'='*80}")
    print(f"Total files checked: {file_count}")
    
    if all_ok:
        print("\n✅ ALL FILES VERIFIED - All Instrument values are valid!")
    else:
        print("\n❌ ISSUES FOUND - See above for details")
    
    return all_ok

if __name__ == "__main__":
    verify_instrument_values()