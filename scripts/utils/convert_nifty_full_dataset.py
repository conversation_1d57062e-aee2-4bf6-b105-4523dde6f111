#!/usr/bin/env python3
"""
Convert full Nifty Option Chain dataset with proper futures joins and ATM calculations.

Input:
- All IV_*_nifty_cleaned.csv files in /srv/samba/shared/market_data/nifty/
- Futures data from /srv/samba/shared/market_data/nifty/nifty_future.csv

Output:
- Processed files in /srv/samba/shared/market_data/nifty/oc_with_futures/
  with naming pattern: IV_yyyy_mmm_nifty_futures.csv
"""
import os
import glob
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

# Paths
INPUT_DIR = "/srv/samba/shared/market_data/nifty"
OUTPUT_DIR = "/srv/samba/shared/market_data/nifty/oc_with_futures"
FUTURES_FILE = f"{INPUT_DIR}/nifty_future.csv"

# Ensure output directory exists
os.makedirs(OUTPUT_DIR, exist_ok=True)

# NSE holidays 2019-2026
NSE_HOLIDAYS = {
    # 2023
    "2023-01-26", "2023-03-07", "2023-03-30", "2023-04-04", "2023-04-07", "2023-04-14", 
    "2023-05-01", "2023-06-28", "2023-08-15", "2023-09-19", "2023-10-02", "2023-10-24", 
    "2023-11-14", "2023-11-27", "2023-12-25",
    # 2024
    "2024-01-26", "2024-03-08", "2024-03-25", "2024-03-29", "2024-04-11", "2024-04-17", 
    "2024-05-01", "2024-06-17", "2024-08-15", "2024-10-02", "2024-11-01", "2024-12-25",
    # 2025
    "2025-02-26", "2025-03-14", "2025-03-31", "2025-04-10", "2025-04-14", "2025-04-18", 
    "2025-05-01", "2025-08-15", "2025-08-27", "2025-10-02", "2025-10-21", "2025-10-22", 
    "2025-11-05", "2025-12-25",
    # 2026
    "2026-01-26", "2026-02-15", "2026-03-03", "2026-03-21", "2026-03-27", "2026-03-31", 
    "2026-04-03", "2026-04-14", "2026-05-01", "2026-05-27", "2026-06-26", "2026-08-15", 
    "2026-09-14", "2026-10-02", "2026-10-20", "2026-11-08", "2026-11-09", "2026-11-24", 
    "2026-12-25",
}

def is_trading_day(day):
    """Check if a date is a trading day (not weekend or holiday)."""
    if isinstance(day, str):
        day = datetime.strptime(day, "%Y-%m-%d").date()
    if day.weekday() >= 5:  # Weekend
        return False
    return day.strftime("%Y-%m-%d") not in NSE_HOLIDAYS

def trading_dte(trade_date, expiry_date):
    """Calculate trading days between trade_date and expiry_date (exclusive start, inclusive end)."""
    if isinstance(trade_date, str):
        td = datetime.strptime(trade_date, "%Y-%m-%d").date()
    else:
        td = trade_date
    
    if isinstance(expiry_date, str):
        ed = datetime.strptime(expiry_date, "%Y-%m-%d").date()
    else:
        ed = expiry_date
    
    if td >= ed:
        return 0
    
    # Count trading days after trade_date through expiry_date
    days = 0
    current = td + timedelta(days=1)
    while current <= ed:
        if is_trading_day(current):
            days += 1
        current += timedelta(days=1)
    
    return days

def get_zone_info(time_str):
    """Determine trading zone based on time."""
    if not isinstance(time_str, str):
        time_str = str(time_str)
    
    t = datetime.strptime(time_str, "%H:%M:%S").time()
    
    if t >= datetime.strptime("09:15:00", "%H:%M:%S").time() and t <= datetime.strptime("10:30:00", "%H:%M:%S").time():
        return 1, "OPEN"
    if t <= datetime.strptime("12:00:00", "%H:%M:%S").time():
        return 2, "MID_MORN"
    if t <= datetime.strptime("13:30:00", "%H:%M:%S").time():
        return 3, "LUNCH"
    if t <= datetime.strptime("15:00:00", "%H:%M:%S").time():
        return 4, "AFTERNOON"
    if t <= datetime.strptime("15:30:00", "%H:%M:%S").time():
        return 5, "CLOSE"
    
    return 1, "OPEN"  # Default fallback

def to_full_date(ymd):
    """Convert YYMMDD to YYYY-MM-DD."""
    ymd = str(ymd).zfill(6)
    year = int(ymd[:2])
    year += 2000 if year < 50 else 1900
    return f"{year:04d}-{int(ymd[2:4]):02d}-{int(ymd[4:6]):02d}"

def to_full_time(hm):
    """Normalize time to HH:MM:SS format."""
    if not isinstance(hm, str):
        hm = str(hm)
    
    if ":" in hm:
        parts = hm.split(":")
        if len(parts) == 2:
            return f"{parts[0].zfill(2)}:{parts[1].zfill(2)}:00"
        return hm
    
    hm = hm.zfill(4)
    return f"{hm[:2]}:{hm[2:]}:00"

def find_atm_strike(group):
    """
    Calculate ATM strike for a group using the correct method:
    1. Calculate synthetic future for each valid strike
    2. Find average synthetic future value
    3. Select the strike closest to this average
    """
    valid_rows = group[(group["ce_close"] > 0) & (group["pe_close"] > 0)].copy()
    
    if len(valid_rows) > 0:
        # Calculate synthetic futures for all valid strikes
        valid_rows["synthetic_future"] = valid_rows["strike"] + valid_rows["ce_close"] - valid_rows["pe_close"]
        
        # Calculate average synthetic future
        avg_synthetic = valid_rows["synthetic_future"].mean()
        
        # Find the strike closest to the average synthetic future
        valid_strikes = sorted(valid_rows["strike"].unique())
        atm_strike = min(valid_strikes, key=lambda x: abs(x - avg_synthetic))
        atm_method = "SYNTHETIC_TICK"
    else:
        # Fallback to rounded spot price
        spot_value = group["spot"].iloc[0]
        atm_strike = round(spot_value / 50) * 50
        atm_method = "SPOT_TICK"
    
    # Set ATM strike for all rows in the group
    group["atm_strike"] = atm_strike
    group["atm_method"] = atm_method
    return group

def process_file(input_path, output_path, futures_df):
    """Process a single input file and convert it to the required format."""
    print(f"\n{'='*80}")
    print(f"Processing: {os.path.basename(input_path)}")
    print(f"{'='*80}")
    
    start_time = datetime.now()
    print(f"Started at: {start_time}")
    
    # Load the input file
    df = pd.read_csv(input_path)
    original_rows = len(df)
    print(f"Loaded {original_rows:,} rows from {input_path}")
    
    # Sample of the first 2 records for verification
    print("\nInput sample (first 2 rows):")
    print(df.head(2))
    
    # 1. Normalize key columns
    print("\nNormalizing date and time fields...")
    df["trade_date"] = df["trade_date"].apply(to_full_date)
    df["expiry_date"] = df["expiry_date"].apply(to_full_date)
    df["trade_time"] = df["trade_time"].apply(to_full_time)
    df["index_name"] = "NIFTY"
    df["spot"] = df["underlying_price"]
    
    # 2. Calculate ATM strikes based on synthetic futures
    print("\nCalculating ATM strikes...")
    total_groups = df[["trade_date", "trade_time", "expiry_date"]].drop_duplicates().shape[0]
    print(f"Total date/time/expiry groups to process: {total_groups:,}")
    
    grp_cols = ["trade_date", "trade_time", "expiry_date"]
    df = df.groupby(grp_cols, group_keys=False).apply(find_atm_strike)
    
    print("ATM method distribution:")
    print(df["atm_method"].value_counts())
    
    # Sample ATM strikes from first day
    first_day = df["trade_date"].min()
    sample_grps = df[df["trade_date"] == first_day][grp_cols + ["atm_strike", "atm_method"]].drop_duplicates().head(5)
    print("\nSample ATM strikes (first day, first 5 groups):")
    print(sample_grps)
    
    # 3. Calculate DTE (trading days to expiry)
    print("\nCalculating DTE...")
    df["dte"] = df.apply(lambda r: trading_dte(r["trade_date"], r["expiry_date"]), axis=1)
    
    # 4. Assign expiry buckets
    print("\nAssigning expiry buckets...")
    df["expiry_bucket"] = "OTHER"
    
    # Process each trade date separately
    date_count = df["trade_date"].nunique()
    print(f"Processing {date_count:,} unique trade dates")
    
    for td in df["trade_date"].unique():
        # Get all expiries for this date
        date_mask = df["trade_date"] == td
        expiries = sorted(df.loc[date_mask, "expiry_date"].unique())
        
        # Assign CW/NW based on rank
        if len(expiries) > 0:
            df.loc[date_mask & (df["expiry_date"] == expiries[0]), "expiry_bucket"] = "CW"
        
        if len(expiries) > 1:
            df.loc[date_mask & (df["expiry_date"] == expiries[1]), "expiry_bucket"] = "NW"
        
        # Find monthly expiries (last Thursday of month)
        for exp in expiries:
            exp_dt = datetime.strptime(exp, "%Y-%m-%d").date()
            
            # Check if it's a Thursday and last in month
            if exp_dt.weekday() == 3:  # Thursday
                next_thu = exp_dt + timedelta(days=7)
                if next_thu.month != exp_dt.month:  # Last Thursday
                    exp_mask = date_mask & (df["expiry_date"] == exp)
                    no_bucket = df["expiry_bucket"] == "OTHER"
                    
                    # If not already CW/NW, assign CM or NM
                    cm_assigned = False
                    if any(exp_mask & no_bucket):
                        df.loc[exp_mask & no_bucket, "expiry_bucket"] = "CM"
                        cm_assigned = True
                    
                    # If already assigned CM, use NM for next monthly
                    if cm_assigned:
                        for next_exp in expiries:
                            next_dt = datetime.strptime(next_exp, "%Y-%m-%d").date()
                            if next_dt > exp_dt and next_dt.weekday() == 3:
                                next_mask = date_mask & (df["expiry_date"] == next_exp)
                                df.loc[next_mask & no_bucket, "expiry_bucket"] = "NM"
                                break
    
    # Print expiry bucket counts
    print("\nExpiry bucket distribution:")
    print(df["expiry_bucket"].value_counts())
    
    # Filter data to keep only relevant expiry buckets 
    old_count = len(df)
    df = df[df["expiry_bucket"].isin(["CW", "NW", "CM", "NM"])]
    print(f"Filtered out {old_count - len(df):,} rows with OTHER expiry bucket")
    
    # 5. Assign zones
    print("\nAssigning time zones...")
    df[["zone_id", "zone_name"]] = df["trade_time"].apply(lambda x: pd.Series(get_zone_info(x)))
    
    # 6. Calculate strike classifications
    print("\nCalculating strike classifications...")
    step = 50  # Always use 50-point increment for Nifty
    
    # For calls: ITM if strike < ATM, OTM if strike > ATM
    df["call_strike_type"] = df.apply(
        lambda r: "ATM" if r["strike"] == r["atm_strike"] else (
            f"ITM{int(np.ceil(abs(r['strike'] - r['atm_strike']) / step))}" 
            if r["strike"] < r["atm_strike"] else 
            f"OTM{int(np.ceil(abs(r['strike'] - r['atm_strike']) / step))}"
        ), axis=1
    )
    
    # For puts: ITM if strike > ATM, OTM if strike < ATM
    df["put_strike_type"] = df.apply(
        lambda r: "ATM" if r["strike"] == r["atm_strike"] else (
            f"ITM{int(np.ceil(abs(r['strike'] - r['atm_strike']) / step))}" 
            if r["strike"] > r["atm_strike"] else 
            f"OTM{int(np.ceil(abs(r['strike'] - r['atm_strike']) / step))}"
        ), axis=1
    )
    
    # 7. Futures join
    print("\nJoining futures data...")
    
    # Prepare mapping of source to target column names
    fut_map = {
        "open": "future_open",
        "high": "future_high",
        "low": "future_low",
        "close": "future_close",
        "volume": "future_volume",
        "oi": "future_oi",
        "coi": "future_coi",
    }
    
    # Initialize futures columns with NaN
    for tgt in fut_map.values():
        df[tgt] = np.nan
    
    # Match futures by date and nearest earlier time
    for td in df["trade_date"].unique():
        print(f"  Joining futures for date: {td}")
        
        # Get subset of futures data for this date
        fut_subset = futures_df[futures_df["date"] == td].copy()
        
        if fut_subset.empty:
            print(f"    No futures data found for date {td}")
            continue
        
        # Convert times to integers for comparison
        fut_subset["time_int"] = fut_subset["time"].str.replace(":", "").astype(int)
        fut_subset.sort_values("time_int", inplace=True)
        
        # Get time values as numpy array for faster lookups
        fut_times = fut_subset["time_int"].values
        
        # Get all rows for this date
        date_rows = df[df["trade_date"] == td]
        
        # Count of rows that will be processed
        total_rows = len(date_rows)
        print(f"    Processing {total_rows:,} option rows")
        
        # Process batches of 10,000 rows at a time to show progress
        batch_size = 10000
        processed = 0
        
        for batch_start in range(0, total_rows, batch_size):
            batch_end = min(batch_start + batch_size, total_rows)
            batch_indices = date_rows.index[batch_start:batch_end]
            
            for idx in batch_indices:
                # Get option time as integer
                opt_time_str = df.at[idx, "trade_time"]
                opt_time_int = int(opt_time_str.replace(":", ""))
                
                # Find futures rows with time <= option time
                earlier_idx = np.where(fut_times <= opt_time_int)[0]
                
                if earlier_idx.size > 0:
                    # Get the last (most recent) earlier futures row
                    fut_row = fut_subset.iloc[earlier_idx[-1]]
                else:
                    # If no earlier row exists, use the earliest available
                    fut_row = fut_subset.iloc[0]
                
                # Copy futures values to the option row
                for src, tgt in fut_map.items():
                    df.at[idx, tgt] = fut_row[src] if src in fut_row else np.nan
            
            # Update progress
            processed += len(batch_indices)
            print(f"    Processed {processed:,}/{total_rows:,} rows ({processed/total_rows:.1%})")
    
    # 8. Final output preparation
    print("\nPreparing output columns...")
    
    # Define column order
    column_order = [
        'trade_date', 'trade_time', 'expiry_date', 'index_name', 'spot', 'atm_strike', 'strike', 
        'dte', 'expiry_bucket', 'zone_id', 'zone_name', 'call_strike_type', 'put_strike_type',
        'ce_symbol', 'ce_open', 'ce_high', 'ce_low', 'ce_close', 'ce_volume', 'ce_oi', 'ce_coi', 
        'ce_iv', 'ce_delta', 'ce_gamma', 'ce_theta', 'ce_vega', 'ce_rho',
        'pe_symbol', 'pe_open', 'pe_high', 'pe_low', 'pe_close', 'pe_volume', 'pe_oi', 'pe_coi', 
        'pe_iv', 'pe_delta', 'pe_gamma', 'pe_theta', 'pe_vega', 'pe_rho',
        'future_open', 'future_high', 'future_low', 'future_close', 'future_volume', 'future_oi', 'future_coi'
    ]
    
    # Ensure all columns exist
    for col in column_order:
        if col not in df.columns:
            df[col] = np.nan
    
    # Select columns in the correct order
    output_df = df[column_order]
    
    # Sample of output data
    print("\nOutput sample (first 3 rows):")
    print(output_df.head(3))
    
    # Save to output file
    print(f"\nSaving {len(output_df):,} rows to {output_path}")
    output_df.to_csv(output_path, index=False)
    
    # Calculate and display processing time
    end_time = datetime.now()
    duration = end_time - start_time
    print(f"\nProcessing completed in {duration}")
    print(f"Output file saved to: {output_path}")
    
    # Return summary statistics
    return {
        "input_file": input_path,
        "output_file": output_path,
        "input_rows": original_rows,
        "output_rows": len(output_df),
        "filtered_rows": original_rows - len(output_df),
        "duration": duration
    }

def main():
    """Process all Nifty option chain files."""
    print(f"{'='*80}")
    print(f"NIFTY OPTION CHAIN CONVERTER")
    print(f"{'='*80}")
    print(f"Started at: {datetime.now()}")
    
    # Load futures data once
    print(f"\nLoading futures data from {FUTURES_FILE}")
    fut_df = pd.read_csv(FUTURES_FILE)
    
    # Normalize futures date and time columns
    fut_df["date"] = pd.to_datetime(fut_df["date"]).dt.strftime("%Y-%m-%d")
    fut_df["time"] = fut_df["time"].astype(str)
    fut_df["time"] = fut_df["time"].apply(lambda t: t if len(t.split(":")) == 3 else f"{t}:00")
    
    # Get list of all input files
    input_files = sorted(glob.glob(f"{INPUT_DIR}/IV_*_nifty_cleaned.csv"))
    print(f"\nFound {len(input_files)} input files to process")
    
    # Process each file
    results = []
    for i, input_file in enumerate(input_files, 1):
        # Extract filename and create output filename
        basename = os.path.basename(input_file)
        output_basename = basename.replace("_cleaned.csv", "_futures.csv")
        output_path = os.path.join(OUTPUT_DIR, output_basename)
        
        # Process the file
        print(f"\nFile {i}/{len(input_files)}: {basename}")
        result = process_file(input_file, output_path, fut_df)
        results.append(result)
    
    # Display summary of all files
    print(f"\n{'='*80}")
    print(f"PROCESSING SUMMARY")
    print(f"{'='*80}")
    print(f"Total files processed: {len(results)}")
    
    total_input_rows = sum(r["input_rows"] for r in results)
    total_output_rows = sum(r["output_rows"] for r in results)
    total_filtered = sum(r["filtered_rows"] for r in results)
    
    print(f"Total input rows: {total_input_rows:,}")
    print(f"Total output rows: {total_output_rows:,}")
    print(f"Total filtered rows: {total_filtered:,}")
    
    # Calculate total duration
    total_seconds = sum(r["duration"].total_seconds() for r in results)
    print(f"Total processing time: {total_seconds:.1f} seconds")
    
    print(f"\nCompleted at: {datetime.now()}")
    print(f"{'='*80}")

if __name__ == "__main__":
    main() 