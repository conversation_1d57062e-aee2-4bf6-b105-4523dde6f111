#!/usr/bin/env python3
import os
import sys
import glob
import csv
import subprocess
import time
import signal
from datetime import datetime

# Configuration
CSV_DIR = "/srv/samba/shared/market_data/nifty/oc_with_futures/"
BATCH_SIZE = 10000  # Reduced batch size for debugging
MAX_RETRIES = 3
DEBUG = True

# Timeout handling
class TimeoutError(Exception):
    pass

def timeout_handler(signum, frame):
    raise TimeoutError("SQL command timed out")

def run_sql_command(sql, timeout=300):
    """Run SQL command via heavysql with timeout"""
    if DEBUG:
        print(f"SQL size: {len(sql)} bytes, approx {sql.count('INSERT')} INSERT statements")
    
    # Set timeout handler
    signal.signal(signal.SIGALRM, timeout_handler)
    signal.alarm(timeout)
    
    cmd = ["/opt/heavyai/bin/heavysql", "-s", "127.0.0.1", "--port", "6274", 
           "-u", "admin", "-p", "HyperInteractive", "-d", "heavyai"]
    
    try:
        process = subprocess.Popen(cmd, stdin=subprocess.PIPE, 
                                stdout=subprocess.PIPE, stderr=subprocess.PIPE,
                                text=True)
        
        stdout, stderr = process.communicate(input=sql)
        
        # Clear the alarm
        signal.alarm(0)
        
        if stderr and "Error" in stderr:
            print(f"SQL Error: {stderr}")
            return False
        
        return True
    except TimeoutError:
        print(f"SQL command timed out after {timeout} seconds")
        # Try to kill the process
        try:
            process.kill()
        except:
            pass
        return False
    except Exception as e:
        print(f"Unexpected error: {e}")
        signal.alarm(0)  # Clear the alarm
        return False

def ensure_table_exists():
    """Ensure the target table exists with correct schema"""
    print("Creating table nifty_option_chain_debug...")
    create_table_sql = """
    DROP TABLE IF EXISTS nifty_option_chain_debug;
    CREATE TABLE nifty_option_chain_debug (
        trade_date       DATE,
        trade_time       TIME,
        expiry_date      DATE,
        index_name       TEXT,
        spot             DOUBLE,
        atm_strike       DOUBLE,
        strike           DOUBLE,
        dte              INT,
        expiry_bucket    TEXT,
        zone_id          SMALLINT,
        zone_name        TEXT,
        call_strike_type TEXT,
        put_strike_type  TEXT,
        ce_symbol        TEXT,
        ce_open          DOUBLE,
        ce_high          DOUBLE,
        ce_low           DOUBLE,
        ce_close         DOUBLE,
        ce_volume        BIGINT,
        ce_oi            BIGINT,
        ce_coi           BIGINT,
        ce_iv            DOUBLE,
        ce_delta         DOUBLE,
        ce_gamma         DOUBLE,
        ce_theta         DOUBLE,
        ce_vega          DOUBLE,
        ce_rho           DOUBLE,
        pe_symbol        TEXT,
        pe_open          DOUBLE,
        pe_high          DOUBLE,
        pe_low           DOUBLE,
        pe_close         DOUBLE,
        pe_volume        BIGINT,
        pe_oi            BIGINT,
        pe_coi           BIGINT,
        pe_iv            DOUBLE,
        pe_delta         DOUBLE,
        pe_gamma         DOUBLE,
        pe_theta         DOUBLE,
        pe_vega          DOUBLE,
        pe_rho           DOUBLE,
        future_open      DOUBLE,
        future_high      DOUBLE,
        future_low       DOUBLE,
        future_close     DOUBLE,
        future_volume    BIGINT,
        future_oi        BIGINT,
        future_coi       BIGINT
    );
    """
    
    if run_sql_command(create_table_sql):
        print("Table created successfully")
        return True
    else:
        print("Failed to create table")
        return False

def get_csv_files():
    """Get a list of all CSV files to process"""
    return sorted(glob.glob(os.path.join(CSV_DIR, "*.csv")))

def count_lines(file_path):
    """Count lines in a file"""
    with open(file_path, 'r') as f:
        return sum(1 for _ in f)

def process_csv_file(file_path, batch_size, max_rows=None):
    """Process a single CSV file in batches with optional row limit"""
    file_name = os.path.basename(file_path)
    print(f"\nProcessing {file_name}...")
    
    # Count total lines
    total_lines = count_lines(file_path)
    print(f"Total lines in file: {total_lines}")
    
    # Account for header
    rows_to_process = total_lines - 1
    if max_rows and max_rows < rows_to_process:
        rows_to_process = max_rows
        print(f"Limited to {max_rows} rows for debugging")
    
    print(f"Rows to process: {rows_to_process}")
    
    # Process in batches
    with open(file_path, 'r') as f:
        reader = csv.reader(f)
        header = next(reader)  # Skip header
        print(f"Header has {len(header)} columns: {header}")
        
        batch_rows = []
        total_processed = 0
        total_inserted = 0
        batch_count = 0
        
        for row in reader:
            if max_rows and total_processed >= max_rows:
                break
                
            if len(row) != len(header):
                print(f"Warning: Row {total_processed+1} has {len(row)} columns, expected {len(header)}")
                continue
                
            batch_rows.append(row)
            
            # Process batch when it reaches the batch size
            if len(batch_rows) >= batch_size:
                batch_count += 1
                print(f"Preparing batch {batch_count} with {len(batch_rows)} rows...")
                start_time = time.time()
                success = insert_batch(batch_rows, batch_count, file_name)
                end_time = time.time()
                total_processed += len(batch_rows)
                if success:
                    total_inserted += len(batch_rows)
                
                # Show progress
                progress = (total_processed / rows_to_process) * 100
                print(f"Progress: {progress:.2f}% - Processed {total_processed}/{rows_to_process} rows (Batch {batch_count}, Time: {end_time - start_time:.2f}s)")
                
                # Clear batch
                batch_rows = []
        
        # Process any remaining rows
        if batch_rows:
            batch_count += 1
            print(f"Preparing final batch {batch_count} with {len(batch_rows)} rows...")
            start_time = time.time()
            success = insert_batch(batch_rows, batch_count, file_name)
            end_time = time.time()
            total_processed += len(batch_rows)
            if success:
                total_inserted += len(batch_rows)
            print(f"Final batch: {len(batch_rows)} rows in {end_time - start_time:.2f}s")
    
    print(f"File {file_name} completed. Inserted {total_inserted}/{rows_to_process} rows.")
    return total_inserted

def insert_batch(rows, batch_num, file_name):
    """Insert a batch of rows into the database"""
    print(f"Building INSERT statements for batch {batch_num}...")
    insert_statements = []
    
    for i, row in enumerate(rows):
        # Clean and format values properly
        values = []
        for j, val in enumerate(row):
            try:
                # Handle different data types
                if j in [0, 2]:  # DATE fields (trade_date, expiry_date)
                    values.append(f"'{val}'")
                elif j == 1:  # TIME field (trade_time)
                    values.append(f"'{val}'")
                elif j in [3, 10, 11, 12, 13, 27]:  # TEXT fields
                    values.append(f"'{val}'")
                elif j == 8:  # expiry_bucket TEXT field
                    values.append(f"'{val}'")
                elif val == '':  # Handle empty values
                    values.append("NULL")
                else:
                    values.append(val)
            except Exception as e:
                print(f"Error processing row {i}, column {j}, value '{val}': {e}")
                # Skip this problematic row
                break
        
        if len(values) == len(row):  # Only add if all values were processed
            # Create INSERT statement
            insert_sql = f"INSERT INTO nifty_option_chain_debug VALUES ({', '.join(values)});"
            insert_statements.append(insert_sql)
    
    # Combine all inserts and execute
    all_inserts = "\n".join(insert_statements)
    print(f"Executing {len(insert_statements)} INSERT statements...")
    
    # Retry logic
    for attempt in range(MAX_RETRIES):
        start_exec = time.time()
        success = run_sql_command(all_inserts, timeout=600)  # 10 minute timeout
        end_exec = time.time()
        
        print(f"SQL execution took {end_exec - start_exec:.2f} seconds")
        
        if success:
            print(f"Batch {batch_num} inserted successfully")
            return True
        else:
            print(f"Batch {batch_num} failed on attempt {attempt+1}/{MAX_RETRIES}. Retrying...")
            time.sleep(2)  # Wait before retry
    
    print(f"Batch {batch_num} failed after {MAX_RETRIES} attempts.")
    return False

def verify_loaded_data():
    """Verify the data was loaded correctly"""
    # Get total count
    print("Verifying loaded data...")
    count_sql = "SELECT COUNT(*) FROM nifty_option_chain_debug;"
    cmd = ["/opt/heavyai/bin/heavysql", "-s", "127.0.0.1", "--port", "6274", 
            "-u", "admin", "-p", "HyperInteractive", "-d", "heavyai", "-q", count_sql]
    
    try:
        process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
        stdout, stderr = process.communicate()
        if stderr and "Error" in stderr:
            print(f"Error verifying data: {stderr}")
            return
        
        # Parse the count
        print(f"Raw output: {stdout}")
        count_line = [line for line in stdout.split('\n') if line and not line.startswith('User') and not line.startswith('Database')]
        if count_line:
            count = count_line[-1].strip()
            print(f"\nVerification: Total rows in nifty_option_chain_debug: {count}")
    except Exception as e:
        print(f"Error during verification: {e}")

def main():
    print(f"Debug load script starting with batch size of {BATCH_SIZE}")
    
    # Ensure table exists
    if not ensure_table_exists():
        sys.exit(1)
    
    # Process just the first file with reduced row count for testing
    file_path = os.path.join(CSV_DIR, "IV_2023_jan_nifty_futures.csv")
    max_rows = 30000  # Process only 30k rows for quick testing
    
    start_time = datetime.now()
    print(f"Starting data load at {start_time}")
    
    inserted = process_csv_file(file_path, BATCH_SIZE, max_rows=max_rows)
    
    # Verify data
    verify_loaded_data()
    
    end_time = datetime.now()
    duration = end_time - start_time
    print(f"\nDebug load completed at {end_time}")
    print(f"Total duration: {duration}")
    print(f"Total rows inserted: {inserted}")

if __name__ == "__main__":
    main() 