#!/usr/bin/env python3
"""
Fast loader for nifty option chain data that handles the actual CSV structure
"""
import os
import csv
import time
import glob
from datetime import datetime
from multiprocessing import Pool, cpu_count
import subprocess

def get_row_count():
    """Get current row count"""
    cmd = """echo "SELECT COUNT(*) FROM nifty_option_chain;" | /opt/heavyai/bin/heavysql -p HyperInteractive -u admin -d heavyai --quiet"""
    result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
    if result.returncode == 0:
        try:
            return int(result.stdout.strip())
        except:
            return 0
    return 0

def transform_row_to_options(row):
    """Transform a single CSV row into call and put option records"""
    records = []
    
    # Common fields
    trade_date = row['trade_date']
    trade_time = row['trade_time']
    expiry_date = row['expiry_date']
    index_name = row['index_name']
    spot = float(row['spot'])
    atm_strike = int(row['atm_strike'])
    strike = int(row['strike'])
    dte = int(row['dte'])
    
    # Calculate strike difference
    strike_diff = abs(strike - atm_strike)
    
    # Call option
    if row.get('ce_close') and row['ce_close'] != '' and row['ce_close'] != 'None':
        call_record = {
            'trade_date': trade_date,
            'trade_time': trade_time,
            'expiry_date': expiry_date,
            'index_name': index_name,
            'spot': spot,
            'atm_strike': atm_strike,
            'strike': strike,
            'dte': dte,
            'opt_type': 'CE',
            'ltp': float(row['ce_close'] or 0),
            'oi': int(float(row['ce_oi'] or 0)),
            'volume': int(float(row['ce_volume'] or 0)),
            'bid': 0.0,  # Not in source data
            'ask': 0.0,  # Not in source data
            'strike_diff': strike_diff
        }
        records.append(call_record)
    
    # Put option
    if row.get('pe_close') and row['pe_close'] != '' and row['pe_close'] != 'None':
        put_record = {
            'trade_date': trade_date,
            'trade_time': trade_time,
            'expiry_date': expiry_date,
            'index_name': index_name,
            'spot': spot,
            'atm_strike': atm_strike,
            'strike': strike,
            'dte': dte,
            'opt_type': 'PE',
            'ltp': float(row['pe_close'] or 0),
            'oi': int(float(row['pe_oi'] or 0)),
            'volume': int(float(row['pe_volume'] or 0)),
            'bid': 0.0,  # Not in source data
            'ask': 0.0,  # Not in source data
            'strike_diff': strike_diff
        }
        records.append(put_record)
    
    return records

def load_csv_batch(args):
    """Load a batch of CSV data"""
    csv_file, start_idx, batch_size = args
    
    all_records = []
    with open(csv_file, 'r') as f:
        reader = csv.DictReader(f)
        for i, row in enumerate(reader):
            if i < start_idx:
                continue
            if i >= start_idx + batch_size:
                break
            records = transform_row_to_options(row)
            all_records.extend(records)
    
    if not all_records:
        return 0
    
    # Build single INSERT with multiple VALUES
    values_list = []
    for rec in all_records:
        values = f"""(
            '{rec['trade_date']}',
            '{rec['trade_time']}',
            '{rec['expiry_date']}',
            '{rec['index_name']}',
            {rec['spot']},
            {rec['atm_strike']},
            {rec['strike']},
            {rec['dte']},
            '{rec['opt_type']}',
            {rec['ltp']},
            {rec['oi']},
            {rec['volume']},
            {rec['bid']},
            {rec['ask']},
            {rec['strike_diff']}
        )"""
        values_list.append(values)
    
    sql = f"""INSERT INTO nifty_option_chain 
    (trade_date, trade_time, expiry_date, index_name, spot, atm_strike, 
     strike, dte, opt_type, ltp, oi, volume, bid, ask, strike_diff)
    VALUES {','.join(values_list)};"""
    
    # Write to temp file and execute
    temp_file = f"/tmp/batch_{os.getpid()}_{time.time()}.sql"
    with open(temp_file, 'w') as f:
        f.write(sql)
    
    try:
        cmd = f'/opt/heavyai/bin/heavysql -p HyperInteractive -u admin -d heavyai < {temp_file} 2>&1'
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
        if result.returncode == 0:
            return len(all_records)
        else:
            print(f"\nError in batch: {result.stderr}")
            # Try smaller batch on error
            if len(all_records) > 100:
                return 0
            return 0
    finally:
        if os.path.exists(temp_file):
            os.remove(temp_file)

def process_file(csv_file):
    """Process a single CSV file in batches"""
    print(f"\nProcessing: {os.path.basename(csv_file)}")
    
    # Count rows in file
    with open(csv_file, 'r') as f:
        total_rows = sum(1 for line in f) - 1  # Subtract header
    
    print(f"Total rows in file: {total_rows:,}")
    
    # Process in batches
    batch_size = 2000  # Smaller batches for stability
    batches = []
    
    for start_idx in range(0, total_rows, batch_size):
        batches.append((csv_file, start_idx, batch_size))
    
    # Process batches in parallel
    loaded = 0
    start_time = time.time()
    
    with Pool(processes=min(4, cpu_count())) as pool:
        for i, result in enumerate(pool.imap(load_csv_batch, batches)):
            loaded += result
            elapsed = time.time() - start_time
            if elapsed > 0:
                speed = loaded / elapsed
                progress = (i + 1) / len(batches) * 100
                print(f"  Progress: {progress:.1f}%, Loaded: {loaded:,} records, Speed: {speed:.0f} rec/sec", end='\r')
    
    elapsed = time.time() - start_time
    print(f"\n  Completed: {loaded:,} records in {elapsed:.1f}s ({loaded/elapsed:.0f} rec/sec)")
    return loaded

def main():
    data_dir = "/srv/samba/shared/market_data/nifty/oc_with_futures"
    
    print("=== Nifty Option Chain Fast Loader ===")
    print(f"Starting at: {datetime.now()}")
    
    initial_count = get_row_count()
    print(f"Initial row count: {initial_count:,}")
    
    # Get all CSV files
    csv_files = sorted(glob.glob(os.path.join(data_dir, "*.csv")))
    print(f"Found {len(csv_files)} CSV files")
    
    # Process each file
    total_loaded = 0
    start_time = time.time()
    
    for i, csv_file in enumerate(csv_files):
        print(f"\n[{i+1}/{len(csv_files)}] {os.path.basename(csv_file)}")
        file_start = time.time()
        loaded = process_file(csv_file)
        file_time = time.time() - file_start
        total_loaded += loaded
        
        # Show overall progress
        elapsed = time.time() - start_time
        overall_speed = total_loaded / elapsed if elapsed > 0 else 0
        
        # Estimate based on ~2 records per CSV row (call + put)
        estimated_total = total_loaded / (i + 1) * len(csv_files)
        eta_seconds = (estimated_total - total_loaded) / overall_speed if overall_speed > 0 else 0
        eta_minutes = eta_seconds / 60
        
        print(f"  Total loaded: {total_loaded:,}, Overall speed: {overall_speed:.0f} rec/sec")
        print(f"  ETA: {eta_minutes:.1f} minutes")
    
    final_count = get_row_count()
    total_time = time.time() - start_time
    
    print("\n=== Summary ===")
    print(f"Files processed: {len(csv_files)}")
    print(f"Records loaded: {total_loaded:,}")
    print(f"Final row count: {final_count:,}")
    print(f"Total time: {total_time/60:.1f} minutes")
    print(f"Average speed: {total_loaded/total_time:.0f} rec/sec")
    print(f"Completed at: {datetime.now()}")

if __name__ == "__main__":
    main()