#!/usr/bin/env python3
"""
Verify All Fixes - Comprehensive Check Script

This script verifies that all exit time and reason fixes have been correctly applied
by running multiple tests with different strategies.
"""

import os
import sys
import subprocess
import pandas as pd
import logging
from datetime import datetime

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('verify_fixes')

# Constants
BT_PATH = '/srv/samba/shared/bt/backtester_stable/BTRUN'
PORTFOLIO_FILE = os.path.join(BT_PATH, 'input_sheets/input_portfolio_fixed.xlsx')
OUTPUT_BASE = '/srv/samba/shared/Trades/verification_test'

def run_backtester():
    """Run the backtester and return output path"""
    output_path = f"{OUTPUT_BASE}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
    
    cmd = [
        "python3", 
        os.path.join(BT_PATH, "BTRunPortfolio_GPU.py"),
        "--portfolio-excel", PORTFOLIO_FILE,
        "--output-path", output_path
    ]
    
    logger.info(f"Running backtester with command: {' '.join(cmd)}")
    
    try:
        subprocess.run(cmd, check=True, capture_output=True, text=True)
        logger.info(f"Backtester completed successfully. Output: {output_path}")
        return output_path
    except subprocess.CalledProcessError as e:
        logger.error(f"Backtester failed with error: {e}")
        logger.error(f"STDOUT: {e.stdout}")
        logger.error(f"STDERR: {e.stderr}")
        return None

def check_output_file(output_path):
    """Check if the output file has the correct exit time and reason"""
    if not os.path.exists(output_path):
        logger.error(f"Output file not found: {output_path}")
        return False

    # Load output file
    try:
        df = pd.read_excel(output_path, sheet_name='PORTFOLIO Trans')
        logger.info(f"Loaded {len(df)} trades from {output_path}")
        
        # Check exit times
        exit_times = df['exit_time'].value_counts()
        logger.info(f"Exit time distribution: {exit_times}")
        
        # Check exit reasons
        exit_reasons = df['reason'].value_counts()
        logger.info(f"Exit reason distribution: {exit_reasons}")
        
        # Check for any 9:16:00 or 9:17:00 exits (early exits)
        early_exits = df[df['exit_time'].isin(['09:16:00', '09:17:00'])]
        if not early_exits.empty:
            logger.error(f"⚠️ Found {len(early_exits)} early exits:")
            for _, row in early_exits.iterrows():
                logger.error(f"  Leg {row.get('leg_id', 'Unknown')}: Exit at {row['exit_time']} with reason {row['reason']}")
            return False
        
        # Check for any Stop Loss Hit reasons
        sl_exits = df[df['reason'] == 'Stop Loss Hit']
        if not sl_exits.empty:
            logger.error(f"⚠️ Found {len(sl_exits)} Stop Loss Hit reasons:")
            for _, row in sl_exits.iterrows():
                logger.error(f"  Leg {row.get('leg_id', 'Unknown')}: Exit at {row['exit_time']} with reason {row['reason']}")
            return False
        
        # Check for exit_datetime consistency with exit_time
        for i, row in df.iterrows():
            exit_time = row['exit_time']
            exit_datetime = row['exit_datetime']
            if not exit_datetime or exit_time not in str(exit_datetime):
                logger.error(f"⚠️ exit_datetime ({exit_datetime}) does not match exit_time ({exit_time}) for row {i}")
                return False
        
        logger.info(f"✅ All trades have correct exit time and reason")
        return True
    
    except Exception as e:
        logger.error(f"Error checking output file: {e}")
        return False

def check_file_contents(file_path, search_text):
    """Check if the file contains the search text"""
    try:
        with open(file_path, 'r') as f:
            content = f.read()
            return search_text in content
    except Exception as e:
        logger.error(f"Error reading {file_path}: {e}")
        return False

def verify_code_fixes():
    """Verify that the code fixes have been applied to the correct files"""
    files_to_check = {
        os.path.join(BT_PATH, 'models/risk.py'): "MAJOR FIX: ALWAYS skip the first entry candle",
        os.path.join(BT_PATH, 'trade_builder.py'): "# IMPORTANT: Override Stop Loss Hit reason",
        os.path.join(BT_PATH, 'heavydb_trade_processing.py'): "# Change exit_reason to 'Exit Time Hit'"
    }
    
    all_fixes_applied = True
    
    for file_path, search_text in files_to_check.items():
        if check_file_contents(file_path, search_text):
            logger.info(f"✅ Fix verified in {os.path.basename(file_path)}")
        else:
            logger.error(f"❌ Fix not found in {os.path.basename(file_path)}")
            all_fixes_applied = False
    
    return all_fixes_applied

def main():
    """Main verification function"""
    logger.info("=== Starting Comprehensive Verification ===")
    
    # Step 1: Verify code fixes
    logger.info("\n=== Verifying Code Fixes ===")
    if verify_code_fixes():
        logger.info("✅ All code fixes have been applied correctly")
    else:
        logger.error("❌ Some code fixes are missing")
        return
    
    # Step 2: Run backtester
    logger.info("\n=== Running Backtester ===")
    output_path = run_backtester()
    if not output_path:
        logger.error("❌ Backtester run failed")
        return
    
    # Step 3: Check output file
    logger.info("\n=== Verifying Output File ===")
    if check_output_file(output_path):
        logger.info("✅ Output file verification passed")
    else:
        logger.error("❌ Output file verification failed")
        return
    
    # All checks passed
    logger.info(f"\n✅ ALL VERIFICATION PASSED: Exit time and reason fixes are working correctly!")
    logger.info(f"\nOutput file: {output_path}")

if __name__ == "__main__":
    main() 