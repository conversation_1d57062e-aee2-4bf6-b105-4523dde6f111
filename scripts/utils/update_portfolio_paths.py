#!/usr/bin/env python3
"""
Update strategy file paths in portfolio Excel files
"""

import pandas as pd
import os

base_path = '/srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/tv/'

# Portfolio files to update
portfolio_updates = [
    ('input_portfolio_long.xlsx', 'input_tbs_long.xlsx'),
    ('input_portfolio_short.xlsx', 'input_tbs_short.xlsx')
]

for portfolio_file, strategy_file in portfolio_updates:
    full_path = base_path + portfolio_file
    print(f"\nUpdating {portfolio_file}:")
    
    # Read all sheets
    all_sheets = pd.read_excel(full_path, sheet_name=None)
    
    # Update StrategySetting sheet
    if 'StrategySetting' in all_sheets:
        df = all_sheets['StrategySetting']
        
        # Show original
        print(f"  Original: {df.iloc[0]['StrategyExcelFilePath']}")
        
        # Update path
        df.loc[0, 'StrategyExcelFilePath'] = strategy_file
        
        # Show updated
        print(f"  Updated: {df.iloc[0]['StrategyExcelFilePath']}")
        
        # Save back
        with pd.ExcelWriter(full_path, engine='openpyxl') as writer:
            for sheet_name, sheet_df in all_sheets.items():
                sheet_df.to_excel(writer, sheet_name=sheet_name, index=False)
        
        print("  ✅ Saved!")

print("\n✅ All portfolio files updated with correct strategy paths!")