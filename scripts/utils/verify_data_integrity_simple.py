#!/usr/bin/env python3
"""
Simplified data integrity verification for multi-index option chain data
"""
import pandas as pd
import numpy as np
from heavydb import connect
from datetime import datetime

def main():
    print("="*80)
    print("MULTI-INDEX DATA INTEGRITY VERIFICATION")
    print("="*80)
    
    # Connect to HeavyDB
    conn = connect(
        host='localhost',
        port=6274,
        user='admin',
        password='HyperInteractive',
        dbname='heavyai'
    )
    cursor = conn.cursor()
    
    # 1. Basic Statistics
    print("\n1. BASIC STATISTICS")
    print("-"*80)
    cursor.execute("""
        SELECT index_name, 
               COUNT(*) as total_rows,
               COUNT(DISTINCT trade_date) as trading_days,
               COUNT(DISTINCT strike) as unique_strikes,
               COUNT(DISTINCT expiry_date) as unique_expiries,
               MIN(trade_date) as start_date,
               MAX(trade_date) as end_date
        FROM nifty_option_chain
        GROUP BY index_name
        ORDER BY index_name
    """)
    
    results = cursor.fetchall()
    print(f"{'Index':<12} {'Rows':>12} {'Days':>6} {'Strikes':>8} {'Expiries':>9} {'Date Range'}")
    print("-"*80)
    for row in results:
        print(f"{row[0]:<12} {row[1]:>12,} {row[2]:>6} {row[3]:>8} {row[4]:>9} {row[5]} to {row[6]}")
    
    # 2. Expiry Type Distribution
    print("\n\n2. EXPIRY TYPE DISTRIBUTION")
    print("-"*80)
    cursor.execute("""
        SELECT index_name, expiry_bucket, COUNT(*) as cnt,
               MIN(dte) as min_dte, MAX(dte) as max_dte
        FROM nifty_option_chain
        GROUP BY index_name, expiry_bucket
        ORDER BY index_name, expiry_bucket
    """)
    
    results = cursor.fetchall()
    current_index = None
    for row in results:
        if row[0] != current_index:
            current_index = row[0]
            print(f"\n{current_index}:")
            print(f"  {'Type':<4} {'Count':>10} {'DTE Range'}")
        print(f"  {row[1]:<4} {row[2]:>10,} {row[3]:>3} - {row[4]:<3} days")
    
    # 3. Strike Increment Analysis
    print("\n\n3. STRIKE INCREMENT ANALYSIS")
    print("-"*80)
    
    for index_name in ['NIFTY', 'BANKNIFTY', 'MIDCAPNIFTY', 'SENSEX']:
        cursor.execute(f"""
            SELECT strike
            FROM nifty_option_chain
            WHERE index_name = '{index_name}'
            AND trade_date = (SELECT MAX(trade_date) FROM nifty_option_chain WHERE index_name = '{index_name}')
            AND trade_time = '09:15:00'
            GROUP BY strike
            ORDER BY strike
            LIMIT 10
        """)
        
        strikes = [row[0] for row in cursor.fetchall()]
        if len(strikes) > 1:
            increments = [strikes[i+1] - strikes[i] for i in range(len(strikes)-1)]
            print(f"\n{index_name}:")
            print(f"  Sample strikes: {strikes[:5]}")
            print(f"  Increments: {list(set(increments))}")
    
    # 4. Zone Distribution
    print("\n\n4. ZONE DISTRIBUTION")
    print("-"*80)
    cursor.execute("""
        SELECT index_name, zone_name, COUNT(*) as cnt,
               ROUND(COUNT(*) * 100.0 / SUM(COUNT(*)) OVER (PARTITION BY index_name), 2) as pct
        FROM nifty_option_chain
        GROUP BY index_name, zone_name
        ORDER BY index_name, 
                 CASE zone_name 
                    WHEN 'ATM' THEN 1 
                    WHEN 'OPEN' THEN 2 
                    WHEN 'NEAR' THEN 3 
                    WHEN 'MID' THEN 4 
                    WHEN 'FAR' THEN 5 
                 END
    """)
    
    results = cursor.fetchall()
    current_index = None
    for row in results:
        if row[0] != current_index:
            current_index = row[0]
            print(f"\n{current_index}:")
        print(f"  {row[1]:<5}: {row[2]:>10,} ({row[3]:>5.1f}%)")
    
    # 5. Data Quality Checks
    print("\n\n5. DATA QUALITY CHECKS")
    print("-"*80)
    
    quality_checks = [
        ("NULL spot prices", "spot IS NULL"),
        ("Zero spot prices", "spot = 0"),
        ("NULL ATM strikes", "atm_strike IS NULL"),
        ("Negative strikes", "strike < 0"),
        ("NULL expiry buckets", "expiry_bucket IS NULL"),
        ("Negative DTEs", "dte < 0")
    ]
    
    has_issues = False
    for check_name, condition in quality_checks:
        cursor.execute(f"""
            SELECT index_name, COUNT(*) as cnt
            FROM nifty_option_chain
            WHERE {condition}
            GROUP BY index_name
        """)
        
        results = cursor.fetchall()
        if results:
            has_issues = True
            print(f"\n❌ {check_name}:")
            for row in results:
                print(f"  {row[0]}: {row[1]:,} rows")
    
    if not has_issues:
        print("\n✅ No data quality issues found!")
    
    # 6. Sample Data Verification
    print("\n\n6. SAMPLE DATA VERIFICATION")
    print("-"*80)
    
    for index_name in ['NIFTY', 'BANKNIFTY', 'MIDCAPNIFTY', 'SENSEX']:
        cursor.execute(f"""
            SELECT trade_date, expiry_date, expiry_bucket, 
                   strike, atm_strike, spot, dte, zone_name
            FROM nifty_option_chain
            WHERE index_name = '{index_name}'
            ORDER BY trade_date DESC, strike
            LIMIT 3
        """)
        
        results = cursor.fetchall()
        if results:
            print(f"\n{index_name} (Latest 3 rows):")
            print(f"  Date       Expiry     Type Strike  ATM    Spot      DTE Zone")
            for row in results:
                print(f"  {row[0]} {row[1]} {row[2]:<4} {row[3]:<7.0f} {row[4]:<7.0f} {row[5]:<9.2f} {row[6]:<3} {row[7]}")
    
    # 7. Expiry Date Analysis
    print("\n\n7. EXPIRY DATE ANALYSIS")
    print("-"*80)
    
    cursor.execute("""
        SELECT index_name, expiry_bucket, expiry_date, COUNT(*) as cnt
        FROM nifty_option_chain
        WHERE trade_date >= '2025-01-01'
        GROUP BY index_name, expiry_bucket, expiry_date
        ORDER BY index_name, expiry_date
    """)
    
    results = cursor.fetchall()
    current_index = None
    for row in results[:20]:  # Show first 20
        if row[0] != current_index:
            current_index = row[0]
            print(f"\n{current_index}:")
        print(f"  {row[2]} ({row[1]}): {row[3]:,} rows")
    
    # 8. Summary
    print("\n\n8. SUMMARY")
    print("-"*80)
    
    cursor.execute("""
        SELECT COUNT(DISTINCT index_name) as indices,
               COUNT(*) as total_rows,
               COUNT(DISTINCT trade_date) as total_days
        FROM nifty_option_chain
    """)
    
    summary = cursor.fetchone()
    print(f"\nTotal indices: {summary[0]}")
    print(f"Total rows: {summary[1]:,}")
    print(f"Total trading days: {summary[2]}")
    
    # Check expected configurations
    print("\n\nEXPECTED vs ACTUAL:")
    expected_configs = {
        'NIFTY': {'expiries': ['CW', 'NW', 'CM', 'NM'], 'increment': 50},
        'BANKNIFTY': {'expiries': ['CM', 'NM'], 'increment': 100},
        'MIDCAPNIFTY': {'expiries': ['CM', 'NM'], 'increment': 25},
        'SENSEX': {'expiries': ['CW', 'NW', 'CM', 'NM'], 'increment': 100}
    }
    
    for index_name, config in expected_configs.items():
        cursor.execute(f"""
            SELECT DISTINCT expiry_bucket
            FROM nifty_option_chain
            WHERE index_name = '{index_name}'
        """)
        actual_expiries = sorted([row[0] for row in cursor.fetchall()])
        expected_expiries = sorted(config['expiries'])
        
        print(f"\n{index_name}:")
        print(f"  Expected expiries: {expected_expiries}")
        print(f"  Actual expiries: {actual_expiries}")
        print(f"  Status: {'✅ MATCH' if actual_expiries == expected_expiries else '❌ MISMATCH'}")
    
    cursor.close()
    conn.close()
    
    print("\n" + "="*80)
    print("VERIFICATION COMPLETE")
    print("="*80)

if __name__ == "__main__":
    main()