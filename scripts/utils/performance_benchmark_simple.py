#!/usr/bin/env python3
"""
Simple Performance Benchmarking Script for Enterprise GPU Backtester
Created: June 6, 2025
"""

import time
import requests
import json
import concurrent.futures
from datetime import datetime
from pathlib import Path

BASE_URL = "http://**************:8000"

class PerformanceBenchmark:
    def __init__(self):
        self.results = []
        
    def benchmark_api_response(self, endpoint, method='GET', data=None):
        """Benchmark API response time"""
        url = f"{BASE_URL}{endpoint}"
        start_time = time.time()
        
        try:
            if method == 'GET':
                response = requests.get(url, timeout=10)
            elif method == 'POST':
                response = requests.post(url, json=data, timeout=10)
            
            end_time = time.time()
            response_time = (end_time - start_time) * 1000  # Convert to ms
            
            return {
                'endpoint': endpoint,
                'method': method,
                'status_code': response.status_code,
                'response_time_ms': response_time,
                'success': response.status_code in [200, 404]  # 404 is acceptable for some endpoints
            }
        except Exception as e:
            return {
                'endpoint': endpoint,
                'method': method,
                'error': str(e),
                'response_time_ms': -1,
                'success': False
            }
    
    def run_concurrent_load_test(self, num_requests=10):
        """Run concurrent requests to test load handling"""
        print(f"Running concurrent load test with {num_requests} requests...")
        
        endpoints = [
            '/',
            '/api/v2/gpu/status',
            '/api/v1/logs/',
            '/health'
        ]
        
        results = []
        start_time = time.time()
        
        with concurrent.futures.ThreadPoolExecutor(max_workers=10) as executor:
            futures = []
            for i in range(num_requests):
                endpoint = endpoints[i % len(endpoints)]
                future = executor.submit(self.benchmark_api_response, endpoint)
                futures.append(future)
            
            for future in concurrent.futures.as_completed(futures):
                results.append(future.result())
        
        end_time = time.time()
        total_time = end_time - start_time
        
        # Calculate statistics
        successful_requests = sum(1 for r in results if r.get('success', False))
        response_times = [r.get('response_time_ms', 0) for r in results if r.get('success', False) and r.get('response_time_ms', 0) > 0]
        avg_response_time = sum(response_times) / max(len(response_times), 1) if response_times else 0
        
        return {
            'total_requests': num_requests,
            'successful_requests': successful_requests,
            'failed_requests': num_requests - successful_requests,
            'total_time_seconds': total_time,
            'requests_per_second': num_requests / total_time if total_time > 0 else 0,
            'avg_response_time_ms': avg_response_time,
            'min_response_time_ms': min(response_times) if response_times else 0,
            'max_response_time_ms': max(response_times) if response_times else 0
        }
    
    def benchmark_ui_navigation(self):
        """Benchmark UI navigation response times"""
        print("Benchmarking UI navigation...")
        
        pages = [
            ('/', 'Homepage'),
            ('/#dashboard', 'Dashboard'),
            ('/#backtest', 'New Backtest'),
            ('/#results', 'Results'),
            ('/#logs', 'Logs'),
            ('/#templates', 'Templates')
        ]
        
        results = []
        for path, name in pages:
            result = self.benchmark_api_response(path)
            result['page_name'] = name
            results.append(result)
            print(f"  - {name}: {result['response_time_ms']:.2f}ms")
            time.sleep(0.5)  # Small delay between requests
        
        return results
    
    def generate_report(self):
        """Generate performance benchmark report"""
        print("\nRunning performance benchmarks...")
        
        report = {
            'timestamp': datetime.now().isoformat(),
            'base_url': BASE_URL,
            'benchmarks': {
                'ui_navigation': self.benchmark_ui_navigation(),
                'concurrent_load_10': self.run_concurrent_load_test(10),
                'concurrent_load_50': self.run_concurrent_load_test(50),
                'concurrent_load_100': self.run_concurrent_load_test(100)
            }
        }
        
        # Save report
        report_path = Path('/srv/samba/shared/docs/performance_benchmark_report.json')
        with open(report_path, 'w') as f:
            json.dump(report, f, indent=2)
        
        # Generate summary
        self.generate_summary(report)
        
        return report
    
    def generate_summary(self, report):
        """Generate human-readable summary"""
        summary_path = Path('/srv/samba/shared/docs/performance_benchmark_summary.md')
        
        with open(summary_path, 'w') as f:
            f.write("# Performance Benchmark Report\n\n")
            f.write(f"**Generated**: {report['timestamp']}\n")
            f.write(f"**Target**: {report['base_url']}\n\n")
            
            # UI Navigation
            f.write("## UI Navigation Performance\n")
            f.write("| Page | Response Time (ms) | Status |\n")
            f.write("|------|-------------------|--------|\n")
            
            total_ui_time = 0
            ui_count = 0
            for result in report['benchmarks']['ui_navigation']:
                status = "✅" if result.get('success') else "❌"
                time_ms = result.get('response_time_ms', -1)
                if time_ms > 0:
                    total_ui_time += time_ms
                    ui_count += 1
                f.write(f"| {result['page_name']} | {time_ms:.2f} | {status} |\n")
            
            avg_ui_time = total_ui_time / ui_count if ui_count > 0 else 0
            f.write(f"\n**Average UI Response Time**: {avg_ui_time:.2f}ms\n")
            
            # Load Tests
            f.write("\n## Load Test Results\n")
            for test_name, test_data in report['benchmarks'].items():
                if test_name.startswith('concurrent_load'):
                    num_requests = test_name.split('_')[-1]
                    f.write(f"\n### {num_requests} Concurrent Requests\n")
                    f.write(f"- Success Rate: {test_data['successful_requests']}/{test_data['total_requests']} ")
                    f.write(f"({test_data['successful_requests']/test_data['total_requests']*100:.1f}%)\n")
                    f.write(f"- Total Time: {test_data['total_time_seconds']:.2f}s\n")
                    f.write(f"- Throughput: {test_data['requests_per_second']:.2f} req/s\n")
                    f.write(f"- Response Times:\n")
                    f.write(f"  - Average: {test_data['avg_response_time_ms']:.2f}ms\n")
                    f.write(f"  - Min: {test_data['min_response_time_ms']:.2f}ms\n")
                    f.write(f"  - Max: {test_data['max_response_time_ms']:.2f}ms\n")
            
            # Performance vs Targets
            f.write("\n## Performance vs Targets\n")
            f.write("| Metric | Target | Actual | Status |\n")
            f.write("|--------|--------|--------|--------|\n")
            
            # Check against targets
            f.write(f"| Page Load Time | < 1000ms | {avg_ui_time:.2f}ms | {'✅' if avg_ui_time < 1000 else '❌'} |\n")
            f.write(f"| API Response Time | < 200ms | {avg_ui_time:.2f}ms | {'✅' if avg_ui_time < 200 else '⚠️' if avg_ui_time < 500 else '❌'} |\n")
            
            load_100 = report['benchmarks']['concurrent_load_100']
            success_rate = load_100['successful_requests']/load_100['total_requests']*100
            f.write(f"| 100 Concurrent Users | > 90% success | {success_rate:.1f}% | {'✅' if success_rate > 90 else '⚠️' if success_rate > 80 else '❌'} |\n")
            f.write(f"| Throughput (100 users) | > 50 req/s | {load_100['requests_per_second']:.2f} req/s | {'✅' if load_100['requests_per_second'] > 50 else '⚠️' if load_100['requests_per_second'] > 20 else '❌'} |\n")
            
            # Summary
            f.write("\n## Summary\n")
            if avg_ui_time < 200 and success_rate > 90:
                f.write("✅ **Excellent Performance** - All targets met!\n")
            elif avg_ui_time < 500 and success_rate > 80:
                f.write("⚠️ **Good Performance** - Most targets met, some optimization possible.\n")
            else:
                f.write("❌ **Performance Issues** - Optimization needed.\n")
            
            # Recommendations
            f.write("\n## Recommendations\n")
            if avg_ui_time > 500:
                f.write("- Consider implementing caching for static resources\n")
                f.write("- Optimize database queries\n")
            if success_rate < 90:
                f.write("- Increase connection pool size\n")
                f.write("- Implement rate limiting to prevent overload\n")
            if load_100['requests_per_second'] < 50:
                f.write("- Consider horizontal scaling\n")
                f.write("- Optimize server configuration\n")

def main():
    print("🚀 Starting Performance Benchmark for Enterprise GPU Backtester")
    print("=" * 60)
    
    benchmark = PerformanceBenchmark()
    
    # Run benchmarks
    report = benchmark.generate_report()
    
    print("\n✅ Benchmark complete!")
    print(f"📊 Report saved to: /srv/samba/shared/docs/performance_benchmark_report.json")
    print(f"📄 Summary saved to: /srv/samba/shared/docs/performance_benchmark_summary.md")

if __name__ == "__main__":
    main()