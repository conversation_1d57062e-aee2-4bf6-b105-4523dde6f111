"""
Mock column mapping module for testing.

This module provides mock implementations of the column mapping functions
for testing purposes without needing the full backtester codebase.
"""

import pandas as pd
from datetime import datetime, date, time
from enum import Enum, auto
from typing import Dict, Any, List, Optional, Union

# Mock Enums
class OptionType(str, Enum):
    CALL = "CALL"
    PUT = "PUT"

class TransactionType(str, Enum):
    BUY = "BUY"
    SELL = "SELL"

class StrikeRule(str, Enum):
    ATM = "ATM"
    ITM = "ITM"
    OTM = "OTM"
    ITM1 = "ITM1"
    ITM2 = "ITM2"
    ITM3 = "ITM3"
    OTM1 = "OTM1"
    OTM2 = "OTM2"
    OTM3 = "OTM3"
    FIXED = "FIXED"

class ExpiryRule(str, Enum):
    CURRENT_WEEK = "CURRENT_WEEK"
    NEXT_WEEK = "NEXT_WEEK"
    CURRENT_MONTH = "CURRENT_MONTH"
    NEXT_MONTH = "NEXT_MONTH"

# Mock Models
class BaseModel:
    """Base model with dictionary-like access."""
    
    def __init__(self, **kwargs):
        for key, value in kwargs.items():
            setattr(self, key, value)
    
    def __getitem__(self, key):
        return getattr(self, key)
    
    def __setitem__(self, key, value):
        setattr(self, key, value)
    
    def __contains__(self, key):
        return hasattr(self, key)

class LegModel(BaseModel):
    """Mock Leg model."""
    
    def __init__(self, **kwargs):
        self.strategy_name = kwargs.get('strategy_name', '')
        self.leg_id = kwargs.get('leg_id', '')
        self.option_type = kwargs.get('option_type', '')
        self.transaction = kwargs.get('transaction', TransactionType.BUY)
        self.expiry_rule = kwargs.get('expiry_rule', ExpiryRule.CURRENT_WEEK)
        self.strike_rule = kwargs.get('strike_rule', StrikeRule.ATM)
        self.fixed_strike = kwargs.get('fixed_strike', 0)
        self.lots = kwargs.get('lots', 0)
        self.extra_params = kwargs.get('extra_params', {})
        super().__init__(**kwargs)

class StrategyModel(BaseModel):
    """Mock Strategy model."""
    
    def __init__(self, **kwargs):
        self.strategy_name = kwargs.get('strategy_name', '')
        self.strategy_excel_path = kwargs.get('strategy_excel_path', '')
        self.evaluator = kwargs.get('evaluator', '')
        self.portfolio_name = kwargs.get('portfolio_name', '')
        self.entry_start = kwargs.get('entry_start', time(9, 15, 0))
        self.entry_end = kwargs.get('entry_end', time(15, 30, 0))
        self.dte_filter = kwargs.get('dte_filter', 0)
        self.extra_params = kwargs.get('extra_params', {})
        self.legs = kwargs.get('legs', [])
        super().__init__(**kwargs)

class PortfolioModel(BaseModel):
    """Mock Portfolio model."""
    
    def __init__(self, **kwargs):
        self.portfolio_name = kwargs.get('portfolio_name', '')
        self.start_date = kwargs.get('start_date', date.today())
        self.end_date = kwargs.get('end_date', date.today())
        self.is_tick_bt = kwargs.get('is_tick_bt', False)
        self.margin_multiplier = kwargs.get('margin_multiplier', 1.0)
        self.slippage_percent = kwargs.get('slippage_percent', 0.0)
        self.extra_params = kwargs.get('extra_params', {})
        self.strategies = kwargs.get('strategies', [])
        super().__init__(**kwargs)

# Mock Transformation Functions
def _transform_date(date_str: str) -> date:
    """Transform a date string from DD_MM_YYYY to a date object."""
    try:
        return datetime.strptime(date_str, '%d_%m_%Y').date()
    except ValueError:
        raise ValueError(f"Invalid date format: {date_str}. Expected DD_MM_YYYY.")

def _transform_time(time_int: int) -> time:
    """Transform a time integer from HHMMSS to a time object."""
    time_str = str(time_int).zfill(6)
    
    try:
        hour = int(time_str[:2])
        minute = int(time_str[2:4])
        second = int(time_str[4:])
        
        if hour > 23 or minute > 59 or second > 59:
            raise ValueError(f"Invalid time: {time_int}")
        
        return time(hour, minute, second)
    except (ValueError, IndexError):
        raise ValueError(f"Invalid time format: {time_int}. Expected HHMMSS.")

def _transform_boolean(value: str) -> bool:
    """Transform a YES/NO string to a boolean."""
    if value.upper() in ('YES', 'Y'):
        return True
    elif value.upper() in ('NO', 'N'):
        return False
    else:
        raise ValueError(f"Invalid boolean value: {value}. Expected YES/NO/Y/N.")

def _transform_option_type(value: str) -> str:
    """Transform option type string (CE/PE/CALL/PUT) to OptionType."""
    if value.upper() in ('CE', 'CALL'):
        return OptionType.CALL
    elif value.upper() in ('PE', 'PUT'):
        return OptionType.PUT
    else:
        raise ValueError(f"Invalid option type: {value}. Expected CE/PE/CALL/PUT.")

# Mock Parsing Functions
def parse_portfolio_excel(excel_sheets: Dict[str, pd.DataFrame]) -> List[PortfolioModel]:
    """Parse Excel sheets into PortfolioModel objects."""
    portfolio_models = []
    
    # Check for portfolio sheet
    if 'PortfolioSetting' not in excel_sheets:
        return []
    
    portfolio_df = excel_sheets['PortfolioSetting']
    strategy_df = excel_sheets.get('StrategySetting', pd.DataFrame())
    
    # Filter enabled portfolios
    if 'Enabled' in portfolio_df.columns:
        portfolio_df = portfolio_df[portfolio_df['Enabled'].str.upper().isin(['YES', 'Y'])]
    
    # Create portfolio models
    for _, row in portfolio_df.iterrows():
        portfolio_kwargs = {}
        extra_params = {}
        
        # Map direct fields
        if 'PortfolioName' in row:
            portfolio_kwargs['portfolio_name'] = row['PortfolioName']
        
        if 'StartDate' in row:
            portfolio_kwargs['start_date'] = _transform_date(row['StartDate'])
        
        if 'EndDate' in row:
            portfolio_kwargs['end_date'] = _transform_date(row['EndDate'])
        
        if 'IsTickBT' in row:
            portfolio_kwargs['is_tick_bt'] = _transform_boolean(row['IsTickBT'])
        
        if 'Multiplier' in row:
            portfolio_kwargs['margin_multiplier'] = row['Multiplier']
        
        if 'SlippagePercent' in row:
            portfolio_kwargs['slippage_percent'] = row['SlippagePercent']
        
        # Map extra params
        for col in row.index:
            if col not in ['PortfolioName', 'StartDate', 'EndDate', 'IsTickBT', 
                          'Multiplier', 'SlippagePercent', 'Enabled']:
                extra_params[col] = row[col]
        
        portfolio_kwargs['extra_params'] = extra_params
        
        # Add strategies if available
        strategies = []
        if not strategy_df.empty and 'PortfolioName' in row:
            # Filter for this portfolio
            port_strategies_df = strategy_df[
                (strategy_df['PortfolioName'] == row['PortfolioName']) & 
                (strategy_df['Enabled'].str.upper().isin(['YES', 'Y']))
            ]
            
            for _, strat_row in port_strategies_df.iterrows():
                strategy_kwargs = {
                    'portfolio_name': row['PortfolioName'],
                    'evaluator': strat_row.get('StrategyType', ''),
                    'strategy_excel_path': strat_row.get('StrategyExcelFilePath', '')
                }
                
                strategies.append(StrategyModel(**strategy_kwargs))
        
        portfolio_kwargs['strategies'] = strategies
        
        # Create and add portfolio model
        portfolio_models.append(PortfolioModel(**portfolio_kwargs))
    
    return portfolio_models

def parse_strategy_excel(excel_sheets: Dict[str, pd.DataFrame]) -> StrategyModel:
    """Parse strategy Excel sheets into a StrategyModel object."""
    # Check for required sheets
    if 'GeneralParameter' not in excel_sheets or 'LegParameter' not in excel_sheets:
        raise ValueError("Missing required sheets: GeneralParameter and/or LegParameter")
    
    general_df = excel_sheets['GeneralParameter']
    leg_df = excel_sheets['LegParameter']
    
    # Must have at least one row in GeneralParameter
    if len(general_df) == 0:
        raise ValueError("Empty GeneralParameter sheet")
    
    # Get first row from GeneralParameter
    general_row = general_df.iloc[0]
    
    # Create strategy kwargs
    strategy_kwargs = {}
    extra_params = {}
    
    # Map direct fields
    if 'StrategyName' in general_row:
        strategy_kwargs['strategy_name'] = general_row['StrategyName']
    
    if 'DTE' in general_row:
        strategy_kwargs['dte_filter'] = general_row['DTE']
    
    if 'StartTime' in general_row:
        strategy_kwargs['entry_start'] = _transform_time(general_row['StartTime'])
    
    if 'EndTime' in general_row:
        strategy_kwargs['entry_end'] = _transform_time(general_row['EndTime'])
    
    # Map extra params
    for col in general_row.index:
        if col not in ['StrategyName', 'DTE', 'StartTime', 'EndTime']:
            # Special case for boolean fields
            if col in ['MoveSlToCost', 'ConsiderHedgePnLForStgyPnL', 'OnExpiryDayTradeNextExpiry']:
                extra_params[col] = _transform_boolean(general_row[col])
            else:
                extra_params[col] = general_row[col]
    
    strategy_kwargs['extra_params'] = extra_params
    
    # Parse legs
    legs = []
    
    # Filter out idle legs
    if 'IsIdle' in leg_df.columns:
        leg_df = leg_df[~leg_df['IsIdle'].str.upper().isin(['YES', 'Y'])]
    
    for _, leg_row in leg_df.iterrows():
        leg_kwargs = {}
        leg_extra_params = {}
        parsed_indicator_conditions = {}
        
        # Map direct fields
        if 'StrategyName' in leg_row:
            leg_kwargs['strategy_name'] = leg_row['StrategyName']
        
        if 'LegID' in leg_row:
            leg_kwargs['leg_id'] = leg_row['LegID']
        
        if 'Instrument' in leg_row:
            leg_kwargs['option_type'] = _transform_option_type(leg_row['Instrument'])
        
        if 'Transaction' in leg_row:
            if leg_row['Transaction'].upper() == 'BUY':
                leg_kwargs['transaction'] = TransactionType.BUY
            else:
                leg_kwargs['transaction'] = TransactionType.SELL
        
        if 'Expiry' in leg_row:
            if leg_row['Expiry'].upper() in ['CURRENT', 'CW']:
                leg_kwargs['expiry_rule'] = ExpiryRule.CURRENT_WEEK
            elif leg_row['Expiry'].upper() in ['NEXT', 'NW']:
                leg_kwargs['expiry_rule'] = ExpiryRule.NEXT_WEEK
            elif leg_row['Expiry'].upper() == 'CM':
                leg_kwargs['expiry_rule'] = ExpiryRule.CURRENT_MONTH
            elif leg_row['Expiry'].upper() == 'NM':
                leg_kwargs['expiry_rule'] = ExpiryRule.NEXT_MONTH
        
        if 'StrikeMethod' in leg_row:
            method = leg_row['StrikeMethod'].upper()
            if method == 'ATM':
                leg_kwargs['strike_rule'] = StrikeRule.ATM
            elif method == 'ITM':
                leg_kwargs['strike_rule'] = StrikeRule.ITM
            elif method == 'OTM':
                leg_kwargs['strike_rule'] = StrikeRule.OTM
            elif method == 'ITM1':
                leg_kwargs['strike_rule'] = StrikeRule.ITM1
            elif method == 'ITM2':
                leg_kwargs['strike_rule'] = StrikeRule.ITM2
            elif method == 'ITM3':
                leg_kwargs['strike_rule'] = StrikeRule.ITM3
            elif method == 'OTM1':
                leg_kwargs['strike_rule'] = StrikeRule.OTM1
            elif method == 'OTM2':
                leg_kwargs['strike_rule'] = StrikeRule.OTM2
            elif method == 'OTM3':
                leg_kwargs['strike_rule'] = StrikeRule.OTM3
            elif method == 'FIXED':
                leg_kwargs['strike_rule'] = StrikeRule.FIXED
        
        if 'StrikeValue' in leg_row:
            leg_kwargs['fixed_strike'] = leg_row['StrikeValue']
        
        if 'Lots' in leg_row:
            leg_kwargs['lots'] = leg_row['Lots']
        
        # Parse indicator fields
        indicator_fields = [
            'ConsiderEMAForEntry', 'ConsiderVwapForEntry',
            'ConsiderVwapForExit', 'ConsiderEMAForExit', 
            'ConsiderSTForEntry', 'ConsiderSTForExit', 
            'ConsiderRSIForExit', 'ConsiderVolSmaForEntry', 
            'ConsiderVolSmaForExit'
        ]
        
        for field in indicator_fields:
            if field in leg_row:
                leg_extra_params[field] = _transform_boolean(leg_row[field])
        
        # Parse indicator conditions
        condition_fields = [
            'EmaEntryCondition', 'VwapEntryCondition', 'RsiEntryCondition',
            'VwapExitCondition', 'EmaExitCondition', 'StEntryCondition',
            'StExitCondition', 'RsiExitCondition', 'VolSmaEntryCondition',
            'VolSmaExitCondition'
        ]
        
        for field in condition_fields:
            if field in leg_row and pd.notna(leg_row[field]) and leg_row[field]:
                parsed_indicator_conditions[field] = leg_row[field]
        
        # Add parsed conditions to extra_params
        leg_extra_params['_parsed_indicator_conditions'] = parsed_indicator_conditions
        
        # Add other fields to extra_params
        for col in leg_row.index:
            if col not in ['StrategyName', 'LegID', 'Instrument', 'Transaction',
                          'Expiry', 'StrikeMethod', 'StrikeValue', 'Lots'] + indicator_fields + condition_fields:
                
                # Boolean fields
                if col in ['IsIdle', 'TrailW&T', 'OpenHedge', 'OnEntry_OpenTradeOn', 
                          'OnEntry_SqOffTradeOff', 'OnEntry_SqOffAllLegs', 'OnExit_OpenTradeOn', 
                          'OnExit_SqOffTradeOff', 'OnExit_SqOffAllLegs', 'OnExit_OpenAllLegs']:
                    if pd.notna(leg_row[col]) and leg_row[col]:
                        leg_extra_params[col] = _transform_boolean(leg_row[col])
                else:
                    if pd.notna(leg_row[col]):
                        leg_extra_params[col] = leg_row[col]
        
        leg_kwargs['extra_params'] = leg_extra_params
        
        # Create leg model
        legs.append(LegModel(**leg_kwargs))
    
    strategy_kwargs['legs'] = legs
    
    # Create and return strategy model
    return StrategyModel(**strategy_kwargs) 