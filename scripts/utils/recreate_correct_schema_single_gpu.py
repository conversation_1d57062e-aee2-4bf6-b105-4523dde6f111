#!/usr/bin/env python3
"""
Recreate the nifty_option_chain table with the CORRECT schema expected by backtester
Optimized for single GPU without sharding
"""
import sys
import time
from heavydb import connect

def recreate_table_correct_schema():
    """Drop and recreate the table with correct schema for single GPU"""
    
    # Connection parameters
    conn_params = {
        'host': 'localhost',
        'port': 6274,
        'user': 'admin',
        'password': 'HyperInteractive',
        'dbname': 'heavyai'
    }
    
    print(f"Connecting to HeavyDB...")
    try:
        conn = connect(**conn_params)
        cursor = conn.cursor()
        print("Successfully connected to HeavyDB")
    except Exception as e:
        print(f"Failed to connect: {e}")
        sys.exit(1)
    
    # Drop the incorrect table
    print("\nDropping the current table...")
    try:
        cursor.execute("DROP TABLE IF EXISTS nifty_option_chain")
        print("Table dropped successfully")
    except Exception as e:
        print(f"Error dropping table: {e}")
    
    # Create new table with CORRECT schema optimized for single GPU
    print("\nCreating new table with correct schema optimized for single GPU...")
    
    create_table_sql = """
    CREATE TABLE nifty_option_chain (
        trade_date       DATE ENCODING DAYS(32),
        trade_time       TIME,
        expiry_date      DATE ENCODING DAYS(32),
        index_name       TEXT ENCODING DICT(32),
        spot             DOUBLE,
        atm_strike       DOUBLE,
        strike           DOUBLE,
        dte              INT,
        expiry_bucket    TEXT ENCODING DICT(32),
        zone_id          SMALLINT,
        zone_name        TEXT ENCODING DICT(32),
        call_strike_type TEXT ENCODING DICT(32),
        put_strike_type  TEXT ENCODING DICT(32),
        
        -- Call option columns
        ce_symbol TEXT ENCODING DICT(32),
        ce_open   DOUBLE,
        ce_high   DOUBLE,
        ce_low    DOUBLE,
        ce_close  DOUBLE,
        ce_volume BIGINT,
        ce_oi     BIGINT,
        ce_coi    BIGINT,
        ce_iv     DOUBLE,
        ce_delta  DOUBLE,
        ce_gamma  DOUBLE,
        ce_theta  DOUBLE,
        ce_vega   DOUBLE,
        ce_rho    DOUBLE,
        
        -- Put option columns
        pe_symbol TEXT ENCODING DICT(32),
        pe_open   DOUBLE,
        pe_high   DOUBLE,
        pe_low    DOUBLE,
        pe_close  DOUBLE,
        pe_volume BIGINT,
        pe_oi     BIGINT,
        pe_coi    BIGINT,
        pe_iv     DOUBLE,
        pe_delta  DOUBLE,
        pe_gamma  DOUBLE,
        pe_theta  DOUBLE,
        pe_vega   DOUBLE,
        pe_rho    DOUBLE,
        
        -- Futures columns
        future_open   DOUBLE,
        future_high   DOUBLE,
        future_low    DOUBLE,
        future_close  DOUBLE,
        future_volume BIGINT,
        future_oi     BIGINT,
        future_coi    BIGINT
    ) WITH (
        fragment_size = 32000000,
        sort_column = 'trade_date'
    );
    """
    
    try:
        cursor.execute(create_table_sql)
        print("Table created successfully with single GPU optimization")
        print("Key optimizations:")
        print("- NO SHARD KEY or SHARD_COUNT (single GPU)")
        print("- Fragment size: 32M rows")
        print("- Sorted by trade_date")
        print("- Dictionary encoding for text columns")
        print("- Original schema format expected by backtester")
    except Exception as e:
        print(f"Error creating table: {e}")
        conn.close()
        sys.exit(1)
    
    # Verify table creation
    print("\nVerifying table creation...")
    try:
        cursor.execute("SELECT COUNT(*) FROM nifty_option_chain")
        count = cursor.fetchone()[0]
        print(f"Table created successfully with {count} rows")
        
        # Check columns
        cursor.execute("SELECT * FROM nifty_option_chain LIMIT 0")
        columns = [desc[0] for desc in cursor.description]
        print(f"\nTotal columns: {len(columns)}")
        print(f"First 10 columns: {columns[:10]}")
        
    except Exception as e:
        print(f"Error verifying table: {e}")
    
    conn.close()
    print("\nTable recreation completed successfully!")
    print("Ready to load data with COPY FROM")

if __name__ == "__main__":
    recreate_table_correct_schema()