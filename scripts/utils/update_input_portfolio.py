import pandas as pd

# Path to the input portfolio Excel file
input_file = '/srv/samba/shared/input_portfolio.xlsx'

# Read the PortfolioSetting sheet
portfolio_settings = pd.read_excel(input_file, sheet_name='PortfolioSetting')

# Print current settings
print("Current settings:")
print(portfolio_settings[['StartDate', 'EndDate']])

# Update StartDate and EndDate to match the golden file
# Based on the golden file transactions, we should use 03_04_2025 to 09_04_2025
portfolio_settings.loc[0, 'StartDate'] = '03_04_2025'
portfolio_settings.loc[0, 'EndDate'] = '09_04_2025'

# Write back to the file
with pd.ExcelWriter(input_file, engine='openpyxl', mode='a', if_sheet_exists='replace') as writer:
    portfolio_settings.to_excel(writer, sheet_name='PortfolioSetting', index=False)

# Verify the changes
updated_settings = pd.read_excel(input_file, sheet_name='PortfolioSetting')
print("\nUpdated settings:")
print(updated_settings[['StartDate', 'EndDate']])

print("\nChanges applied successfully. Portfolio date range now matches the golden file transactions.") 