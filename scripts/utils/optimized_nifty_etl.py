#!/usr/bin/env python3
"""
Optimized ETL script for loading nifty option chain data into HeavyDB
Following best practices from the Performance Optimization Guide
"""

import os
import sys
import glob
import pandas as pd
import numpy as np
from datetime import datetime, time
import logging
import concurrent.futures
from pathlib import Path

# Add parent directory to path to import dal
sys.path.append('/srv/samba/shared')

try:
    from bt.dal.heavydb_conn import get_conn
except ImportError:
    print("Error: Unable to import heavydb connection module")
    print("Attempting alternative import...")
    import heavyai

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class OptimizedNiftyETL:
    def __init__(self, data_dir="/srv/samba/shared/market_data/nifty/oc_with_futures"):
        self.data_dir = data_dir
        self.conn = None
        self.cursor = None
        self.batch_size = 500000  # Optimal batch size for HeavyDB
        self.buffer_size = 128 * 1024 * 1024  # 128MB buffer for sorting
        
    def connect(self):
        """Establish connection to HeavyDB"""
        try:
            # Try using the project's connection method first
            from bt.dal.heavydb_conn import get_conn
            self.conn = get_conn()
        except:
            # Fallback to direct connection
            import heavyai
            self.conn = heavyai.connect(
                user='admin',
                password='HyperInteractive', 
                host='localhost',
                port=6274,
                dbname='heavyai',
                protocol='binary'
            )
        self.cursor = self.conn.cursor()
        logger.info("Connected to HeavyDB")
        
    def create_table(self):
        """Execute the optimized table creation SQL"""
        try:
            with open('/srv/samba/shared/create_optimized_nifty_option_chain.sql', 'r') as f:
                sql = f.read()
            
            # Execute each statement separately
            statements = [s.strip() for s in sql.split(';') if s.strip()]
            for stmt in statements:
                logger.info(f"Executing: {stmt[:50]}...")
                self.cursor.execute(stmt)
            
            logger.info("Table created successfully")
        except Exception as e:
            logger.error(f"Error creating table: {e}")
            raise
            
    def process_csv_file(self, file_path):
        """Process a single CSV file and prepare data for loading"""
        logger.info(f"Processing file: {file_path}")
        
        try:
            # Read CSV in chunks to handle large files
            chunks = []
            for chunk in pd.read_csv(file_path, chunksize=100000):
                # Convert data types as per optimized schema
                chunk['trade_date'] = pd.to_datetime(chunk['trade_date']).dt.date
                chunk['trade_time'] = pd.to_datetime(chunk['trade_time'], format='%H:%M:%S').dt.time
                
                # Create combined timestamp for efficient filtering
                chunk['trade_ts'] = pd.to_datetime(
                    chunk['trade_date'].astype(str) + ' ' + chunk['trade_time'].astype(str)
                )
                
                chunk['expiry_date'] = pd.to_datetime(chunk['expiry_date']).dt.date
                
                # Convert numeric fields
                numeric_fields = ['spot', 'atm_strike', 'strike', 'dte', 'zone_id',
                                'ce_open', 'ce_high', 'ce_low', 'ce_close', 'ce_volume', 
                                'ce_oi', 'ce_coi', 'ce_iv', 'ce_delta', 'ce_gamma', 
                                'ce_theta', 'ce_vega', 'ce_rho',
                                'pe_open', 'pe_high', 'pe_low', 'pe_close', 'pe_volume',
                                'pe_oi', 'pe_coi', 'pe_iv', 'pe_delta', 'pe_gamma',
                                'pe_theta', 'pe_vega', 'pe_rho',
                                'future_open', 'future_high', 'future_low', 'future_close',
                                'future_volume', 'future_oi', 'future_coi']
                
                for field in numeric_fields:
                    if field in chunk.columns:
                        chunk[field] = pd.to_numeric(chunk[field], errors='coerce')
                
                # Convert integer fields
                int_fields = ['atm_strike', 'strike', 'dte', 'zone_id', 
                            'ce_volume', 'ce_oi', 'ce_coi',
                            'pe_volume', 'pe_oi', 'pe_coi',
                            'future_volume', 'future_oi', 'future_coi']
                
                for field in int_fields:
                    if field in chunk.columns:
                        chunk[field] = chunk[field].fillna(0).astype('int32')
                
                chunks.append(chunk)
            
            # Combine all chunks
            df = pd.concat(chunks, ignore_index=True)
            
            # Sort by trade_ts for optimal fragment skipping
            df = df.sort_values('trade_ts')
            
            logger.info(f"Processed {len(df)} rows from {file_path}")
            return df
            
        except Exception as e:
            logger.error(f"Error processing file {file_path}: {e}")
            raise
            
    def load_dataframe_to_heavydb(self, df, table_name='nifty_option_chain'):
        """Load DataFrame to HeavyDB using optimized bulk loading"""
        temp_file = None
        try:
            # Try using PyArrow for direct insertion if available
            try:
                import pyarrow as pa
                # Convert DataFrame to PyArrow Table
                arrow_table = pa.Table.from_pandas(df)
                # Use HeavyDB's load_table method
                self.conn.load_table(table_name, arrow_table)
                logger.info(f"Successfully loaded {len(df)} rows using PyArrow")
                return
            except Exception as arrow_error:
                logger.warning(f"PyArrow load failed: {arrow_error}, falling back to CSV")
            
            # Fallback to CSV method - write to the data directory which should be whitelisted
            temp_file = os.path.join(self.data_dir, f"temp_load_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv")
            df.to_csv(temp_file, index=False, date_format='%Y-%m-%d')
            
            # Use COPY FROM for bulk loading
            copy_sql = f"COPY {table_name} FROM '{temp_file}' WITH (header='true')"
            
            logger.info(f"Loading {len(df)} rows to HeavyDB from {temp_file}...")
            self.cursor.execute(copy_sql)
            
            logger.info(f"Successfully loaded {len(df)} rows")
            
        except Exception as e:
            logger.error(f"Error loading data to HeavyDB: {e}")
            raise
        finally:
            # Clean up temp file
            if temp_file and os.path.exists(temp_file):
                try:
                    os.remove(temp_file)
                except:
                    logger.warning(f"Could not remove temp file {temp_file}")
            
    def verify_load(self):
        """Verify data was loaded correctly"""
        try:
            self.cursor.execute("SELECT COUNT(*) FROM nifty_option_chain")
            count = self.cursor.fetchone()[0]
            logger.info(f"Total rows in table: {count}")
            
            # Check memory usage
            self.cursor.execute("""
                SELECT 
                    table_name,
                    SUM(fragment_size) as total_rows,
                    COUNT(*) as fragment_count
                FROM memory_summary 
                WHERE table_name = 'nifty_option_chain'
                GROUP BY table_name
            """)
            
            result = self.cursor.fetchone()
            if result:
                logger.info(f"Table fragments: {result[2]}, Total size: {result[1]}")
                
        except Exception as e:
            logger.error(f"Error verifying load: {e}")
            
    def run_etl(self, pattern="*.csv"):
        """Run the complete ETL process"""
        try:
            # Connect to database
            self.connect()
            
            # Create table
            self.create_table()
            
            # Get all CSV files
            csv_files = sorted(glob.glob(os.path.join(self.data_dir, pattern)))
            logger.info(f"Found {len(csv_files)} files to process")
            
            # Process files one by one to avoid memory issues
            for csv_file in csv_files:
                logger.info(f"\nProcessing {os.path.basename(csv_file)}...")
                
                # Process and load file
                df = self.process_csv_file(csv_file)
                
                # Load in batches if file is too large
                if len(df) > self.batch_size:
                    for i in range(0, len(df), self.batch_size):
                        batch = df.iloc[i:i+self.batch_size]
                        self.load_dataframe_to_heavydb(batch)
                else:
                    self.load_dataframe_to_heavydb(df)
                    
                # Clear dataframe to free memory
                del df
                
            # Verify final load
            self.verify_load()
            
            logger.info("ETL process completed successfully")
            
        except Exception as e:
            logger.error(f"ETL process failed: {e}")
            raise
        finally:
            if self.cursor:
                self.cursor.close()
            if self.conn:
                self.conn.close()

def main():
    """Main entry point"""
    etl = OptimizedNiftyETL()
    
    # Run ETL for all CSV files
    etl.run_etl()
    
if __name__ == "__main__":
    main()