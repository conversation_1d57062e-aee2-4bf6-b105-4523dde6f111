#!/usr/bin/env python3
import os
import sys
import glob
import csv
import subprocess
import time
import signal
import argparse
from datetime import datetime, timedelta

# Configuration
CSV_DIR = "/srv/samba/shared/market_data/nifty/oc_with_futures/"
DEFAULT_BATCH_SIZE = 1000  # Much smaller batch size for better progress feedback
MAX_RETRIES = 3

def parse_args():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser(description='Optimized data loader for Nifty Option Chain')
    parser.add_argument('--batch-size', type=int, default=DEFAULT_BATCH_SIZE, help=f'Batch size (default: {DEFAULT_BATCH_SIZE})')
    parser.add_argument('--file', type=str, help='Process only a specific file')
    parser.add_argument('--limit', type=int, help='Limit rows per file (for testing)')
    parser.add_argument('--start-file', type=int, default=0, help='Start with this file index (0-based)')
    parser.add_argument('--end-file', type=int, help='End with this file index (0-based)')
    parser.add_argument('--timeout', type=int, default=300, help='SQL command timeout in seconds')
    parser.add_argument('--skip-existing', action='store_true', help='Skip table creation if exists')
    return parser.parse_args()

def run_sql_command(sql, timeout=300):
    """Run SQL command via heavysql with timeout"""
    # Set timeout handler
    signal.signal(signal.SIGALRM, timeout_handler)
    signal.alarm(timeout)
    
    cmd = ["/opt/heavyai/bin/heavysql", "-s", "127.0.0.1", "--port", "6274", 
           "-u", "admin", "-p", "HyperInteractive", "-d", "heavyai"]
    
    try:
        process = subprocess.Popen(cmd, stdin=subprocess.PIPE, 
                                stdout=subprocess.PIPE, stderr=subprocess.PIPE,
                                text=True)
        
        stdout, stderr = process.communicate(input=sql)
        
        # Clear the alarm
        signal.alarm(0)
        
        if stderr and "Error" in stderr:
            print(f"SQL Error: {stderr}")
            return False
        
        return True
    except TimeoutError:
        print(f"SQL command timed out after {timeout} seconds")
        # Try to kill the process
        try:
            process.kill()
        except:
            pass
        return False
    except Exception as e:
        print(f"Unexpected error: {e}")
        signal.alarm(0)  # Clear the alarm
        return False

# Timeout handling
class TimeoutError(Exception):
    pass

def timeout_handler(signum, frame):
    raise TimeoutError("SQL command timed out")

def ensure_table_exists(skip_existing=False):
    """Ensure the target table exists with correct schema"""
    if skip_existing:
        # Check if table exists first
        check_sql = "SELECT COUNT(*) FROM information_schema.tables WHERE table_name = 'nifty_option_chain';"
        cmd = ["/opt/heavyai/bin/heavysql", "-s", "127.0.0.1", "--port", "6274", 
              "-u", "admin", "-p", "HyperInteractive", "-d", "heavyai", "-q", check_sql]
        
        try:
            process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
            stdout, stderr = process.communicate()
            if "1" in stdout:
                print("Table nifty_option_chain already exists, skipping creation")
                return True
        except Exception as e:
            print(f"Error checking table existence: {e}")
    
    print("Creating table nifty_option_chain...")
    create_table_sql = """
    DROP TABLE IF EXISTS nifty_option_chain;
    CREATE TABLE nifty_option_chain (
        trade_date       DATE,
        trade_time       TIME,
        expiry_date      DATE,
        index_name       TEXT,
        spot             DOUBLE,
        atm_strike       DOUBLE,
        strike           DOUBLE,
        dte              INT,
        expiry_bucket    TEXT,
        zone_id          SMALLINT,
        zone_name        TEXT,
        call_strike_type TEXT,
        put_strike_type  TEXT,
        ce_symbol        TEXT,
        ce_open          DOUBLE,
        ce_high          DOUBLE,
        ce_low           DOUBLE,
        ce_close         DOUBLE,
        ce_volume        BIGINT,
        ce_oi            BIGINT,
        ce_coi           BIGINT,
        ce_iv            DOUBLE,
        ce_delta         DOUBLE,
        ce_gamma         DOUBLE,
        ce_theta         DOUBLE,
        ce_vega          DOUBLE,
        ce_rho           DOUBLE,
        pe_symbol        TEXT,
        pe_open          DOUBLE,
        pe_high          DOUBLE,
        pe_low           DOUBLE,
        pe_close         DOUBLE,
        pe_volume        BIGINT,
        pe_oi            BIGINT,
        pe_coi           BIGINT,
        pe_iv            DOUBLE,
        pe_delta         DOUBLE,
        pe_gamma         DOUBLE,
        pe_theta         DOUBLE,
        pe_vega          DOUBLE,
        pe_rho           DOUBLE,
        future_open      DOUBLE,
        future_high      DOUBLE,
        future_low       DOUBLE,
        future_close     DOUBLE,
        future_volume    BIGINT,
        future_oi        BIGINT,
        future_coi       BIGINT
    );
    """
    
    if run_sql_command(create_table_sql, timeout=120):
        print("Table created successfully")
        return True
    else:
        print("Failed to create table")
        return False

def get_csv_files():
    """Get a list of all CSV files to process"""
    return sorted(glob.glob(os.path.join(CSV_DIR, "*.csv")))

def count_lines(file_path):
    """Count lines in a file"""
    with open(file_path, 'r') as f:
        return sum(1 for _ in f)

def estimate_completion_time(remaining_rows, rows_processed, elapsed_time):
    """Estimate time to completion based on current progress"""
    if rows_processed == 0 or elapsed_time.total_seconds() == 0:
        return "Unknown"
    
    rows_per_second = rows_processed / elapsed_time.total_seconds()
    if rows_per_second == 0:
        return "Never (zero throughput)"
    
    seconds_remaining = remaining_rows / rows_per_second
    
    # Format the time
    if seconds_remaining < 60:
        return f"{int(seconds_remaining)} seconds"
    elif seconds_remaining < 3600:
        return f"{int(seconds_remaining / 60)} minutes"
    elif seconds_remaining < 86400:  # 24 hours
        hours = int(seconds_remaining / 3600)
        minutes = int((seconds_remaining % 3600) / 60)
        return f"{hours} hours, {minutes} minutes"
    else:
        days = int(seconds_remaining / 86400)
        hours = int((seconds_remaining % 86400) / 3600)
        return f"{days} days, {hours} hours"

def process_csv_file(file_path, batch_size, max_rows=None, timeout=300):
    """Process a single CSV file in batches with optional row limit"""
    file_name = os.path.basename(file_path)
    file_start_time = datetime.now()
    print(f"\nProcessing {file_name}...")
    
    # Count total lines
    total_lines = count_lines(file_path)
    print(f"Total lines in file: {total_lines}")
    
    # Account for header
    rows_to_process = total_lines - 1
    if max_rows and max_rows < rows_to_process:
        rows_to_process = max_rows
        print(f"Limited to {max_rows} rows for debugging/testing")
    
    print(f"Rows to process: {rows_to_process}")
    
    # Process in batches
    with open(file_path, 'r') as f:
        reader = csv.reader(f)
        header = next(reader)  # Skip header
        
        batch_rows = []
        total_processed = 0
        total_inserted = 0
        batch_count = 0
        
        processing_start = datetime.now()
        
        for row in reader:
            if max_rows and total_processed >= max_rows:
                break
                
            if len(row) != len(header):
                print(f"Warning: Row {total_processed+1} has {len(row)} columns, expected {len(header)}")
                continue
                
            batch_rows.append(row)
            
            # Process batch when it reaches the batch size
            if len(batch_rows) >= batch_size:
                batch_count += 1
                batch_start = datetime.now()
                success = insert_batch(batch_rows, batch_count, file_name, timeout)
                batch_end = datetime.now()
                total_processed += len(batch_rows)
                if success:
                    total_inserted += len(batch_rows)
                
                # Show progress
                progress = (total_processed / rows_to_process) * 100
                elapsed = datetime.now() - processing_start
                eta = estimate_completion_time(rows_to_process - total_processed, total_processed, elapsed)
                
                print(f"Progress: {progress:.2f}% - Processed {total_processed}/{rows_to_process} rows")
                print(f"Batch {batch_count}: {len(batch_rows)} rows in {(batch_end-batch_start).total_seconds():.2f}s")
                print(f"Time elapsed: {elapsed}, ETA: {eta}")
                
                # Clear batch
                batch_rows = []
        
        # Process any remaining rows
        if batch_rows:
            batch_count += 1
            batch_start = datetime.now()
            success = insert_batch(batch_rows, batch_count, file_name, timeout)
            batch_end = datetime.now()
            total_processed += len(batch_rows)
            if success:
                total_inserted += len(batch_rows)
            print(f"Final batch: {len(batch_rows)} rows in {(batch_end-batch_start).total_seconds():.2f}s")
    
    file_duration = datetime.now() - file_start_time
    print(f"File {file_name} completed. Inserted {total_inserted}/{rows_to_process} rows in {file_duration}.")
    return total_inserted

def insert_batch(rows, batch_num, file_name, timeout=300):
    """Insert a batch of rows into the database"""
    insert_statements = []
    
    for i, row in enumerate(rows):
        # Clean and format values properly
        values = []
        try:
            for j, val in enumerate(row):
                # Handle different data types
                if j in [0, 2]:  # DATE fields (trade_date, expiry_date)
                    values.append(f"'{val}'")
                elif j == 1:  # TIME field (trade_time)
                    values.append(f"'{val}'")
                elif j in [3, 10, 11, 12, 13, 27]:  # TEXT fields
                    values.append(f"'{val}'")
                elif j == 8:  # expiry_bucket TEXT field
                    values.append(f"'{val}'")
                elif val == '':  # Handle empty values
                    values.append("NULL")
                else:
                    values.append(val)
            
            # Create INSERT statement
            insert_sql = f"INSERT INTO nifty_option_chain VALUES ({', '.join(values)});"
            insert_statements.append(insert_sql)
        except Exception as e:
            print(f"Error processing row {i}, column {j if 'j' in locals() else 'unknown'}, value '{val if 'val' in locals() else 'unknown'}': {e}")
            # Skip this problematic row
            continue
    
    # Combine all inserts and execute
    all_inserts = "\n".join(insert_statements)
    
    # Retry logic
    for attempt in range(MAX_RETRIES):
        success = run_sql_command(all_inserts, timeout=timeout)
        
        if success:
            return True
        else:
            print(f"Batch {batch_num} failed on attempt {attempt+1}/{MAX_RETRIES}. Retrying...")
            time.sleep(2)  # Wait before retry
    
    print(f"Batch {batch_num} failed after {MAX_RETRIES} attempts.")
    return False

def verify_loaded_data():
    """Verify the data was loaded correctly"""
    # Get total count
    print("Verifying loaded data...")
    count_sql = "SELECT COUNT(*) FROM nifty_option_chain;"
    cmd = ["/opt/heavyai/bin/heavysql", "-s", "127.0.0.1", "--port", "6274", 
            "-u", "admin", "-p", "HyperInteractive", "-d", "heavyai", "-q", count_sql]
    
    try:
        process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
        stdout, stderr = process.communicate()
        if stderr and "Error" in stderr:
            print(f"Error verifying data: {stderr}")
            return
        
        # Parse the count
        count_line = [line for line in stdout.split('\n') if line and not line.startswith('User') and not line.startswith('Database')]
        if count_line:
            count = count_line[-1].strip()
            print(f"\nVerification: Total rows in nifty_option_chain: {count}")
    except Exception as e:
        print(f"Error during verification: {e}")

def main():
    args = parse_args()
    
    print("=== Nifty Option Chain Optimized Loader ===")
    print(f"Starting at: {datetime.now()}")
    print(f"Using batch size: {args.batch_size}")
    
    # Get list of files to process
    if args.file:
        csv_files = [os.path.join(CSV_DIR, args.file)]
        if not os.path.exists(csv_files[0]):
            print(f"Error: File {csv_files[0]} does not exist")
            sys.exit(1)
    else:
        csv_files = get_csv_files()
        
        # Apply start/end filters if provided
        if args.start_file > 0:
            if args.start_file >= len(csv_files):
                print(f"Error: Start file index {args.start_file} exceeds available files ({len(csv_files)})")
                sys.exit(1)
            csv_files = csv_files[args.start_file:]
            
        if args.end_file is not None:
            if args.end_file >= len(csv_files):
                print(f"Warning: End file index {args.end_file} exceeds available files, using all remaining files")
            else:
                csv_files = csv_files[:args.end_file - args.start_file + 1]
    
    print(f"Found {len(csv_files)} CSV files to process")
    for i, f in enumerate(csv_files):
        print(f"  {i+1}. {os.path.basename(f)}")
    
    # Ensure table exists unless we're skipping that step
    if not args.skip_existing and not ensure_table_exists():
        sys.exit(1)
    
    # Process each file
    total_inserted = 0
    start_time = datetime.now()
    
    for i, file_path in enumerate(csv_files):
        file_name = os.path.basename(file_path)
        print(f"[{i+1}/{len(csv_files)}] Processing file: {file_name}")
        file_inserted = process_csv_file(file_path, args.batch_size, args.limit, args.timeout)
        total_inserted += file_inserted
        
        # Show overall progress
        current_time = datetime.now()
        elapsed = current_time - start_time
        print(f"Overall progress: {i+1}/{len(csv_files)} files completed")
        print(f"Total rows inserted so far: {total_inserted}")
        print(f"Time elapsed: {elapsed}")
    
    # Verify data
    verify_loaded_data()
    
    end_time = datetime.now()
    duration = end_time - start_time
    print(f"\nData load completed at {end_time}")
    print(f"Total duration: {duration}")
    print(f"Total rows inserted: {total_inserted}")
    print(f"Average insertion rate: {total_inserted / duration.total_seconds():.2f} rows/second")

if __name__ == "__main__":
    main() 