#!/usr/bin/env python3
"""
Enhanced ETL Dashboard with connection pooling and API documentation
"""

import os
import sys
import json
import logging
from datetime import datetime, timedelta
from flask import Flask, render_template_string, jsonify, request
from flask_cors import CORS
import pandas as pd
from heavydb import connect
from contextlib import contextmanager
import threading
import time
from typing import Dict, List, Optional, Any
import requests
from urllib.parse import urlparse, unquote

# Add path for performance monitor
sys.path.append('/srv/samba/shared')
from etl_performance_monitor import ETLPerformanceMonitor

app = Flask(__name__)
CORS(app)

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Connection pool configuration
class ConnectionPool:
    def __init__(self, max_connections=5):
        self.max_connections = max_connections
        self.connections = []
        self.lock = threading.Lock()
        self._initialize_pool()
    
    def _initialize_pool(self):
        """Initialize connection pool"""
        for _ in range(self.max_connections):
            try:
                conn = connect(
                    host='localhost',
                    port=6274,
                    user='admin',
                    password='HyperInteractive',
                    dbname='heavyai'
                )
                self.connections.append(conn)
            except Exception as e:
                logger.error(f"Failed to create connection: {e}")
    
    @contextmanager
    def get_connection(self):
        """Get a connection from the pool"""
        conn = None
        with self.lock:
            if self.connections:
                conn = self.connections.pop()
        
        if conn is None:
            # Create new connection if pool is empty
            conn = connect(
                host='localhost',
                port=6274,
                user='admin',
                password='HyperInteractive',
                dbname='heavyai'
            )
        
        try:
            yield conn
        finally:
            # Return connection to pool
            with self.lock:
                if len(self.connections) < self.max_connections:
                    self.connections.append(conn)
                else:
                    conn.close()

# Initialize connection pool
conn_pool = ConnectionPool()

# Initialize performance monitor with connection pooling
class PooledETLPerformanceMonitor(ETLPerformanceMonitor):
    def __init__(self, pool):
        self.pool = pool
        self.metrics_table = "etl_performance_metrics"
        self.ensure_metrics_table()
    
    def connect_db(self):
        """Override to use connection pool"""
        pass
    
    def ensure_metrics_table(self):
        """Create metrics table if it doesn't exist"""
        with self.pool.get_connection() as conn:
            try:
                cursor = conn.cursor()
                cursor.execute(f"""
                    CREATE TABLE IF NOT EXISTS {self.metrics_table} (
                        metric_time TIMESTAMP,
                        index_name TEXT ENCODING DICT(32),
                        operation TEXT ENCODING DICT(32),
                        rows_processed BIGINT,
                        duration_seconds DOUBLE,
                        rows_per_second DOUBLE,
                        cpu_percent DOUBLE,
                        memory_mb DOUBLE,
                        gpu_utilization DOUBLE,
                        gpu_memory_mb DOUBLE,
                        disk_read_mb DOUBLE,
                        disk_write_mb DOUBLE,
                        query_count INTEGER,
                        error_count INTEGER,
                        status TEXT ENCODING DICT(32)
                    ) WITH (fragment_size=1000000);
                """)
                logger.info("Metrics table ready")
            except Exception as e:
                if "already exists" not in str(e):
                    logger.error(f"Failed to create metrics table: {e}")

monitor = PooledETLPerformanceMonitor(conn_pool)

# Dropbox file browser functionality
class DropboxBrowser:
    def __init__(self):
        self.base_url = "https://www.dropbox.com/scl/fo/gy85j8fe4qyff97urjf9j/AOpgTIMN9bWpgByhwOCcRNc"
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
    
    def list_files(self, path=""):
        """List files in Dropbox folder"""
        try:
            # This is a simplified version - in production you'd use Dropbox API
            # For now, return mock data structure
            return {
                'files': [
                    {
                        'name': '2025/06/04_NIFTY.csv',
                        'size': 1024000,
                        'modified': '2025-06-04T18:30:00Z',
                        'type': 'file',
                        'path': '/2025/06/04_NIFTY.csv'
                    },
                    {
                        'name': '2025/06/04_BANKNIFTY.csv',
                        'size': 2048000,
                        'modified': '2025-06-04T18:30:00Z',
                        'type': 'file',
                        'path': '/2025/06/04_BANKNIFTY.csv'
                    }
                ],
                'folders': [
                    {
                        'name': '2025',
                        'type': 'folder',
                        'path': '/2025'
                    }
                ]
            }
        except Exception as e:
            logger.error(f"Error listing Dropbox files: {e}")
            return {'files': [], 'folders': [], 'error': str(e)}

dropbox_browser = DropboxBrowser()

def format_number(num):
    """Format large numbers with K/M/B suffixes"""
    if num >= 1e9:
        return f"{num/1e9:.1f}B"
    elif num >= 1e6:
        return f"{num/1e6:.1f}M"
    elif num >= 1e3:
        return f"{num/1e3:.1f}K"
    else:
        return str(int(num))

# HTML template with API documentation
DASHBOARD_TEMPLATE = '''
<!DOCTYPE html>
<html>
<head>
    <title>ETL Dashboard - Enhanced</title>
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
            background-color: #f8f9fa;
        }
        .navbar {
            background-color: #2c3e50;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .metric-card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
            transition: transform 0.2s;
        }
        .metric-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .metric-value {
            font-size: 2.5em;
            font-weight: 600;
            color: #2c3e50;
        }
        .metric-label {
            color: #6c757d;
            font-size: 0.9em;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        .status-current { color: #27ae60; }
        .status-recent { color: #f39c12; }
        .status-stale { color: #e74c3c; }
        .status-nodata { color: #95a5a6; }
        .chart-container {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
            margin-bottom: 20px;
        }
        .api-doc {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-top: 30px;
        }
        .api-endpoint {
            background-color: #f8f9fa;
            border-left: 4px solid #007bff;
            padding: 10px 15px;
            margin-bottom: 10px;
            font-family: 'Courier New', monospace;
        }
        .file-browser {
            background: white;
            border-radius: 8px;
            padding: 20px;
            max-height: 400px;
            overflow-y: auto;
        }
        .file-item {
            padding: 8px 12px;
            border-bottom: 1px solid #eee;
            cursor: pointer;
            transition: background-color 0.2s;
        }
        .file-item:hover {
            background-color: #f8f9fa;
        }
        .loading-spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #3498db;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-dark">
        <div class="container-fluid">
            <span class="navbar-brand mb-0 h1">
                <i class="fas fa-chart-line"></i> ETL Performance Dashboard
            </span>
            <div class="text-white">
                <small>Auto-refresh: <span id="countdown">30</span>s | 
                Last update: <span id="last-update">-</span></small>
            </div>
        </div>
    </nav>
    
    <div class="container-fluid mt-4">
        <!-- Alerts -->
        <div id="alerts-container"></div>
        
        <!-- Data Freshness -->
        <h4 class="mb-3"><i class="fas fa-clock"></i> Data Freshness</h4>
        <div id="freshness-grid" class="row mb-4">
            <div class="col-12 text-center">
                <div class="loading-spinner"></div> Loading data freshness...
            </div>
        </div>
        
        <!-- Performance Metrics -->
        <h4 class="mb-3"><i class="fas fa-tachometer-alt"></i> Performance Metrics (Last 24 Hours)</h4>
        <div id="performance-grid" class="row mb-4">
            <div class="col-12 text-center">
                <div class="loading-spinner"></div> Loading performance metrics...
            </div>
        </div>
        
        <!-- Dropbox File Browser -->
        <div class="row mb-4">
            <div class="col-12">
                <h4 class="mb-3"><i class="fas fa-folder-open"></i> Dropbox File Browser</h4>
                <div class="file-browser">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <div id="current-path">/</div>
                        <button class="btn btn-sm btn-primary" onclick="refreshFiles()">
                            <i class="fas fa-sync"></i> Refresh
                        </button>
                    </div>
                    <div id="file-list">
                        <div class="text-center">
                            <div class="loading-spinner"></div> Loading files...
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Charts -->
        <div class="row">
            <div class="col-lg-6">
                <div class="chart-container">
                    <h5>Loading Speed Trend</h5>
                    <div id="speed-chart"></div>
                </div>
            </div>
            <div class="col-lg-6">
                <div class="chart-container">
                    <h5>System Resources</h5>
                    <div id="resource-chart"></div>
                </div>
            </div>
        </div>
        
        <!-- API Documentation -->
        <div class="api-doc">
            <h4><i class="fas fa-book"></i> API Documentation</h4>
            <p>All API endpoints return JSON responses. Base URL: <code>http://173.208.247.17:8001</code></p>
            
            <h5 class="mt-4">Dashboard APIs</h5>
            <div class="api-endpoint">GET /api/freshness</div>
            <p>Returns data freshness status for all indices</p>
            
            <div class="api-endpoint">GET /api/performance</div>
            <p>Returns performance metrics summary for the last 24 hours</p>
            
            <div class="api-endpoint">GET /api/alerts</div>
            <p>Returns current system alerts and warnings</p>
            
            <div class="api-endpoint">GET /api/report</div>
            <p>Returns comprehensive performance report</p>
            
            <h5 class="mt-4">Dropbox Integration APIs</h5>
            <div class="api-endpoint">GET /api/dropbox/files?path=/</div>
            <p>List files and folders in specified Dropbox path</p>
            
            <div class="api-endpoint">POST /api/etl/trigger</div>
            <p>Trigger ETL process for specific files. Body: <code>{"files": ["path1", "path2"], "index": "NIFTY"}</code></p>
            
            <div class="api-endpoint">GET /api/etl/status/:job_id</div>
            <p>Get status of running ETL job</p>
            
            <h5 class="mt-4">Chart APIs</h5>
            <div class="api-endpoint">GET /api/charts/speed</div>
            <p>Returns loading speed trend chart data</p>
            
            <div class="api-endpoint">GET /api/charts/resources</div>
            <p>Returns system resource usage chart data</p>
            
            <div class="api-endpoint">GET /api/charts/operations</div>
            <p>Returns operations by index chart data</p>
        </div>
    </div>
    
    <script>
        let countdown = 30;
        let currentPath = '/';
        
        function updateDashboard() {
            // Update freshness
            $.ajax({
                url: '/api/freshness',
                method: 'GET',
                timeout: 10000,
                success: function(data) {
                    $('#freshness-grid').html('');
                    data.forEach(function(index) {
                        const statusClass = 'status-' + index.status.toLowerCase().replace(' ', '');
                        const card = `
                            <div class="col-md-4 col-lg-2 mb-3">
                                <div class="metric-card">
                                    <div class="metric-value ${statusClass}">${index.status}</div>
                                    <div class="metric-label">${index.index_name}</div>
                                    <div class="mt-2 small text-muted">
                                        ${index.row_count > 0 ? 
                                            `${index.row_count_formatted} rows<br>
                                             Latest: ${index.latest_date}<br>
                                             ${index.days_behind} days behind` : 
                                            'No data loaded'}
                                    </div>
                                </div>
                            </div>
                        `;
                        $('#freshness-grid').append(card);
                    });
                },
                error: function(xhr, status, error) {
                    $('#freshness-grid').html('<div class="col-12 text-center text-danger">Error loading data freshness</div>');
                }
            });
            
            // Update performance metrics
            $.get('/api/performance', function(data) {
                $('#performance-grid').html('');
                Object.keys(data).forEach(function(key) {
                    const metric = data[key];
                    const card = `
                        <div class="col-md-6 col-lg-4 mb-3">
                            <div class="metric-card">
                                <div class="metric-value">${metric.value}</div>
                                <div class="metric-label">${metric.label}</div>
                                ${metric.detail ? `<div class="mt-2 small text-muted">${metric.detail}</div>` : ''}
                            </div>
                        </div>
                    `;
                    $('#performance-grid').append(card);
                });
            });
            
            // Update charts
            updateCharts();
            
            // Update alerts
            $.get('/api/alerts', function(data) {
                $('#alerts-container').html('');
                data.forEach(function(alert) {
                    const alertClass = alert.level === 'error' ? 'danger' : 'warning';
                    const alertDiv = `
                        <div class="alert alert-${alertClass} alert-dismissible fade show" role="alert">
                            <strong>${alert.type.replace(/_/g, ' ').toUpperCase()}:</strong> 
                            ${alert.message}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    `;
                    $('#alerts-container').append(alertDiv);
                });
            });
            
            // Update last update time
            $('#last-update').text(new Date().toLocaleTimeString());
            countdown = 30;
        }
        
        function updateCharts() {
            $.get('/api/charts/speed', function(data) {
                Plotly.newPlot('speed-chart', data.data, data.layout, {responsive: true});
            });
            
            $.get('/api/charts/resources', function(data) {
                Plotly.newPlot('resource-chart', data.data, data.layout, {responsive: true});
            });
        }
        
        function loadFiles(path) {
            currentPath = path || '/';
            $('#file-list').html('<div class="text-center"><div class="loading-spinner"></div> Loading files...</div>');
            
            $.get('/api/dropbox/files', {path: currentPath}, function(data) {
                $('#current-path').text(currentPath);
                $('#file-list').html('');
                
                // Add parent directory if not root
                if (currentPath !== '/') {
                    $('#file-list').append(`
                        <div class="file-item" onclick="loadFiles('${currentPath.split('/').slice(0, -1).join('/')}')">
                            <i class="fas fa-folder text-warning"></i> ..
                        </div>
                    `);
                }
                
                // Add folders
                data.folders.forEach(function(folder) {
                    $('#file-list').append(`
                        <div class="file-item" onclick="loadFiles('${folder.path}')">
                            <i class="fas fa-folder text-warning"></i> ${folder.name}
                        </div>
                    `);
                });
                
                // Add files
                data.files.forEach(function(file) {
                    $('#file-list').append(`
                        <div class="file-item" onclick="selectFile('${file.path}')">
                            <i class="fas fa-file-csv text-success"></i> ${file.name}
                            <span class="float-end text-muted small">${formatFileSize(file.size)}</span>
                        </div>
                    `);
                });
            }).fail(function() {
                $('#file-list').html('<div class="text-danger">Error loading files</div>');
            });
        }
        
        function formatFileSize(bytes) {
            if (bytes >= 1e9) return (bytes / 1e9).toFixed(1) + ' GB';
            if (bytes >= 1e6) return (bytes / 1e6).toFixed(1) + ' MB';
            if (bytes >= 1e3) return (bytes / 1e3).toFixed(1) + ' KB';
            return bytes + ' B';
        }
        
        function selectFile(path) {
            if (confirm('Download and process this file?')) {
                triggerETL([path]);
            }
        }
        
        function triggerETL(files) {
            $.post('/api/etl/trigger', JSON.stringify({files: files}), function(data) {
                alert('ETL process started. Job ID: ' + data.job_id);
            }).fail(function() {
                alert('Error starting ETL process');
            });
        }
        
        function refreshFiles() {
            loadFiles(currentPath);
        }
        
        // Initial load
        updateDashboard();
        loadFiles('/');
        
        // Auto-refresh
        setInterval(function() {
            countdown--;
            $('#countdown').text(countdown);
            if (countdown <= 0) {
                updateDashboard();
            }
        }, 1000);
    </script>
</body>
</html>
'''

@app.route('/')
def dashboard():
    """Main dashboard page"""
    return render_template_string(DASHBOARD_TEMPLATE)

@app.route('/api/freshness')
def api_freshness():
    """Get data freshness for all indices"""
    try:
        with conn_pool.get_connection() as conn:
            indices = ['NIFTY', 'BANKNIFTY', 'MIDCAPNIFTY', 'SENSEX', 'FINNIFTY', 'BANKEX']
            freshness_data = []
            
            cursor = conn.cursor()
            
            # Get all indices data at once
            cursor.execute("""
                SELECT 
                    index_name,
                    COUNT(*) as row_count,
                    MAX(trade_date) as latest_date,
                    MIN(trade_date) as earliest_date,
                    COUNT(DISTINCT trade_date) as trading_days
                FROM nifty_option_chain
                WHERE index_name IN ('NIFTY', 'BANKNIFTY', 'MIDCAPNIFTY', 'SENSEX', 'FINNIFTY', 'BANKEX')
                GROUP BY index_name
            """)
            
            results = cursor.fetchall()
            results_dict = {row[0]: row for row in results}
            
            for index in indices:
                if index in results_dict:
                    row = results_dict[index]
                    days_behind = (datetime.now().date() - row[2]).days if row[2] else -1
                    freshness_data.append({
                        'index_name': index,
                        'status': 'Current' if days_behind <= 1 else 
                                 'Recent' if days_behind <= 7 else 
                                 'Stale' if days_behind <= 30 else 'Outdated',
                        'row_count': int(row[1]),
                        'row_count_formatted': format_number(row[1]),
                        'latest_date': str(row[2]) if row[2] else 'N/A',
                        'days_behind': days_behind if days_behind >= 0 else 'N/A'
                    })
                else:
                    freshness_data.append({
                        'index_name': index,
                        'status': 'No Data',
                        'row_count': 0,
                        'row_count_formatted': '0',
                        'latest_date': 'N/A',
                        'days_behind': 'N/A'
                    })
            
            return jsonify(freshness_data)
            
    except Exception as e:
        logger.error(f"Error getting freshness: {e}")
        # Return error data
        indices = ['NIFTY', 'BANKNIFTY', 'MIDCAPNIFTY', 'SENSEX', 'FINNIFTY', 'BANKEX']
        return jsonify([{
            'index_name': idx,
            'status': 'Error',
            'row_count': 0,
            'row_count_formatted': '0',
            'latest_date': 'Error',
            'days_behind': 'N/A'
        } for idx in indices])

@app.route('/api/performance')
def api_performance():
    """Get performance metrics summary"""
    try:
        with conn_pool.get_connection() as conn:
            cursor = conn.cursor()
            
            # Check if metrics table exists
            cursor.execute("""
                SELECT COUNT(*) FROM information_schema.tables 
                WHERE table_name = 'etl_performance_metrics'
            """)
            
            if cursor.fetchone()[0] == 0:
                # Return default metrics if table doesn't exist
                return jsonify({
                    'total_operations': {'value': '0', 'label': 'Total Operations'},
                    'avg_speed': {'value': '0', 'label': 'Avg Speed (rows/sec)'},
                    'total_rows': {'value': '0', 'label': 'Total Rows Processed'},
                    'active_indices': {'value': '0', 'label': 'Active Indices'},
                    'system_status': {'value': 'Initializing', 'label': 'System Status', 'detail': 'Metrics table being created'}
                })
            
            # Get performance metrics
            cursor.execute("""
                SELECT 
                    COUNT(*) as operation_count,
                    SUM(rows_processed) as total_rows,
                    AVG(rows_per_second) as avg_speed,
                    MAX(rows_per_second) as max_speed,
                    COUNT(DISTINCT index_name) as active_indices,
                    SUM(CASE WHEN status != 'success' THEN 1 ELSE 0 END) as error_count
                FROM etl_performance_metrics
                WHERE metric_time >= NOW() - INTERVAL '24' HOUR
            """)
            
            result = cursor.fetchone()
            
            if result and result[0] > 0:
                return jsonify({
                    'total_operations': {
                        'value': str(int(result[0])),
                        'label': 'Total Operations',
                        'detail': 'Last 24 hours'
                    },
                    'avg_speed': {
                        'value': f"{result[2]:,.0f}" if result[2] else "0",
                        'label': 'Avg Speed (rows/sec)',
                        'detail': f"Peak: {result[3]:,.0f}" if result[3] else "No data"
                    },
                    'total_rows': {
                        'value': format_number(result[1]) if result[1] else "0",
                        'label': 'Total Rows Processed',
                        'detail': 'Last 24 hours'
                    },
                    'active_indices': {
                        'value': str(int(result[4])) if result[4] else "0",
                        'label': 'Active Indices'
                    },
                    'error_rate': {
                        'value': f"{(result[5] / result[0] * 100):.1f}%" if result[0] > 0 else "0%",
                        'label': 'Error Rate',
                        'detail': f"{result[5]} errors" if result[5] else "No errors"
                    }
                })
            else:
                return jsonify({
                    'total_operations': {'value': '0', 'label': 'Total Operations'},
                    'avg_speed': {'value': '0', 'label': 'Avg Speed (rows/sec)'},
                    'total_rows': {'value': '0', 'label': 'Total Rows Processed'},
                    'active_indices': {'value': '0', 'label': 'Active Indices'}
                })
                
    except Exception as e:
        logger.error(f"Error getting performance: {e}")
        return jsonify({
            'total_operations': {'value': '0', 'label': 'Total Operations'},
            'avg_speed': {'value': '0', 'label': 'Avg Speed (rows/sec)'},
            'total_rows': {'value': '0', 'label': 'Total Rows Processed'},
            'active_indices': {'value': '0', 'label': 'Active Indices'}
        })

@app.route('/api/charts/speed')
def api_chart_speed():
    """Get speed trend chart data"""
    try:
        # Return mock data for now
        return jsonify({
            'data': [{
                'x': ['2025-06-01', '2025-06-02', '2025-06-03', '2025-06-04'],
                'y': [500000, 520000, 510000, 530000],
                'type': 'scatter',
                'mode': 'lines+markers',
                'name': 'Loading Speed',
                'line': {'color': '#3498db', 'width': 2}
            }],
            'layout': {
                'xaxis': {'title': 'Date'},
                'yaxis': {'title': 'Rows per Second'},
                'showlegend': False,
                'margin': {'t': 20, 'b': 40, 'l': 60, 'r': 20},
                'height': 300
            }
        })
    except Exception as e:
        logger.error(f"Error generating speed chart: {e}")
        return jsonify({'data': [], 'layout': {}})

@app.route('/api/charts/resources')
def api_chart_resources():
    """Get resource usage chart data"""
    try:
        # Return mock data for now
        return jsonify({
            'data': [
                {
                    'x': ['CPU', 'Memory', 'GPU', 'Disk I/O'],
                    'y': [45, 62, 78, 35],
                    'type': 'bar',
                    'marker': {'color': ['#3498db', '#2ecc71', '#e74c3c', '#f39c12']}
                }
            ],
            'layout': {
                'xaxis': {'title': 'Resource'},
                'yaxis': {'title': 'Usage %'},
                'showlegend': False,
                'margin': {'t': 20, 'b': 40, 'l': 60, 'r': 20},
                'height': 300
            }
        })
    except Exception as e:
        logger.error(f"Error generating resource chart: {e}")
        return jsonify({'data': [], 'layout': {}})

@app.route('/api/charts/operations')
def api_chart_operations():
    """Get operations by index chart data"""
    try:
        # Return mock data for now
        return jsonify({
            'data': [{
                'x': ['NIFTY', 'BANKNIFTY', 'MIDCAPNIFTY', 'SENSEX'],
                'y': [150, 120, 80, 90],
                'type': 'bar',
                'marker': {'color': '#3498db'}
            }],
            'layout': {
                'xaxis': {'title': 'Index'},
                'yaxis': {'title': 'Operations Count'},
                'showlegend': False,
                'margin': {'t': 20, 'b': 40, 'l': 60, 'r': 20},
                'height': 300
            }
        })
    except Exception as e:
        logger.error(f"Error generating operations chart: {e}")
        return jsonify({'data': [], 'layout': {}})

@app.route('/api/alerts')
def api_alerts():
    """Get current alerts"""
    try:
        alerts = []
        
        # Check for connection issues
        with conn_pool.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT 1")
            
        return jsonify(alerts)
        
    except Exception as e:
        logger.error(f"Error getting alerts: {e}")
        return jsonify([{
            'level': 'warning',
            'type': 'connection_issue',
            'message': 'Database connection may be unstable'
        }])

@app.route('/api/report')
def api_report():
    """Get performance report"""
    try:
        report = monitor.generate_performance_report()
        return jsonify({'report': report})
    except Exception as e:
        logger.error(f"Error generating report: {e}")
        return jsonify({'report': 'Error generating report'})

@app.route('/api/dropbox/files')
def api_dropbox_files():
    """List files in Dropbox folder"""
    path = request.args.get('path', '/')
    result = dropbox_browser.list_files(path)
    return jsonify(result)

@app.route('/api/etl/trigger', methods=['POST'])
def api_etl_trigger():
    """Trigger ETL process for specific files"""
    data = request.get_json()
    files = data.get('files', [])
    index = data.get('index', 'AUTO')
    
    # Generate job ID
    job_id = f"etl_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    
    # In production, this would queue the job
    # For now, return mock response
    return jsonify({
        'job_id': job_id,
        'status': 'queued',
        'files': files,
        'index': index
    })

@app.route('/api/etl/status/<job_id>')
def api_etl_status(job_id):
    """Get ETL job status"""
    # In production, this would check actual job status
    # For now, return mock response
    return jsonify({
        'job_id': job_id,
        'status': 'running',
        'progress': 45,
        'message': 'Processing files...'
    })

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=8001, debug=False)