#!/usr/bin/env python3
"""
Excel to YAML Converter for Backtester
======================================

Automatic conversion of Excel strategy files to YAML format with comprehensive error handling.

Author: Senior Engineer
Date: June 9, 2025
"""

import pandas as pd
import yaml
import json
from pathlib import Path
from typing import Dict, Any, List, Tuple, Optional
import logging
from datetime import datetime

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ExcelToYamlConverter:
    """Convert Excel strategy files to YAML format with validation"""
    
    def __init__(self):
        self.supported_strategies = ['TBS', 'TV', 'ORB', 'OI', 'POS', 'ML']
        self.required_sheets = {
            'TBS': ['GeneralParameter', 'LegParameter'],
            'TV': ['config'],
            'ORB': ['ORBParameter'],
            'OI': ['OIParameter'],
            'POS': ['POSParameter', 'Greeks'],
            'ML': ['MLConfig', 'Indicators']
        }
        
    def validate_excel_file(self, file_path: str) -> Tuple[bool, str, Optional[str]]:
        """
        Validate Excel file structure and content
        
        Returns:
            Tuple of (is_valid, error_message, strategy_type)
        """
        try:
            # Check file exists
            path = Path(file_path)
            if not path.exists():
                return False, "File not found", None
                
            # Check file extension
            if path.suffix.lower() not in ['.xlsx', '.xls']:
                return False, "Invalid file format. Only .xlsx and .xls files are supported", None
                
            # Try to read Excel file
            try:
                excel_file = pd.ExcelFile(file_path)
            except Exception as e:
                return False, f"Cannot read Excel file: {str(e)}", None
                
            # Get sheet names
            sheet_names = excel_file.sheet_names
            
            # Detect strategy type
            strategy_type = self._detect_strategy_type(sheet_names)
            if not strategy_type:
                return False, f"Cannot detect strategy type. Available sheets: {', '.join(sheet_names)}", None
                
            # Validate required sheets
            required = self.required_sheets.get(strategy_type, [])
            missing = [sheet for sheet in required if sheet not in sheet_names]
            if missing:
                return False, f"Missing required sheets for {strategy_type}: {', '.join(missing)}", strategy_type
                
            # Validate sheet content
            for sheet in required:
                try:
                    df = pd.read_excel(file_path, sheet_name=sheet)
                    if df.empty:
                        return False, f"Sheet '{sheet}' is empty", strategy_type
                except Exception as e:
                    return False, f"Error reading sheet '{sheet}': {str(e)}", strategy_type
                    
            return True, "Valid", strategy_type
            
        except Exception as e:
            return False, f"Validation error: {str(e)}", None
            
    def _detect_strategy_type(self, sheet_names: List[str]) -> Optional[str]:
        """Detect strategy type from sheet names"""
        sheet_names_lower = [s.lower() for s in sheet_names]
        
        if 'generalparameter' in sheet_names_lower and 'legparameter' in sheet_names_lower:
            return 'TBS'
        elif 'config' in sheet_names_lower:
            return 'TV'
        elif 'orbparameter' in sheet_names_lower:
            return 'ORB'
        elif 'oiparameter' in sheet_names_lower:
            return 'OI'
        elif 'posparameter' in sheet_names_lower:
            return 'POS'
        elif 'mlconfig' in sheet_names_lower:
            return 'ML'
            
        return None
        
    def convert_to_yaml(self, excel_path: str, output_dir: str = None) -> Tuple[bool, str, Optional[str]]:
        """
        Convert Excel file to YAML format
        
        Returns:
            Tuple of (success, message, yaml_path)
        """
        # Validate first
        is_valid, error_msg, strategy_type = self.validate_excel_file(excel_path)
        if not is_valid:
            return False, error_msg, None
            
        try:
            # Prepare output directory
            if output_dir:
                output_path = Path(output_dir)
            else:
                output_path = Path(excel_path).parent / 'yaml_configs'
            output_path.mkdir(exist_ok=True)
            
            # Generate output filename
            base_name = Path(excel_path).stem
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            yaml_filename = f"{base_name}_{strategy_type}_{timestamp}.yaml"
            yaml_path = output_path / yaml_filename
            
            # Convert based on strategy type
            yaml_data = {}
            
            if strategy_type == 'TBS':
                yaml_data = self._convert_tbs(excel_path)
            elif strategy_type == 'TV':
                yaml_data = self._convert_tv(excel_path)
            elif strategy_type == 'ORB':
                yaml_data = self._convert_orb(excel_path)
            elif strategy_type == 'OI':
                yaml_data = self._convert_oi(excel_path)
            elif strategy_type == 'POS':
                yaml_data = self._convert_pos(excel_path)
            elif strategy_type == 'ML':
                yaml_data = self._convert_ml(excel_path)
                
            # Add metadata
            yaml_data['metadata'] = {
                'source_file': str(Path(excel_path).name),
                'strategy_type': strategy_type,
                'converted_at': datetime.now().isoformat(),
                'converter_version': '2.0'
            }
            
            # Write YAML file
            with open(yaml_path, 'w') as f:
                yaml.dump(yaml_data, f, default_flow_style=False, sort_keys=False)
                
            logger.info(f"Successfully converted to: {yaml_path}")
            return True, "Conversion successful", str(yaml_path)
            
        except Exception as e:
            error_msg = f"Conversion error: {str(e)}"
            logger.error(error_msg)
            return False, error_msg, None
            
    def _convert_tbs(self, excel_path: str) -> Dict[str, Any]:
        """Convert TBS Excel to YAML structure"""
        yaml_data = {'strategy_type': 'TBS'}
        
        # Read GeneralParameter
        general_df = pd.read_excel(excel_path, sheet_name='GeneralParameter')
        general_params = {}
        for _, row in general_df.iterrows():
            param_name = row.get('Parameter', row.iloc[0])
            param_value = row.get('Value', row.iloc[1])
            general_params[self._clean_key(param_name)] = self._clean_value(param_value)
        yaml_data['general_parameters'] = general_params
        
        # Read LegParameter
        leg_df = pd.read_excel(excel_path, sheet_name='LegParameter')
        legs = []
        for _, row in leg_df.iterrows():
            leg = {}
            for col in leg_df.columns:
                leg[self._clean_key(col)] = self._clean_value(row[col])
            legs.append(leg)
        yaml_data['legs'] = legs
        
        return yaml_data
        
    def _convert_tv(self, excel_path: str) -> Dict[str, Any]:
        """Convert TV Excel to YAML structure"""
        yaml_data = {'strategy_type': 'TV'}
        
        # Read config sheet
        config_df = pd.read_excel(excel_path, sheet_name='config')
        config = {}
        for col in config_df.columns:
            values = config_df[col].dropna().tolist()
            if len(values) == 1:
                config[self._clean_key(col)] = self._clean_value(values[0])
            elif len(values) > 1:
                config[self._clean_key(col)] = [self._clean_value(v) for v in values]
        yaml_data['config'] = config
        
        # Check for signal files
        if 'signal_files' in config_df.columns:
            yaml_data['signal_files'] = config_df['signal_files'].dropna().tolist()
            
        return yaml_data
        
    def _convert_orb(self, excel_path: str) -> Dict[str, Any]:
        """Convert ORB Excel to YAML structure"""
        yaml_data = {'strategy_type': 'ORB'}
        
        # Read ORBParameter
        orb_df = pd.read_excel(excel_path, sheet_name='ORBParameter')
        orb_params = {}
        for col in orb_df.columns:
            values = orb_df[col].dropna().tolist()
            if values:
                orb_params[self._clean_key(col)] = self._clean_value(values[0]) if len(values) == 1 else values
        yaml_data['orb_parameters'] = orb_params
        
        return yaml_data
        
    def _convert_oi(self, excel_path: str) -> Dict[str, Any]:
        """Convert OI Excel to YAML structure"""
        yaml_data = {'strategy_type': 'OI'}
        
        # Read OIParameter
        oi_df = pd.read_excel(excel_path, sheet_name='OIParameter')
        oi_params = {}
        for col in oi_df.columns:
            values = oi_df[col].dropna().tolist()
            if values:
                oi_params[self._clean_key(col)] = self._clean_value(values[0]) if len(values) == 1 else values
        yaml_data['oi_parameters'] = oi_params
        
        return yaml_data
        
    def _convert_pos(self, excel_path: str) -> Dict[str, Any]:
        """Convert POS Excel to YAML structure"""
        yaml_data = {'strategy_type': 'POS'}
        
        # Read POSParameter
        pos_df = pd.read_excel(excel_path, sheet_name='POSParameter')
        pos_params = {}
        for col in pos_df.columns:
            values = pos_df[col].dropna().tolist()
            if values:
                pos_params[self._clean_key(col)] = self._clean_value(values[0]) if len(values) == 1 else values
        yaml_data['pos_parameters'] = pos_params
        
        # Read Greeks if available
        if 'Greeks' in pd.ExcelFile(excel_path).sheet_names:
            greeks_df = pd.read_excel(excel_path, sheet_name='Greeks')
            greeks = []
            for _, row in greeks_df.iterrows():
                greek = {}
                for col in greeks_df.columns:
                    greek[self._clean_key(col)] = self._clean_value(row[col])
                greeks.append(greek)
            yaml_data['greeks'] = greeks
            
        return yaml_data
        
    def _convert_ml(self, excel_path: str) -> Dict[str, Any]:
        """Convert ML Excel to YAML structure"""
        yaml_data = {'strategy_type': 'ML'}
        
        # Read MLConfig
        ml_df = pd.read_excel(excel_path, sheet_name='MLConfig')
        ml_config = {}
        for col in ml_df.columns:
            values = ml_df[col].dropna().tolist()
            if values:
                ml_config[self._clean_key(col)] = self._clean_value(values[0]) if len(values) == 1 else values
        yaml_data['ml_config'] = ml_config
        
        # Read Indicators
        if 'Indicators' in pd.ExcelFile(excel_path).sheet_names:
            indicators_df = pd.read_excel(excel_path, sheet_name='Indicators')
            indicators = []
            for _, row in indicators_df.iterrows():
                indicator = {}
                for col in indicators_df.columns:
                    indicator[self._clean_key(col)] = self._clean_value(row[col])
                indicators.append(indicator)
            yaml_data['indicators'] = indicators
            
        return yaml_data
        
    def _clean_key(self, key: Any) -> str:
        """Clean and standardize key names"""
        if pd.isna(key):
            return 'unknown'
        key_str = str(key).strip()
        # Convert to snake_case
        key_str = key_str.replace(' ', '_').replace('-', '_').lower()
        # Remove special characters
        key_str = ''.join(c for c in key_str if c.isalnum() or c == '_')
        return key_str
        
    def _clean_value(self, value: Any) -> Any:
        """Clean and convert values to appropriate types"""
        if pd.isna(value):
            return None
        if isinstance(value, pd.Timestamp):
            return value.strftime('%Y-%m-%d %H:%M:%S')
        if isinstance(value, (pd.Timedelta, pd.Period)):
            return str(value)
        if isinstance(value, bool):
            return value
        if isinstance(value, (int, float)):
            if pd.isna(value):
                return None
            return int(value) if value == int(value) else float(value)
        # Convert string booleans
        value_str = str(value).strip().upper()
        if value_str in ['TRUE', 'YES', '1']:
            return True
        if value_str in ['FALSE', 'NO', '0']:
            return False
        return str(value).strip()


def create_ui_error_handler():
    """Create error handler for UI integration"""
    
    error_handler_code = '''
# UI Error Handler for Excel Upload
# Add this to the FastAPI endpoint that handles file uploads

from fastapi import UploadFile, HTTPException
from excel_to_yaml_converter import ExcelToYamlConverter
import tempfile
import os

async def handle_excel_upload(file: UploadFile):
    """Handle Excel file upload with validation and conversion"""
    
    # Validate file type
    if not file.filename.endswith(('.xlsx', '.xls')):
        raise HTTPException(
            status_code=400,
            detail="Invalid file type. Only Excel files (.xlsx, .xls) are supported."
        )
    
    # Save temporary file
    with tempfile.NamedTemporaryFile(delete=False, suffix=Path(file.filename).suffix) as tmp:
        content = await file.read()
        tmp.write(content)
        tmp_path = tmp.name
    
    try:
        # Initialize converter
        converter = ExcelToYamlConverter()
        
        # Validate Excel structure
        is_valid, error_msg, strategy_type = converter.validate_excel_file(tmp_path)
        if not is_valid:
            raise HTTPException(
                status_code=400,
                detail=f"Excel validation failed: {error_msg}"
            )
        
        # Convert to YAML
        success, message, yaml_path = converter.convert_to_yaml(tmp_path)
        if not success:
            raise HTTPException(
                status_code=500,
                detail=f"Conversion failed: {message}"
            )
        
        # Return success response
        return {
            "status": "success",
            "message": "File successfully converted to YAML",
            "strategy_type": strategy_type,
            "yaml_path": yaml_path,
            "original_filename": file.filename
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Unexpected error: {str(e)}"
        )
    finally:
        # Clean up temporary file
        if os.path.exists(tmp_path):
            os.unlink(tmp_path)
'''
    
    # Save the error handler code
    handler_path = Path("/srv/samba/shared/ui_excel_error_handler.py")
    handler_path.write_text(error_handler_code)
    
    return str(handler_path)


# Create standalone test function
def test_converter():
    """Test the converter with sample files"""
    converter = ExcelToYamlConverter()
    
    # Test with TBS file
    test_file = "/srv/samba/shared/test_results/tbs/inputs/test_tbs_strategy_basic.xlsx"
    if Path(test_file).exists():
        print(f"\nTesting with: {test_file}")
        is_valid, error_msg, strategy_type = converter.validate_excel_file(test_file)
        print(f"Validation: {is_valid}, Type: {strategy_type}, Message: {error_msg}")
        
        if is_valid:
            success, message, yaml_path = converter.convert_to_yaml(test_file)
            print(f"Conversion: {success}, Message: {message}")
            if success:
                print(f"YAML saved to: {yaml_path}")


if __name__ == "__main__":
    # Run test
    test_converter()
    
    # Create UI error handler
    handler_path = create_ui_error_handler()
    print(f"\nUI error handler created: {handler_path}")