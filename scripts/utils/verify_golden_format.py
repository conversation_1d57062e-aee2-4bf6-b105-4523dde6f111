#!/usr/bin/env python3
"""
Verify output matches golden file format exactly
"""

import pandas as pd
import numpy as np

def verify_golden_format():
    print('='*80)
    print('DETAILED GOLDEN FILE vs OUTPUT COMPARISON')
    print('='*80)

    golden_file = 'Nifty_Golden_Ouput.xlsx'
    output_file = 'optimized_golden_test.xlsx'

    # Read both files
    golden_xl = pd.ExcelFile(golden_file)
    output_xl = pd.ExcelFile(output_file)

    print(f'\nGolden file sheets: {golden_xl.sheet_names}')
    print(f'Output file sheets: {output_xl.sheet_names}')

    # 1. Check PORTFOLIO Trans columns
    print('\n1. PORTFOLIO Trans Sheet Comparison:')
    print('-'*60)
    golden_trans = pd.read_excel(golden_file, sheet_name='PORTFOLIO Trans')
    output_trans = pd.read_excel(output_file, sheet_name='PORTFOLIO Trans')

    print(f'Golden columns ({len(golden_trans.columns)}): {list(golden_trans.columns)}')
    print(f'\nOutput columns ({len(output_trans.columns)}): {list(output_trans.columns)}')

    missing_cols = set(golden_trans.columns) - set(output_trans.columns)
    extra_cols = set(output_trans.columns) - set(golden_trans.columns)

    if missing_cols:
        print(f'\n❌ Missing columns: {missing_cols}')
    else:
        print('\n✅ All columns present!')

    if extra_cols:
        print(f'⚠️  Extra columns: {extra_cols}')

    # 2. Check Max Profit and Loss sheet
    print('\n\n2. Max Profit and Loss Sheet:')
    print('-'*60)
    golden_maxpl = pd.read_excel(golden_file, sheet_name='Max Profit and Loss')
    output_maxpl = pd.read_excel(output_file, sheet_name='Max Profit and Loss')

    print(f'Golden columns: {list(golden_maxpl.columns)}')
    print(f'Output columns: {list(output_maxpl.columns)}')

    print(f'\nGolden data sample:')
    print(golden_maxpl.head())

    print(f'\nOutput data sample:')
    print(output_maxpl.head())

    # 3. Check if slippage is included in calculations
    print('\n\n3. Slippage Analysis:')
    print('-'*60)
    
    # Check slippage columns
    slippage_cols = ['Points After Slippage', 'AfterSlippage']
    for col in slippage_cols:
        if col in output_trans.columns:
            print(f'✅ {col} column exists')
        else:
            print(f'❌ {col} column missing')
    
    # Check if slippage is actually applied
    if 'Points' in output_trans.columns and 'Points After Slippage' in output_trans.columns:
        slippage_diff = output_trans['Points'] - output_trans['Points After Slippage']
        non_zero_mask = slippage_diff != 0
        non_zero = slippage_diff[non_zero_mask]
        
        if len(non_zero) > 0:
            print(f'\n✅ Slippage is being applied to {len(non_zero)} trades:')
            print(non_zero.head())
        else:
            print('\n⚠️  No slippage difference found - check if slippage is being applied')

    # 4. Check MaxProfit and MaxLoss columns
    print('\n\n4. MaxProfit/MaxLoss in Transactions:')
    print('-'*60)
    if 'MaxProfit' in output_trans.columns and 'MaxLoss' in output_trans.columns:
        print('✅ MaxProfit and MaxLoss columns exist in transactions')
        print(f'\nSample MaxProfit values:')
        print(output_trans['MaxProfit'].head())
        print(f'\nSample MaxLoss values:')
        print(output_trans['MaxLoss'].head())
        
        # Check if values include slippage
        if 'AfterSlippage' in output_trans.columns:
            print('\n⚠️  Need to verify MaxProfit/MaxLoss include slippage impact')
            print('   (MaxProfit/MaxLoss should be calculated AFTER slippage)')
    else:
        print('❌ MaxProfit/MaxLoss columns missing in transactions')

    # 5. Check specific column issues
    print('\n\n5. Column Name Exact Match Check:')
    print('-'*60)
    
    # Map of potentially problematic columns
    column_checks = {
        'Exit at.1': 'Exit at column with .1 suffix',
        'Exit at': 'Exit at column without suffix',
        'Entry at': 'Entry price column',
        'Qty': 'Quantity column',
        'CE/PE': 'Option type column',
        'Trade': 'Buy/Sell column'
    }
    
    for col, desc in column_checks.items():
        in_golden = col in golden_trans.columns
        in_output = col in output_trans.columns
        
        if in_golden and in_output:
            print(f'✅ {col}: Present in both')
        elif in_golden and not in_output:
            print(f'❌ {col}: In golden but NOT in output ({desc})')
        elif not in_golden and in_output:
            print(f'⚠️  {col}: In output but NOT in golden ({desc})')

    # 6. Check data types match
    print('\n\n6. Data Type Verification:')
    print('-'*60)
    
    common_cols = set(golden_trans.columns) & set(output_trans.columns)
    type_mismatches = []
    
    for col in common_cols:
        golden_type = golden_trans[col].dtype
        output_type = output_trans[col].dtype
        
        if golden_type != output_type:
            type_mismatches.append({
                'column': col,
                'golden_type': str(golden_type),
                'output_type': str(output_type)
            })
    
    if type_mismatches:
        print('❌ Data type mismatches found:')
        for mismatch in type_mismatches:
            print(f"   {mismatch['column']}: Golden={mismatch['golden_type']}, Output={mismatch['output_type']}")
    else:
        print('✅ All data types match')

    # 7. Summary
    print('\n\n7. SUMMARY:')
    print('='*60)
    
    issues = []
    
    if missing_cols:
        issues.append(f'Missing {len(missing_cols)} columns from golden format')
    
    if 'Exit at.1' in golden_trans.columns and 'Exit at.1' not in output_trans.columns:
        issues.append('Exit at.1 column name mismatch')
    
    if 'MaxProfit' not in output_trans.columns or 'MaxLoss' not in output_trans.columns:
        issues.append('MaxProfit/MaxLoss columns missing')
    
    if issues:
        print('❌ Issues to fix:')
        for issue in issues:
            print(f'   - {issue}')
    else:
        print('✅ Output matches golden format perfectly!')

if __name__ == "__main__":
    verify_golden_format()