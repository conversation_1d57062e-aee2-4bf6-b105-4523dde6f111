import pandas as pd

# Generate dates
start_date = '2019-01-01'
end_date = '2026-12-31'
all_dates = pd.date_range(start=start_date, end=end_date, freq='D')

# Create DataFrame
df = pd.DataFrame({'cal_date': all_dates})

# Save to CSV
output_file = 'all_dates_2019_2026.csv'
df.to_csv(output_file, index=False, header=False, date_format='%Y-%m-%d')

print(f'Generated {len(df)} dates and saved to {output_file}') 