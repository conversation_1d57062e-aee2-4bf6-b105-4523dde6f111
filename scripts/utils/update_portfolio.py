import pandas as pd
import os

# Paths
original_portfolio = "/srv/samba/shared/input_portfolio.xlsx"
updated_portfolio = "/srv/samba/shared/input_portfolio_fixed.xlsx"

# Read the StrategySetting sheet
strategy_settings = pd.read_excel(original_portfolio, sheet_name="StrategySetting")

# Find the enabled strategy (YES in the Enabled column)
enabled_strategies = strategy_settings[strategy_settings["Enabled"] == "YES"]

if not enabled_strategies.empty:
    print(f"Found {len(enabled_strategies)} enabled strategies")
    
    # Update the StrategyExcelFilePath for each enabled strategy
    for idx, row in enabled_strategies.iterrows():
        # Get the current strategy path
        current_path = row["StrategyExcelFilePath"]
        print(f"Current strategy path: {current_path}")
        
        # Create updated path pointing to our new strategy file
        updated_path = "/srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/input_tbs_fixed_exits.xlsx"
        
        # Update the path in the DataFrame
        strategy_settings.at[idx, "StrategyExcelFilePath"] = updated_path
        print(f"Updated to: {updated_path}")
else:
    print("No enabled strategies found in portfolio file")

# Now write the updated StrategySetting sheet back to a new file
with pd.ExcelWriter(updated_portfolio, engine="openpyxl") as writer:
    # Copy all sheets from the original file
    xls = pd.ExcelFile(original_portfolio)
    for sheet_name in xls.sheet_names:
        if sheet_name == "StrategySetting":
            # Use our updated StrategySetting sheet
            strategy_settings.to_excel(writer, sheet_name="StrategySetting", index=False)
        else:
            # Copy other sheets as-is
            sheet_df = pd.read_excel(original_portfolio, sheet_name=sheet_name)
            sheet_df.to_excel(writer, sheet_name=sheet_name, index=False)

print(f"\nUpdated portfolio file created: {updated_portfolio}")
print("The updated portfolio file now points to the new strategy file with fixed SL/TP values.")
print("\nTo run the backtest with the updated files, use:")
print(f"python3 bt/backtester_stable/BTRUN/BTRunPortfolio_GPU.py --portfolio-excel {updated_portfolio} --output-path /srv/samba/shared/Trades/Fixed_Exit_Test.xlsx") 