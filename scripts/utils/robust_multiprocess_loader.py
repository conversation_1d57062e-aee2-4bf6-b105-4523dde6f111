#!/usr/bin/env python3
"""
Robust multiprocess bulk loader for Nifty Option Chain data
Simplified and more reliable version
"""

import os
import sys
import glob
import pandas as pd
import numpy as np
from datetime import datetime
import logging
import time
import multiprocessing
from concurrent.futures import ProcessPoolExecutor, as_completed
import traceback

sys.path.append('/srv/samba/shared')

from bt.dal.heavydb_conn import get_conn

# Configure logging with immediate flush
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - [%(processName)s] - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

# Force unbuffered output
sys.stdout = os.fdopen(sys.stdout.fileno(), 'w', 1)
sys.stderr = os.fdopen(sys.stderr.fileno(), 'w', 1)

# Configuration
DEFAULT_BATCH_SIZE = 10000  # Smaller batch size for better progress tracking
DEFAULT_WORKERS = 12  # Fewer workers to avoid overwhelming the system

def prepare_value(val, col_type='str'):
    """Prepare value for SQL insert"""
    if pd.isna(val) or val is None or str(val).strip() == '':
        return 'NULL'
    elif col_type == 'str':
        val_str = str(val).replace("'", "''")
        return f"'{val_str}'"
    elif col_type in ['date', 'time']:
        return f"'{val}'"
    else:  # numeric
        try:
            # Ensure it's a valid number
            float(val)
            return str(val)
        except:
            return 'NULL'

def process_file_worker(args):
    """Process a single CSV file - worker function"""
    file_path, worker_id, batch_size = args
    file_name = os.path.basename(file_path)
    
    logger.info(f"Worker {worker_id}: Starting processing of {file_name}")
    
    try:
        # Connect to database
        conn = get_conn()
        cursor = conn.cursor()
        
        # Read the entire file
        logger.info(f"Worker {worker_id}: Reading {file_name}")
        df = pd.read_csv(file_path)
        total_rows = len(df)
        logger.info(f"Worker {worker_id}: File {file_name} has {total_rows:,} rows")
        
        # Process dates
        df['trade_date'] = pd.to_datetime(df['trade_date']).dt.strftime('%Y-%m-%d')
        df['trade_time'] = pd.to_datetime(df['trade_time'], format='%H:%M:%S').dt.strftime('%H:%M:%S')
        df['expiry_date'] = pd.to_datetime(df['expiry_date']).dt.strftime('%Y-%m-%d')
        
        rows_processed = 0
        batch_num = 0
        
        # Process in batches
        for batch_start in range(0, total_rows, batch_size):
            batch_end = min(batch_start + batch_size, total_rows)
            batch_df = df.iloc[batch_start:batch_end]
            
            values_list = []
            for _, row in batch_df.iterrows():
                values = []
                
                # Build values in exact column order
                values.append(prepare_value(row['trade_date'], 'date'))
                values.append(prepare_value(row['trade_time'], 'time'))
                values.append(prepare_value(row['expiry_date'], 'date'))
                values.append(prepare_value(row['index_name'], 'str'))
                
                # Numeric columns
                for col in ['spot', 'atm_strike', 'strike', 'dte']:
                    values.append(prepare_value(row[col], 'num'))
                
                # Text/ID columns
                values.append(prepare_value(row['expiry_bucket'], 'str'))
                values.append(prepare_value(row['zone_id'], 'num'))
                values.append(prepare_value(row['zone_name'], 'str'))
                values.append(prepare_value(row['call_strike_type'], 'str'))
                values.append(prepare_value(row['put_strike_type'], 'str'))
                
                # Option columns
                option_cols = ['ce_symbol', 'ce_open', 'ce_high', 'ce_low', 'ce_close', 'ce_volume', 
                              'ce_oi', 'ce_coi', 'ce_iv', 'ce_delta', 'ce_gamma', 'ce_theta', 
                              'ce_vega', 'ce_rho', 'pe_symbol', 'pe_open', 'pe_high', 'pe_low', 
                              'pe_close', 'pe_volume', 'pe_oi', 'pe_coi', 'pe_iv', 'pe_delta', 
                              'pe_gamma', 'pe_theta', 'pe_vega', 'pe_rho']
                
                for col in option_cols:
                    if col in ['ce_symbol', 'pe_symbol']:
                        values.append(prepare_value(row[col], 'str'))
                    else:
                        values.append(prepare_value(row[col], 'num'))
                
                # Future columns
                for col in ['future_open', 'future_high', 'future_low', 'future_close',
                           'future_volume', 'future_oi', 'future_coi']:
                    values.append(prepare_value(row[col], 'num'))
                
                values_list.append(f"({','.join(values)})")
            
            # Execute batch insert
            if values_list:
                insert_sql = f"""
                INSERT INTO nifty_option_chain (
                    trade_date, trade_time, expiry_date, index_name,
                    spot, atm_strike, strike, dte, expiry_bucket,
                    zone_id, zone_name, call_strike_type, put_strike_type,
                    ce_symbol, ce_open, ce_high, ce_low, ce_close,
                    ce_volume, ce_oi, ce_coi, ce_iv, ce_delta,
                    ce_gamma, ce_theta, ce_vega, ce_rho,
                    pe_symbol, pe_open, pe_high, pe_low, pe_close,
                    pe_volume, pe_oi, pe_coi, pe_iv, pe_delta,
                    pe_gamma, pe_theta, pe_vega, pe_rho,
                    future_open, future_high, future_low, future_close,
                    future_volume, future_oi, future_coi
                ) VALUES {','.join(values_list)}
                """
                
                try:
                    cursor.execute(insert_sql)
                    rows_processed += len(values_list)
                    batch_num += 1
                    
                    progress = (rows_processed / total_rows) * 100
                    logger.info(f"Worker {worker_id}: {file_name} - Batch {batch_num} inserted {len(values_list)} rows. Progress: {rows_processed:,}/{total_rows:,} ({progress:.1f}%)")
                    
                except Exception as e:
                    logger.error(f"Worker {worker_id}: Error inserting batch {batch_num} from {file_name}: {e}")
                    logger.error(f"Worker {worker_id}: First few values: {values_list[0] if values_list else 'None'}")
                    raise
        
        cursor.close()
        conn.close()
        
        logger.info(f"Worker {worker_id}: COMPLETED {file_name} - Total rows processed: {rows_processed:,}")
        return file_name, rows_processed, True
        
    except Exception as e:
        logger.error(f"Worker {worker_id}: FAILED processing {file_name}: {e}")
        logger.error(f"Worker {worker_id}: Traceback: {traceback.format_exc()}")
        return file_name, 0, False

def main():
    import argparse
    
    parser = argparse.ArgumentParser(description='Robust multiprocess bulk loader')
    parser.add_argument('--data-dir', default='/srv/samba/shared/market_data/nifty/oc_with_futures',
                        help='Directory containing CSV files')
    parser.add_argument('--batch-size', type=int, default=DEFAULT_BATCH_SIZE,
                        help='Batch size for inserts')
    parser.add_argument('--workers', type=int, default=DEFAULT_WORKERS,
                        help='Number of parallel workers')
    parser.add_argument('--pattern', default='*.csv', help='File pattern to match')
    parser.add_argument('--skip-sorted', action='store_true', 
                        help='Skip _sorted.csv files')
    
    args = parser.parse_args()
    
    start_time = time.time()
    
    logger.info("=== Starting Robust Multiprocess Bulk Loader ===")
    logger.info(f"Workers: {args.workers}")
    logger.info(f"Batch size: {args.batch_size}")
    
    # Get CSV files
    csv_files = sorted(glob.glob(os.path.join(args.data_dir, args.pattern)))
    
    if args.skip_sorted:
        csv_files = [f for f in csv_files if not f.endswith('_sorted.csv')]
    
    if not csv_files:
        logger.error("No CSV files found!")
        return
    
    logger.info(f"Found {len(csv_files)} CSV files to process")
    
    # Check initial row count
    try:
        conn = get_conn()
        cursor = conn.cursor()
        cursor.execute("SELECT COUNT(*) FROM nifty_option_chain")
        initial_count = cursor.fetchone()[0]
        logger.info(f"Starting with {initial_count:,} rows in table")
        cursor.close()
        conn.close()
    except Exception as e:
        logger.error(f"Error checking initial count: {e}")
        return
    
    # Create work items
    work_items = [(csv_file, i % args.workers, args.batch_size) 
                  for i, csv_file in enumerate(csv_files)]
    
    # Process files in parallel
    success_count = 0
    failed_count = 0
    total_rows_loaded = 0
    
    with ProcessPoolExecutor(max_workers=args.workers) as executor:
        # Submit all tasks
        future_to_file = {executor.submit(process_file_worker, item): item[0] 
                          for item in work_items}
        
        # Process results as they complete
        for future in as_completed(future_to_file):
            file_name, rows_loaded, success = future.result()
            
            if success:
                success_count += 1
                total_rows_loaded += rows_loaded
                logger.info(f"✓ Completed: {os.path.basename(file_name)} ({rows_loaded:,} rows)")
            else:
                failed_count += 1
                logger.error(f"✗ Failed: {os.path.basename(file_name)}")
            
            # Progress update
            completed = success_count + failed_count
            logger.info(f"Overall progress: {completed}/{len(csv_files)} files completed")
    
    # Final verification
    try:
        conn = get_conn()
        cursor = conn.cursor()
        cursor.execute("SELECT COUNT(*) FROM nifty_option_chain")
        final_count = cursor.fetchone()[0]
        cursor.close()
        conn.close()
    except Exception as e:
        logger.error(f"Error checking final count: {e}")
        final_count = initial_count
    
    elapsed_time = time.time() - start_time
    
    logger.info("\n=== Loading Complete ===")
    logger.info(f"Total files processed: {len(csv_files)}")
    logger.info(f"Successful: {success_count}")
    logger.info(f"Failed: {failed_count}")
    logger.info(f"Total rows loaded in session: {total_rows_loaded:,}")
    logger.info(f"Initial row count: {initial_count:,}")
    logger.info(f"Final row count: {final_count:,}")
    logger.info(f"Net rows added: {final_count - initial_count:,}")
    logger.info(f"Total time: {elapsed_time:.1f} seconds")
    logger.info(f"Average rate: {total_rows_loaded / elapsed_time:.0f} rows/second")

if __name__ == "__main__":
    main()