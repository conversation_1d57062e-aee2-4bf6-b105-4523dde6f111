
# UI Error Handler for Excel Upload
# Add this to the FastAPI endpoint that handles file uploads

from fastapi import UploadFile, HTTPException
from excel_to_yaml_converter import ExcelToYamlConverter
import tempfile
import os

async def handle_excel_upload(file: UploadFile):
    """Handle Excel file upload with validation and conversion"""
    
    # Validate file type
    if not file.filename.endswith(('.xlsx', '.xls')):
        raise HTTPException(
            status_code=400,
            detail="Invalid file type. Only Excel files (.xlsx, .xls) are supported."
        )
    
    # Save temporary file
    with tempfile.NamedTemporaryFile(delete=False, suffix=Path(file.filename).suffix) as tmp:
        content = await file.read()
        tmp.write(content)
        tmp_path = tmp.name
    
    try:
        # Initialize converter
        converter = ExcelToYamlConverter()
        
        # Validate Excel structure
        is_valid, error_msg, strategy_type = converter.validate_excel_file(tmp_path)
        if not is_valid:
            raise HTTPException(
                status_code=400,
                detail=f"Excel validation failed: {error_msg}"
            )
        
        # Convert to YAML
        success, message, yaml_path = converter.convert_to_yaml(tmp_path)
        if not success:
            raise HTTPException(
                status_code=500,
                detail=f"Conversion failed: {message}"
            )
        
        # Return success response
        return {
            "status": "success",
            "message": "File successfully converted to YAML",
            "strategy_type": strategy_type,
            "yaml_path": yaml_path,
            "original_filename": file.filename
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Unexpected error: {str(e)}"
        )
    finally:
        # Clean up temporary file
        if os.path.exists(tmp_path):
            os.unlink(tmp_path)
