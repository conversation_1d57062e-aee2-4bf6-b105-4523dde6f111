#!/usr/bin/env python3
"""
Quick load of sample multi-index data - one month per index
"""
import pandas as pd
import numpy as np
from datetime import datetime
import os
import zipfile
from heavydb import connect

def quick_transform_and_load(index_name, zip_path, csv_pattern, strike_increment, conn):
    """Quick transform and load for one month of data"""
    print(f"\nProcessing {index_name}...")
    
    try:
        temp_dir = f"/srv/samba/shared/temp_{index_name.lower()}"
        os.makedirs(temp_dir, exist_ok=True)
        
        # Extract one CSV file
        with zipfile.ZipFile(zip_path, 'r') as zf:
            csv_files = [f for f in zf.namelist() if csv_pattern in f.lower() and f.endswith('.csv')]
            if not csv_files:
                print(f"  No matching CSV files found")
                return 0
            
            csv_file = csv_files[0]
            print(f"  Processing {csv_file}...")
            zf.extract(csv_file, temp_dir)
            csv_path = os.path.join(temp_dir, csv_file)
        
        # Read data
        df = pd.read_csv(csv_path, low_memory=False)
        print(f"  Read {len(df)} rows")
        
        # Basic transformation
        df['date'] = pd.to_datetime(df['date'], format='%y%m%d', errors='coerce')
        df['expiry'] = pd.to_datetime(df['expiry'], format='%y%m%d', errors='coerce')
        df = df.dropna(subset=['date', 'expiry'])
        
        # Calculate expiry type
        df['dte'] = (df['expiry'] - df['date']).dt.days
        df['month_diff'] = (df['expiry'].dt.year - df['date'].dt.year) * 12 + (df['expiry'].dt.month - df['date'].dt.month)
        
        # Apply expiry rules
        if index_name == 'SENSEX':
            # Include weekly and monthly
            df['expiry_bucket'] = df.apply(
                lambda row: 'CW' if row['dte'] <= 7 else
                           'NW' if row['dte'] <= 14 else
                           'CM' if row['month_diff'] == 0 else
                           'NM' if row['month_diff'] == 1 else None,
                axis=1
            )
        else:
            # Only monthly
            df['expiry_bucket'] = df.apply(
                lambda row: 'CM' if row['month_diff'] == 0 else
                           'NM' if row['month_diff'] == 1 else None,
                axis=1
            )
        
        # Filter valid expiries
        df = df[df['expiry_bucket'].notna()]
        print(f"  After filter: {len(df)} rows")
        
        if len(df) == 0:
            os.remove(csv_path)
            return 0
        
        # Quick transformation
        if 'time' in df.columns and ':' in str(df['time'].iloc[0]):
            df['trade_time'] = df['time'].astype(str) + ':00'
        else:
            df['trade_time'] = pd.to_datetime(df['time'].astype(str).str.zfill(4), format='%H%M').dt.strftime('%H:%M:%S')
        
        # ATM and zones
        if 'ATM' in df.columns:
            df['atm_strike'] = df['ATM']
        else:
            df['atm_strike'] = (df.get('underlying_price', df.get('spot', 0)) / strike_increment).round() * strike_increment
        
        df['strike_diff'] = (df['strike'] - df['atm_strike']).abs() / strike_increment
        df['zone_id'] = pd.cut(df['strike_diff'], bins=[-np.inf, 0.5, 5, 10, 20, np.inf], labels=[0, 1, 2, 3, 4]).astype(int)
        df['zone_name'] = df['zone_id'].map({0: 'ATM', 1: 'OPEN', 2: 'NEAR', 3: 'MID', 4: 'FAR'})
        
        # Strike types
        df['call_strike_type'] = df.apply(
            lambda row: 'ATM' if row['strike'] == row['atm_strike'] else
                       f"OTM{int(row['strike_diff'])}" if row['strike'] > row['atm_strike'] else
                       f"ITM{int(row['strike_diff'])}",
            axis=1
        )
        df['put_strike_type'] = df.apply(
            lambda row: 'ATM' if row['strike'] == row['atm_strike'] else
                       f"ITM{int(row['strike_diff'])}" if row['strike'] > row['atm_strike'] else
                       f"OTM{int(row['strike_diff'])}",
            axis=1
        )
        
        # Create output
        output_df = pd.DataFrame({
            'trade_date': df['date'].dt.strftime('%Y-%m-%d'),
            'trade_time': df['trade_time'],
            'expiry_date': df['expiry'].dt.strftime('%Y-%m-%d'),
            'index_name': index_name,
            'spot': df.get('underlying_price', df.get('spot', 0)),
            'atm_strike': df['atm_strike'],
            'strike': df['strike'],
            'dte': df['dte'],
            'expiry_bucket': df['expiry_bucket'],
            'zone_id': df['zone_id'],
            'zone_name': df['zone_name'],
            'call_strike_type': df['call_strike_type'],
            'put_strike_type': df['put_strike_type']
        })
        
        # Add option columns
        for col in ['symbol', 'open', 'high', 'low', 'close', 'volume', 'oi', 'coi']:
            output_df[f'ce_{col}'] = df.get(f'CE_{col}', '' if col == 'symbol' else 0).fillna('' if col == 'symbol' else 0)
            output_df[f'pe_{col}'] = df.get(f'PE_{col}', '' if col == 'symbol' else 0).fillna('' if col == 'symbol' else 0)
        
        # Greeks
        output_df['ce_iv'] = df.get('call_implied_volatility', df.get('CE_IV', np.nan))
        output_df['pe_iv'] = df.get('put_implied_volatility', df.get('PE_IV', np.nan))
        
        for greek in ['delta', 'gamma', 'theta', 'vega', 'rho']:
            output_df[f'ce_{greek}'] = df.get(f'call_{greek}', np.nan)
            output_df[f'pe_{greek}'] = df.get(f'put_{greek}', np.nan)
        
        # Future columns (empty)
        for col in ['open', 'high', 'low', 'close', 'volume', 'oi', 'coi']:
            output_df[f'future_{col}'] = 0 if col != 'open' else np.nan
        
        # Save and load
        temp_csv = os.path.join(temp_dir, f"{index_name.lower()}_quick_load.csv")
        output_df.to_csv(temp_csv, index=False)
        
        # Load into HeavyDB
        cursor = conn.cursor()
        copy_query = f"COPY nifty_option_chain FROM '{temp_csv}' WITH (header='true', delimiter=',')"
        cursor.execute(copy_query)
        cursor.close()
        
        print(f"  ✅ Loaded {len(output_df):,} rows")
        
        # Cleanup
        os.remove(csv_path)
        os.remove(temp_csv)
        os.rmdir(temp_dir)
        
        return len(output_df)
        
    except Exception as e:
        print(f"  ❌ Error: {e}")
        return 0

def main():
    print("="*80)
    print("QUICK LOAD MULTI-INDEX DATA")
    print("="*80)
    
    # Connect to HeavyDB
    try:
        conn = connect(
            host='localhost',
            port=6274,
            user='admin',
            password='HyperInteractive',
            dbname='heavyai'
        )
        print("✅ Connected to HeavyDB")
    except Exception as e:
        print(f"❌ HeavyDB connection error: {e}")
        return
    
    # Process one month per index
    test_data = [
        ('BANKNIFTY', '/srv/samba/shared/market_data/banknifty/banknifty_2024.zip', 'dec', 100),
        ('MIDCAPNIFTY', '/srv/samba/shared/market_data/midcpnifty/2024_midcpnifty.zip', 'dec', 25),
        ('SENSEX', '/srv/samba/shared/market_data/sensex/sensex_2024.zip', 'dec', 100)
    ]
    
    total_rows = 0
    for index_name, zip_path, pattern, strike_inc in test_data:
        if os.path.exists(zip_path):
            rows = quick_transform_and_load(index_name, zip_path, pattern, strike_inc, conn)
            total_rows += rows
    
    # Summary
    print("\n" + "="*80)
    print("QUICK LOAD SUMMARY")
    print("="*80)
    
    cursor = conn.cursor()
    cursor.execute("""
        SELECT index_name, expiry_bucket, COUNT(*) as cnt
        FROM nifty_option_chain
        WHERE trade_date >= '2024-12-01'
        GROUP BY index_name, expiry_bucket
        ORDER BY index_name, expiry_bucket
    """)
    
    results = cursor.fetchall()
    current_index = None
    for row in results:
        if row[0] != current_index:
            current_index = row[0]
            print(f"\n{current_index}:")
        print(f"  {row[1]}: {row[2]:,} rows")
    
    cursor.close()
    conn.close()
    
    print(f"\n✅ Total rows loaded: {total_rows:,}")

if __name__ == "__main__":
    main()