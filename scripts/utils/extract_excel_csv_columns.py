import os
import pandas as pd
import csv
from collections import defaultdict

# Paths to input files (update as needed)
EXCEL_FILES = [
    'bt/backtester_stable/BTRUN/input_sheets/input_portfolio.xlsx',
    'bt/backtester_stable/BTRUN/input_sheets/input_tbs_multi_legs.xlsx',
]
CSV_FILES = [
    'LOTSIZE.csv',
]

# Output file for mapping matrix
def get_output_path():
    if os.path.exists('docs'):
        return 'docs/column_mapping.md'
    elif os.path.exists('memory-bank'):
        return 'memory-bank/column_mapping.md'
    else:
        return 'column_mapping.md'

OUTPUT_FILE = get_output_path()

# Helper to extract columns from Excel sheets
def extract_excel_columns(excel_path):
    columns_by_sheet = defaultdict(list)
    try:
        xl = pd.ExcelFile(excel_path)
        for sheet in xl.sheet_names:
            try:
                df = xl.parse(sheet, nrows=0)
                columns_by_sheet[sheet] = list(df.columns)
            except Exception as e:
                columns_by_sheet[sheet] = [f"[Error reading columns: {e}]"]
    except Exception as e:
        print(f"Could not open {excel_path}: {e}")
    return columns_by_sheet

# Helper to extract columns from CSV
def extract_csv_columns(csv_path):
    try:
        with open(csv_path, 'r', newline='') as f:
            reader = csv.reader(f)
            header = next(reader)
            return header
    except Exception as e:
        print(f"Could not open {csv_path}: {e}")
        return []

def main():
    mapping_rows = []
    print("# Column Extraction Report\n")
    print("## Excel Files\n")
    for excel_path in EXCEL_FILES:
        print(f"### {excel_path}")
        if not os.path.exists(excel_path):
            print(f"File not found: {excel_path}\n")
            continue
        columns_by_sheet = extract_excel_columns(excel_path)
        for sheet, columns in columns_by_sheet.items():
            print(f"- Sheet: {sheet}")
            print(f"  Columns: {columns}")
            for col in columns:
                mapping_rows.append([f"{sheet}", col, '', '', ''])
    print("\n## CSV Files\n")
    for csv_path in CSV_FILES:
        print(f"### {csv_path}")
        if not os.path.exists(csv_path):
            print(f"File not found: {csv_path}\n")
            continue
        columns = extract_csv_columns(csv_path)
        print(f"  Columns: {columns}")
        for col in columns:
            mapping_rows.append([os.path.basename(csv_path), col, '', '', ''])
    # Write mapping matrix as Markdown
    with open(OUTPUT_FILE, 'w') as f:
        f.write("| Sheet / File | Column | Model Field | DB Column(s) | Notes/Transform |\n")
        f.write("|--------------|--------|-------------|--------------|-----------------|")
        for row in mapping_rows:
            f.write("\n| " + " | ".join(row) + " |")
    print(f"\nDraft mapping matrix written to {OUTPUT_FILE}")

if __name__ == "__main__":
    main() 