#!/usr/bin/env python3
"""
Final optimized ETL script for loading nifty option chain data into HeavyDB
This version loads directly from the source directory to avoid whitelist issues
"""

import os
import sys
import glob
import pandas as pd
import numpy as np
from datetime import datetime
import logging
from pathlib import Path

# Add parent directory to path
sys.path.append('/srv/samba/shared')

try:
    from bt.dal.heavydb_conn import get_conn
except ImportError:
    print("Error: Unable to import heavydb connection module")
    sys.exit(1)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class FinalNiftyETL:
    def __init__(self, data_dir="/srv/samba/shared/market_data/nifty/oc_with_futures"):
        self.data_dir = data_dir
        self.conn = None
        self.cursor = None
        
    def connect(self):
        """Establish connection to HeavyDB"""
        self.conn = get_conn()
        self.cursor = self.conn.cursor()
        logger.info("Connected to HeavyDB")
        
    def verify_and_reset_table(self):
        """Verify the table exists and optionally reset it"""
        try:
            self.cursor.execute("SELECT COUNT(*) FROM nifty_option_chain")
            count = self.cursor.fetchone()[0]
            logger.info(f"Table exists with {count:,} rows")
            
            if count > 1:  # We have test data
                response = input("Table already has data. Reset it? (y/N): ")
                if response.lower() == 'y':
                    self.cursor.execute("TRUNCATE TABLE nifty_option_chain")
                    logger.info("Table truncated")
            return True
        except:
            logger.error("Table nifty_option_chain does not exist!")
            return False
            
    def load_csv_directly(self, csv_file):
        """Load a CSV file directly using COPY FROM"""
        try:
            logger.info(f"Loading {os.path.basename(csv_file)}...")
            
            # Get file size
            file_size_mb = os.path.getsize(csv_file) / (1024 * 1024)
            logger.info(f"File size: {file_size_mb:.2f} MB")
            
            # Use COPY FROM directly on the source file
            copy_sql = f"COPY nifty_option_chain FROM '{csv_file}' WITH (header='true')"
            
            start_time = datetime.now()
            self.cursor.execute(copy_sql)
            end_time = datetime.now()
            
            duration = (end_time - start_time).total_seconds()
            logger.info(f"Loaded successfully in {duration:.2f} seconds")
            
            # Get current count
            self.cursor.execute("SELECT COUNT(*) FROM nifty_option_chain")
            count = self.cursor.fetchone()[0]
            logger.info(f"Total rows now: {count:,}")
            
            return True
            
        except Exception as e:
            logger.error(f"Error loading {csv_file}: {e}")
            return False
    
    def post_process_data(self):
        """Post-process loaded data to add computed columns"""
        try:
            logger.info("Post-processing data...")
            
            # Update trade_ts column
            update_sql = """
            UPDATE nifty_option_chain 
            SET trade_ts = CAST(trade_date AS TIMESTAMP) + trade_time
            WHERE trade_ts IS NULL
            """
            
            self.cursor.execute(update_sql)
            logger.info("Updated trade_ts column")
            
        except Exception as e:
            logger.warning(f"Post-processing failed: {e}")
            
    def verify_final_load(self):
        """Comprehensive verification of loaded data"""
        try:
            # Basic stats
            self.cursor.execute("""
                SELECT 
                    COUNT(*) as total_rows,
                    COUNT(DISTINCT trade_date) as unique_dates,
                    MIN(trade_date) as min_date,
                    MAX(trade_date) as max_date,
                    COUNT(DISTINCT strike) as unique_strikes,
                    COUNT(DISTINCT ce_symbol) as unique_calls,
                    COUNT(DISTINCT pe_symbol) as unique_puts
                FROM nifty_option_chain
            """)
            
            result = self.cursor.fetchone()
            
            logger.info("\n=== FINAL DATA VERIFICATION ===")
            logger.info(f"Total rows: {result[0]:,}")
            logger.info(f"Date range: {result[2]} to {result[3]} ({result[1]} unique dates)")
            logger.info(f"Unique strikes: {result[4]:,}")
            logger.info(f"Unique call symbols: {result[5]:,}")
            logger.info(f"Unique put symbols: {result[6]:,}")
            
            # Sample data
            self.cursor.execute("""
                SELECT trade_date, strike, ce_symbol, ce_close, pe_symbol, pe_close
                FROM nifty_option_chain
                LIMIT 5
            """)
            
            logger.info("\nSample data:")
            for row in self.cursor.fetchall():
                logger.info(f"  {row}")
                
        except Exception as e:
            logger.error(f"Error during verification: {e}")
            
    def run_etl(self, pattern="*.csv", skip_sorted=True):
        """Run the complete ETL process"""
        try:
            # Connect to database
            self.connect()
            
            # Verify table
            if not self.verify_and_reset_table():
                logger.error("Please create the table first using create_optimized_nifty_option_chain.sql")
                return
            
            # Get all CSV files
            csv_files = sorted(glob.glob(os.path.join(self.data_dir, pattern)))
            
            # Skip sorted files if requested
            if skip_sorted:
                csv_files = [f for f in csv_files if not f.endswith('_sorted.csv')]
            
            logger.info(f"\nFound {len(csv_files)} files to process")
            
            # Process each file
            successful = 0
            failed = 0
            
            for i, csv_file in enumerate(csv_files, 1):
                logger.info(f"\n[{i}/{len(csv_files)}] Processing {os.path.basename(csv_file)}...")
                
                if self.load_csv_directly(csv_file):
                    successful += 1
                else:
                    failed += 1
                    logger.warning(f"Failed to load {csv_file}")
            
            # Post-process if needed
            if successful > 0:
                self.post_process_data()
            
            # Final verification
            self.verify_final_load()
            
            logger.info(f"\n=== ETL COMPLETE ===")
            logger.info(f"Files processed: {len(csv_files)}")
            logger.info(f"Successful: {successful}")
            logger.info(f"Failed: {failed}")
            
        except Exception as e:
            logger.error(f"ETL process failed: {e}")
            raise
        finally:
            if self.cursor:
                self.cursor.close()
            if self.conn:
                self.conn.close()

def main():
    """Main entry point"""
    import argparse
    
    parser = argparse.ArgumentParser(description='Load Nifty Option Chain data to HeavyDB')
    parser.add_argument('--data-dir', default="/srv/samba/shared/market_data/nifty/oc_with_futures",
                        help='Directory containing CSV files')
    parser.add_argument('--pattern', default="*.csv", help='File pattern to match')
    parser.add_argument('--include-sorted', action='store_true', 
                        help='Include _sorted.csv files (default: skip them)')
    
    args = parser.parse_args()
    
    etl = FinalNiftyETL(data_dir=args.data_dir)
    etl.run_etl(pattern=args.pattern, skip_sorted=not args.include_sorted)
    
if __name__ == "__main__":
    main()