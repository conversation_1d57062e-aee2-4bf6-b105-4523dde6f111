#!/usr/bin/env python3
"""
Transform BANKNIFTY, MIDCAPNIFTY, and SENSEX data
- BANKNIFTY & MIDCAPNIFTY: CM and NM expiries only
- SENSEX: CW, NW, CM, and NM expiries
"""
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import os
import zipfile

def get_expiry_type(trade_date, expiry_date, index_name):
    """
    Get expiry type based on index
    - BANKNIFTY & MIDCAPNIFTY: CM/NM only
    - SENSEX: CW/NW/CM/NM
    """
    trade_date = pd.to_datetime(trade_date)
    expiry_date = pd.to_datetime(expiry_date)
    
    # Calculate days to expiry
    dte = (expiry_date - trade_date).days
    
    # Get month difference
    month_diff = (expiry_date.year - trade_date.year) * 12 + (expiry_date.month - trade_date.month)
    
    # For SENSEX, include all expiry types
    if index_name == 'SENSEX':
        if dte <= 7:
            return "CW", dte  # Current week
        elif dte <= 14:
            return "NW", dte  # Next week  
        elif month_diff == 0:
            return "CM", dte  # Current month
        elif month_diff == 1:
            return "NM", dte  # Next month
        else:
            return None, dte  # Skip far month
    
    # For BANKNIFTY and MIDCAPNIFTY, only CM/NM
    else:
        if month_diff == 0:
            return "CM", dte
        elif month_diff == 1:
            return "NM", dte
        else:
            return None, dte

def process_index_file(csv_file, index_name, strike_increment, output_dir, sample_dates=None):
    """Process a single CSV file for an index"""
    print(f"\nProcessing {csv_file}...")
    
    try:
        # Read data
        df = pd.read_csv(csv_file, low_memory=False)
        print(f"  Loaded {len(df)} rows")
        
        # Convert dates
        df['date'] = pd.to_datetime(df['date'], format='%y%m%d', errors='coerce')
        df['expiry'] = pd.to_datetime(df['expiry'], format='%y%m%d', errors='coerce')
        df = df.dropna(subset=['date', 'expiry'])
        
        # Get expiry type based on index
        df[['expiry_type', 'dte']] = df.apply(
            lambda row: get_expiry_type(row['date'], row['expiry'], index_name), 
            axis=1, result_type='expand'
        )
        
        # Filter out None expiry types
        df = df[df['expiry_type'].notna()]
        
        print(f"  After expiry filter: {len(df)} rows")
        
        if len(df) == 0:
            print(f"  No valid expiry data found")
            return None
        
        # If sample dates provided, filter
        if sample_dates:
            df = df[df['date'].dt.strftime('%Y-%m-%d').isin(sample_dates)]
            print(f"  After date filter: {len(df)} rows")
        
        # Convert time
        if 'time' in df.columns and ':' in str(df['time'].iloc[0]):
            df['trade_time'] = df['time'].astype(str) + ':00'
        else:
            df['trade_time'] = pd.to_datetime(df['time'].astype(str).str.zfill(4), format='%H%M').dt.strftime('%H:%M:%S')
        
        # Use existing ATM or calculate
        if 'ATM' in df.columns:
            df['atm_strike'] = df['ATM']
        else:
            df['atm_strike'] = (df['underlying_price'] / strike_increment).round() * strike_increment
        
        # Calculate zone
        df['strike_diff'] = (df['strike'] - df['atm_strike']).abs()
        df['zone_id'] = pd.cut(
            df['strike_diff'] / strike_increment,
            bins=[-np.inf, 0.5, 5, 10, 20, np.inf],
            labels=[0, 1, 2, 3, 4]
        ).astype(int)
        
        zone_map = {0: 'ATM', 1: 'OPEN', 2: 'NEAR', 3: 'MID', 4: 'FAR'}
        df['zone_name'] = df['zone_id'].map(zone_map)
        
        # Calculate strike types
        df['call_strike_type'] = df.apply(
            lambda row: 'ATM' if row['strike'] == row['atm_strike'] 
            else f"OTM{int(abs(row['strike'] - row['atm_strike']) / strike_increment)}" 
            if row['strike'] > row['atm_strike'] 
            else f"ITM{int(abs(row['strike'] - row['atm_strike']) / strike_increment)}", 
            axis=1
        )
        
        df['put_strike_type'] = df.apply(
            lambda row: 'ATM' if row['strike'] == row['atm_strike'] 
            else f"ITM{int(abs(row['strike'] - row['atm_strike']) / strike_increment)}" 
            if row['strike'] > row['atm_strike'] 
            else f"OTM{int(abs(row['strike'] - row['atm_strike']) / strike_increment)}", 
            axis=1
        )
        
        # Create output dataframe
        output_df = pd.DataFrame({
            'trade_date': df['date'].dt.strftime('%Y-%m-%d'),
            'trade_time': df['trade_time'],
            'expiry_date': df['expiry'].dt.strftime('%Y-%m-%d'),
            'index_name': index_name,
            'spot': df.get('underlying_price', df.get('spot', np.nan)),
            'atm_strike': df['atm_strike'],
            'strike': df['strike'],
            'dte': df['dte'],
            'expiry_bucket': df['expiry_type'],
            'zone_id': df['zone_id'],
            'zone_name': df['zone_name'],
            'call_strike_type': df['call_strike_type'],
            'put_strike_type': df['put_strike_type'],
            
            # Option data
            'ce_symbol': df.get('CE_symbol', '').fillna(''),
            'ce_open': df.get('CE_open', np.nan),
            'ce_high': df.get('CE_high', np.nan),
            'ce_low': df.get('CE_low', np.nan),
            'ce_close': df.get('CE_close', np.nan),
            'ce_volume': df.get('CE_volume', 0).fillna(0),
            'ce_oi': df.get('CE_oi', 0).fillna(0),
            'ce_coi': df.get('CE_coi', 0).fillna(0),
            'ce_iv': df.get('call_implied_volatility', df.get('CE_IV', np.nan)),
            'ce_delta': df.get('call_delta', np.nan),
            'ce_gamma': df.get('call_gamma', np.nan),
            'ce_theta': df.get('call_theta', np.nan),
            'ce_vega': df.get('call_vega', np.nan),
            'ce_rho': df.get('call_rho', np.nan),
            
            'pe_symbol': df.get('PE_symbol', '').fillna(''),
            'pe_open': df.get('PE_open', np.nan),
            'pe_high': df.get('PE_high', np.nan),
            'pe_low': df.get('PE_low', np.nan),
            'pe_close': df.get('PE_close', np.nan),
            'pe_volume': df.get('PE_volume', 0).fillna(0),
            'pe_oi': df.get('PE_oi', 0).fillna(0),
            'pe_coi': df.get('PE_coi', 0).fillna(0),
            'pe_iv': df.get('put_implied_volatility', df.get('PE_IV', np.nan)),
            'pe_delta': df.get('put_delta', np.nan),
            'pe_gamma': df.get('put_gamma', np.nan),
            'pe_theta': df.get('put_theta', np.nan),
            'pe_vega': df.get('put_vega', np.nan),
            'pe_rho': df.get('put_rho', np.nan),
            
            # Future data
            'future_open': np.nan,
            'future_high': np.nan,
            'future_low': np.nan,
            'future_close': np.nan,
            'future_volume': 0,
            'future_oi': 0,
            'future_coi': 0
        })
        
        # Sort and save
        output_df = output_df.sort_values(['trade_date', 'trade_time', 'strike'])
        
        # Create filename based on index
        base_name = os.path.basename(csv_file).replace('.csv', '')
        if index_name == 'SENSEX':
            output_file = os.path.join(output_dir, f"{index_name.lower()}_{base_name}_cw_nw_cm_nm.csv")
        else:
            output_file = os.path.join(output_dir, f"{index_name.lower()}_{base_name}_cm_nm.csv")
        
        output_df.to_csv(output_file, index=False)
        
        print(f"\n  Saved to: {output_file}")
        print(f"  Total rows: {len(output_df)}")
        print(f"  Date range: {output_df['trade_date'].min()} to {output_df['trade_date'].max()}")
        
        # Show expiry summary
        expiry_summary = output_df.groupby(['expiry_bucket', 'expiry_date']).size().reset_index(name='count')
        print("\n  Expiry Summary:")
        for _, row in expiry_summary.iterrows():
            print(f"    {row['expiry_bucket']}: {row['expiry_date']} ({row['count']:,} rows)")
        
        return output_file
        
    except Exception as e:
        print(f"  ERROR: {e}")
        return None

def main():
    """Process sample data for all indices with appropriate expiry types"""
    
    # Create output directory
    output_dir = "/srv/samba/shared/indices_all_expiries"
    os.makedirs(output_dir, exist_ok=True)
    
    print("="*80)
    print("PROCESSING INDICES WITH APPROPRIATE EXPIRY TYPES")
    print("="*80)
    print("BANKNIFTY & MIDCAPNIFTY: CM, NM only")
    print("SENSEX: CW, NW, CM, NM")
    print("="*80)
    
    # Process BANKNIFTY sample
    banknifty_file = "/tmp/banknifty_2025/filtered_processed_2025_jan_final_output.csv"
    if os.path.exists(banknifty_file):
        print("\nProcessing BANKNIFTY (CM/NM only)...")
        process_index_file(
            banknifty_file, 
            "BANKNIFTY", 
            100, 
            output_dir,
            sample_dates=['2025-01-01', '2025-01-02', '2025-01-03']
        )
    
    # Process MIDCAPNIFTY sample
    midcap_file = "/srv/samba/shared/temp_extract/midcpnifty/2025_midcpnifty/filtered_processed_2025_midcpnifty_jan.csv"
    if os.path.exists(midcap_file):
        print("\nProcessing MIDCAPNIFTY (CM/NM only)...")
        process_index_file(
            midcap_file, 
            "MIDCAPNIFTY", 
            25, 
            output_dir,
            sample_dates=['2025-01-01', '2025-01-02', '2025-01-03']
        )
    
    # Extract and process SENSEX sample
    print("\nExtracting SENSEX data...")
    sensex_zip = "/srv/samba/shared/market_data/sensex/sensex_2025.zip"
    if os.path.exists(sensex_zip):
        with zipfile.ZipFile(sensex_zip, 'r') as zf:
            # Extract January 2025 file
            for file in zf.namelist():
                if 'jan' in file.lower() and file.endswith('.csv'):
                    zf.extract(file, output_dir)
                    sensex_file = os.path.join(output_dir, file)
                    
                    print("\nProcessing SENSEX (CW/NW/CM/NM)...")
                    process_index_file(
                        sensex_file,
                        "SENSEX",
                        100,
                        output_dir,
                        sample_dates=['2025-01-01', '2025-01-02', '2025-01-03']
                    )
                    
                    # Clean up extracted file
                    os.remove(sensex_file)
                    break
    
    print("\n" + "="*80)
    print("PROCESSING COMPLETE")
    print("="*80)
    print(f"Output directory: {output_dir}")
    print("\nExpiry types by index:")
    print("- BANKNIFTY: CM, NM only")
    print("- MIDCAPNIFTY: CM, NM only")  
    print("- SENSEX: CW, NW, CM, NM")

if __name__ == "__main__":
    main()