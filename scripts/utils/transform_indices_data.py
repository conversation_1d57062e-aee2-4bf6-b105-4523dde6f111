#!/usr/bin/env python3
"""
Transform BANKNIFTY and MIDCAPNIFTY data to match the nifty_option_chain schema
Focus on current month (CM) and next month (NM) expiries
"""
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import os
import sys
from pathlib import Path

def identify_expiry_type(trade_date, expiry_date):
    """Identify if expiry is CW (current week), NW (next week), CM (current month), or NM (next month)"""
    trade_date = pd.to_datetime(trade_date)
    expiry_date = pd.to_datetime(expiry_date)
    
    # Calculate days to expiry
    dte = (expiry_date - trade_date).days
    
    # Get month difference
    month_diff = (expiry_date.year - trade_date.year) * 12 + expiry_date.month - trade_date.month
    
    if dte <= 7:
        return "CW", dte  # Current week
    elif dte <= 14:
        return "NW", dte  # Next week
    elif month_diff == 0:
        return "CM", dte  # Current month
    elif month_diff == 1:
        return "NM", dte  # Next month
    else:
        return "FM", dte  # Far month

def calculate_zone_info(strike, atm_strike, strike_increment):
    """Calculate zone_id and zone_name based on strike distance from ATM"""
    strike_diff = abs(strike - atm_strike)
    num_strikes_away = int(strike_diff / strike_increment)
    
    # Zone mapping (simplified)
    if num_strikes_away == 0:
        zone_id = 0
        zone_name = "ATM"
    elif num_strikes_away <= 5:
        zone_id = 1
        zone_name = "OPEN"
    elif num_strikes_away <= 10:
        zone_id = 2
        zone_name = "NEAR"
    elif num_strikes_away <= 20:
        zone_id = 3
        zone_name = "MID"
    else:
        zone_id = 4
        zone_name = "FAR"
    
    return zone_id, zone_name

def calculate_strike_type(strike, atm_strike, option_type, strike_increment):
    """Calculate strike type (ITM/OTM) with distance"""
    strike_diff = strike - atm_strike
    num_strikes = int(abs(strike_diff) / strike_increment)
    
    if option_type == "CE":
        if strike_diff < 0:
            return f"ITM{num_strikes}"
        elif strike_diff > 0:
            return f"OTM{num_strikes}"
        else:
            return "ATM"
    else:  # PE
        if strike_diff > 0:
            return f"ITM{num_strikes}"
        elif strike_diff < 0:
            return f"OTM{num_strikes}"
        else:
            return "ATM"

def transform_data(input_file, index_name, output_dir, strike_increment):
    """Transform data to match nifty_option_chain schema"""
    print(f"\nProcessing {input_file}...")
    
    # Read the data
    df = pd.read_csv(input_file)
    
    # Convert date/time columns
    df['date'] = pd.to_datetime(df['date'], format='%y%m%d')
    df['expiry'] = pd.to_datetime(df['expiry'], format='%y%m%d')
    
    # Convert time to proper format (handle both HH:MM and HHMM formats)
    if df['time'].dtype == 'object' and df['time'].str.contains(':').any():
        # Time is already in HH:MM format
        df['trade_time'] = pd.to_datetime(df['time'], format='%H:%M').dt.time
    else:
        # Time is in integer HHMM format
        df['time_str'] = df['time'].apply(lambda x: f"{int(x):04d}")
        df['trade_time'] = pd.to_datetime(df['time_str'], format='%H%M').dt.time
    
    # Create output dataframe with required columns
    output_data = []
    
    # Group by unique date/time/strike/expiry combinations
    grouped = df.groupby(['date', 'time', 'strike', 'expiry'])
    
    for (date, time, strike, expiry), group in grouped:
        # Get the row (should be unique for date/time/strike/expiry)
        row = group.iloc[0]
        
        # Identify expiry bucket and DTE
        expiry_bucket, dte = identify_expiry_type(date, expiry)
        
        # Only process CM and NM expiries
        if expiry_bucket not in ['CM', 'NM']:
            continue
        
        # Use ATM from data or calculate based on underlying_price
        atm_strike = row.get('ATM', round(row['underlying_price'] / strike_increment) * strike_increment)
        
        # Calculate zone info
        zone_id, zone_name = calculate_zone_info(strike, atm_strike, strike_increment)
        
        # Calculate strike types
        call_strike_type = calculate_strike_type(strike, atm_strike, "CE", strike_increment)
        put_strike_type = calculate_strike_type(strike, atm_strike, "PE", strike_increment)
        
        # Create output row
        output_row = {
            'trade_date': date.strftime('%Y-%m-%d'),
            'trade_time': str(row['trade_time']),
            'expiry_date': expiry.strftime('%Y-%m-%d'),
            'index_name': index_name,
            'spot': row['underlying_price'],
            'atm_strike': atm_strike,
            'strike': strike,
            'dte': dte,
            'expiry_bucket': expiry_bucket,
            'zone_id': zone_id,
            'zone_name': zone_name,
            'call_strike_type': call_strike_type,
            'put_strike_type': put_strike_type,
            
            # Call option data
            'ce_symbol': row.get('CE_symbol', ''),
            'ce_open': row.get('CE_open', np.nan),
            'ce_high': row.get('CE_high', np.nan),
            'ce_low': row.get('CE_low', np.nan),
            'ce_close': row.get('CE_close', np.nan),
            'ce_volume': row.get('CE_volume', 0),
            'ce_oi': row.get('CE_oi', 0),
            'ce_coi': row.get('CE_coi', 0),
            'ce_iv': row.get('call_implied_volatility', np.nan),
            'ce_delta': row.get('call_delta', np.nan),
            'ce_gamma': row.get('call_gamma', np.nan),
            'ce_theta': row.get('call_theta', np.nan),
            'ce_vega': row.get('call_vega', np.nan),
            'ce_rho': row.get('call_rho', np.nan),
            
            # Put option data
            'pe_symbol': row.get('PE_symbol', ''),
            'pe_open': row.get('PE_open', np.nan),
            'pe_high': row.get('PE_high', np.nan),
            'pe_low': row.get('PE_low', np.nan),
            'pe_close': row.get('PE_close', np.nan),
            'pe_volume': row.get('PE_volume', 0),
            'pe_oi': row.get('PE_oi', 0),
            'pe_coi': row.get('PE_coi', 0),
            'pe_iv': row.get('put_implied_volatility', np.nan),
            'pe_delta': row.get('put_delta', np.nan),
            'pe_gamma': row.get('put_gamma', np.nan),
            'pe_theta': row.get('put_theta', np.nan),
            'pe_vega': row.get('put_vega', np.nan),
            'pe_rho': row.get('put_rho', np.nan),
            
            # Future data (if available - set defaults for now)
            'future_open': np.nan,
            'future_high': np.nan,
            'future_low': np.nan,
            'future_close': np.nan,
            'future_volume': 0,
            'future_oi': 0,
            'future_coi': 0
        }
        
        output_data.append(output_row)
    
    # Create output dataframe
    output_df = pd.DataFrame(output_data)
    
    # Sort by date, time, strike
    output_df = output_df.sort_values(['trade_date', 'trade_time', 'strike'])
    
    # Save to file
    output_file = os.path.join(output_dir, f"{index_name.lower()}_cm_nm_{os.path.basename(input_file)}")
    output_df.to_csv(output_file, index=False)
    
    print(f"Saved transformed data to: {output_file}")
    print(f"Total rows: {len(output_df)}")
    print(f"Date range: {output_df['trade_date'].min()} to {output_df['trade_date'].max()}")
    print(f"Expiry buckets: {output_df['expiry_bucket'].value_counts().to_dict()}")
    
    # Show sample of CM and NM expiries
    cm_count = len(output_df[output_df['expiry_bucket'] == 'CM'])
    nm_count = len(output_df[output_df['expiry_bucket'] == 'NM'])
    print(f"CM (Current Month) rows: {cm_count}")
    print(f"NM (Next Month) rows: {nm_count}")
    
    # Show unique expiry dates for CM and NM
    cm_expiries = output_df[output_df['expiry_bucket'] == 'CM']['expiry_date'].unique()
    nm_expiries = output_df[output_df['expiry_bucket'] == 'NM']['expiry_date'].unique()
    print(f"CM expiry dates: {sorted(cm_expiries)}")
    print(f"NM expiry dates: {sorted(nm_expiries)}")
    
    return output_df

def main():
    """Main function to process BANKNIFTY and MIDCAPNIFTY data"""
    
    # Create output directory in writable location
    output_dir = "/srv/samba/shared/transformed_indices_data"
    os.makedirs(output_dir, exist_ok=True)
    
    # Process BANKNIFTY data (Jan 2025)
    banknifty_files = [
        "/tmp/banknifty_2025/filtered_processed_2025_jan_final_output.csv",
        "/tmp/banknifty_2025/filtered_processed_2025_feb_final_output.csv"
    ]
    
    print("="*60)
    print("PROCESSING BANKNIFTY DATA")
    print("="*60)
    
    for file in banknifty_files:
        if os.path.exists(file):
            transform_data(file, "BANKNIFTY", output_dir, strike_increment=100)
    
    # Process MIDCAPNIFTY data (Jan-Feb 2025)
    midcap_files = [
        "/srv/samba/shared/temp_extract/midcpnifty/2025_midcpnifty/filtered_processed_2025_midcpnifty_jan.csv",
        "/srv/samba/shared/temp_extract/midcpnifty/2025_midcpnifty/filtered_processed_2025_midcpnifty_feb.csv"
    ]
    
    print("\n" + "="*60)
    print("PROCESSING MIDCAPNIFTY DATA")
    print("="*60)
    
    for file in midcap_files:
        if os.path.exists(file):
            transform_data(file, "MIDCAPNIFTY", output_dir, strike_increment=25)
    
    print("\n" + "="*60)
    print("TRANSFORMATION COMPLETE")
    print("="*60)
    print(f"Output files saved to: {output_dir}")
    print("\nNext steps:")
    print("1. Review the transformed data")
    print("2. Load into HeavyDB using COPY FROM")
    print("3. Test with backtester")

if __name__ == "__main__":
    main()