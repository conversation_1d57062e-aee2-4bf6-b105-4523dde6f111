#!/usr/bin/env python3
"""
ATM Calculation Verification Script

This script verifies the GPU system's synthetic future ATM calculation against:
1. HeavyDB's hardcoded view methodology (spot-based)
2. The actual implementation in strike_selection.py
3. Real option chain data from HeavyDB

Author: Claude
Date: 2025-06-09
"""

import sys
import logging
import pandas as pd
import numpy as np
from datetime import datetime
from heavydb import connect
import json

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class ATMCalculationVerifier:
    """Verifies ATM calculation methodologies"""
    
    def __init__(self):
        """Initialize database connection"""
        self.conn = None
        self.connect_to_heavydb()
        
    def connect_to_heavydb(self):
        """Connect to HeavyDB"""
        try:
            self.conn = connect(
                host='localhost',
                port=6274,
                user='admin',
                password='HyperInteractive',
                dbname='heavyai'
            )
            logger.info("Connected to HeavyDB successfully")
        except Exception as e:
            logger.error(f"Failed to connect to HeavyDB: {e}")
            sys.exit(1)
            
    def get_sample_option_chain_data(self, trade_date='2024-04-01', expiry_date='2024-04-04'):
        """Fetch sample option chain data from HeavyDB"""
        query = f"""
        SELECT 
            trade_date,
            expiry_date,
            strike,
            ce_close,
            pe_close,
            spot as spot_price,
            atm_strike as table_atm_strike
        FROM nifty_option_chain
        WHERE trade_date = '{trade_date}'
        AND expiry_date = '{expiry_date}'
        ORDER BY strike
        """
        
        try:
            df = pd.read_sql(query, self.conn)
            logger.info(f"Fetched {len(df)} rows for {trade_date}")
            return df
        except Exception as e:
            logger.error(f"Error fetching data: {e}")
            return None
            
    def calculate_spot_based_atm(self, spot_price, strike_increment=50.0):
        """
        Calculate ATM using spot-based method (HeavyDB view methodology)
        This is what the SQL view uses
        """
        rounded_atm = round(spot_price / strike_increment) * strike_increment
        return rounded_atm
        
    def calculate_synthetic_future_atm(self, df, spot_price):
        """
        Calculate ATM using synthetic future method (strike_selection.py methodology)
        Synthetic Future = Strike + CE_Close - PE_Close
        """
        # Calculate synthetic future for each strike
        df['synthetic_future'] = df['strike'] + df['ce_close'] - df['pe_close']
        
        # Find strike with synthetic future closest to spot price
        df['diff'] = abs(df['synthetic_future'] - spot_price)
        atm_row = df.loc[df['diff'].idxmin()]
        
        return {
            'atm_strike': atm_row['strike'],
            'synthetic_future': atm_row['synthetic_future'],
            'ce_close': atm_row['ce_close'],
            'pe_close': atm_row['pe_close'],
            'diff_from_spot': atm_row['diff']
        }
        
    def verify_calculations(self):
        """Run verification on multiple dates"""
        test_dates = [
            ('2025-05-19', '2025-05-22'),
            ('2025-05-20', '2025-05-22'),
            ('2025-05-21', '2025-05-22'),
            ('2025-05-22', '2025-05-29'),
            ('2025-05-23', '2025-05-29'),
        ]
        
        results = []
        
        for trade_date, expiry_date in test_dates:
            logger.info(f"\n{'='*60}")
            logger.info(f"Testing {trade_date} (Expiry: {expiry_date})")
            logger.info(f"{'='*60}")
            
            # Get option chain data
            df = self.get_sample_option_chain_data(trade_date, expiry_date)
            if df is None or df.empty:
                continue
                
            spot_price = df['spot_price'].iloc[0]
            strike_increment = 100.0 if spot_price >= 10000 else 50.0
            
            # Get ATM from table (what HeavyDB actually stored)
            table_atm = df['table_atm_strike'].iloc[0]
            
            # Method 1: Spot-based ATM (HeavyDB view)
            spot_based_atm = self.calculate_spot_based_atm(spot_price, strike_increment)
            
            # Method 2: Synthetic future-based ATM (strike_selection.py)
            synthetic_result = self.calculate_synthetic_future_atm(df.copy(), spot_price)
            
            # Log results
            logger.info(f"\nSpot Price: {spot_price:.2f}")
            logger.info(f"Strike Increment: {strike_increment}")
            logger.info(f"\n--- HeavyDB Table ATM ---")
            logger.info(f"ATM Strike from table: {table_atm}")
            
            logger.info(f"\n--- Spot-based ATM (HeavyDB View) ---")
            logger.info(f"ATM Strike: {spot_based_atm}")
            
            logger.info(f"\n--- Synthetic Future-based ATM (strike_selection.py) ---")
            logger.info(f"ATM Strike: {synthetic_result['atm_strike']}")
            logger.info(f"Synthetic Future: {synthetic_result['synthetic_future']:.2f}")
            logger.info(f"CE Close: {synthetic_result['ce_close']:.2f}")
            logger.info(f"PE Close: {synthetic_result['pe_close']:.2f}")
            logger.info(f"Diff from Spot: {synthetic_result['diff_from_spot']:.2f}")
            
            logger.info(f"\n--- Comparison ---")
            strike_diff = abs(spot_based_atm - synthetic_result['atm_strike'])
            logger.info(f"Strike Difference: {strike_diff}")
            logger.info(f"In Strike Increments: {strike_diff / strike_increment}")
            
            # Store results
            results.append({
                'trade_date': trade_date,
                'expiry_date': expiry_date,
                'spot_price': spot_price,
                'spot_based_atm': spot_based_atm,
                'synthetic_atm': synthetic_result['atm_strike'],
                'synthetic_future': synthetic_result['synthetic_future'],
                'strike_difference': strike_diff,
                'strike_increments_diff': strike_diff / strike_increment
            })
            
            # Show strikes around ATM for context
            logger.info(f"\n--- Strikes Around ATM ---")
            atm_range = df[
                (df['strike'] >= min(spot_based_atm, synthetic_result['atm_strike']) - 2 * strike_increment) &
                (df['strike'] <= max(spot_based_atm, synthetic_result['atm_strike']) + 2 * strike_increment)
            ].copy()
            
            atm_range['synthetic_future'] = atm_range['strike'] + atm_range['ce_close'] - atm_range['pe_close']
            
            for _, row in atm_range.iterrows():
                marker = ""
                if row['strike'] == spot_based_atm:
                    marker += " [Spot ATM]"
                if row['strike'] == synthetic_result['atm_strike']:
                    marker += " [Synthetic ATM]"
                    
                logger.info(
                    f"Strike: {row['strike']:7.0f} | "
                    f"CE: {row['ce_close']:7.2f} | "
                    f"PE: {row['pe_close']:7.2f} | "
                    f"Synthetic: {row['synthetic_future']:7.2f}{marker}"
                )
                
        return results
        
    def generate_report(self, results):
        """Generate verification report"""
        if not results:
            logger.error("No results to generate report")
            return {
                'summary': {'error': 'No test results available'},
                'details': [],
                'recommendation': 'Unable to verify - no data found'
            }
            
        df_results = pd.DataFrame(results)
        
        report = {
            'summary': {
                'total_tests': len(results),
                'avg_strike_difference': df_results['strike_difference'].mean(),
                'max_strike_difference': df_results['strike_difference'].max(),
                'avg_increments_diff': df_results['strike_increments_diff'].mean(),
                'max_increments_diff': df_results['strike_increments_diff'].max()
            },
            'details': results,
            'recommendation': self.get_recommendation(df_results)
        }
        
        # Save report
        report_path = '/srv/samba/shared/atm_calculation_verification_report.json'
        with open(report_path, 'w') as f:
            json.dump(report, f, indent=2)
            
        logger.info(f"\nReport saved to: {report_path}")
        
        return report
        
    def get_recommendation(self, df_results):
        """Generate recommendation based on findings"""
        avg_diff = df_results['strike_increments_diff'].mean()
        max_diff = df_results['strike_increments_diff'].max()
        
        if max_diff <= 1.0:
            return (
                "The synthetic future-based ATM calculation is working correctly. "
                "The differences are within one strike increment, which is expected "
                "due to the different methodologies. The strike_selection.py implementation "
                "matches the intended synthetic future calculation."
            )
        else:
            return (
                "There are significant differences between the two ATM calculation methods. "
                "The synthetic future method may need adjustment or the HeavyDB view "
                "should be updated to use synthetic future calculation for consistency."
            )
            
    def run(self):
        """Run the verification process"""
        logger.info("Starting ATM Calculation Verification")
        logger.info("="*80)
        
        # Run verifications
        results = self.verify_calculations()
        
        # Generate report
        report = self.generate_report(results)
        
        # Print summary
        logger.info("\n" + "="*80)
        logger.info("VERIFICATION SUMMARY")
        logger.info("="*80)
        logger.info(f"Total Tests: {report['summary']['total_tests']}")
        logger.info(f"Average Strike Difference: {report['summary']['avg_strike_difference']:.1f}")
        logger.info(f"Max Strike Difference: {report['summary']['max_strike_difference']:.1f}")
        logger.info(f"Average Increments Diff: {report['summary']['avg_increments_diff']:.2f}")
        logger.info(f"Max Increments Diff: {report['summary']['max_increments_diff']:.2f}")
        logger.info("\nRecommendation:")
        logger.info(report['recommendation'])
        
        # Close connection
        if self.conn:
            self.conn.close()
            

if __name__ == "__main__":
    verifier = ATMCalculationVerifier()
    verifier.run()