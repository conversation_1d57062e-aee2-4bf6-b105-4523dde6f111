#!/usr/bin/env python3
"""
Golden Output Format Requirements
This module documents the exact format requirements for GPU backtester outputs
to ensure compatibility with the archive system's golden output format.
"""

from dataclasses import dataclass
from typing import List, Dict, Any
import pandas as pd

@dataclass
class GoldenOutputFormat:
    """Defines the exact format requirements for golden output compatibility."""
    
    # Sheet 1: PortfolioParameter - 2 columns, key-value pairs
    PORTFOLIO_PARAMETER_ROWS = [
        "StartDate", "EndDate", "IsTickBT", "InitialCapital", "LotSize",
        "NoOfPortfolio", "PortfolioList", "BrokerUsed", "PositionSizing",
        "PositionSizingMethodSelected", "CapitaltobeusedpercentSelected",
        "CapitaltobeusedSelected", "MaxRiskPerTradeSelected", "MaxRiskValue",
        "MinNoOfLots", "MaxCapitalPerTrade", "MaxCapitaltobeused",
        "MaxOpenPositions", "StartTimeNSE", "EndTimeNSE", "Version"
    ]
    
    # Sheet 2: GeneralParameter - Strategy level settings (36 columns)
    GENERAL_PARAMETER_COLUMNS = [
        "StrategyName", "MoveSlToCost", "Underlying", "Index", "Weekdays",
        "DTE", "StrikeSelectionTime", "StartTime", "LastEntryTime", "EndTime",
        "StrategyProfit", "StrategyLoss", "StrategyProfitReExecuteNo",
        "StrategyLossReExecuteNo", "StrategyTrailingType", "PnLCalTime",
        "LockPercent", "TrailPercent", "SqOff1Time", "SqOff1Percent",
        "SqOff2Time", "SqOff2Percent", "ProfitReaches", "LockMinProfitAt",
        "IncreaseInProfit", "TrailMinProfitBy", "TgtTrackingFrom",
        "TgtRegisterPriceFrom", "SlTrackingFrom", "SlRegisterPriceFrom",
        "PnLCalculationFrom", "ConsiderHedgePnLForStgyPnL",
        "StoplossCheckingInterval", "TargetCheckingInterval",
        "ReEntryCheckingInterval", "OnExpiryDayTradeNextExpiry"
    ]
    
    # Sheet 3: LegParameter - Leg level settings (38 columns)
    LEG_PARAMETER_COLUMNS = [
        "StrategyName", "IsIdle", "LegID", "Instrument", "Transaction",
        "Expiry", "W&Type", "W&TValue", "TrailW&T", "StrikeMethod",
        "MatchPremium", "StrikeValue", "StrikePremiumCondition", "SLType",
        "SLValue", "TGTType", "TGTValue", "TrailSLType", "SL_TrailAt",
        "SL_TrailBy", "Lots", "ReEntryType", "ReEnteriesCount",
        "OnEntry_OpenTradeOn", "OnEntry_SqOffTradeOff", "OnEntry_SqOffAllLegs",
        "OnEntry_OpenTradeDelay", "OnEntry_SqOffDelay", "OnExit_OpenTradeOn",
        "OnExit_SqOffTradeOff", "OnExit_SqOffAllLegs", "OnExit_OpenAllLegs",
        "OnExit_OpenTradeDelay", "OnExit_SqOffDelay", "OpenHedge",
        "HedgeStrikeMethod", "HedgeStrikeValue", "HedgeStrikePremiumCondition"
    ]
    
    # Sheet 4: Metrics - Performance metrics (3 columns minimum)
    METRICS_ROWS = [
        "Backtest Start Date", "Backtest End Date", "Margin Required",
        "Max Trades (Day)", "Max Trades (Week)", "Max Trades (Month)",
        "Total Trades", "Total PnL", "Total Profit", "Total Loss",
        "Number of Wins", "Number of Losses", "Win Rate %", "Loss Rate %",
        "Avg Profit on Win days", "Avg Loss on loss days", "Avg Daily PnL",
        "Max Win Streak", "Max Loss Streak", "Sharpe Ratio", "Max DrawDown",
        "Max DrawDown Days", "Calmar Ratio", "Avg Trade PnL", "Profit Factor"
    ]
    
    # Sheet 5: Max Profit and Loss - Daily max profit/loss tracking (5 columns)
    MAX_PROFIT_LOSS_COLUMNS = [
        "Date", "Max Profit", "Max Profit Time", "Max Loss", "Max Loss Time"
    ]
    
    # Sheet 6: PORTFOLIO Trans - Individual trade details (32 columns)
    PORTFOLIO_TRANS_COLUMNS = [
        "Portfolio Name", "Strategy Name", "ID", "Entry Date", "Enter On",
        "Entry Day", "Exit Date", "Exit at", "Exit Day", "Index", "Expiry",
        "Strike", "CE/PE", "Trade", "Qty", "Entry at", "Exit at.1", "Points",
        "Points After Slippage", "PNL", "AfterSlippage", "Taxes", "Net PNL",
        "Re-entry No", "SL Re-entry No", "TGT Re-entry No", "Reason",
        "Strategy Entry No", "Index At Entry", "Index At Exit", "MaxProfit",
        "MaxLoss"
    ]
    
    # Sheet 7: PORTFOLIO Results - Calendar view of results (14 columns)
    PORTFOLIO_RESULTS_STRUCTURE = {
        "rows": ["Year", "Monday", "Tuesday", "Wednesday", "Thursday", 
                 "Friday", "Saturday", "Total"],
        "additional_cols": ["Unnamed: 8", "Unnamed: 9", "Unnamed: 10", 
                           "Unnamed: 11", "Unnamed: 12", "Unnamed: 13"]
    }
    
    # Sheet 8+: Individual strategy sheets (same structure as PORTFOLIO Trans)
    # These have dynamic names based on strategy names
    
    @classmethod
    def validate_dataframe_columns(cls, df: pd.DataFrame, expected_columns: List[str], 
                                 sheet_name: str) -> List[str]:
        """Validate that a DataFrame has the expected columns."""
        errors = []
        actual_columns = list(df.columns)
        
        # Check for missing columns
        missing = set(expected_columns) - set(actual_columns)
        if missing:
            errors.append(f"{sheet_name}: Missing columns: {missing}")
        
        # Check for extra columns
        extra = set(actual_columns) - set(expected_columns)
        if extra:
            errors.append(f"{sheet_name}: Extra columns: {extra}")
        
        # Check column order
        if actual_columns != expected_columns:
            errors.append(f"{sheet_name}: Column order mismatch")
        
        return errors
    
    @classmethod
    def create_empty_golden_output(cls) -> Dict[str, pd.DataFrame]:
        """Create an empty golden output structure with all required sheets."""
        output = {}
        
        # PortfolioParameter - Key-value format
        output["PortfolioParameter"] = pd.DataFrame({
            "Head": cls.PORTFOLIO_PARAMETER_ROWS,
            "Value": [""] * len(cls.PORTFOLIO_PARAMETER_ROWS)
        })
        
        # GeneralParameter - One row per strategy
        output["GeneralParameter"] = pd.DataFrame(columns=cls.GENERAL_PARAMETER_COLUMNS)
        
        # LegParameter - Multiple rows per strategy
        output["LegParameter"] = pd.DataFrame(columns=cls.LEG_PARAMETER_COLUMNS)
        
        # Metrics - Key-value format with strategy columns
        output["Metrics"] = pd.DataFrame({
            "Particulars": cls.METRICS_ROWS,
            "Combined": [""] * len(cls.METRICS_ROWS)
        })
        
        # Max Profit and Loss - Daily tracking
        output["Max Profit and Loss"] = pd.DataFrame(columns=cls.MAX_PROFIT_LOSS_COLUMNS)
        
        # PORTFOLIO Trans - Trade details
        output["PORTFOLIO Trans"] = pd.DataFrame(columns=cls.PORTFOLIO_TRANS_COLUMNS)
        
        # PORTFOLIO Results - Calendar format
        output["PORTFOLIO Results"] = pd.DataFrame()
        
        return output
    
    @classmethod
    def format_datetime_columns(cls, df: pd.DataFrame, sheet_name: str) -> pd.DataFrame:
        """Apply correct datetime formatting based on sheet requirements."""
        datetime_columns = {
            "Max Profit and Loss": ["Date"],
            "PORTFOLIO Trans": ["Entry Date", "Exit Date", "Expiry"],
        }
        
        if sheet_name in datetime_columns:
            for col in datetime_columns[sheet_name]:
                if col in df.columns and not df[col].empty:
                    df[col] = pd.to_datetime(df[col])
        
        return df
    
    @classmethod
    def format_time_columns(cls, df: pd.DataFrame, sheet_name: str) -> pd.DataFrame:
        """Format time columns as HH:MM:SS strings."""
        time_columns = {
            "Max Profit and Loss": ["Max Profit Time", "Max Loss Time"],
            "PORTFOLIO Trans": ["Enter On", "Exit at"],
        }
        
        if sheet_name in time_columns:
            for col in time_columns[sheet_name]:
                if col in df.columns and not df[col].empty:
                    # Convert to time string format
                    if isinstance(df[col].iloc[0], (int, float)):
                        # Convert from HHMMSS format to HH:MM:SS
                        df[col] = df[col].apply(lambda x: f"{int(x//10000):02d}:{int((x%10000)//100):02d}:00" if pd.notna(x) else "")
        
        return df
    
    @classmethod
    def get_strategy_sheet_name(cls, strategy_name: str) -> str:
        """
        Convert strategy name to sheet name format.
        Note: Excel has 31 character limit for sheet names.
        """
        # Remove special characters and truncate if needed
        sheet_name = strategy_name.upper().replace(",", ",")
        if len(sheet_name) > 31:
            sheet_name = sheet_name[:28] + "..."
        return sheet_name


def create_golden_output_validator():
    """Create a validator function for checking output compatibility."""
    
    def validate(excel_file_path: str) -> Dict[str, Any]:
        """Validate an Excel file against golden output format."""
        errors = []
        warnings = []
        
        try:
            # Read all sheets
            xl_file = pd.ExcelFile(excel_file_path)
            sheets = xl_file.sheet_names
            
            # Required sheets
            required_sheets = [
                "PortfolioParameter", "GeneralParameter", "LegParameter",
                "Metrics", "Max Profit and Loss", "PORTFOLIO Trans", 
                "PORTFOLIO Results"
            ]
            
            # Check for missing required sheets
            missing_sheets = set(required_sheets) - set(sheets)
            if missing_sheets:
                errors.append(f"Missing required sheets: {missing_sheets}")
            
            # Validate each sheet structure
            for sheet in required_sheets:
                if sheet in sheets:
                    df = pd.read_excel(excel_file_path, sheet_name=sheet)
                    
                    if sheet == "PortfolioParameter":
                        if list(df.columns) != ["Head", "Value"]:
                            errors.append(f"{sheet}: Invalid column structure")
                    
                    elif sheet == "GeneralParameter":
                        col_errors = GoldenOutputFormat.validate_dataframe_columns(
                            df, GoldenOutputFormat.GENERAL_PARAMETER_COLUMNS, sheet
                        )
                        errors.extend(col_errors)
                    
                    elif sheet == "LegParameter":
                        col_errors = GoldenOutputFormat.validate_dataframe_columns(
                            df, GoldenOutputFormat.LEG_PARAMETER_COLUMNS, sheet
                        )
                        errors.extend(col_errors)
                    
                    elif sheet == "Metrics":
                        if "Particulars" not in df.columns:
                            errors.append(f"{sheet}: Missing 'Particulars' column")
                        if "Combined" not in df.columns:
                            errors.append(f"{sheet}: Missing 'Combined' column")
                    
                    elif sheet == "Max Profit and Loss":
                        col_errors = GoldenOutputFormat.validate_dataframe_columns(
                            df, GoldenOutputFormat.MAX_PROFIT_LOSS_COLUMNS, sheet
                        )
                        errors.extend(col_errors)
                    
                    elif sheet == "PORTFOLIO Trans":
                        col_errors = GoldenOutputFormat.validate_dataframe_columns(
                            df, GoldenOutputFormat.PORTFOLIO_TRANS_COLUMNS, sheet
                        )
                        errors.extend(col_errors)
            
            # Check for strategy-specific sheets (should match PORTFOLIO Trans structure)
            strategy_sheets = [s for s in sheets if s not in required_sheets]
            for sheet in strategy_sheets:
                df = pd.read_excel(excel_file_path, sheet_name=sheet)
                if len(df.columns) == 32:  # Should match PORTFOLIO Trans
                    col_errors = GoldenOutputFormat.validate_dataframe_columns(
                        df, GoldenOutputFormat.PORTFOLIO_TRANS_COLUMNS, sheet
                    )
                    if col_errors:
                        warnings.extend(col_errors)
                        
        except Exception as e:
            errors.append(f"Error reading file: {str(e)}")
        
        return {
            "valid": len(errors) == 0,
            "errors": errors,
            "warnings": warnings
        }
    
    return validate


if __name__ == "__main__":
    # Example usage
    print("Golden Output Format Requirements")
    print("=" * 80)
    
    # Create empty template
    template = GoldenOutputFormat.create_empty_golden_output()
    print(f"\nCreated template with {len(template)} sheets:")
    for sheet_name, df in template.items():
        print(f"  - {sheet_name}: {len(df.columns)} columns")
    
    # Create validator
    validator = create_golden_output_validator()
    
    # Test validation on golden output if it exists
    import os
    if os.path.exists("Nifty_Golden_Ouput.xlsx"):
        print("\nValidating Nifty_Golden_Ouput.xlsx...")
        result = validator("Nifty_Golden_Ouput.xlsx")
        print(f"Valid: {result['valid']}")
        if result['errors']:
            print("Errors:")
            for error in result['errors']:
                print(f"  - {error}")
        if result['warnings']:
            print("Warnings:")
            for warning in result['warnings']:
                print(f"  - {warning}")