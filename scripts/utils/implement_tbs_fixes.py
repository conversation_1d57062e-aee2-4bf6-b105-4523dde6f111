#!/usr/bin/env python3
"""
Implementation of TBS column fixes
This script provides the actual code changes needed to fix the identified issues
"""

def fix_dte_filtering_in_query():
    """Fix to add DTE filtering in the query WHERE clause"""
    
    print("\n1. FIX: Add DTE filtering to query generation")
    print("="*60)
    
    print("File: /srv/samba/shared/bt/backtester_stable/BTRUN/query_builder/leg_sql.py")
    print("\nAdd DTE filter to WHERE clause:")
    
    fix_code = '''
# In build_leg_sql function, after line 90 (where WHERE parts are built):

# Add DTE filtering if present in extra_params
dte_filter = extra_params.get('DTE') if extra_params else None
if dte_filter is not None:
    try:
        dte_value = int(dte_filter)
        if dte_value == 0:
            # For DTE=0, only select rows where trade_date equals expiry_date
            where_parts.append("oc.trade_date = oc.expiry_date")
        else:
            # For other DTE values, use the dte column
            where_parts.append(f"oc.dte = {dte_value}")
    except (ValueError, TypeError):
        pass  # Invalid DTE value, skip filtering
'''
    
    print(fix_code)
    return fix_code

def fix_strike_selection_time():
    """Fix to use StrikeSelectionTime instead of entry time"""
    
    print("\n2. FIX: Use StrikeSelectionTime for strike selection")
    print("="*60)
    
    print("File: /srv/samba/shared/bt/backtester_stable/BTRUN/query_builder/leg_sql.py")
    print("\nModify time handling:")
    
    fix_code = '''
# In build_leg_sql function, modify the time parameter handling:

def build_leg_sql(
    leg: LegModel, 
    trade_date: date, 
    entry_time: str,
    extra_params: dict = None  # Add extra_params parameter
) -> str:
    """Build SQL for a single leg..."""
    
    # Use StrikeSelectionTime if available, otherwise use entry_time
    strike_time = entry_time  # default
    if extra_params and 'StrikeSelectionTime' in extra_params:
        strike_time_raw = extra_params['StrikeSelectionTime']
        # Convert to HH:MM:SS format
        strike_time = _convert_time_format(strike_time_raw)
    
    # Use strike_time in WHERE clause instead of entry_time
    where_parts.append(f"trade_time = time '{strike_time}'")
'''
    
    print(fix_code)
    return fix_code

def fix_weekday_filtering():
    """Fix to add weekday filtering to query"""
    
    print("\n3. FIX: Add weekday filtering")
    print("="*60)
    
    print("File: /srv/samba/shared/bt/backtester_stable/BTRUN/query_builder/leg_sql.py")
    print("\nAdd weekday filter to WHERE clause:")
    
    fix_code = '''
# Add weekday filtering if present in extra_params
weekdays_str = extra_params.get('Weekdays') if extra_params else None
if weekdays_str:
    try:
        # Convert "1,2,3,4,5" to SQL IN clause
        weekday_list = [int(d.strip()) for d in weekdays_str.split(',')]
        if weekday_list:
            # HeavyDB uses EXTRACT(DOW FROM date) where 0=Sunday, 1=Monday
            # Excel uses 1=Monday, so we need to adjust
            sql_days = [(d % 7) for d in weekday_list]  # Convert Excel to SQL format
            where_parts.append(f"EXTRACT(DOW FROM oc.trade_date) IN ({','.join(map(str, sql_days))})")
    except (ValueError, TypeError):
        pass  # Invalid weekday format, skip filtering
'''
    
    print(fix_code)
    return fix_code

def create_complete_fix_file():
    """Create a complete fix file that can be applied"""
    
    complete_fix = '''#!/usr/bin/env python3
"""
Complete fix for TBS column issues
Apply these changes to the respective files
"""

# FILE: /srv/samba/shared/bt/backtester_stable/BTRUN/query_builder/leg_sql.py
# 
# Add this function for time conversion:

def _convert_time_format(value):
    """Convert various time formats to HH:MM:SS"""
    if isinstance(value, str):
        s = value.strip()
        if ":" in s:
            return s  # Already in HH:MM:SS format
        if len(s) == 5:
            s = "0" + s  # Add leading zero
        if len(s) == 6:
            # HHMMSS to HH:MM:SS
            return f"{s[0:2]}:{s[2:4]}:{s[4:6]}"
    elif isinstance(value, (int, float)):
        # Handle integer time like 91600
        s = str(int(value)).zfill(6)
        return f"{s[0:2]}:{s[2:4]}:{s[4:6]}"
    return "09:16:00"  # Default

# Modify the build_leg_sql function signature to accept extra_params:
def build_leg_sql(
    leg: LegModel, 
    trade_date: date, 
    entry_time: str,
    extra_params: dict = None
) -> str:
    """Build SQL for a single leg with extra parameter support."""
    
    # ... existing code ...
    
    # After line 90, where WHERE parts are built, add:
    
    # 1. Handle StrikeSelectionTime
    strike_time = entry_time  # default
    if extra_params and 'StrikeSelectionTime' in extra_params:
        strike_time_raw = extra_params['StrikeSelectionTime']
        strike_time = _convert_time_format(strike_time_raw)
    
    # Use strike_time instead of entry_time in WHERE clause
    where_parts = [f"trade_date = date '{trade_date_iso}'"]
    where_parts.append(f"trade_time = time '{strike_time}'")  # Use strike_time here
    
    # 2. Add DTE filtering
    dte_filter = extra_params.get('DTE') if extra_params else None
    if dte_filter is not None:
        try:
            dte_value = int(dte_filter)
            if dte_value == 0:
                # For DTE=0, only select rows where trade_date equals expiry_date
                where_parts.append("oc.trade_date = oc.expiry_date")
            else:
                # For other DTE values, use the dte column
                where_parts.append(f"oc.dte = {dte_value}")
        except (ValueError, TypeError):
            pass
    
    # 3. Add weekday filtering
    weekdays_str = extra_params.get('Weekdays') if extra_params else None
    if weekdays_str and weekdays_str != "1,2,3,4,5":  # Only filter if not all weekdays
        try:
            weekday_list = [int(d.strip()) for d in weekdays_str.split(',')]
            if weekday_list:
                # Convert Excel format (1=Mon) to SQL format (1=Mon, 0=Sun)
                # In HeavyDB, DOW returns 0=Sun, 1=Mon, ..., 6=Sat
                where_parts.append(f"EXTRACT(DOW FROM oc.trade_date) IN ({','.join(map(str, weekday_list))})")
        except (ValueError, TypeError):
            pass
    
    # ... rest of existing code ...

# FILE: /srv/samba/shared/bt/backtester_stable/BTRUN/query_builder/strategy_sql.py
#
# Update build_strategy_sql to pass extra_params to leg_sql:

def build_strategy_sql(strategy: StrategyModel, trade_date: date) -> str:
    """Return a full SQL query for all legs on *trade_date*."""
    
    # ... existing code ...
    
    # When calling build_leg_sql, pass extra_params:
    for i, leg in enumerate(strategy.legs):
        alias = f"leg{i+1}"
        
        # Get extra_params from strategy
        extra_params = getattr(strategy, 'extra_params', {})
        
        # Build SQL with extra_params
        leg_sql = build_leg_sql(
            leg, 
            trade_date, 
            strategy.entry_start,
            extra_params  # Pass extra_params
        )
        
        # ... rest of code ...
'''
    
    with open('/srv/samba/shared/tbs_column_fixes_complete.py', 'w') as f:
        f.write(complete_fix)
    
    print("\n✅ Created complete fix file: tbs_column_fixes_complete.py")
    print("This file contains all the code changes needed to fix TBS column issues")

def main():
    print("TBS Column Issue Fixes - Implementation Details")
    print("="*80)
    
    # Generate all fixes
    fix_dte_filtering_in_query()
    fix_strike_selection_time()
    fix_weekday_filtering()
    
    # Create complete fix file
    create_complete_fix_file()
    
    print("\n" + "="*80)
    print("SUMMARY OF FIXES:")
    print("="*80)
    print("1. ✅ DTE filtering: Add WHERE clause condition based on DTE value")
    print("2. ✅ StrikeSelectionTime: Use separate time for strike selection")
    print("3. ✅ Weekday filtering: Add DOW extraction to WHERE clause")
    print("4. ✅ Expiry column: Already selected via oc.*, just needs to be in result set")
    print("\nNext step: Apply these fixes to the respective files")

if __name__ == "__main__":
    main()