import pandas as pd
import sys
import json # Using json for structured output

if __name__ == "__main__":
    if len(sys.argv) < 2:
        print(json.dumps({"error": "Usage: python list_excel_sheets.py <excel_file_path>"}))
        sys.exit(1)
    
    file_path = sys.argv[1]
    try:
        xls = pd.ExcelFile(file_path)
        print(json.dumps({"file": file_path, "sheet_names": xls.sheet_names}))
    except FileNotFoundError:
        print(json.dumps({"error": f"File not found: {file_path}"}))
        sys.exit(1)
    except Exception as e:
        print(json.dumps({"error": f"Error reading Excel file {file_path}: {str(e)}"}))
        sys.exit(1) 