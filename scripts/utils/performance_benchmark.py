#!/usr/bin/env python3
"""
Performance Benchmarking Script for Enterprise GPU Backtester
Created: June 6, 2025
"""

import time
import psutil
import GPUtil
import requests
import json
import concurrent.futures
from datetime import datetime
import pandas as pd
import matplotlib.pyplot as plt
from pathlib import Path

BASE_URL = "http://**************:8000"

class PerformanceBenchmark:
    def __init__(self):
        self.results = []
        self.gpu_metrics = []
        self.cpu_metrics = []
        self.memory_metrics = []
        
    def get_system_metrics(self):
        """Capture current system metrics"""
        # CPU metrics
        cpu_percent = psutil.cpu_percent(interval=1)
        cpu_count = psutil.cpu_count()
        
        # Memory metrics
        memory = psutil.virtual_memory()
        memory_percent = memory.percent
        memory_used_gb = memory.used / (1024**3)
        
        # GPU metrics
        gpu_metrics = []
        try:
            gpus = GPUtil.getGPUs()
            for gpu in gpus:
                gpu_metrics.append({
                    'id': gpu.id,
                    'name': gpu.name,
                    'load': gpu.load * 100,
                    'memory_used': gpu.memoryUsed,
                    'memory_total': gpu.memoryTotal,
                    'temperature': gpu.temperature
                })
        except:
            gpu_metrics = [{'error': 'GPU metrics unavailable'}]
        
        return {
            'timestamp': datetime.now().isoformat(),
            'cpu_percent': cpu_percent,
            'cpu_count': cpu_count,
            'memory_percent': memory_percent,
            'memory_used_gb': memory_used_gb,
            'gpu_metrics': gpu_metrics
        }
    
    def benchmark_api_response(self, endpoint, method='GET', data=None):
        """Benchmark API response time"""
        url = f"{BASE_URL}{endpoint}"
        start_time = time.time()
        
        try:
            if method == 'GET':
                response = requests.get(url)
            elif method == 'POST':
                response = requests.post(url, json=data)
            
            end_time = time.time()
            response_time = (end_time - start_time) * 1000  # Convert to ms
            
            return {
                'endpoint': endpoint,
                'method': method,
                'status_code': response.status_code,
                'response_time_ms': response_time,
                'success': response.status_code == 200
            }
        except Exception as e:
            return {
                'endpoint': endpoint,
                'method': method,
                'error': str(e),
                'response_time_ms': -1,
                'success': False
            }
    
    def run_concurrent_load_test(self, num_requests=10):
        """Run concurrent requests to test load handling"""
        print(f"Running concurrent load test with {num_requests} requests...")
        
        endpoints = [
            '/api/v2/gpu/status',
            '/api/v1/logs/',
            '/health',
            '/'
        ]
        
        results = []
        start_time = time.time()
        
        with concurrent.futures.ThreadPoolExecutor(max_workers=10) as executor:
            futures = []
            for i in range(num_requests):
                endpoint = endpoints[i % len(endpoints)]
                future = executor.submit(self.benchmark_api_response, endpoint)
                futures.append(future)
            
            for future in concurrent.futures.as_completed(futures):
                results.append(future.result())
        
        end_time = time.time()
        total_time = end_time - start_time
        
        # Calculate statistics
        successful_requests = sum(1 for r in results if r.get('success', False))
        avg_response_time = sum(r.get('response_time_ms', 0) for r in results if r.get('success', False)) / max(successful_requests, 1)
        
        return {
            'total_requests': num_requests,
            'successful_requests': successful_requests,
            'failed_requests': num_requests - successful_requests,
            'total_time_seconds': total_time,
            'requests_per_second': num_requests / total_time,
            'avg_response_time_ms': avg_response_time,
            'results': results
        }
    
    def benchmark_ui_navigation(self):
        """Benchmark UI navigation response times"""
        print("Benchmarking UI navigation...")
        
        pages = [
            ('/', 'Homepage'),
            ('/#dashboard', 'Dashboard'),
            ('/#backtest', 'New Backtest'),
            ('/#results', 'Results'),
            ('/#logs', 'Logs'),
            ('/#templates', 'Templates')
        ]
        
        results = []
        for path, name in pages:
            result = self.benchmark_api_response(path)
            result['page_name'] = name
            results.append(result)
            time.sleep(0.5)  # Small delay between requests
        
        return results
    
    def monitor_system_during_load(self, duration_seconds=30):
        """Monitor system metrics during load test"""
        print(f"Monitoring system metrics for {duration_seconds} seconds...")
        
        metrics = []
        start_time = time.time()
        
        while time.time() - start_time < duration_seconds:
            metric = self.get_system_metrics()
            metrics.append(metric)
            time.sleep(1)
        
        return metrics
    
    def generate_report(self):
        """Generate performance benchmark report"""
        report = {
            'timestamp': datetime.now().isoformat(),
            'system_info': {
                'cpu_count': psutil.cpu_count(),
                'total_memory_gb': psutil.virtual_memory().total / (1024**3),
                'platform': psutil.platform.platform()
            },
            'benchmarks': {
                'ui_navigation': self.benchmark_ui_navigation(),
                'concurrent_load_10': self.run_concurrent_load_test(10),
                'concurrent_load_50': self.run_concurrent_load_test(50),
                'concurrent_load_100': self.run_concurrent_load_test(100)
            }
        }
        
        # Save report
        report_path = Path('/srv/samba/shared/docs/performance_benchmark_report.json')
        with open(report_path, 'w') as f:
            json.dump(report, f, indent=2)
        
        # Generate summary
        self.generate_summary(report)
        
        return report
    
    def generate_summary(self, report):
        """Generate human-readable summary"""
        summary_path = Path('/srv/samba/shared/docs/performance_benchmark_summary.md')
        
        with open(summary_path, 'w') as f:
            f.write("# Performance Benchmark Report\n\n")
            f.write(f"**Generated**: {report['timestamp']}\n\n")
            
            # System Info
            f.write("## System Information\n")
            f.write(f"- CPUs: {report['system_info']['cpu_count']}\n")
            f.write(f"- Memory: {report['system_info']['total_memory_gb']:.2f} GB\n\n")
            
            # UI Navigation
            f.write("## UI Navigation Performance\n")
            f.write("| Page | Response Time (ms) | Status |\n")
            f.write("|------|-------------------|--------|\n")
            for result in report['benchmarks']['ui_navigation']:
                status = "✅" if result.get('success') else "❌"
                time_ms = result.get('response_time_ms', -1)
                f.write(f"| {result['page_name']} | {time_ms:.2f} | {status} |\n")
            
            # Load Tests
            f.write("\n## Load Test Results\n")
            for test_name, test_data in report['benchmarks'].items():
                if test_name.startswith('concurrent_load'):
                    f.write(f"\n### {test_name.replace('_', ' ').title()}\n")
                    f.write(f"- Total Requests: {test_data['total_requests']}\n")
                    f.write(f"- Successful: {test_data['successful_requests']}\n")
                    f.write(f"- Failed: {test_data['failed_requests']}\n")
                    f.write(f"- Total Time: {test_data['total_time_seconds']:.2f}s\n")
                    f.write(f"- Requests/Second: {test_data['requests_per_second']:.2f}\n")
                    f.write(f"- Avg Response Time: {test_data['avg_response_time_ms']:.2f}ms\n")
            
            # Performance Targets
            f.write("\n## Performance vs Targets\n")
            f.write("| Metric | Target | Actual | Status |\n")
            f.write("|--------|--------|--------|--------|\n")
            
            # Check against targets
            avg_ui_response = sum(r.get('response_time_ms', 0) for r in report['benchmarks']['ui_navigation']) / len(report['benchmarks']['ui_navigation'])
            f.write(f"| UI Response Time | < 200ms | {avg_ui_response:.2f}ms | {'✅' if avg_ui_response < 200 else '❌'} |\n")
            
            load_100 = report['benchmarks']['concurrent_load_100']
            f.write(f"| 100 Concurrent Requests | > 90% success | {load_100['successful_requests']/load_100['total_requests']*100:.1f}% | {'✅' if load_100['successful_requests']/load_100['total_requests'] > 0.9 else '❌'} |\n")

def main():
    print("🚀 Starting Performance Benchmark for Enterprise GPU Backtester")
    print("=" * 60)
    
    benchmark = PerformanceBenchmark()
    
    # Run benchmarks
    report = benchmark.generate_report()
    
    print("\n✅ Benchmark complete!")
    print(f"📊 Report saved to: /srv/samba/shared/docs/performance_benchmark_report.json")
    print(f"📄 Summary saved to: /srv/samba/shared/docs/performance_benchmark_summary.md")

if __name__ == "__main__":
    main()