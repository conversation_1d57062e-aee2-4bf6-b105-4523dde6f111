#!/usr/bin/env python3
"""
Debug script to investigate why stop losses are still triggering immediately.
"""

import os
import sys
import pandas as pd
import logging
from datetime import datetime
import json

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger('debug_sl_exits')

# Path to check both Excel and log files
TRADES_DIR = '/srv/samba/shared/Trades'
OUTPUT_FILE = os.path.join(TRADES_DIR, 'backtest_output_20250521_145409.xlsx')

def check_strategy_settings():
    """Check the strategy settings to ensure SL/TP values are appropriate"""
    try:
        strategy_file = '/srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/input_tbs_fixed_exits.xlsx'
        logger.info(f"Checking strategy file: {strategy_file}")
        
        if not os.path.exists(strategy_file):
            logger.error(f"Strategy file not found: {strategy_file}")
            return
        
        # Read LegParameter sheet
        leg_params = pd.read_excel(strategy_file, sheet_name="LegParameter")
        logger.info(f"Found {len(leg_params)} legs in strategy")
        
        # Check SL/TP values for each leg
        for idx, row in leg_params.iterrows():
            leg_id = row.get('LegID', idx+1)
            transaction = row.get('Transaction', 'UNKNOWN')
            instrument = row.get('Instrument', 'UNKNOWN')
            sl_type = row.get('SLType', 'UNKNOWN')
            sl_value = row.get('SLValue', 'UNKNOWN')
            tgt_type = row.get('TGTType', 'UNKNOWN')
            tgt_value = row.get('TGTValue', 'UNKNOWN')
            
            logger.info(f"Leg {leg_id}: {transaction} {instrument} - SL: {sl_type}:{sl_value}, TP: {tgt_type}:{tgt_value}")
            
            # Check if SL values are within safe ranges
            if transaction.lower() == 'sell' and sl_type.lower() == 'percentage' and sl_value < 200:
                logger.warning(f"⚠️ Leg {leg_id} ({transaction} {instrument}) has too tight SL: {sl_value}% (should be 200%+ for SELL legs)")
            elif transaction.lower() == 'buy' and sl_type.lower() == 'percentage' and sl_value < 20:
                logger.warning(f"⚠️ Leg {leg_id} ({transaction} {instrument}) has too tight SL: {sl_value}% (should be 20%+ for BUY legs)")
        
        # Read GeneralParameter sheet
        general_params = pd.read_excel(strategy_file, sheet_name="GeneralParameter")
        if not general_params.empty:
            start_time = general_params['StartTime'].iloc[0]
            last_entry_time = general_params['LastEntryTime'].iloc[0]
            end_time = general_params['EndTime'].iloc[0]
            
            logger.info(f"Strategy times: Start={start_time}, LastEntry={last_entry_time}, End={end_time}")
            
            # Check if exit time is configured properly
            if end_time != 120000:
                logger.warning(f"⚠️ EndTime ({end_time}) is not set to 12:00:00 (120000)")
    
    except Exception as e:
        logger.error(f"Error checking strategy settings: {e}")

def check_risk_implementation():
    """Check if risk.py contains the proper fixes"""
    try:
        risk_file = '/srv/samba/shared/bt/backtester_stable/BTRUN/models/risk.py'
        logger.info(f"Checking risk implementation: {risk_file}")
        
        if not os.path.exists(risk_file):
            logger.error(f"Risk file not found: {risk_file}")
            return
        
        # Read the file content
        with open(risk_file, 'r') as f:
            content = f.read()
        
        # Check for key fixes
        fixes_to_check = [
            "# FIX: Skip the first entry candle",
            "# FIX: If SL value is too tight",
            "adjusted tight SL",
            "original_sl = sl_value"
        ]
        
        for fix in fixes_to_check:
            if fix in content:
                logger.info(f"✅ Found fix in risk.py: '{fix}'")
            else:
                logger.warning(f"❌ Missing fix in risk.py: '{fix}'")
    
    except Exception as e:
        logger.error(f"Error checking risk implementation: {e}")

def check_trade_builder():
    """Check if trade_builder.py contains the proper fixes"""
    try:
        builder_file = '/srv/samba/shared/bt/backtester_stable/BTRUN/trade_builder.py'
        logger.info(f"Checking trade builder implementation: {builder_file}")
        
        if not os.path.exists(builder_file):
            logger.error(f"Trade builder file not found: {builder_file}")
            return
        
        # Read the file content
        with open(builder_file, 'r') as f:
            content = f.read()
        
        # Check for key fixes
        fixes_to_check = [
            "IMPORTANT FIX: Get the scheduled exit time",
            "strategy_exit_time",
            "early exit detected",
            "within 5 minutes of entry"
        ]
        
        for fix in fixes_to_check:
            if fix in content.lower():
                logger.info(f"✅ Found fix in trade_builder.py: '{fix}'")
            else:
                logger.warning(f"❌ Missing fix in trade_builder.py: '{fix}'")
    
    except Exception as e:
        logger.error(f"Error checking trade builder implementation: {e}")

def analyze_output_file():
    """Analyze the output file for exit time issues"""
    try:
        if not os.path.exists(OUTPUT_FILE):
            logger.error(f"Output file not found: {OUTPUT_FILE}")
            return
        
        # Read the transaction sheet
        df = pd.read_excel(OUTPUT_FILE, sheet_name='PORTFOLIO Trans')
        logger.info(f"Found {len(df)} trades in output file")
        
        # Check exit times
        exit_times = df['exit_time'].value_counts()
        logger.info(f"Exit time distribution: {exit_times.to_dict()}")
        
        # Check exit reasons
        exit_reasons = df['reason'].value_counts()
        logger.info(f"Exit reason distribution: {exit_reasons.to_dict()}")
        
        # Check for early exits
        for idx, row in df.iterrows():
            entry_time = row['entry_time']
            exit_time = row['exit_time']
            reason = row['reason']
            
            # Parse times for comparison
            if isinstance(entry_time, str):
                entry_hours, entry_minutes, _ = map(int, entry_time.split(':'))
                entry_minutes_total = entry_hours * 60 + entry_minutes
            else:
                entry_minutes_total = 0
            
            if isinstance(exit_time, str):
                exit_hours, exit_minutes, _ = map(int, exit_time.split(':'))
                exit_minutes_total = exit_hours * 60 + exit_minutes
            else:
                exit_minutes_total = 0
            
            time_diff_minutes = exit_minutes_total - entry_minutes_total
            
            if time_diff_minutes < 5 and reason == "Stop Loss Hit":
                logger.warning(f"⚠️ Trade {idx} exited very quickly: Entry={entry_time}, Exit={exit_time}, Reason={reason}")
                logger.warning(f"  Details: {row.to_dict()}")
    
    except Exception as e:
        logger.error(f"Error analyzing output file: {e}")

def check_debug_logs():
    """Check debug logs for SL triggers"""
    try:
        # Look for the most recent debug log
        log_files = [f for f in os.listdir('.') if f.startswith('portfolio_backtest_debug_') and f.endswith('.log')]
        
        if not log_files:
            logger.error("No debug log files found")
            return
        
        # Sort by modification time (most recent first)
        log_files.sort(key=lambda x: os.path.getmtime(x), reverse=True)
        latest_log = log_files[0]
        
        logger.info(f"Checking most recent debug log: {latest_log}")
        
        # Read the log file
        with open(latest_log, 'r') as f:
            log_content = f.read()
        
        # Look for risk rule evaluation
        risk_eval_entries = []
        for line in log_content.split('\n'):
            if "RISK: evaluating risk rule" in line:
                risk_eval_entries.append(line)
            elif "RISK: Adjusted tight SL" in line:
                logger.info(f"Found SL adjustment: {line}")
            elif "RISK: SL Triggered" in line:
                logger.warning(f"⚠️ SL Trigger found: {line}")
        
        logger.info(f"Found {len(risk_eval_entries)} risk rule evaluations in log")
        
        # Check for JSON output as well
        json_output = os.path.join(TRADES_DIR, 'backtest_output_20250521_145409.json')
        if os.path.exists(json_output):
            logger.info(f"Checking JSON output: {json_output}")
            try:
                with open(json_output, 'r') as f:
                    json_data = json.load(f)
                
                if 'data' in json_data and 'trades' in json_data['data']:
                    trades = json_data['data']['trades']
                    logger.info(f"Found {len(trades)} trades in JSON output")
                    
                    for i, trade in enumerate(trades):
                        if trade.get('reason') == 'Stop Loss Hit':
                            logger.warning(f"⚠️ Trade {i} in JSON has SL hit: {trade.get('entry_time')} -> {trade.get('exit_time')}")
            except Exception as e:
                logger.error(f"Error parsing JSON output: {e}")
    
    except Exception as e:
        logger.error(f"Error checking debug logs: {e}")

def main():
    logger.info("=== Starting SL Exit Debug ===")
    
    # Check all components
    check_strategy_settings()
    check_risk_implementation()
    check_trade_builder()
    analyze_output_file()
    check_debug_logs()
    
    logger.info("=== Debug Complete ===")
    logger.info("Check the output above for any warnings or errors")

if __name__ == "__main__":
    main() 