#!/usr/bin/env python3
import os
import sys
import glob
import logging
import pandas as pd
import subprocess
from datetime import datetime, timedelta

# Configure logging
logging.basicConfig(level=logging.INFO, 
                   format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def generate_date_range(start_date='2023-01-01', end_date='2025-04-11'):
    """Generate a range of dates for data loading"""
    start = datetime.strptime(start_date, '%Y-%m-%d')
    end = datetime.strptime(end_date, '%Y-%m-%d')
    
    # Generate monthly date ranges for batch processing
    date_ranges = []
    current = start
    
    while current <= end:
        # Calculate end of month
        if current.month == 12:
            month_end = datetime(current.year+1, 1, 1) - timedelta(days=1)
        else:
            month_end = datetime(current.year, current.month+1, 1) - timedelta(days=1)
        
        # If month_end is beyond our end date, use end date
        month_end = min(month_end, end)
        
        date_ranges.append((current.strftime('%Y-%m-%d'), month_end.strftime('%Y-%m-%d')))
        
        # Move to next month
        if current.month == 12:
            current = datetime(current.year+1, 1, 1)
        else:
            current = datetime(current.year, current.month+1, 1)
    
    logger.info(f"Generated {len(date_ranges)} monthly date ranges from {start_date} to {end_date}")
    return date_ranges

def generate_sample_data_for_date_range(start_date, end_date, rows_per_day=10):
    """Generate sample data for a date range with fewer rows per day"""
    logger.info(f"Generating sample data for {start_date} to {end_date}")
    
    # Parse dates
    start = datetime.strptime(start_date, '%Y-%m-%d')
    end = datetime.strptime(end_date, '%Y-%m-%d')
    days = (end - start).days + 1
    
    # Generate trading dates
    trade_dates = [start + timedelta(days=i) for i in range(days)]
    
    # Generate expiry dates (Thursday of each week in next 2 months)
    today = trade_dates[0]
    expiry_base = today
    expiry_dates = []
    
    # Find next Thursday
    days_until_thursday = (3 - expiry_base.weekday()) % 7
    next_thursday = expiry_base + timedelta(days=days_until_thursday)
    
    # Generate 8 weekly expiries
    for i in range(8):
        expiry_dates.append(next_thursday + timedelta(weeks=i))
    
    # Add monthly expiries (last Thursday of month)
    for month in range(3):
        year = today.year + ((today.month + month) // 12)
        month_num = (today.month + month) % 12 + 1
        
        # Find last day of month
        if month_num == 12:
            last_day = datetime(year+1, 1, 1) - timedelta(days=1)
        else:
            last_day = datetime(year, month_num+1, 1) - timedelta(days=1)
        
        # Find last Thursday
        offset = (last_day.weekday() - 3) % 7
        last_thursday = last_day - timedelta(days=offset)
        
        if last_thursday not in expiry_dates:
            expiry_dates.append(last_thursday)
    
    # Sort expiry dates
    expiry_dates.sort()
    
    # Trading times - just use 3 per day to reduce data volume
    trade_times = ['09:15:00', '12:00:00', '15:00:00']
    
    # Create sample data
    data = []
    for trade_date in trade_dates:
        # Skip weekends
        if trade_date.weekday() >= 5:  # 5=Saturday, 6=Sunday
            continue
            
        for _ in range(rows_per_day):
            # Pick a time and expiry
            time_idx = _ % len(trade_times)
            expiry_idx = _ % len(expiry_dates)
            
            # Skip if expiry is before trade date
            if expiry_dates[expiry_idx] < trade_date:
                continue
                
            # Basic fields
            spot_base = 18000 + (_ % 500)
            strike_base = 17800 + (_ % 10) * 50
            
            row = {
                'trade_date': trade_date.strftime('%Y-%m-%d'),
                'trade_time': trade_times[time_idx],
                'expiry_date': expiry_dates[expiry_idx].strftime('%Y-%m-%d'),
                'index_name': 'NIFTY',
                'spot': spot_base,
                'atm_strike': spot_base // 50 * 50,
                'strike': strike_base,
                'dte': (expiry_dates[expiry_idx] - trade_date).days,
                'expiry_bucket': ['CW', 'NW', 'CM', 'NM'][_ % 4],
                'zone_id': (time_idx % 5) + 1,
                'zone_name': ['OPEN', 'MID_MORN', 'LUNCH'][time_idx],
                'call_strike_type': ['ATM', 'ITM1', 'OTM1'][_ % 3],
                'put_strike_type': ['ATM', 'OTM1', 'ITM1'][_ % 3],
            }
            
            # Call option fields
            row.update({
                'ce_symbol': f"NIFTY{expiry_dates[expiry_idx].strftime('%d%b%y').upper()}{strike_base}CE",
                'ce_open': 100 + (_ % 50),
                'ce_high': 120 + (_ % 50),
                'ce_low': 90 + (_ % 50),
                'ce_close': 110 + (_ % 50),
                'ce_volume': 1000 + _ * 10,
                'ce_oi': 5000 + _ * 50,
                'ce_coi': (_ % 200) - 100,
                'ce_iv': 15 + (_ % 10),
                'ce_delta': 0.5 + (_ % 50) / 100,
                'ce_gamma': 0.05 + (_ % 10) / 100,
                'ce_theta': -10 - (_ % 5),
                'ce_vega': 10 + (_ % 5),
                'ce_rho': 5 + (_ % 3),
            })
            
            # Put option fields
            row.update({
                'pe_symbol': f"NIFTY{expiry_dates[expiry_idx].strftime('%d%b%y').upper()}{strike_base}PE",
                'pe_open': 100 + (_ % 50),
                'pe_high': 120 + (_ % 50),
                'pe_low': 90 + (_ % 50),
                'pe_close': 110 + (_ % 50),
                'pe_volume': 1000 + _ * 10,
                'pe_oi': 5000 + _ * 50,
                'pe_coi': (_ % 200) - 100,
                'pe_iv': 15 + (_ % 10),
                'pe_delta': -0.5 - (_ % 50) / 100,
                'pe_gamma': 0.05 + (_ % 10) / 100,
                'pe_theta': -10 - (_ % 5),
                'pe_vega': 10 + (_ % 5),
                'pe_rho': -5 - (_ % 3),
            })
            
            # Future fields
            row.update({
                'future_open': spot_base + 50 + (_ % 50),
                'future_high': spot_base + 100 + (_ % 50),
                'future_low': spot_base + (_ % 50),
                'future_close': spot_base + 75 + (_ % 50),
                'future_volume': 5000 + _ * 20,
                'future_oi': 10000 + _ * 100,
                'future_coi': (_ % 400) - 200,
            })
            
            data.append(row)
    
    logger.info(f"Generated {len(data)} sample rows")
    return data

def generate_insert_sql(data, output_file):
    """Generate SQL INSERT statements from data rows"""
    logger.info(f"Generating INSERT statements for {len(data)} rows to {output_file}")
    
    with open(output_file, 'w') as f:
        # Generate INSERT statements for each row
        for row in data:
            # Start the INSERT statement
            f.write("INSERT INTO nifty_option_chain VALUES (\n")
            
            # Add values
            values = []
            for field in [
                'trade_date', 'trade_time', 'expiry_date', 'index_name', 'spot', 
                'atm_strike', 'strike', 'dte', 'expiry_bucket', 'zone_id', 
                'zone_name', 'call_strike_type', 'put_strike_type', 'ce_symbol', 
                'ce_open', 'ce_high', 'ce_low', 'ce_close', 'ce_volume', 
                'ce_oi', 'ce_coi', 'ce_iv', 'ce_delta', 'ce_gamma', 
                'ce_theta', 'ce_vega', 'ce_rho', 'pe_symbol', 'pe_open', 
                'pe_high', 'pe_low', 'pe_close', 'pe_volume', 'pe_oi', 
                'pe_coi', 'pe_iv', 'pe_delta', 'pe_gamma', 'pe_theta', 
                'pe_vega', 'pe_rho', 'future_open', 'future_high', 'future_low', 
                'future_close', 'future_volume', 'future_oi', 'future_coi'
            ]:
                value = row[field]
                if isinstance(value, str):
                    values.append(f"'{value}'")
                else:
                    values.append(str(value))
            
            # Join values with commas
            f.write("    " + ",\n    ".join(values))
            
            # End the statement
            f.write("\n);\n")
    
    logger.info(f"SQL statements written to {output_file}")
    return output_file

def execute_sql_file(sql_file):
    """Execute a SQL file using heavysql command line"""
    logger.info(f"Executing SQL file: {sql_file}")
    cmd = f"/opt/heavyai/bin/heavysql -s 127.0.0.1 --port 6274 -u admin -p HyperInteractive -d heavyai < {sql_file}"
    
    try:
        result = subprocess.run(cmd, shell=True, check=True, capture_output=True, text=True)
        logger.info(f"SQL execution successful")
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"SQL execution failed: {e}")
        logger.error(f"Error output: {e.stderr}")
        return False

def main():
    try:
        # Generate date ranges for batch processing - just pick a few representative months
        sample_months = [
            ('2023-01-01', '2023-01-31'),  # January 2023
            ('2023-06-01', '2023-06-30'),  # June 2023
            ('2023-12-01', '2023-12-31'),  # December 2023
            ('2024-01-01', '2024-01-31'),  # January 2024
            ('2024-06-01', '2024-06-30'),  # June 2024
            ('2024-12-01', '2024-12-31'),  # December 2024
            ('2025-01-01', '2025-01-31'),  # January 2025
            ('2025-04-01', '2025-04-11'),  # April 2025 (partial)
        ]
        
        logger.info(f"Processing {len(sample_months)} sample months from 2023-2025")
        
        # Process each month in batches
        total_batches = len(sample_months)
        successful_batches = 0
        total_rows = 0
        
        for i, (start_date, end_date) in enumerate(sample_months):
            logger.info(f"Processing batch {i+1}/{total_batches}: {start_date} to {end_date}")
            
            # Generate sample data for this date range
            batch_data = generate_sample_data_for_date_range(start_date, end_date)
            
            # Skip if no data
            if not batch_data:
                logger.warning(f"No data generated for {start_date} to {end_date}")
                continue
                
            # Generate SQL file
            sql_file = f"batch_{i+1}_{start_date}_{end_date}.sql"
            generate_insert_sql(batch_data, sql_file)
            
            # Execute SQL file
            if execute_sql_file(sql_file):
                successful_batches += 1
                total_rows += len(batch_data)
            else:
                logger.error(f"Failed to execute batch {i+1}")
            
            # Optional: Remove SQL file after execution
            # os.remove(sql_file)
        
        logger.info(f"Processing completed. Successfully loaded {successful_batches}/{total_batches} batches.")
        logger.info(f"Total rows inserted: {total_rows}")
        
        # Verify data in table
        verify_cmd = f"python3 check_table.py"
        subprocess.run(verify_cmd, shell=True)
            
    except Exception as e:
        logger.error(f"Error in main process: {str(e)}")

if __name__ == "__main__":
    main() 