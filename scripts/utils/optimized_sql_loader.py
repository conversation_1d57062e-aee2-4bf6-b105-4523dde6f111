#!/usr/bin/env python3
"""
Optimized SQL loader that generates SQL files for batch loading
This avoids timeout issues by creating executable SQL scripts
"""

import os
import sys
import glob
import pandas as pd
import time
from datetime import datetime
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def prepare_value(val, col_type='str'):
    """Prepare value for SQL insert"""
    if pd.isna(val) or val is None or str(val).strip() == '':
        return 'NULL'
    elif col_type == 'str':
        val_str = str(val).replace("'", "''")
        return f"'{val_str}'"
    elif col_type in ['date', 'time']:
        return f"'{val}'"
    else:  # numeric
        try:
            float(val)
            return str(val)
        except:
            return 'NULL'

def create_sql_file(csv_file, output_dir, batch_size=1000):
    """Convert CSV to SQL INSERT statements"""
    file_name = os.path.basename(csv_file)
    base_name = os.path.splitext(file_name)[0]
    
    logger.info(f"Processing {file_name}...")
    
    try:
        # Read CSV file
        df = pd.read_csv(csv_file)
        total_rows = len(df)
        
        # Process dates
        df['trade_date'] = pd.to_datetime(df['trade_date']).dt.strftime('%Y-%m-%d')
        df['trade_time'] = pd.to_datetime(df['trade_time'], format='%H:%M:%S').dt.strftime('%H:%M:%S')
        df['expiry_date'] = pd.to_datetime(df['expiry_date']).dt.strftime('%Y-%m-%d')
        
        # Create SQL files for batches
        batch_num = 0
        sql_files = []
        
        for batch_start in range(0, total_rows, batch_size):
            batch_end = min(batch_start + batch_size, total_rows)
            batch_df = df.iloc[batch_start:batch_end]
            
            values_list = []
            for _, row in batch_df.iterrows():
                values = []
                
                # Build values in exact column order
                values.append(prepare_value(row['trade_date'], 'date'))
                values.append(prepare_value(row['trade_time'], 'time'))
                values.append(prepare_value(row['expiry_date'], 'date'))
                values.append(prepare_value(row['index_name'], 'str'))
                
                # Numeric columns
                for col in ['spot', 'atm_strike', 'strike', 'dte']:
                    values.append(prepare_value(row[col], 'num'))
                
                # Text/ID columns
                values.append(prepare_value(row['expiry_bucket'], 'str'))
                values.append(prepare_value(row['zone_id'], 'num'))
                values.append(prepare_value(row['zone_name'], 'str'))
                values.append(prepare_value(row['call_strike_type'], 'str'))
                values.append(prepare_value(row['put_strike_type'], 'str'))
                
                # Option columns
                option_cols = ['ce_symbol', 'ce_open', 'ce_high', 'ce_low', 'ce_close', 'ce_volume', 
                              'ce_oi', 'ce_coi', 'ce_iv', 'ce_delta', 'ce_gamma', 'ce_theta', 
                              'ce_vega', 'ce_rho', 'pe_symbol', 'pe_open', 'pe_high', 'pe_low', 
                              'pe_close', 'pe_volume', 'pe_oi', 'pe_coi', 'pe_iv', 'pe_delta', 
                              'pe_gamma', 'pe_theta', 'pe_vega', 'pe_rho']
                
                for col in option_cols:
                    if col in ['ce_symbol', 'pe_symbol']:
                        values.append(prepare_value(row[col], 'str'))
                    else:
                        values.append(prepare_value(row[col], 'num'))
                
                # Future columns
                for col in ['future_open', 'future_high', 'future_low', 'future_close',
                           'future_volume', 'future_oi', 'future_coi']:
                    values.append(prepare_value(row[col], 'num'))
                
                values_list.append(f"({','.join(values)})")
            
            # Write SQL file
            batch_num += 1
            sql_file = os.path.join(output_dir, f"{base_name}_batch_{batch_num:04d}.sql")
            
            with open(sql_file, 'w') as f:
                f.write(f"-- Batch {batch_num} of {file_name}\n")
                f.write(f"-- Rows {batch_start + 1} to {batch_end}\n")
                f.write("INSERT INTO nifty_option_chain (\n")
                f.write("    trade_date, trade_time, expiry_date, index_name,\n")
                f.write("    spot, atm_strike, strike, dte, expiry_bucket,\n")
                f.write("    zone_id, zone_name, call_strike_type, put_strike_type,\n")
                f.write("    ce_symbol, ce_open, ce_high, ce_low, ce_close,\n")
                f.write("    ce_volume, ce_oi, ce_coi, ce_iv, ce_delta,\n")
                f.write("    ce_gamma, ce_theta, ce_vega, ce_rho,\n")
                f.write("    pe_symbol, pe_open, pe_high, pe_low, pe_close,\n")
                f.write("    pe_volume, pe_oi, pe_coi, pe_iv, pe_delta,\n")
                f.write("    pe_gamma, pe_theta, pe_vega, pe_rho,\n")
                f.write("    future_open, future_high, future_low, future_close,\n")
                f.write("    future_volume, future_oi, future_coi\n")
                f.write(") VALUES\n")
                f.write(',\n'.join(values_list))
                f.write(";\n")
            
            sql_files.append(sql_file)
        
        logger.info(f"Created {len(sql_files)} SQL files for {file_name}")
        return sql_files, total_rows
        
    except Exception as e:
        logger.error(f"Error processing {file_name}: {e}")
        return [], 0

def main():
    data_dir = "/srv/samba/shared/market_data/nifty/oc_with_futures"
    output_dir = "/srv/samba/shared/sql_batches"
    batch_size = 1000  # Small batches to avoid timeouts
    
    logger.info("=== SQL File Generator ===")
    logger.info(f"Batch size: {batch_size} rows")
    
    # Create output directory
    os.makedirs(output_dir, exist_ok=True)
    
    # Get CSV files
    csv_files = sorted(glob.glob(os.path.join(data_dir, "*.csv")))
    csv_files = [f for f in csv_files if not f.endswith('_sorted.csv')]
    
    if not csv_files:
        logger.error("No CSV files found!")
        return
    
    logger.info(f"Found {len(csv_files)} CSV files to process")
    
    # Process each file
    total_sql_files = 0
    total_rows = 0
    
    for csv_file in csv_files:
        sql_files, rows = create_sql_file(csv_file, output_dir, batch_size)
        total_sql_files += len(sql_files)
        total_rows += rows
    
    logger.info(f"\n=== Generation Complete ===")
    logger.info(f"Total SQL files created: {total_sql_files}")
    logger.info(f"Total rows prepared: {total_rows:,}")
    logger.info(f"SQL files location: {output_dir}")
    
    # Create execution script
    exec_script = os.path.join(output_dir, "execute_all.sh")
    with open(exec_script, 'w') as f:
        f.write("#!/bin/bash\n")
        f.write("# Execute all SQL batch files\n")
        f.write("HEAVYSQL='/opt/heavyai/bin/heavysql -p HyperInteractive -u admin -d heavyai'\n")
        f.write("TOTAL=$(ls -1 *.sql | grep -v execute | wc -l)\n")
        f.write("CURRENT=0\n")
        f.write("echo \"Starting batch execution of $TOTAL SQL files...\"\n")
        f.write("for sql_file in $(ls -1 *.sql | grep -v execute | sort); do\n")
        f.write("    CURRENT=$((CURRENT + 1))\n")
        f.write("    echo \"[$CURRENT/$TOTAL] Executing $sql_file...\"\n")
        f.write("    $HEAVYSQL < $sql_file\n")
        f.write("    if [ $? -eq 0 ]; then\n")
        f.write("        echo \"  ✓ Success\"\n")
        f.write("    else\n")
        f.write("        echo \"  ✗ Failed\"\n")
        f.write("    fi\n")
        f.write("done\n")
        f.write("echo \"Batch execution complete.\"\n")
    
    os.chmod(exec_script, 0o755)
    logger.info(f"Created execution script: {exec_script}")

if __name__ == "__main__":
    main()