#!/usr/bin/env python3
"""
Fast loader using PyHeavyDB's load_table_columnar method
This should be MUCH faster than SQL INSERT or COPY
"""

import os
import sys
import glob
import pandas as pd
import numpy as np
from datetime import datetime
import time
import logging

sys.path.append('/srv/samba/shared')
from bt.dal.heavydb_conn import get_conn

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def load_csv_file_fast(conn, csv_file):
    """Load CSV file using PyHeavyDB's fast columnar loading"""
    file_name = os.path.basename(csv_file)
    file_size_mb = os.path.getsize(csv_file) / (1024 * 1024)
    
    logger.info(f"Loading {file_name} ({file_size_mb:.1f} MB)...")
    
    try:
        # Read CSV in chunks to handle large files
        chunk_size = 100000  # 100k rows at a time
        total_rows = 0
        start_time = time.time()
        
        for chunk_num, chunk_df in enumerate(pd.read_csv(csv_file, chunksize=chunk_size)):
            # Process dates
            chunk_df['trade_date'] = pd.to_datetime(chunk_df['trade_date']).dt.strftime('%Y-%m-%d')
            chunk_df['trade_time'] = pd.to_datetime(chunk_df['trade_time'], format='%H:%M:%S').dt.strftime('%H:%M:%S')
            chunk_df['expiry_date'] = pd.to_datetime(chunk_df['expiry_date']).dt.strftime('%Y-%m-%d')
            
            # Replace NaN with None for proper NULL handling
            chunk_df = chunk_df.where(pd.notnull(chunk_df), None)
            
            # Convert to columnar format
            data = {}
            for col in chunk_df.columns:
                data[col] = chunk_df[col].tolist()
            
            # Use load_table_columnar for fast loading
            conn.load_table_columnar("nifty_option_chain", data)
            
            total_rows += len(chunk_df)
            
            if (chunk_num + 1) % 10 == 0:
                elapsed = time.time() - start_time
                rate = total_rows / elapsed
                logger.info(f"  Loaded {total_rows:,} rows so far ({rate:.0f} rows/sec)")
        
        elapsed = time.time() - start_time
        rate = total_rows / elapsed if elapsed > 0 else 0
        
        logger.info(f"  ✓ Completed {file_name}: {total_rows:,} rows in {elapsed:.1f}s ({rate:.0f} rows/sec)")
        return True, total_rows, elapsed
        
    except Exception as e:
        logger.error(f"  ✗ Error loading {file_name}: {e}")
        return False, 0, 0

def main():
    data_dir = "/srv/samba/shared/market_data/nifty/oc_with_futures"
    
    logger.info("=== PyHeavyDB Fast Loader ===")
    logger.info(f"Starting at: {datetime.now()}")
    
    # Connect to HeavyDB
    conn = get_conn()
    cursor = conn.cursor()
    
    # Get initial row count
    cursor.execute("SELECT COUNT(*) FROM nifty_option_chain")
    initial_count = cursor.fetchone()[0]
    logger.info(f"Initial row count: {initial_count:,}")
    
    # Get CSV files
    csv_files = sorted(glob.glob(os.path.join(data_dir, "*.csv")))
    csv_files = [f for f in csv_files if not f.endswith('_sorted.csv')]
    
    # Skip files we may have already loaded
    if initial_count > 300000:
        skip_count = min(1, initial_count // 400000)  # Conservative estimate
        csv_files = csv_files[skip_count:]
        logger.info(f"Skipping first {skip_count} files based on row count")
    
    logger.info(f"Found {len(csv_files)} CSV files to process")
    
    # Process files
    start_time = time.time()
    success_count = 0
    failed_count = 0
    total_rows_loaded = 0
    
    for i, csv_file in enumerate(csv_files, 1):
        logger.info(f"\n[{i}/{len(csv_files)}] Processing {os.path.basename(csv_file)}")
        
        success, rows_loaded, elapsed = load_csv_file_fast(conn, csv_file)
        
        if success:
            success_count += 1
            total_rows_loaded += rows_loaded
        else:
            failed_count += 1
        
        # Progress summary every 3 files
        if i % 3 == 0:
            cursor.execute("SELECT COUNT(*) FROM nifty_option_chain")
            current_count = cursor.fetchone()[0]
            elapsed_total = time.time() - start_time
            overall_rate = (current_count - initial_count) / elapsed_total
            
            logger.info(f"\n--- Progress Update ---")
            logger.info(f"Files: {i}/{len(csv_files)} ({i/len(csv_files)*100:.1f}%)")
            logger.info(f"Current rows: {current_count:,} (+{current_count - initial_count:,})")
            logger.info(f"Overall rate: {overall_rate:.0f} rows/sec")
            
            if overall_rate > 0:
                remaining_rows = 11500000 - current_count
                eta_minutes = remaining_rows / overall_rate / 60
                logger.info(f"ETA for 11.5M rows: {eta_minutes:.1f} minutes")
    
    # Final summary
    cursor.execute("SELECT COUNT(*) FROM nifty_option_chain")
    final_count = cursor.fetchone()[0]
    cursor.close()
    conn.close()
    
    total_elapsed = time.time() - start_time
    rows_added = final_count - initial_count
    
    logger.info("\n=== Loading Complete ===")
    logger.info(f"Files processed: {len(csv_files)}")
    logger.info(f"Successful: {success_count}")
    logger.info(f"Failed: {failed_count}")
    logger.info(f"Initial count: {initial_count:,}")
    logger.info(f"Final count: {final_count:,}")
    logger.info(f"Rows added: {rows_added:,}")
    logger.info(f"Total time: {total_elapsed/60:.1f} minutes")
    logger.info(f"Average rate: {rows_added/total_elapsed:.0f} rows/second")

if __name__ == "__main__":
    main()