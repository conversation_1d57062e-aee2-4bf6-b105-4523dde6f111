#!/usr/bin/env python3
"""
Optimized ETL script for loading nifty option chain data into HeavyDB
Following best practices from the Performance Optimization Guide
"""

import os
import sys
import glob
import pandas as pd
import numpy as np
from datetime import datetime, time
import logging
import shutil
from pathlib import Path

# Add parent directory to path to import dal
sys.path.append('/srv/samba/shared')

try:
    from bt.dal.heavydb_conn import get_conn
except ImportError:
    print("Error: Unable to import heavydb connection module")
    sys.exit(1)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class OptimizedNiftyETL:
    def __init__(self, data_dir="/srv/samba/shared/market_data/nifty/oc_with_futures"):
        self.data_dir = data_dir
        self.import_dir = "/var/lib/heavyai/import"  # HeavyDB's import directory
        self.conn = None
        self.cursor = None
        self.batch_size = 500000  # Optimal batch size for HeavyDB
        
    def connect(self):
        """Establish connection to HeavyDB"""
        self.conn = get_conn()
        self.cursor = self.conn.cursor()
        logger.info("Connected to HeavyDB")
        
    def verify_table_exists(self):
        """Verify the table exists"""
        try:
            self.cursor.execute("SELECT COUNT(*) FROM nifty_option_chain")
            count = self.cursor.fetchone()[0]
            logger.info(f"Table exists with {count} rows")
            return True
        except:
            logger.error("Table nifty_option_chain does not exist!")
            return False
            
    def process_csv_file(self, file_path):
        """Process a single CSV file and prepare data for loading"""
        logger.info(f"Processing file: {file_path}")
        
        try:
            # Read CSV in chunks to handle large files
            chunks = []
            chunk_count = 0
            
            for chunk in pd.read_csv(file_path, chunksize=100000):
                chunk_count += 1
                logger.info(f"Processing chunk {chunk_count} with {len(chunk)} rows")
                
                # Convert data types as per optimized schema
                chunk['trade_date'] = pd.to_datetime(chunk['trade_date']).dt.strftime('%Y-%m-%d')
                chunk['trade_time'] = pd.to_datetime(chunk['trade_time'], format='%H:%M:%S').dt.strftime('%H:%M:%S')
                
                # Create combined timestamp for efficient filtering
                chunk['trade_ts'] = pd.to_datetime(
                    chunk['trade_date'] + ' ' + chunk['trade_time']
                ).dt.strftime('%Y-%m-%d %H:%M:%S')
                
                chunk['expiry_date'] = pd.to_datetime(chunk['expiry_date']).dt.strftime('%Y-%m-%d')
                
                # Convert numeric fields, handling NaN values
                numeric_fields = ['spot', 'atm_strike', 'strike', 'dte', 'zone_id',
                                'ce_open', 'ce_high', 'ce_low', 'ce_close', 'ce_volume', 
                                'ce_oi', 'ce_coi', 'ce_iv', 'ce_delta', 'ce_gamma', 
                                'ce_theta', 'ce_vega', 'ce_rho',
                                'pe_open', 'pe_high', 'pe_low', 'pe_close', 'pe_volume',
                                'pe_oi', 'pe_coi', 'pe_iv', 'pe_delta', 'pe_gamma',
                                'pe_theta', 'pe_vega', 'pe_rho',
                                'future_open', 'future_high', 'future_low', 'future_close',
                                'future_volume', 'future_oi', 'future_coi']
                
                for field in numeric_fields:
                    if field in chunk.columns:
                        chunk[field] = pd.to_numeric(chunk[field], errors='coerce').fillna(0)
                
                # Convert integer fields
                int_fields = ['atm_strike', 'strike', 'dte', 'zone_id', 
                            'ce_volume', 'ce_oi', 'ce_coi',
                            'pe_volume', 'pe_oi', 'pe_coi',
                            'future_volume', 'future_oi', 'future_coi']
                
                for field in int_fields:
                    if field in chunk.columns:
                        chunk[field] = chunk[field].astype('int32')
                
                chunks.append(chunk)
            
            # Combine all chunks
            df = pd.concat(chunks, ignore_index=True)
            
            # Sort by trade_ts for optimal fragment skipping
            df = df.sort_values('trade_ts')
            
            logger.info(f"Processed {len(df)} rows from {file_path}")
            return df
            
        except Exception as e:
            logger.error(f"Error processing file {file_path}: {e}")
            raise
            
    def load_dataframe_to_heavydb(self, df, table_name='nifty_option_chain'):
        """Load DataFrame to HeavyDB using optimized bulk loading"""
        temp_file = None
        try:
            # Generate unique filename
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S_%f')
            filename = f"nifty_load_{timestamp}.csv"
            temp_file = os.path.join(self.import_dir, filename)
            
            # Write to HeavyDB's import directory
            logger.info(f"Writing {len(df)} rows to {temp_file}")
            df.to_csv(temp_file, index=False)
            
            # Use COPY FROM for bulk loading - use full path
            copy_sql = f"COPY {table_name} FROM '{temp_file}' WITH (header='true')"
            
            logger.info(f"Loading data to HeavyDB...")
            self.cursor.execute(copy_sql)
            
            logger.info(f"Successfully loaded {len(df)} rows")
            
        except Exception as e:
            logger.error(f"Error loading data to HeavyDB: {e}")
            raise
        finally:
            # Clean up temp file
            if temp_file and os.path.exists(temp_file):
                try:
                    os.remove(temp_file)
                    logger.info(f"Cleaned up temp file {temp_file}")
                except Exception as cleanup_error:
                    logger.warning(f"Could not remove temp file {temp_file}: {cleanup_error}")
                    
    def verify_load(self):
        """Verify data was loaded correctly"""
        try:
            self.cursor.execute("SELECT COUNT(*) FROM nifty_option_chain")
            count = self.cursor.fetchone()[0]
            logger.info(f"Total rows in table: {count:,}")
            
            # Get date range
            self.cursor.execute("""
                SELECT 
                    MIN(trade_date) as min_date,
                    MAX(trade_date) as max_date,
                    COUNT(DISTINCT trade_date) as unique_dates,
                    COUNT(DISTINCT strike) as unique_strikes
                FROM nifty_option_chain
            """)
            
            result = self.cursor.fetchone()
            if result:
                logger.info(f"Date range: {result[0]} to {result[1]}")
                logger.info(f"Unique dates: {result[2]}, Unique strikes: {result[3]}")
                
        except Exception as e:
            logger.error(f"Error verifying load: {e}")
            
    def run_etl(self, pattern="*.csv", skip_sorted=True):
        """Run the complete ETL process"""
        try:
            # Connect to database
            self.connect()
            
            # Verify table exists
            if not self.verify_table_exists():
                logger.error("Please create the table first using create_optimized_nifty_option_chain.sql")
                return
            
            # Check if import directory is writable
            if not os.access(self.import_dir, os.W_OK):
                logger.error(f"Import directory {self.import_dir} is not writable. You may need sudo access.")
                logger.info("Try running: sudo chmod 777 /var/lib/heavyai/import")
                return
            
            # Get all CSV files
            csv_files = sorted(glob.glob(os.path.join(self.data_dir, pattern)))
            
            # Skip sorted files if requested
            if skip_sorted:
                csv_files = [f for f in csv_files if not f.endswith('_sorted.csv')]
            
            logger.info(f"Found {len(csv_files)} files to process")
            
            # Process files one by one to avoid memory issues
            for i, csv_file in enumerate(csv_files, 1):
                logger.info(f"\n[{i}/{len(csv_files)}] Processing {os.path.basename(csv_file)}...")
                
                # Process and load file
                df = self.process_csv_file(csv_file)
                
                # Load in batches if file is too large
                if len(df) > self.batch_size:
                    num_batches = (len(df) + self.batch_size - 1) // self.batch_size
                    logger.info(f"Splitting into {num_batches} batches of max {self.batch_size} rows")
                    
                    for batch_num in range(0, len(df), self.batch_size):
                        batch = df.iloc[batch_num:batch_num + self.batch_size]
                        logger.info(f"Loading batch {batch_num // self.batch_size + 1}/{num_batches} ({len(batch)} rows)")
                        self.load_dataframe_to_heavydb(batch)
                else:
                    self.load_dataframe_to_heavydb(df)
                    
                # Clear dataframe to free memory
                del df
                
                # Verify after each file
                self.verify_load()
                
            # Final verification
            logger.info("\n=== FINAL VERIFICATION ===")
            self.verify_load()
            
            logger.info("\nETL process completed successfully!")
            
        except Exception as e:
            logger.error(f"ETL process failed: {e}")
            raise
        finally:
            if self.cursor:
                self.cursor.close()
            if self.conn:
                self.conn.close()

def main():
    """Main entry point"""
    import argparse
    
    parser = argparse.ArgumentParser(description='Load Nifty Option Chain data to HeavyDB')
    parser.add_argument('--data-dir', default="/srv/samba/shared/market_data/nifty/oc_with_futures",
                        help='Directory containing CSV files')
    parser.add_argument('--pattern', default="*.csv", help='File pattern to match')
    parser.add_argument('--include-sorted', action='store_true', 
                        help='Include _sorted.csv files (default: skip them)')
    
    args = parser.parse_args()
    
    etl = OptimizedNiftyETL(data_dir=args.data_dir)
    
    # Run ETL
    etl.run_etl(pattern=args.pattern, skip_sorted=not args.include_sorted)
    
if __name__ == "__main__":
    main()