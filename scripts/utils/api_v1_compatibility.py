"""
API v1 Compatibility Layer
Maps v1 endpoints to v2 functionality for backward compatibility
"""
from fastapi import APIRouter, File, UploadFile, Form, HTTPException
from fastapi.responses import JSONResponse
from typing import List, Optional
import json
import tempfile
import os
from datetime import datetime

from api.v2.contracts import BacktestRequest, JobStatus
from services.job_manager import get_job_manager, JobManager
from services.strategy_executor import StrategyExecutor

router = APIRouter(prefix="/api/v1", tags=["v1-compatibility"])

@router.post("/auto-backtest/upload-and-run")
async def upload_and_run_backtest(
    portfolio_file: Optional[UploadFile] = File(None),
    strategy_file: Optional[UploadFile] = File(None),
    tv_file: Optional[UploadFile] = File(None),
    orb_file: Optional[UploadFile] = File(None),
    oi_file: Optional[UploadFile] = File(None),
    strategy_type: str = Form(...),
    index: str = Form("NIFTY"),
    gpu_config: str = Form("auto")
):
    """
    V1 compatibility endpoint for auto-backtest
    Maps to v2 backtest submission
    """
    try:
        # Create temporary directory for uploaded files
        temp_dir = tempfile.mkdtemp()
        
        # Save uploaded files
        file_paths = {}
        
        if portfolio_file:
            portfolio_path = os.path.join(temp_dir, portfolio_file.filename)
            with open(portfolio_path, "wb") as f:
                f.write(await portfolio_file.read())
            file_paths["portfolio"] = portfolio_path  # Changed from portfolio_file
            
        if strategy_file:
            strategy_path = os.path.join(temp_dir, strategy_file.filename)
            with open(strategy_path, "wb") as f:
                f.write(await strategy_file.read())
            file_paths["strategy"] = strategy_path  # Changed from strategy_files
            
        if tv_file:
            tv_path = os.path.join(temp_dir, tv_file.filename)
            with open(tv_path, "wb") as f:
                f.write(await tv_file.read())
            file_paths["tv_file"] = tv_path
            
        if orb_file:
            orb_path = os.path.join(temp_dir, orb_file.filename)
            with open(orb_path, "wb") as f:
                f.write(await orb_file.read())
            file_paths["orb_file"] = orb_path
            
        if oi_file:
            oi_path = os.path.join(temp_dir, oi_file.filename)
            with open(oi_path, "wb") as f:
                f.write(await oi_file.read())
            file_paths["oi_file"] = oi_path
        
        # Create v2 request
        backtest_request = BacktestRequest(
            strategy_type=strategy_type.lower(),  # BacktestRequest expects lowercase
            test_mode="test",  # Use test mode for v1 compatibility
            files=file_paths  # Pass files as a dictionary
        )
        
        # Submit to job manager
        job_manager = get_job_manager()
        executor = StrategyExecutor()
        
        # Generate job ID
        job_id = f"v1_job_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{strategy_type}"
        
        # Create job
        job = job_manager.create_job(
            job_id=job_id,
            strategy_type=strategy_type.lower(),  # Use lowercase for enum
            test_mode="test",
            files=file_paths
        )
        
        # Execute in background (for v1 compatibility, we'll return immediately)
        # In production, this would be handled by a background task
        try:
            result = await executor.execute_strategy(job_id, backtest_request)
            job_manager.set_job_results(job_id, result)
            
            return JSONResponse(content={
                "status": "success",
                "job_id": job_id,
                "message": "Backtest completed successfully",
                "result": result
            })
            
        except Exception as e:
            job_manager.set_job_error(job_id, str(e))
            raise HTTPException(status_code=500, detail=str(e))
            
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))
        
@router.get("/dashboard/stats")
async def get_dashboard_stats():
    """V1 compatibility endpoint for dashboard stats"""
    return {
        "active_backtests": 0,
        "completed_today": 0,
        "failed_today": 0,
        "gpu_utilization": 0,
        "recent_backtests": []
    }
    
@router.get("/data/index-availability")
async def get_index_availability():
    """V1 compatibility endpoint for index availability"""
    return {
        "NIFTY": {
            "available": True,
            "rows": 14953923,
            "days": 590,
            "start_date": "2023-01-02",
            "end_date": "2025-05-26"
        }
    }