#!/usr/bin/env python3
"""
Ultra fast loader using direct COPY FROM commands
"""

import os
import sys
import glob
import subprocess
import time
from datetime import datetime
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def get_row_count():
    """Get current row count"""
    cmd = ["/opt/heavyai/bin/heavysql", "-s", "127.0.0.1", "--port", "6274", 
           "-u", "admin", "-p", "HyperInteractive", "-d", "heavyai"]
    
    sql = "SELECT COUNT(*) FROM nifty_option_chain;"
    
    try:
        process = subprocess.Popen(cmd, stdin=subprocess.PIPE, 
                                stdout=subprocess.PIPE, stderr=subprocess.PIPE,
                                text=True)
        stdout, stderr = process.communicate(input=sql)
        
        lines = stdout.strip().split('\n')
        for line in lines:
            if line.strip().isdigit():
                return int(line.strip())
        return 0
    except:
        return 0

def copy_csv_file(csv_file):
    """Use COPY FROM to load entire CSV file at once"""
    file_name = os.path.basename(csv_file)
    file_size_mb = os.path.getsize(csv_file) / (1024 * 1024)
    
    logger.info(f"Loading {file_name} ({file_size_mb:.1f} MB)...")
    
    # Get row count before
    count_before = get_row_count()
    
    # COPY command
    copy_sql = f"COPY nifty_option_chain FROM '{csv_file}' WITH (header='true', delimiter=',');"
    
    cmd = ["/opt/heavyai/bin/heavysql", "-s", "127.0.0.1", "--port", "6274", 
           "-u", "admin", "-p", "HyperInteractive", "-d", "heavyai"]
    
    try:
        start_time = time.time()
        process = subprocess.Popen(cmd, stdin=subprocess.PIPE, 
                                stdout=subprocess.PIPE, stderr=subprocess.PIPE,
                                text=True)
        
        stdout, stderr = process.communicate(input=copy_sql)
        elapsed = time.time() - start_time
        
        if process.returncode == 0 and "Exception" not in stderr:
            # Get row count after
            count_after = get_row_count()
            rows_loaded = count_after - count_before
            rate = rows_loaded / elapsed if elapsed > 0 else 0
            
            logger.info(f"  ✓ Loaded {rows_loaded:,} rows in {elapsed:.1f}s ({rate:.0f} rows/sec)")
            return True, rows_loaded, elapsed
        else:
            logger.error(f"  ✗ Failed: {stderr}")
            return False, 0, elapsed
            
    except Exception as e:
        logger.error(f"  ✗ Exception: {e}")
        return False, 0, 0

def main():
    data_dir = "/srv/samba/shared/market_data/nifty/oc_with_futures"
    
    logger.info("=== Ultra Fast COPY Loader ===")
    logger.info(f"Starting at: {datetime.now()}")
    
    # Get initial row count
    initial_count = get_row_count()
    logger.info(f"Initial row count: {initial_count:,}")
    
    # Get CSV files
    csv_files = sorted(glob.glob(os.path.join(data_dir, "*.csv")))
    csv_files = [f for f in csv_files if not f.endswith('_sorted.csv')]
    
    # Skip files we may have already loaded
    # Based on row count, estimate which files are done
    if initial_count > 300000:
        # We've loaded some files, skip the first few
        skip_count = initial_count // 400000  # Rough estimate
        csv_files = csv_files[skip_count:]
        logger.info(f"Skipping first {skip_count} files based on row count")
    
    logger.info(f"Found {len(csv_files)} CSV files to process")
    
    # Process files
    start_time = time.time()
    success_count = 0
    failed_count = 0
    total_rows_loaded = 0
    
    for i, csv_file in enumerate(csv_files, 1):
        logger.info(f"\n[{i}/{len(csv_files)}] Processing {os.path.basename(csv_file)}")
        
        success, rows_loaded, elapsed = copy_csv_file(csv_file)
        
        if success:
            success_count += 1
            total_rows_loaded += rows_loaded
        else:
            failed_count += 1
            logger.warning(f"Retrying {os.path.basename(csv_file)} with smaller chunks...")
            # If COPY fails, we could fall back to batch approach
            # For now, just log and continue
        
        # Progress summary every 5 files
        if i % 5 == 0:
            elapsed_total = time.time() - start_time
            current_count = get_row_count()
            overall_rate = (current_count - initial_count) / elapsed_total
            remaining_files = len(csv_files) - i
            eta_minutes = (remaining_files * elapsed_total / i) / 60
            
            logger.info(f"\n--- Progress Update ---")
            logger.info(f"Files: {i}/{len(csv_files)} | Success: {success_count} | Failed: {failed_count}")
            logger.info(f"Total rows: {current_count:,} | Added: {current_count - initial_count:,}")
            logger.info(f"Overall rate: {overall_rate:.0f} rows/sec | ETA: {eta_minutes:.1f} minutes")
    
    # Final summary
    total_elapsed = time.time() - start_time
    final_count = get_row_count()
    rows_added = final_count - initial_count
    
    logger.info("\n=== Loading Complete ===")
    logger.info(f"Files processed: {len(csv_files)}")
    logger.info(f"Successful: {success_count}")
    logger.info(f"Failed: {failed_count}")
    logger.info(f"Initial count: {initial_count:,}")
    logger.info(f"Final count: {final_count:,}")
    logger.info(f"Rows added: {rows_added:,}")
    logger.info(f"Total time: {total_elapsed/60:.1f} minutes")
    logger.info(f"Average rate: {rows_added/total_elapsed:.0f} rows/second")

if __name__ == "__main__":
    main()