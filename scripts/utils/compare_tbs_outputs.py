#!/usr/bin/env python3
"""
Compare TBS outputs from archive and new system
"""
import pandas as pd
import os

print("="*80)
print("TBS OUTPUT COMPARISON - ARCHIVE vs NEW SYSTEM")
print("="*80)

# File paths
archive_file = "/srv/samba/shared/comparison_results/archive_TBS_2024-04-01.xlsx"
new_file = "/srv/samba/shared/comparison_results/new_TBS_2024-04-01.xlsx"

# Analyze Archive TBS
print("\n1. ARCHIVE SYSTEM OUTPUT")
print("-"*40)
try:
    archive_xl = pd.ExcelFile(archive_file)
    archive_sheets = archive_xl.sheet_names
    print(f"Sheets ({len(archive_sheets)}): {', '.join(archive_sheets)}")
    
    # Check key sheets
    for sheet in ['PortfolioParameter', 'Metrics', 'PORTFOLIO Trans']:
        if sheet in archive_sheets:
            df = pd.read_excel(archive_file, sheet_name=sheet)
            print(f"\n{sheet}:")
            print(f"  Shape: {df.shape}")
            if sheet == 'Metrics':
                # Show some metrics
                for metric in ['Gross P&L', 'Net P&L', 'Win Rate %']:
                    rows = df[df['Particulars'] == metric]
                    if not rows.empty:
                        val = rows['Combined'].values[0]
                        print(f"  {metric}: {val}")
except Exception as e:
    print(f"Error reading archive file: {e}")

# Analyze New TBS
print("\n\n2. NEW SYSTEM OUTPUT")
print("-"*40)
try:
    new_xl = pd.ExcelFile(new_file)
    new_sheets = new_xl.sheet_names
    print(f"Sheets ({len(new_sheets)}): {', '.join(new_sheets)}")
    
    # Check key sheets
    for sheet in ['PortfolioParameter', 'Metrics', 'PORTFOLIO Trans']:
        if sheet in new_sheets:
            df = pd.read_excel(new_file, sheet_name=sheet)
            print(f"\n{sheet}:")
            print(f"  Shape: {df.shape}")
            if sheet == 'Metrics':
                # Show some metrics
                for metric in ['Gross P&L', 'Net P&L', 'Win Rate %']:
                    rows = df[df['Particulars'] == metric]
                    if not rows.empty:
                        val = rows['Combined'].values[0]
                        print(f"  {metric}: {val}")
except Exception as e:
    print(f"Error reading new file: {e}")

# Compare sheets
print("\n\n3. COMPARISON")
print("-"*40)
try:
    archive_set = set(archive_sheets)
    new_set = set(new_sheets)
    
    common = archive_set & new_set
    only_archive = archive_set - new_set
    only_new = new_set - archive_set
    
    print(f"Common sheets: {len(common)}")
    print(f"Only in archive: {only_archive}")
    print(f"Only in new: {only_new}")
    
    # Missing critical sheets
    critical_sheets = ['PortfolioParameter', 'GeneralParameter', 'LegParameter', 
                      'Metrics', 'Max Profit and Loss', 'PORTFOLIO Trans', 'PORTFOLIO Results']
    
    missing_in_new = [s for s in critical_sheets if s not in new_sheets]
    if missing_in_new:
        print(f"\n⚠️  Critical sheets missing in new system: {missing_in_new}")
    else:
        print("\n✓ All critical sheets present in new system")
        
    # Check for strategy-specific sheets
    print("\nStrategy-specific sheets:")
    print(f"  Archive: {[s for s in archive_sheets if 'Trans' in s or 'Results' in s]}")
    print(f"  New: {[s for s in new_sheets if 'Trans' in s or 'Results' in s]}")
    
except Exception as e:
    print(f"Error in comparison: {e}")

print("\n" + "="*80)
print("KEY FINDINGS:")
print("- Archive has 9 sheets including strategy-specific Trans/Results sheets")
print("- New system should generate the same structure")
print("- Both should have metrics, transactions, and parameter sheets")