#!/usr/bin/env python3
"""
Find a complete TBS input file that has all required sheets
"""
import pandas as pd
import sys
import os

def check_tbs_file(file_path):
    """Check if a TBS file has all required sheets"""
    try:
        xl = pd.ExcelFile(file_path)
        required_sheets = ['GeneralParameter', 'LegParameter']
        optional_sheets = ['PortfolioSetting', 'StrategySetting']
        
        has_required = all(sheet in xl.sheet_names for sheet in required_sheets)
        has_optional = any(sheet in xl.sheet_names for sheet in optional_sheets)
        
        print(f"\n{file_path}:")
        print(f"  Sheets: {xl.sheet_names}")
        print(f"  Has required: {has_required}")
        print(f"  Has optional: {has_optional}")
        
        if has_required:
            # Check GeneralParameter for strategies
            try:
                gp = pd.read_excel(file_path, sheet_name='GeneralParameter')
                strategies = len(gp) if not gp.empty else 0
                print(f"  Strategies: {strategies}")
                
                if strategies > 0:
                    print(f"  Sample strategies: {list(gp['StrategyName'].head(3))}")
                    return True, strategies
            except Exception as e:
                print(f"  Error reading GeneralParameter: {e}")
        
        return has_required, 0
        
    except Exception as e:
        print(f"  Error: {e}")
        return False, 0

def main():
    tbs_files = [
        "/srv/samba/shared/bt/backtester_stable/BTRUN/test_files/TBS_Test_Input.xlsx",
        "/srv/samba/shared/INPUT TBS MULTI LEGS.xlsx",
        "/srv/samba/shared/input_tbs_multi_legs.xlsx",
        "/srv/samba/shared/TBS_EXACT_TEMPLATE.xlsx",
        "/srv/samba/shared/TBS_TEMPLATE_ALL_COLUMNS.xlsx",
        "/srv/samba/shared/input_sheets/INPUT TBS.xlsx"
    ]
    
    print("Checking TBS files for completeness...")
    
    best_file = None
    best_strategies = 0
    
    for file_path in tbs_files:
        if os.path.exists(file_path):
            is_valid, strategies = check_tbs_file(file_path)
            if is_valid and strategies > best_strategies:
                best_file = file_path
                best_strategies = strategies
        else:
            print(f"\n{file_path}: FILE NOT FOUND")
    
    print(f"\n" + "="*80)
    if best_file:
        print(f"BEST TBS FILE FOUND: {best_file}")
        print(f"Strategies: {best_strategies}")
        
        # Show more details about the best file
        try:
            xl = pd.ExcelFile(best_file)
            print(f"All sheets: {xl.sheet_names}")
            
            # Show sample data
            gp = pd.read_excel(best_file, sheet_name='GeneralParameter')
            print(f"\nSample GeneralParameter:")
            print(gp[['StrategyName', 'Underlying', 'Index', 'DTE']].head())
            
        except Exception as e:
            print(f"Error showing details: {e}")
    else:
        print("NO SUITABLE TBS FILE FOUND")

if __name__ == "__main__":
    main()