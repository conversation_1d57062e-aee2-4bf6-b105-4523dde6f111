#!/usr/bin/env python3
import pandas as pd
import sys

def read_excel_structure(filepath):
    try:
        xls = pd.ExcelFile(filepath)
        print(f'Excel file: {filepath}')
        print(f'Available sheets: {xls.sheet_names}')
        
        for sheet in xls.sheet_names:
            print(f'\n== {sheet} Sheet Columns ==')
            df = pd.read_excel(xls, sheet)
            print(df.columns.tolist())
            
            # Print a few rows as examples
            print(f"\nSample rows from {sheet}:")
            try:
                print(df.head(3).to_string())
            except:
                print("Cannot display sample rows")
    except Exception as e:
        print(f"Error reading {filepath}: {e}")

if __name__ == "__main__":
    if len(sys.argv) < 2:
        print("Usage: python read_excel_columns.py <excel_file_path>")
        sys.exit(1)
    
    read_excel_structure(sys.argv[1]) 