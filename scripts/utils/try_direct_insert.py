#!/usr/bin/env python3
import os
import sys
import csv
import subprocess
import time
import argparse
from datetime import datetime

# Configuration
CSV_DIR = "/srv/samba/shared/market_data/nifty/oc_with_futures/"
MAX_RETRIES = 3
TIMEOUT = 300  # 5 minutes timeout for SQL queries

def parse_args():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser(description='Direct SQL INSERT loader for Nifty Option Chain')
    parser.add_argument('--file', type=str, required=True, help='CSV file to load')
    parser.add_argument('--limit', type=int, default=1000, help='Maximum number of rows to load')
    parser.add_argument('--batch-size', type=int, default=100, help='Batch size for inserts')
    parser.add_argument('--drop-table', action='store_true', help='Drop and recreate the table')
    parser.add_argument('--max-errors', type=int, default=10, help='Maximum number of errors before aborting')
    return parser.parse_args()

def run_sql_command(sql, timeout=TIMEOUT):
    """Run SQL command via heavysql with timeout"""
    cmd = ["/opt/heavyai/bin/heavysql", "-s", "127.0.0.1", "--port", "6274", 
           "-u", "admin", "-p", "HyperInteractive", "-d", "heavyai"]
    
    try:
        process = subprocess.Popen(cmd, stdin=subprocess.PIPE, 
                                stdout=subprocess.PIPE, stderr=subprocess.PIPE,
                                text=True)
        
        stdout, stderr = process.communicate(input=sql, timeout=timeout)
        
        if stderr and "Error" in stderr:
            print(f"SQL Error: {stderr}")
            return False, stderr
        
        return True, stdout
    except subprocess.TimeoutExpired:
        print(f"SQL command timed out after {timeout} seconds")
        # Kill the process
        process.kill()
        return False, f"Timeout after {timeout}s"
    except Exception as e:
        print(f"Error executing SQL: {e}")
        return False, str(e)

def get_row_count():
    """Get the current row count in the table"""
    success, result = run_sql_command("SELECT COUNT(*) FROM nifty_option_chain;")
    if success:
        try:
            # Extract the count from the output
            lines = result.strip().split('\n')
            for line in lines:
                if line.isdigit():
                    return int(line)
            return 0
        except Exception as e:
            print(f"Error parsing row count: {e}")
            return 0
    else:
        print("Failed to get row count")
        return 0

def format_value(val, col_type):
    """Format a value based on its column type for SQL insertion"""
    if val == '':
        return 'NULL'
    
    if col_type == 'TEXT':
        # Escape single quotes
        val = val.replace("'", "''")
        return f"'{val}'"
    elif col_type == 'DATE':
        return f"'{val}'"
    elif col_type == 'TIME':
        return f"'{val}'"
    else:  # Numeric types
        return val

def get_column_types():
    """Return a dictionary mapping column names to their SQL types"""
    column_types = {
        'trade_date': 'DATE',
        'trade_time': 'TIME',
        'expiry_date': 'DATE',
        'index_name': 'TEXT',
        'spot': 'DOUBLE',
        'atm_strike': 'DOUBLE',
        'strike': 'DOUBLE',
        'dte': 'INT',
        'expiry_bucket': 'TEXT',
        'zone_id': 'SMALLINT',
        'zone_name': 'TEXT',
        'call_strike_type': 'TEXT',
        'put_strike_type': 'TEXT',
        'ce_symbol': 'TEXT',
        'ce_open': 'DOUBLE',
        'ce_high': 'DOUBLE',
        'ce_low': 'DOUBLE',
        'ce_close': 'DOUBLE',
        'ce_volume': 'BIGINT',
        'ce_oi': 'BIGINT',
        'ce_coi': 'BIGINT',
        'ce_iv': 'DOUBLE',
        'ce_delta': 'DOUBLE',
        'ce_gamma': 'DOUBLE',
        'ce_theta': 'DOUBLE',
        'ce_vega': 'DOUBLE',
        'ce_rho': 'DOUBLE',
        'pe_symbol': 'TEXT',
        'pe_open': 'DOUBLE',
        'pe_high': 'DOUBLE',
        'pe_low': 'DOUBLE',
        'pe_close': 'DOUBLE',
        'pe_volume': 'BIGINT',
        'pe_oi': 'BIGINT',
        'pe_coi': 'BIGINT',
        'pe_iv': 'DOUBLE',
        'pe_delta': 'DOUBLE',
        'pe_gamma': 'DOUBLE',
        'pe_theta': 'DOUBLE',
        'pe_vega': 'DOUBLE',
        'pe_rho': 'DOUBLE',
        'future_open': 'DOUBLE',
        'future_high': 'DOUBLE',
        'future_low': 'DOUBLE',
        'future_close': 'DOUBLE',
        'future_volume': 'BIGINT',
        'future_oi': 'BIGINT',
        'future_coi': 'BIGINT'
    }
    return column_types

def main():
    args = parse_args()
    
    print("=== Direct SQL INSERT Nifty Option Chain Loader ===")
    print(f"Starting at: {datetime.now()}")
    
    csv_file = args.file
    if not os.path.exists(csv_file):
        print(f"Error: File {csv_file} does not exist")
        return 1
    
    # Create or recreate table if needed
    if args.drop_table:
        print("Dropping and recreating table nifty_option_chain...")
        try:
            with open('create_noc_table_fixed.sql', 'r') as f:
                create_table_sql = f.read()
            
            success, message = run_sql_command(create_table_sql)
            if not success:
                print(f"Failed to create table: {message}")
                return 1
            
            print("Table created successfully")
        except Exception as e:
            print(f"Error handling SQL file: {e}")
            return 1
    
    # Get column types
    column_types = get_column_types()
    
    # Process the CSV file
    row_count = 0
    error_count = 0
    success_count = 0
    start_time = time.time()
    
    try:
        with open(csv_file, 'r') as f:
            reader = csv.reader(f)
            header = next(reader)  # Read header row
            
            # Verify header matches our expectations
            if len(header) != len(column_types):
                print(f"Warning: CSV has {len(header)} columns, expected {len(column_types)}")
                missing_cols = set(column_types.keys()) - set(header)
                extra_cols = set(header) - set(column_types.keys())
                if missing_cols:
                    print(f"Missing columns: {missing_cols}")
                if extra_cols:
                    print(f"Extra columns: {extra_cols}")
            
            # Batch processing
            batch = []
            
            for row in reader:
                row_count += 1
                
                if row_count > args.limit:
                    print(f"Reached limit of {args.limit} rows")
                    break
                
                # Format values for SQL
                values = []
                for i, val in enumerate(row):
                    if i < len(header):
                        col_name = header[i]
                        col_type = column_types.get(col_name, 'TEXT')
                        values.append(format_value(val, col_type))
                
                # Add row to batch
                row_sql = f"({', '.join(values)})"
                batch.append(row_sql)
                
                # Execute batch when it reaches batch size
                if len(batch) >= args.batch_size:
                    insert_sql = f"INSERT INTO nifty_option_chain VALUES {', '.join(batch)};"
                    success, message = run_sql_command(insert_sql)
                    
                    if success:
                        success_count += len(batch)
                        print(f"Inserted {len(batch)} rows, total {success_count}/{row_count}")
                    else:
                        error_count += 1
                        print(f"Failed to insert batch at row {row_count}: {message}")
                        
                        if error_count >= args.max_errors:
                            print(f"Aborting after {error_count} errors")
                            break
                    
                    batch = []
                
                # Progress reporting
                if row_count % 1000 == 0:
                    elapsed = time.time() - start_time
                    rows_per_sec = row_count / elapsed if elapsed > 0 else 0
                    print(f"Processed {row_count} rows, inserted {success_count} rows ({rows_per_sec:.2f} rows/sec)")
            
            # Insert any remaining rows
            if batch:
                insert_sql = f"INSERT INTO nifty_option_chain VALUES {', '.join(batch)};"
                success, message = run_sql_command(insert_sql)
                
                if success:
                    success_count += len(batch)
                    print(f"Inserted final {len(batch)} rows, total {success_count}/{row_count}")
                else:
                    error_count += 1
                    print(f"Failed to insert final batch: {message}")
    
    except Exception as e:
        print(f"Error processing file: {e}")
        return 1
    
    # Final report
    elapsed = time.time() - start_time
    rows_per_sec = row_count / elapsed if elapsed > 0 else 0
    
    print("\n=== Loading Results ===")
    print(f"Processed {row_count} rows")
    print(f"Successfully inserted {success_count} rows")
    print(f"Failed batches: {error_count}")
    print(f"Time elapsed: {elapsed:.2f} seconds")
    print(f"Average speed: {rows_per_sec:.2f} rows/sec")
    
    # Final row count
    final_count = get_row_count()
    print(f"Final table row count: {final_count}")
    
    print(f"Finished at: {datetime.now()}")
    return 0

if __name__ == "__main__":
    sys.exit(main()) 