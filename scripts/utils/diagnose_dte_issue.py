#!/usr/bin/env python3
"""
Diagnose why DTE filtering is not working
"""

import sys
import json
import pandas as pd

sys.path.insert(0, '/srv/samba/shared')

from bt.backtester_stable.BTRUN.excel_parser import portfolio_parser, strategy_parser

def diagnose_dte():
    """Check if DTE is being parsed correctly"""
    
    print("="*80)
    print("DIAGNOSING DTE PARSING")
    print("="*80)
    
    # Parse portfolio
    portfolio_file = '/srv/samba/shared/test_dte0_portfolio_proper.xlsx'
    strategy_file = '/srv/samba/shared/test_dte0_strategy_proper.xlsx'
    
    print(f"\n1. Parsing portfolio: {portfolio_file}")
    portfolios = portfolio_parser.parse_portfolio_excel(portfolio_file)
    
    print(f"\n2. Number of portfolios parsed: {len(portfolios)}")
    
    for portfolio in portfolios:
        if hasattr(portfolio, 'portfolio_name'):
            print(f"\n3. Portfolio name: {portfolio.portfolio_name}")
            print(f"   Strategies: {len(portfolio.strategies)}")
        else:
            print(f"\n3. Portfolio object type: {type(portfolio)}")
            print(f"   Portfolio data: {portfolio}")
        
        for i, strategy in enumerate(portfolio.strategies):
            print(f"\n   Strategy {i+1}: {strategy.strategy_name}")
            print(f"   Entry time: {strategy.entry_start}")
            print(f"   Exit time: {strategy.entry_end}")
            print(f"   Extra params: {json.dumps(strategy.extra_params, indent=2)}")
            
            # Check if DTE is in extra_params
            if 'DTE' in strategy.extra_params:
                print(f"   ✅ DTE found: {strategy.extra_params['DTE']}")
            else:
                print(f"   ❌ DTE NOT FOUND in extra_params!")
                
                # Check GeneralParameter sheet directly
                print("\n4. Checking GeneralParameter sheet directly:")
                general_df = pd.read_excel(strategy_file, sheet_name='GeneralParameter')
                print(general_df[['StrategyName', 'DTE']].to_string())
                
            print(f"\n   Legs: {len(strategy.legs)}")
            for leg in strategy.legs:
                print(f"     - Leg {leg.leg_id}: {leg.option_type} {leg.transaction}")

def check_query_generation():
    """Check if query includes DTE filter"""
    from bt.backtester_stable.BTRUN.query_builder.leg_sql import build_leg_sql
    from bt.backtester_stable.BTRUN.models.leg import LegModel
    from bt.backtester_stable.BTRUN.models.common import OptionType, TransactionType, ExpiryRule, StrikeRule
    from datetime import date
    
    print("\n"+"="*80)
    print("CHECKING QUERY GENERATION")
    print("="*80)
    
    # Create test leg
    test_leg = LegModel(
        leg_id="test1",
        index="NIFTY",
        option_type=OptionType.CALL,
        transaction=TransactionType.SELL,
        expiry_rule=ExpiryRule.CURRENT_WEEK,
        strike_rule=StrikeRule.ATM,
        lots=1,
        entry_time="09:16:00",
        exit_time="12:00:00",
        extra_params={}
    )
    
    # Test with DTE=0
    strategy_params = {'DTE': 0, 'StrikeSelectionTime': 91600}
    
    query = build_leg_sql(
        test_leg,
        "2024-01-04",
        alias="oc",
        entry_time="09:16:00",
        strategy_extra_params=strategy_params
    )
    
    print("\nGenerated query with DTE=0:")
    print(query)
    
    # Check if DTE filter is in query
    if "trade_date = oc.expiry_date" in query:
        print("\n✅ DTE=0 filter found in query!")
    else:
        print("\n❌ DTE=0 filter NOT in query!")

if __name__ == "__main__":
    diagnose_dte()
    check_query_generation()