#!/usr/bin/env python3
"""
Complete fix for TBS column issues
Apply these changes to the respective files
"""

# FILE: /srv/samba/shared/bt/backtester_stable/BTRUN/query_builder/leg_sql.py
# 
# Add this function for time conversion:

def _convert_time_format(value):
    """Convert various time formats to HH:MM:SS"""
    if isinstance(value, str):
        s = value.strip()
        if ":" in s:
            return s  # Already in HH:MM:SS format
        if len(s) == 5:
            s = "0" + s  # Add leading zero
        if len(s) == 6:
            # HHMMSS to HH:MM:SS
            return f"{s[0:2]}:{s[2:4]}:{s[4:6]}"
    elif isinstance(value, (int, float)):
        # Handle integer time like 91600
        s = str(int(value)).zfill(6)
        return f"{s[0:2]}:{s[2:4]}:{s[4:6]}"
    return "09:16:00"  # Default

# Modify the build_leg_sql function signature to accept extra_params:
def build_leg_sql(
    leg: LegModel, 
    trade_date: date, 
    entry_time: str,
    extra_params: dict = None
) -> str:
    """Build SQL for a single leg with extra parameter support."""
    
    # ... existing code ...
    
    # After line 90, where WHERE parts are built, add:
    
    # 1. Handle StrikeSelectionTime
    strike_time = entry_time  # default
    if extra_params and 'StrikeSelectionTime' in extra_params:
        strike_time_raw = extra_params['StrikeSelectionTime']
        strike_time = _convert_time_format(strike_time_raw)
    
    # Use strike_time instead of entry_time in WHERE clause
    where_parts = [f"trade_date = date '{trade_date_iso}'"]
    where_parts.append(f"trade_time = time '{strike_time}'")  # Use strike_time here
    
    # 2. Add DTE filtering
    dte_filter = extra_params.get('DTE') if extra_params else None
    if dte_filter is not None:
        try:
            dte_value = int(dte_filter)
            if dte_value == 0:
                # For DTE=0, only select rows where trade_date equals expiry_date
                where_parts.append("oc.trade_date = oc.expiry_date")
            else:
                # For other DTE values, use the dte column
                where_parts.append(f"oc.dte = {dte_value}")
        except (ValueError, TypeError):
            pass
    
    # 3. Add weekday filtering
    weekdays_str = extra_params.get('Weekdays') if extra_params else None
    if weekdays_str and weekdays_str != "1,2,3,4,5":  # Only filter if not all weekdays
        try:
            weekday_list = [int(d.strip()) for d in weekdays_str.split(',')]
            if weekday_list:
                # Convert Excel format (1=Mon) to SQL format (1=Mon, 0=Sun)
                # In HeavyDB, DOW returns 0=Sun, 1=Mon, ..., 6=Sat
                where_parts.append(f"EXTRACT(DOW FROM oc.trade_date) IN ({','.join(map(str, weekday_list))})")
        except (ValueError, TypeError):
            pass
    
    # ... rest of existing code ...

# FILE: /srv/samba/shared/bt/backtester_stable/BTRUN/query_builder/strategy_sql.py
#
# Update build_strategy_sql to pass extra_params to leg_sql:

def build_strategy_sql(strategy: StrategyModel, trade_date: date) -> str:
    """Return a full SQL query for all legs on *trade_date*."""
    
    # ... existing code ...
    
    # When calling build_leg_sql, pass extra_params:
    for i, leg in enumerate(strategy.legs):
        alias = f"leg{i+1}"
        
        # Get extra_params from strategy
        extra_params = getattr(strategy, 'extra_params', {})
        
        # Build SQL with extra_params
        leg_sql = build_leg_sql(
            leg, 
            trade_date, 
            strategy.entry_start,
            extra_params  # Pass extra_params
        )
        
        # ... rest of code ...
