#!/usr/bin/env python3
"""
Direct CSV loader for nifty option chain that matches the table structure
"""
import os
import csv
import time
import glob
from datetime import datetime
from multiprocessing import Pool, cpu_count
import subprocess

def get_row_count():
    """Get current row count"""
    cmd = """echo "SELECT COUNT(*) FROM nifty_option_chain;" | /opt/heavyai/bin/heavysql -p HyperInteractive -u admin -d heavyai --quiet"""
    result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
    if result.returncode == 0:
        try:
            return int(result.stdout.strip())
        except:
            return 0
    return 0

def clean_value(val, default='0'):
    """Clean a value for SQL insertion"""
    if val is None or val == '' or val == 'None':
        return default
    return val

def load_csv_batch(args):
    """Load a batch of CSV data directly"""
    csv_file, start_idx, batch_size = args
    
    rows = []
    with open(csv_file, 'r') as f:
        reader = csv.DictReader(f)
        for i, row in enumerate(reader):
            if i < start_idx:
                continue
            if i >= start_idx + batch_size:
                break
            rows.append(row)
    
    if not rows:
        return 0
    
    # Build single INSERT with multiple VALUES
    values_list = []
    for row in rows:
        # Clean numeric values
        values = f"""(
            '{row['trade_date']}',
            '{row['trade_time']}',
            '{row['expiry_date']}',
            '{row['index_name']}',
            {clean_value(row['spot'])},
            {clean_value(row['atm_strike'])},
            {clean_value(row['strike'])},
            {clean_value(row['dte'])},
            '{clean_value(row['expiry_bucket'], '')}',
            {clean_value(row['zone_id'])},
            '{clean_value(row['zone_name'], '')}',
            '{clean_value(row['call_strike_type'], '')}',
            '{clean_value(row['put_strike_type'], '')}',
            '{clean_value(row['ce_symbol'], '')}',
            {clean_value(row['ce_open'])},
            {clean_value(row['ce_high'])},
            {clean_value(row['ce_low'])},
            {clean_value(row['ce_close'])},
            {clean_value(row['ce_volume'])},
            {clean_value(row['ce_oi'])},
            {clean_value(row['ce_coi'])},
            {clean_value(row['ce_iv'])},
            {clean_value(row['ce_delta'])},
            {clean_value(row['ce_gamma'])},
            {clean_value(row['ce_theta'])},
            {clean_value(row['ce_vega'])},
            {clean_value(row['ce_rho'])},
            '{clean_value(row['pe_symbol'], '')}',
            {clean_value(row['pe_open'])},
            {clean_value(row['pe_high'])},
            {clean_value(row['pe_low'])},
            {clean_value(row['pe_close'])},
            {clean_value(row['pe_volume'])},
            {clean_value(row['pe_oi'])},
            {clean_value(row['pe_coi'])},
            {clean_value(row['pe_iv'])},
            {clean_value(row['pe_delta'])},
            {clean_value(row['pe_gamma'])},
            {clean_value(row['pe_theta'])},
            {clean_value(row['pe_vega'])},
            {clean_value(row['pe_rho'])},
            {clean_value(row['future_open'])},
            {clean_value(row['future_high'])},
            {clean_value(row['future_low'])},
            {clean_value(row['future_close'])},
            {clean_value(row['future_volume'])},
            {clean_value(row['future_oi'])},
            {clean_value(row['future_coi'])}
        )"""
        values_list.append(values)
    
    sql = f"""INSERT INTO nifty_option_chain VALUES {','.join(values_list)};"""
    
    # Write to temp file and execute
    temp_file = f"/tmp/batch_{os.getpid()}_{time.time()}.sql"
    with open(temp_file, 'w') as f:
        f.write(sql)
    
    try:
        cmd = f'/opt/heavyai/bin/heavysql -p HyperInteractive -u admin -d heavyai < {temp_file} 2>&1'
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
        if result.returncode == 0:
            return len(rows)
        else:
            print(f"\nError in batch: {result.stdout}")
            return 0
    finally:
        if os.path.exists(temp_file):
            os.remove(temp_file)

def process_file(csv_file):
    """Process a single CSV file in batches"""
    print(f"\nProcessing: {os.path.basename(csv_file)}")
    
    # Count rows in file
    with open(csv_file, 'r') as f:
        total_rows = sum(1 for line in f) - 1  # Subtract header
    
    print(f"Total rows: {total_rows:,}")
    
    # Process in batches
    batch_size = 1000  # Batch size for stability
    batches = []
    
    for start_idx in range(0, total_rows, batch_size):
        batches.append((csv_file, start_idx, batch_size))
    
    # Process batches in parallel
    loaded = 0
    start_time = time.time()
    
    with Pool(processes=min(8, cpu_count()//2)) as pool:
        for i, result in enumerate(pool.imap(load_csv_batch, batches)):
            loaded += result
            elapsed = time.time() - start_time
            if elapsed > 0:
                speed = loaded / elapsed
                progress = (i + 1) / len(batches) * 100
                print(f"  Progress: {progress:.1f}%, Loaded: {loaded:,} rows, Speed: {speed:.0f} rows/sec", end='\r')
    
    elapsed = time.time() - start_time
    print(f"\n  Completed: {loaded:,} rows in {elapsed:.1f}s ({loaded/elapsed:.0f} rows/sec)")
    return loaded

def main():
    data_dir = "/srv/samba/shared/market_data/nifty/oc_with_futures"
    
    print("=== Direct CSV Loader for Nifty Option Chain ===")
    print(f"Starting at: {datetime.now()}")
    
    initial_count = get_row_count()
    print(f"Initial row count: {initial_count:,}")
    
    # Get all CSV files
    csv_files = sorted(glob.glob(os.path.join(data_dir, "*.csv")))
    print(f"Found {len(csv_files)} CSV files")
    
    # Process each file
    total_loaded = 0
    start_time = time.time()
    
    for i, csv_file in enumerate(csv_files):
        print(f"\n[{i+1}/{len(csv_files)}] {os.path.basename(csv_file)}")
        file_start = time.time()
        loaded = process_file(csv_file)
        file_time = time.time() - file_start
        total_loaded += loaded
        
        # Show overall progress
        elapsed = time.time() - start_time
        overall_speed = total_loaded / elapsed if elapsed > 0 else 0
        
        # Estimate remaining time
        avg_per_file = total_loaded / (i + 1)
        remaining_files = len(csv_files) - (i + 1)
        eta_seconds = (remaining_files * avg_per_file) / overall_speed if overall_speed > 0 else 0
        eta_minutes = eta_seconds / 60
        
        print(f"  Total loaded: {total_loaded:,}, Overall speed: {overall_speed:.0f} rows/sec")
        print(f"  ETA: {eta_minutes:.1f} minutes")
    
    final_count = get_row_count()
    total_time = time.time() - start_time
    
    print("\n=== Summary ===")
    print(f"Files processed: {len(csv_files)}")
    print(f"Rows loaded: {total_loaded:,}")
    print(f"Final row count: {final_count:,}")
    print(f"Total time: {total_time/60:.1f} minutes")
    print(f"Average speed: {total_loaded/total_time:.0f} rows/sec")
    print(f"Completed at: {datetime.now()}")

if __name__ == "__main__":
    main()