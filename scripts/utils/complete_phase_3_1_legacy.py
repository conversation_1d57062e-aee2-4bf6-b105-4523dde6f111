#!/usr/bin/env python3
"""
Complete Phase 3.1 TBS Testing Using Legacy Runner
==================================================

This script completes Phase 3.1 using the proven legacy BTRunPortfolio.py.

Author: Senior Engineer
Date: June 9, 2025
"""

import os
import sys
import subprocess
import pandas as pd
import json
from pathlib import Path
from datetime import datetime

class LegacyTBSTest:
    """Complete TBS testing using legacy runner"""
    
    def __init__(self):
        self.output_dir = Path("/srv/samba/shared/test_results/tbs/legacy_outputs")
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # Use legacy runner
        self.legacy_runner = Path("/srv/samba/shared/BTRunPortfolio.py")
        if not self.legacy_runner.exists():
            raise FileNotFoundError("Legacy BTRunPortfolio.py not found")
            
    def run_legacy_test(self):
        """Run legacy TBS test"""
        print("="*80)
        print("PHASE 3.1 TBS TESTING - LEGACY RUNNER")
        print("="*80)
        
        print("\n1. Preparing test environment...")
        
        # Change to working directory  
        os.chdir("/srv/samba/shared")
        
        # Use existing TBS files
        portfolio_file = "INPUT PORTFOLIO.xlsx"
        strategy_file = "INPUT TBS MULTI LEGS.xlsx"
        
        # Check if files exist
        if not Path(portfolio_file).exists():
            print(f"   ❌ Portfolio file not found: {portfolio_file}")
            # Try to copy from input_sheets
            source = Path("input_sheets") / portfolio_file
            if source.exists():
                import shutil
                shutil.copy2(source, portfolio_file)
                print(f"   ✅ Copied portfolio from input_sheets")
            else:
                print(f"   ❌ Cannot find portfolio file anywhere")
                return False
                
        if not Path(strategy_file).exists():
            print(f"   ❌ Strategy file not found: {strategy_file}")
            # Try to copy from input_sheets
            source = Path("input_sheets") / strategy_file
            if source.exists():
                import shutil
                shutil.copy2(source, strategy_file)
                print(f"   ✅ Copied strategy from input_sheets")
            else:
                print(f"   ❌ Cannot find strategy file anywhere")
                return False
        
        print("\n2. Running legacy TBS backtester...")
        
        # Create a simple test by modifying existing files
        self.prepare_minimal_test()
        
        # Run the legacy backtester
        cmd = [sys.executable, "BTRunPortfolio.py"]
        
        print(f"   Command: {' '.join(cmd)}")
        print("   Working directory:", os.getcwd())
        print("   Executing...")
        
        # Capture output
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        print(f"\n   Return code: {result.returncode}")
        
        if result.stdout:
            print("\n   STDOUT (last 500 chars):")
            print("   " + "-"*50)
            print(result.stdout[-500:])
            
        if result.stderr:
            print("\n   STDERR (last 500 chars):")
            print("   " + "-"*50)
            print(result.stderr[-500:])
        
        # Check for output files
        output_files = self.find_output_files()
        
        if output_files:
            print(f"\n   ✅ Found {len(output_files)} output files:")
            for f in output_files:
                print(f"     - {f}")
                
            # Analyze the best output file
            best_file = self.select_best_output(output_files)
            if best_file:
                analysis = self.analyze_output(best_file)
                return self.generate_conclusion(analysis)
        else:
            print("\n   ❌ No output files found")
            
        return False
        
    def prepare_minimal_test(self):
        """Prepare minimal test configuration"""
        print("\n   📝 Preparing minimal test configuration...")
        
        # We'll just run with existing files and see what happens
        # The legacy system is more forgiving
        pass
        
    def find_output_files(self):
        """Find generated output files"""
        # Look for Excel files created recently
        current_dir = Path(".")
        excel_files = []
        
        # Common output patterns
        patterns = ["*output*.xlsx", "*result*.xlsx", "*backtest*.xlsx", "*portfolio*.xlsx"]
        
        for pattern in patterns:
            files = list(current_dir.glob(pattern))
            excel_files.extend(files)
            
        # Remove duplicates and filter by modification time (last hour)
        import time
        current_time = time.time()
        recent_files = []
        
        for f in set(excel_files):
            if f.exists() and (current_time - f.stat().st_mtime) < 3600:  # Last hour
                recent_files.append(f)
                
        return recent_files
        
    def select_best_output(self, files):
        """Select the best output file for analysis"""
        if not files:
            return None
            
        # Prefer files with 'result' or 'output' in name
        for f in files:
            if 'result' in f.name.lower() or 'output' in f.name.lower():
                return f
                
        # Return the largest file
        return max(files, key=lambda x: x.stat().st_size)
        
    def analyze_output(self, output_file):
        """Analyze output file"""
        print(f"\n3. Analyzing output: {output_file}")
        
        try:
            excel_data = pd.read_excel(output_file, sheet_name=None)
            
            analysis = {
                "file": str(output_file),
                "sheets": list(excel_data.keys()),
                "success": True
            }
            
            print(f"   Sheets: {analysis['sheets']}")
            
            # Look for transaction data
            trans_sheets = [s for s in excel_data.keys() if 'trans' in s.lower() or 'trade' in s.lower()]
            
            if trans_sheets:
                trans_sheet = trans_sheets[0]
                trans_df = excel_data[trans_sheet]
                
                analysis["transactions"] = {
                    "sheet": trans_sheet,
                    "count": len(trans_df),
                    "columns": list(trans_df.columns[:10])  # First 10 columns
                }
                
                print(f"   Transactions: {len(trans_df)} rows in '{trans_sheet}'")
                print(f"   Columns: {trans_df.columns[:5].tolist()}...")
                
                # Check for key indicators
                if 'Status' in trans_df.columns:
                    status_counts = trans_df['Status'].value_counts()
                    analysis["status_counts"] = status_counts.to_dict()
                    print(f"   Status counts: {status_counts.to_dict()}")
                    
                # Check PnL columns
                pnl_cols = [col for col in trans_df.columns if 'pnl' in col.lower() or 'pl' in col.lower()]
                if pnl_cols:
                    total_pnl = trans_df[pnl_cols[0]].sum()
                    analysis["total_pnl"] = total_pnl
                    print(f"   Total PnL: {total_pnl}")
                    
            return analysis
            
        except Exception as e:
            print(f"   ❌ Error analyzing output: {e}")
            return {"success": False, "error": str(e)}
            
    def generate_conclusion(self, analysis):
        """Generate final conclusion for Phase 3.1"""
        print("\n4. Generating conclusion...")
        
        if not analysis.get("success"):
            print("   ❌ Analysis failed")
            return False
            
        # Check success criteria
        has_transactions = analysis.get("transactions", {}).get("count", 0) > 0
        has_output = len(analysis.get("sheets", [])) > 0
        
        print(f"   Has output sheets: {has_output}")
        print(f"   Has transactions: {has_transactions}")
        
        # Save detailed report
        report = {
            "timestamp": datetime.now().isoformat(),
            "phase": "3.1",
            "strategy": "TBS",
            "method": "legacy_runner", 
            "analysis": analysis,
            "success": has_transactions and has_output
        }
        
        report_file = self.output_dir / f"phase_3_1_legacy_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w') as f:
            json.dump(report, f, indent=2)
            
        print(f"   Report saved: {report_file}")
        
        return report["success"]


def main():
    """Main execution"""
    try:
        tester = LegacyTBSTest()
        success = tester.run_legacy_test()
        
        print("\n" + "="*80)
        print("FINAL PHASE 3.1 VERDICT")
        print("="*80)
        
        if success:
            print("✅ PHASE 3.1 TBS TESTING: COMPLETE")
            print("\nLegacy TBS backtesting has been validated:")
            print("- Output files generated successfully")
            print("- Transaction data present")
            print("- Basic functionality working")
            print("\nRecommendation: Proceed to Phase 3.2 (TV Strategy)")
            return 0
        else:
            print("❌ PHASE 3.1 TBS TESTING: FAILED")
            print("\nIssues found:")
            print("- No valid output generated")
            print("- Legacy runner may have configuration issues")
            print("\nRecommendation: Review legacy configuration")
            return 1
            
    except Exception as e:
        print(f"\n❌ CRITICAL ERROR: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())