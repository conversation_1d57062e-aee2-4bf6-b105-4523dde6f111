#!/usr/bin/env python3
"""Update TV settings to use fixed portfolio files"""

import pandas as pd
import os

tv_file = 'bt/backtester_stable/BTRUN/input_sheets/input_tv.xlsx'

# Read the TV settings
with pd.ExcelFile(tv_file) as xls:
    sheets = {}
    for sheet_name in xls.sheet_names:
        sheets[sheet_name] = pd.read_excel(xls, sheet_name=sheet_name)

# Update the Setting sheet
if 'Setting' in sheets:
    df = sheets['Setting']
    
    # Find the enabled row
    enabled_mask = df['Enabled'].astype(str).str.upper() == 'YES'
    
    if enabled_mask.any():
        # Update portfolio paths for enabled rows
        df.loc[enabled_mask, 'LongPortfolioFilePath'] = '/srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/InputTbsLong_fixed.xlsx'
        df.loc[enabled_mask, 'ShortPortfolioFilePath'] = '/srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/InputTbsShort_fixed.xlsx'
        df.loc[enabled_mask, 'ManualPortfolioFilePath'] = '/srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/InputPortfolioStraddle_fixed.xlsx'
        
        print("Updated portfolio paths:")
        for idx in df[enabled_mask].index:
            print(f"\nRow {idx} ({df.loc[idx, 'Name']}):")
            print(f"  Long: {df.loc[idx, 'LongPortfolioFilePath']}")
            print(f"  Short: {df.loc[idx, 'ShortPortfolioFilePath']}")
            print(f"  Manual: {df.loc[idx, 'ManualPortfolioFilePath']}")

# Save the updated file
output_path = 'bt/backtester_stable/BTRUN/input_sheets/input_tv_fixed.xlsx'
with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
    for sheet_name, df in sheets.items():
        df.to_excel(writer, sheet_name=sheet_name, index=False)

print(f"\nSaved updated TV settings to: {output_path}")

# Also overwrite the original file
import shutil
shutil.copy2(output_path, tv_file)
print(f"Updated original file: {tv_file}") 