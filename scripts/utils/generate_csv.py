#!/usr/bin/env python3
import os
import sys
import logging
import pandas as pd
from datetime import datetime

# Configure logging
logging.basicConfig(level=logging.INFO, 
                   format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def create_sample_data(num_rows=100):
    """Creates sample data for testing the table and insertion process"""
    logger.info(f"Creating sample dataset with {num_rows} rows")
    
    # Generate date range for testing
    trade_dates = pd.date_range(start='2023-01-01', periods=10).tolist()
    expiry_dates = pd.date_range(start='2023-01-15', periods=5).tolist()
    trade_times = ['09:15:00', '10:30:00', '12:00:00', '13:30:00', '15:00:00']
    
    # Create sample data
    data = []
    for i in range(num_rows):
        trade_date_idx = i % len(trade_dates)
        expiry_date_idx = i % len(expiry_dates)
        time_idx = i % len(trade_times)
        
        # Basic fields
        row = {
            'trade_date': trade_dates[trade_date_idx].strftime('%Y-%m-%d'),
            'trade_time': trade_times[time_idx],
            'expiry_date': expiry_dates[expiry_date_idx].strftime('%Y-%m-%d'),
            'index_name': 'NIFTY',
            'spot': 18000 + (i % 500),
            'atm_strike': 18000 + (i % 500) // 50 * 50,
            'strike': 17800 + (i % 10) * 50,
            'dte': (expiry_dates[expiry_date_idx] - trade_dates[trade_date_idx]).days,
            'expiry_bucket': ['CW', 'NW', 'CM', 'NM'][i % 4],
            'zone_id': (i % 5) + 1,
            'zone_name': ['OPEN', 'MID_MORN', 'LUNCH', 'AFTERNOON', 'CLOSE'][i % 5],
            'call_strike_type': ['ATM', 'ITM1', 'ITM2', 'OTM1', 'OTM2'][i % 5],
            'put_strike_type': ['ATM', 'OTM1', 'OTM2', 'ITM1', 'ITM2'][i % 5],
        }
        
        # Call option fields
        row.update({
            'ce_symbol': f"NIFTY{expiry_dates[expiry_date_idx].strftime('%d%b%y').upper()}{17800 + (i % 10) * 50}CE",
            'ce_open': 100 + (i % 50),
            'ce_high': 120 + (i % 50),
            'ce_low': 90 + (i % 50),
            'ce_close': 110 + (i % 50),
            'ce_volume': 1000 + i * 10,
            'ce_oi': 5000 + i * 50,
            'ce_coi': (i % 200) - 100,
            'ce_iv': 15 + (i % 10),
            'ce_delta': 0.5 + (i % 50) / 100,
            'ce_gamma': 0.05 + (i % 10) / 100,
            'ce_theta': -10 - (i % 5),
            'ce_vega': 10 + (i % 5),
            'ce_rho': 5 + (i % 3),
        })
        
        # Put option fields
        row.update({
            'pe_symbol': f"NIFTY{expiry_dates[expiry_date_idx].strftime('%d%b%y').upper()}{17800 + (i % 10) * 50}PE",
            'pe_open': 100 + (i % 50),
            'pe_high': 120 + (i % 50),
            'pe_low': 90 + (i % 50),
            'pe_close': 110 + (i % 50),
            'pe_volume': 1000 + i * 10,
            'pe_oi': 5000 + i * 50,
            'pe_coi': (i % 200) - 100,
            'pe_iv': 15 + (i % 10),
            'pe_delta': -0.5 - (i % 50) / 100,
            'pe_gamma': 0.05 + (i % 10) / 100,
            'pe_theta': -10 - (i % 5),
            'pe_vega': 10 + (i % 5),
            'pe_rho': -5 - (i % 3),
        })
        
        # Future fields
        row.update({
            'future_open': 18050 + (i % 50),
            'future_high': 18100 + (i % 50),
            'future_low': 18000 + (i % 50),
            'future_close': 18075 + (i % 50),
            'future_volume': 5000 + i * 20,
            'future_oi': 10000 + i * 100,
            'future_coi': (i % 400) - 200,
        })
        
        data.append(row)
    
    # Convert to DataFrame
    df = pd.DataFrame(data)
    
    logger.info(f"Sample data created with columns: {df.columns.tolist()}")
    return df

def main():
    try:
        # Generate sample data
        sample_df = create_sample_data(num_rows=100)
        
        # Save to CSV
        csv_file = "sample_data.csv"
        sample_df.to_csv(csv_file, index=False)
        logger.info(f"Data saved to {csv_file}")
        
        # Generate COPY command script
        with open("load_csv.sh", "w") as f:
            f.write("#!/bin/bash\n\n")
            f.write(f"echo 'Loading data from {csv_file}...'\n")
            f.write(f"/opt/heavyai/bin/heavysql -s 127.0.0.1 --port 6274 -u admin -p HyperInteractive -d heavyai <<EOF\n")
            f.write("COPY nifty_option_chain FROM '/srv/samba/shared/sample_data.csv' WITH (header='true');\n")
            f.write("EOF\n\n")
            f.write("echo 'Data loading completed.'\n")
        
        os.chmod("load_csv.sh", 0o755)  # Make executable
        logger.info("Generated load_csv.sh script")
        logger.info("Run ./load_csv.sh to load the data")
            
    except Exception as e:
        logger.error(f"Error in main process: {str(e)}")

if __name__ == "__main__":
    main() 