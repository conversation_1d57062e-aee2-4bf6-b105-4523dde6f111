#!/usr/bin/env python3
"""
Final comparison test between TV backtester versions
"""

import os
import sys
import subprocess
import pandas as pd
from datetime import datetime
import time
import argparse

# Set up environment
project_root = "/srv/samba/shared"
bt_path = os.path.join(project_root, "bt")
sys.path.insert(0, bt_path)

def run_with_subprocess(script_name, args_list, version_name):
    """Run a script with subprocess to capture output"""
    print(f"\n{'='*80}")
    print(f"Running {version_name}")
    print(f"{'='*80}")
    
    # Build command
    cmd = [sys.executable, script_name] + args_list
    
    # Set environment
    env = os.environ.copy()
    env["PYTHONPATH"] = bt_path
    
    print(f"Command: {' '.join(cmd)}")
    print(f"Working directory: {bt_path}")
    
    start_time = time.time()
    
    try:
        # Run from bt directory
        result = subprocess.run(
            cmd, 
            capture_output=True, 
            text=True, 
            env=env,
            cwd=bt_path
        )
        
        elapsed = time.time() - start_time
        
        print(f"\nReturn code: {result.returncode}")
        print(f"Time taken: {elapsed:.2f} seconds")
        
        # Show output
        if result.stdout:
            lines = result.stdout.split('\n')
            print(f"\nKey output lines:")
            for line in lines:
                if any(keyword in line.lower() for keyword in ['completed', 'error', 'saved', 'output', 'trades']):
                    print(f"  {line}")
                    
        if result.stderr and result.returncode != 0:
            print(f"\nErrors (last 500 chars):")
            print(result.stderr[-500:])
            
        return result.returncode == 0, elapsed
        
    except Exception as e:
        print(f"Error running {version_name}: {e}")
        return False, 0

def check_outputs(output_dir):
    """Check what outputs were generated"""
    if not os.path.exists(output_dir):
        return []
        
    files = []
    for root, dirs, filenames in os.walk(output_dir):
        for f in filenames:
            if f.endswith('.xlsx'):
                files.append(os.path.join(root, f))
                
    return files

def main():
    """Main comparison"""
    print("TV Backtester Final Comparison")
    print("="*80)
    print(f"Started at: {datetime.now()}")
    
    # Test with limited date range
    start_date = "240401"  # April 1, 2024
    end_date = "240405"    # April 5, 2024
    
    print(f"\nTest parameters:")
    print(f"  Date range: {start_date} - {end_date}")
    print(f"  TV file: input_tv.xlsx")
    
    results = {}
    
    # 1. Test CPU Aggregated V4
    v4_output_dir = "/srv/samba/shared/Trades/tv_v4_test_" + datetime.now().strftime("%Y%m%d_%H%M%S")
    os.makedirs(v4_output_dir, exist_ok=True)
    
    v4_args = [
        "--start-date", start_date,
        "--end-date", end_date,
        "--output-dir", v4_output_dir,
        "--slippage", "0.1"
    ]
    
    success, elapsed = run_with_subprocess(
        "backtester_stable/BTRUN/BT_TV_GPU_aggregated_v4.py",
        v4_args,
        "CPU Aggregated V4"
    )
    
    v4_files = check_outputs(v4_output_dir)
    results['v4'] = {
        'success': success,
        'time': elapsed,
        'files': v4_files,
        'file_count': len(v4_files)
    }
    
    print(f"\nV4 Results:")
    print(f"  Success: {success}")
    print(f"  Files generated: {len(v4_files)}")
    if v4_files:
        print(f"  First file: {v4_files[0]}")
    
    # 2. Test GPU Enhanced
    enhanced_output_dir = "/srv/samba/shared/Trades/tv_enhanced_test_" + datetime.now().strftime("%Y%m%d_%H%M%S")
    os.makedirs(enhanced_output_dir, exist_ok=True)
    
    enhanced_args = [
        "--tv-file", "input_tv.xlsx",
        "--output-dir", enhanced_output_dir
    ]
    
    success, elapsed = run_with_subprocess(
        "backtester_stable/BTRUN/BT_TV_GPU_enhanced.py",
        enhanced_args,
        "GPU Enhanced"
    )
    
    enhanced_files = check_outputs(enhanced_output_dir)
    results['enhanced'] = {
        'success': success,
        'time': elapsed,
        'files': enhanced_files,
        'file_count': len(enhanced_files)
    }
    
    print(f"\nEnhanced Results:")
    print(f"  Success: {success}")
    print(f"  Files generated: {len(enhanced_files)}")
    if enhanced_files:
        print(f"  First file: {enhanced_files[0]}")
    
    # 3. Summary
    print(f"\n{'='*80}")
    print("SUMMARY")
    print(f"{'='*80}")
    
    print(f"\n📊 Performance Comparison:")
    print(f"  V4 time: {results['v4']['time']:.2f}s")
    print(f"  Enhanced time: {results['enhanced']['time']:.2f}s")
    
    if results['v4']['time'] > 0 and results['enhanced']['time'] > 0:
        speedup = results['v4']['time'] / results['enhanced']['time']
        print(f"  Speedup: {speedup:.2f}x")
    
    print(f"\n📁 Output Comparison:")
    print(f"  V4 files: {results['v4']['file_count']}")
    print(f"  Enhanced files: {results['enhanced']['file_count']}")
    
    # Try to compare actual results if both generated files
    if results['v4']['files'] and results['enhanced']['files']:
        print(f"\n📈 Attempting to compare results...")
        try:
            # Read first file from each
            v4_df = pd.read_excel(results['v4']['files'][0], sheet_name=None)
            enhanced_df = pd.read_excel(results['enhanced']['files'][0], sheet_name=None)
            
            print(f"\nV4 sheets: {list(v4_df.keys())}")
            print(f"Enhanced sheets: {list(enhanced_df.keys())}")
            
            # Find common sheets with transactions
            for sheet in ['PORTFOLIO Trans', 'Transactions', 'All Transactions']:
                if sheet in v4_df and sheet in enhanced_df:
                    v4_trans = v4_df[sheet]
                    enh_trans = enhanced_df[sheet]
                    
                    print(f"\n{sheet} comparison:")
                    print(f"  V4 trades: {len(v4_trans)}")
                    print(f"  Enhanced trades: {len(enh_trans)}")
                    
                    # Compare P&L if available
                    pnl_cols = ['Net PNL', 'pnl', 'PnL', 'P&L']
                    for col in pnl_cols:
                        if col in v4_trans.columns and col in enh_trans.columns:
                            v4_pnl = v4_trans[col].sum()
                            enh_pnl = enh_trans[col].sum()
                            print(f"  V4 total {col}: {v4_pnl:.2f}")
                            print(f"  Enhanced total {col}: {enh_pnl:.2f}")
                            print(f"  Difference: {abs(v4_pnl - enh_pnl):.2f}")
                            break
                    
                    break
                    
        except Exception as e:
            print(f"  Error comparing: {e}")
    
    print(f"\n{'='*80}")
    print("TEST COMPLETE")
    print(f"Completed at: {datetime.now()}")

if __name__ == "__main__":
    main()