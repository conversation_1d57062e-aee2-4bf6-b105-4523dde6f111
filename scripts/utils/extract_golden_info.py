import pandas as pd

# Load the golden output file
golden_file = "/srv/samba/shared/Nifty_Golden_Ouput.xlsx"
excel = pd.ExcelFile(golden_file)

# Print all sheets in the file
print("Sheets in the golden file:", excel.sheet_names)

# Extract the PortfolioParameter sheet and print column information
portfolio_params = pd.read_excel(excel, sheet_name="PortfolioParameter")
print("\nPortfolioParameter fields (row order):")
for i, row in portfolio_params.iterrows():
    print(f"{i+1}. {row['Head']} = {row['Value']}")

# Extract the GeneralParameter sheet and print column information
general_params = pd.read_excel(excel, sheet_name="GeneralParameter")
print("\nGeneralParameter fields (first row):")
for col in general_params.columns:
    if col in general_params.iloc[0]:
        val = general_params.iloc[0][col]
        print(f"{col} = {val}")

# Check the SL/TGT values in the LegParameter sheet
leg_params = pd.read_excel(excel, sheet_name="LegParameter")
print("\nLegParameter SL/TGT values:")
for col in ["SLType", "SLValue", "TGTType", "TGTValue"]:
    if col in leg_params.columns:
        print(f"{col} values: {leg_params[col].tolist()}")

def extract_and_print_excel_details():
    """
    Reads specified sheets from the golden Excel file and prints
    relevant header and sample data for PortfolioParameter and GeneralParameter.
    """
    try:
        xls = pd.ExcelFile(golden_file)

        print("--- PortfolioParameter Sheet ---")
        if 'PortfolioParameter' in xls.sheet_names:
            df_portfolio = pd.read_excel(xls, sheet_name='PortfolioParameter')
            
            print("\\n[PortfolioParameter] Field Order (from 'Head' column):")
            if 'Head' in df_portfolio.columns:
                for head_name in df_portfolio['Head']:
                    print(f"- {head_name}")
            else:
                print("'Head' column not found in PortfolioParameter sheet.")

            print("\\n[PortfolioParameter] Example SL/Exit/Timing Values:")
            # Adjust these parameter names if they are different in your golden file
            portfolio_params_to_check = [
                'PortfolioTarget', 'PortfolioStoploss', 'PortfolioTrailingType', 
                'PnLCalTime', 'PortfolioExitTime', 'StartDate', 'EndDate', 
                'IsTickBT', 'Enabled', 'PortfolioName', 'Multiplier', 'SlippagePercent'
            ] 
            if 'Head' in df_portfolio.columns and 'Value' in df_portfolio.columns:
                for param in portfolio_params_to_check:
                    value_row = df_portfolio[df_portfolio['Head'] == param]
                    if not value_row.empty:
                        print(f"- {param}: {value_row['Value'].iloc[0]}")
                    else:
                        print(f"- {param}: Not found in 'Head' column")
            else:
                print("'Head' or 'Value' column not found, cannot extract example values.")
        else:
            print("Sheet 'PortfolioParameter' not found.")

        print("\\n\\n--- GeneralParameter Sheet ---")
        if 'GeneralParameter' in xls.sheet_names:
            df_general = pd.read_excel(xls, sheet_name='GeneralParameter')
            
            print("\\n[GeneralParameter] Column Headers:")
            for col_name in df_general.columns:
                print(f"- {col_name}")

            print("\\n[GeneralParameter] Example SL/Exit/Timing Values (first 2 rows if available):")
            # Adjust these column names if needed
            general_cols_to_check = [
                'StrategyName', 'MoveSlToCost', 'Underlying', 'Index', 'Weekdays', 
                'DTE', 'StrikeSelectionTime', 'StartTime', 'LastEntryTime', 'EndTime', 
                'StrategyProfit', 'StrategyLoss', 'StrategyProfitReExecuteNo', 
                'StrategyLossReExecuteNo', 'StrategyTrailingType', 'PnLCalTime', 
                'LockPercent', 'TrailPercent', 'SqOff1Time', 'SqOff1Percent', 
                'SqOff2Time', 'SqOff2Percent', 'ProfitReaches', 'LockMinProfitAt', 
                'IncreaseInProfit', 'TrailMinProfitBy', 'TgtTrackingFrom', 
                'TgtRegisterPriceFrom', 'SlTrackingFrom', 'SlRegisterPriceFrom', 
                'PnLCalculationFrom', 'ConsiderHedgePnLForStgyPnL', 
                'StoplossCheckingInterval', 'TargetCheckingInterval', 
                'ReEntryCheckingInterval', 'OnExpiryDayTradeNextExpiry'
            ]
            
            df_general_sample = df_general.head(2) # Get first 2 rows as samples
            
            for idx, row in df_general_sample.iterrows():
                print(f"\\nSample Row {idx + 1}:")
                for col in general_cols_to_check:
                    if col in df_general_sample.columns:
                        print(f"  - {col}: {row[col]}")
                    # else: # Avoid printing "Not in sheet" for every column not present
                        # print(f"  - {col}: Not in sheet for this sample")
            if df_general_sample.empty:
                print("No data rows found in GeneralParameter to sample.")
        else:
            print("Sheet 'GeneralParameter' not found.")

    except FileNotFoundError:
        print(f"ERROR: Golden file not found at {golden_file}")
    except Exception as e:
        print(f"ERROR: Could not read or process Excel file: {e}")
        print("Ensure pandas and openpyxl (or other necessary Excel engine) are installed.")

if __name__ == '__main__':
    extract_and_print_excel_details() 