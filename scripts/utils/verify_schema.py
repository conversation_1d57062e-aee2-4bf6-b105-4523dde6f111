#!/usr/bin/env python3
import subprocess

def run_sql_command(sql):
    """Run a SQL command and return the output"""
    cmd = [
        "/opt/heavyai/bin/heavysql",
        "-s", "127.0.0.1",
        "--port", "6274",
        "-u", "admin",
        "-p", "HyperInteractive",
        "-d", "heavyai"
    ]
    
    process = subprocess.Popen(
        cmd,
        stdin=subprocess.PIPE,
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE,
        text=True
    )
    
    stdout, stderr = process.communicate(input=sql)
    return stdout, stderr

def main():
    # Check if the table exists
    sql_check_table = "SELECT table_name FROM information_schema.tables WHERE table_name = 'nifty_option_chain';"
    stdout, stderr = run_sql_command(sql_check_table)
    print("Table check output:", stdout)
    print("Table check error:", stderr)
    
    # Check schema
    sql_check_schema = "SELECT column_name, data_type FROM information_schema.columns WHERE table_name = 'nifty_option_chain' ORDER BY ordinal_position;"
    stdout, stderr = run_sql_command(sql_check_schema)
    print("\nSchema check output:", stdout)
    print("Schema check error:", stderr)
    
    # Count rows
    sql_count = "SELECT COUNT(*) FROM nifty_option_chain;"
    stdout, stderr = run_sql_command(sql_count)
    print("\nRow count output:", stdout)
    print("Row count error:", stderr)
    
    # Sample data
    sql_sample = "SELECT * FROM nifty_option_chain LIMIT 3;"
    stdout, stderr = run_sql_command(sql_sample)
    print("\nSample data output:", stdout)
    print("Sample data error:", stderr)
    
    # Get unique expiry buckets
    sql_buckets = "SELECT DISTINCT expiry_bucket FROM nifty_option_chain ORDER BY expiry_bucket;"
    stdout, stderr = run_sql_command(sql_buckets)
    print("\nUnique expiry buckets:", stdout)
    print("Expiry buckets error:", stderr)
    
    # Get unique strike types
    sql_strike_types = "SELECT DISTINCT call_strike_type FROM nifty_option_chain ORDER BY call_strike_type;"
    stdout, stderr = run_sql_command(sql_strike_types)
    print("\nUnique call strike types:", stdout)
    print("Strike types error:", stderr)

if __name__ == "__main__":
    main() 