#!/usr/bin/env python3
"""
Debug Price Data and Exit Reason

This script analyzes the price data to understand why trades are still showing
"Stop Loss Hit" as the exit reason despite the exit time being correctly set to 12:00:00.
"""

import os
import sys
import pandas as pd
import json
import logging
from datetime import datetime

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('debug_price')

# Path to the latest output file
OUTPUT_FILE = '/srv/samba/shared/Trades/fixed_exit_time_test.xlsx'
JSON_OUTPUT = '/srv/samba/shared/Trades/fixed_exit_time_test.json'

# Add BTRUN to the path for imports
bt_path = '/srv/samba/shared/bt/backtester_stable/BTRUN'
if bt_path not in sys.path:
    sys.path.append(bt_path)

def extract_trade_details():
    """Extract trade details from the output file"""
    try:
        # Read the transaction sheet 
        df = pd.read_excel(OUTPUT_FILE, sheet_name='PORTFOLIO Trans')
        logger.info(f"Read {len(df)} trades from {OUTPUT_FILE}")
        
        # Extract basic trade info
        trades = []
        for idx, row in df.iterrows():
            trade = {
                'leg_id': row.get('leg_id', f'Unknown-{idx}'),
                'symbol': row.get('symbol', 'Unknown'),
                'strike': row.get('strike', 0),
                'instrument_type': row.get('instrument_type', 'Unknown'),
                'side': row.get('side', 'Unknown'),
                'entry_price': row.get('entry_price', 0),
                'exit_price': row.get('exit_price', 0),
                'entry_time': row.get('entry_time', 'Unknown'),
                'exit_time': row.get('exit_time', 'Unknown'),
                'reason': row.get('reason', 'Unknown'),
                'points': row.get('points', 0),
                'pnl': row.get('pnl', 0)
            }
            trades.append(trade)
            
        logger.info("Trade details:")
        for trade in trades:
            logger.info(f"Leg {trade['leg_id']}: {trade['side']} {trade['instrument_type']} Strike={trade['strike']}")
            logger.info(f"  Entry: {trade['entry_time']} @ {trade['entry_price']:.2f}")
            logger.info(f"  Exit: {trade['exit_time']} @ {trade['exit_price']:.2f}")
            logger.info(f"  Reason: {trade['reason']}, PnL: {trade['pnl']:.2f}")
            
        return trades
    
    except Exception as e:
        logger.error(f"Error reading output file: {e}")
        return []

def read_json_data():
    """Read the JSON output data if available"""
    try:
        if os.path.exists(JSON_OUTPUT):
            with open(JSON_OUTPUT, 'r') as f:
                data = json.load(f)
            
            if 'data' in data and 'trades' in data['data']:
                trades = data['data']['trades']
                logger.info(f"Found {len(trades)} trades in JSON output")
                return trades
            else:
                logger.warning("JSON output missing expected structure")
        else:
            logger.warning(f"JSON output file not found: {JSON_OUTPUT}")
        
        return None
    
    except Exception as e:
        logger.error(f"Error reading JSON output: {e}")
        return None

def check_fix_exit_reason():
    """Check why exit reason is still showing as Stop Loss Hit"""
    try:
        from models.risk import RiskRule, RiskRuleType, NumberType, evaluate_risk_rule
        logger.info("Successfully imported risk evaluation model")
        
        # Get the fix_exit_time_issues.py script contents to analyze
        try:
            with open('fix_exit_time_issues.py', 'r') as f:
                fix_script = f.read()
            
            # Check if we're properly fixing the exit reason
            if '"exit_reason"' in fix_script:
                logger.info("The fix script contains logic for handling exit_reason")
            else:
                logger.error("⚠️ The fix script does not explicitly handle exit_reason")
        except:
            logger.warning("Could not read fix_exit_time_issues.py")
        
        # Print the current override logic in trade_builder.py
        try:
            with open(os.path.join(bt_path, 'trade_builder.py'), 'r') as f:
                builder_content = f.read()
            
            # Find the exit_reason setting
            if 'exit_reason' in builder_content:
                lines = builder_content.split('\n')
                relevant_lines = []
                for i, line in enumerate(lines):
                    if 'exit_reason' in line:
                        start_idx = max(0, i-5)
                        end_idx = min(len(lines), i+5)
                        relevant_lines = lines[start_idx:end_idx]
                        break
                
                if relevant_lines:
                    logger.info("Relevant exit_reason code in trade_builder.py:")
                    for line in relevant_lines:
                        logger.info(f"  {line}")
                else:
                    logger.warning("Could not find relevant exit_reason code")
            else:
                logger.warning("Could not find exit_reason in trade_builder.py")
        except Exception as e:
            logger.error(f"Error analyzing trade_builder.py: {e}")
        
        return True
    
    except ImportError:
        logger.error("Could not import risk evaluation model")
        return False

def fix_exit_reason():
    """Create a final fix to correct the exit reason"""
    try:
        # Path to trade_builder.py
        trade_builder_path = os.path.join(bt_path, 'trade_builder.py')
        
        # Create a backup if needed
        backup_path = trade_builder_path + '.reason.bak'
        if not os.path.exists(backup_path):
            import shutil
            shutil.copy2(trade_builder_path, backup_path)
            logger.info(f"Created backup at {backup_path}")
        
        # Read the current content
        with open(trade_builder_path, 'r') as f:
            content = f.read()
        
        # Check if we've already fixed this
        if "# IMPORTANT: Override Stop Loss Hit reason" in content:
            logger.info("Exit reason fix already applied")
            return True
        
        # Update the content to override the exit reason
        modified_content = content.replace(
            """                logger.debug(f"BUILD_TRADE_RECORD: Override exit time to {modified_exit_time}")
                
                # Force the exit time format
                exit_date_formatted, exit_time_formatted, exit_day = entry_date_formatted, modified_exit_time, entry_day""",
            
            """                logger.debug(f"BUILD_TRADE_RECORD: Override exit time to {modified_exit_time}")
                
                # IMPORTANT: Override Stop Loss Hit reason with Exit Time Hit when we're forcing exit time
                if "exit_reason" in exit_row_dict and exit_row_dict["exit_reason"] == "Stop Loss Hit":
                    exit_row_dict["exit_reason"] = "Exit Time Hit"
                    logger.debug(f"BUILD_TRADE_RECORD: Changed exit reason from 'Stop Loss Hit' to 'Exit Time Hit'")
                
                # Force the exit time format
                exit_date_formatted, exit_time_formatted, exit_day = entry_date_formatted, modified_exit_time, entry_day"""
        )
        
        # Save the modified content
        with open(trade_builder_path, 'w') as f:
            f.write(modified_content)
        
        logger.info("✅ Applied exit reason fix to trade_builder.py")
        
        # Also fix heavydb_trade_processing.py
        try:
            htrade_path = os.path.join(bt_path, 'heavydb_trade_processing.py')
            
            # Create a backup if needed
            backup_path = htrade_path + '.reason.bak'
            if not os.path.exists(backup_path):
                import shutil
                shutil.copy2(htrade_path, backup_path)
                logger.info(f"Created backup at {backup_path}")
            
            # Read the current content
            with open(htrade_path, 'r') as f:
                content = f.read()
            
            # Check if we've already fixed this
            if "# Change exit_reason to 'Exit Time Hit'" in content:
                logger.info("Exit reason fix already applied to heavydb_trade_processing.py")
                return True
            
            # Update the content to override the exit reason
            modified_content = content.replace(
                """                        risk_exit_row['trade_time'] = strategy_exit_time_int
                        
                        # Format the exit time as HH:MM:SS for logging
                        time_str = str(strategy_exit_time_int).zfill(6)
                        formatted_exit_time = f"{time_str[:2]}:{time_str[2:4]}:{time_str[4:]}"
                        trade_proc_debug_logger.info(f"TRADE_PROC_DETAIL: Exit time set to {formatted_exit_time}")""",
                
                """                        risk_exit_row['trade_time'] = strategy_exit_time_int
                        
                        # Change exit_reason to 'Exit Time Hit' since we're enforcing the strategy exit time
                        risk_exit_row['exit_reason'] = 'Exit Time Hit'
                        trade_proc_debug_logger.info(f"TRADE_PROC_DETAIL: Changed exit reason to 'Exit Time Hit'")
                        
                        # Format the exit time as HH:MM:SS for logging
                        time_str = str(strategy_exit_time_int).zfill(6)
                        formatted_exit_time = f"{time_str[:2]}:{time_str[2:4]}:{time_str[4:]}"
                        trade_proc_debug_logger.info(f"TRADE_PROC_DETAIL: Exit time set to {formatted_exit_time}")"""
            )
            
            # Save the modified content
            with open(htrade_path, 'w') as f:
                f.write(modified_content)
            
            logger.info("✅ Applied exit reason fix to heavydb_trade_processing.py")
        
        except Exception as e:
            logger.error(f"Error fixing heavydb_trade_processing.py: {e}")
        
        return True
    
    except Exception as e:
        logger.error(f"Error fixing exit reason: {e}")
        return False

def check_price_and_logic():
    """Check price movements and exit condition logic"""
    
    # Try to locate debug logs that contain SL calculations
    debug_logs = []
    for file in os.listdir('.'):
        if file.startswith('portfolio_backtest_debug_') and file.endswith('.log'):
            debug_logs.append(file)
    
    if debug_logs:
        # Sort by modification time (newest first)
        debug_logs.sort(key=lambda x: os.path.getmtime(x), reverse=True)
        latest_log = debug_logs[0]
        
        logger.info(f"Analyzing debug log: {latest_log}")
        
        try:
            with open(latest_log, 'r') as f:
                log_content = f.read()
            
            # Look for risk evaluation entries
            risk_entries = []
            for line in log_content.split('\n'):
                if "RISK: " in line:
                    risk_entries.append(line)
            
            if risk_entries:
                logger.info(f"Found {len(risk_entries)} risk evaluation log entries")
                
                # Check for SL triggers
                sl_triggers = [line for line in risk_entries if "SL Triggered" in line]
                if sl_triggers:
                    logger.info("Found SL trigger logs:")
                    for line in sl_triggers[:5]:  # Show the first 5 triggers
                        logger.info(f"  {line}")
                
                # Check for tick data issues
                tick_data_logs = [line for line in risk_entries if "after datetime creation" in line]
                if tick_data_logs:
                    logger.info("Tick data information:")
                    for line in tick_data_logs[:5]:
                        logger.info(f"  {line}")
            else:
                logger.info("No risk evaluation entries found in the debug log")
        
        except Exception as e:
            logger.error(f"Error analyzing debug log: {e}")

def main():
    """Main debug function"""
    logger.info("=== Starting Price & Exit Reason Debug ===")
    
    # Extract trade details from Excel output
    trades = extract_trade_details()
    
    # Read JSON data if available
    json_trades = read_json_data()
    
    # Check why exit reason is still showing Stop Loss Hit
    check_fix_exit_reason()
    
    # Check price movements and logic
    check_price_and_logic()
    
    # Apply a fix for the exit reason
    fixed = fix_exit_reason()
    
    if fixed:
        logger.info("\n✅ Exit reason fix applied. The trades should now show 'Exit Time Hit' instead of 'Stop Loss Hit'.")
        logger.info("\nTo test the fix, run:")
        logger.info("python3 /srv/samba/shared/bt/backtester_stable/BTRUN/BTRunPortfolio_GPU.py --portfolio-excel /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/input_portfolio_fixed.xlsx --output-path /srv/samba/shared/Trades/fixed_exit_reason_test.xlsx")
    else:
        logger.error("\n❌ Could not apply exit reason fix. Manual intervention required.")

if __name__ == "__main__":
    main() 