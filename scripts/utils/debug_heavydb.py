#!/usr/bin/env python3
import sys
import os
import traceback
import logging

# Configure detailed logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("debug_heavydb.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def main():
    """Debug HeavyDB connection issues"""
    logger.info("Starting HeavyDB debug script")
    
    # Check if heavyai module is available
    try:
        import heavyai
        logger.info("heavyai module is available")
    except ImportError:
        logger.error("heavyai module is NOT available")
        # Try to find installed packages
        logger.info("Checking installed packages:")
        try:
            import pkg_resources
            installed_packages = [d.project_name for d in pkg_resources.working_set]
            logger.info(f"Installed packages: {', '.join(installed_packages)}")
        except Exception as e:
            logger.error(f"Error checking packages: {e}")
        return
    
    # Try to connect to HeavyDB
    try:
        logger.info("Trying to connect to HeavyDB at 127.0.0.1:6274")
        conn = heavyai.connect(
            host="127.0.0.1",
            port=6274,
            user="admin",
            password="HyperInteractive",
            dbname="heavyai",
            protocol='binary'
        )
        logger.info("Connected to HeavyDB!")
        
        # Try a simple query
        cursor = conn.cursor()
        logger.info("Executing SHOW DATABASES")
        cursor.execute("SHOW DATABASES")
        databases = cursor.fetchall()
        logger.info(f"Databases: {databases}")
        
        # Close connection
        cursor.close()
        conn.close()
        logger.info("Connection closed successfully")
        
    except Exception as e:
        logger.error(f"Error connecting to HeavyDB: {e}")
        logger.error(traceback.format_exc())
        
        # Try to get more system information
        logger.info("System information:")
        try:
            logger.info(f"Python version: {sys.version}")
            logger.info(f"Current directory: {os.getcwd()}")
            if os.path.exists("/opt/heavyai/bin/heavysql"):
                logger.info("heavysql binary exists")
            else:
                logger.info("heavysql binary DOES NOT exist")
                
            # Check if HeavyDB server is running
            try:
                import subprocess
                result = subprocess.run(["ps", "aux"], capture_output=True, text=True)
                heavy_processes = [line for line in result.stdout.split('\n') if 'heavy' in line]
                logger.info(f"Heavy processes: {heavy_processes}")
            except Exception as e2:
                logger.error(f"Error checking processes: {e2}")
                
        except Exception as e3:
            logger.error(f"Error getting system info: {e3}")

if __name__ == "__main__":
    main() 