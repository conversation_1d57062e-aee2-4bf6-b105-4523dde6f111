#!/usr/bin/env python3
"""Update portfolio dates to Jan 3, 2024 for testing"""

import pandas as pd
from openpyxl import load_workbook

# Read the Excel file
file_path = 'input_portfolio.xlsx'
wb = load_workbook(file_path)

# Update PortfolioSetting sheet
if 'PortfolioSetting' in wb.sheetnames:
    ws = wb['PortfolioSetting']
    # Find StartDate and EndDate columns
    for row in ws.iter_rows():
        for cell in row:
            if cell.value == 'StartDate':
                # Get the cell to the right
                date_cell = ws.cell(row=cell.row, column=cell.column + 1)
                date_cell.value = '03_01_2024'
                print(f"Updated StartDate to 03_01_2024")
            elif cell.value == 'EndDate':
                # Get the cell to the right
                date_cell = ws.cell(row=cell.row, column=cell.column + 1)
                date_cell.value = '03_01_2024'
                print(f"Updated EndDate to 03_01_2024")

# Save the updated file
wb.save(file_path)
print(f"Saved updated portfolio file: {file_path}") 