#!/usr/bin/env python3
"""
Verify SL/TP Exit Handling

This script runs a focused test with one SL/TP combination to verify
that the backtester correctly handles stop loss and take profit values
with the fixed exit time logic.
"""

import os
import sys
import subprocess
import pandas as pd
import logging
import shutil
from datetime import datetime
from pathlib import Path

# Set up logging
logging.basicConfig(
    level=logging.DEBUG,  # Use DEBUG level for more detailed logging
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('verify_sl_tp')

# Constants
BT_PATH = '/srv/samba/shared/bt/backtester_stable/BTRUN'
PORTFOLIO_FILE = os.path.join(BT_PATH, 'input_sheets/input_portfolio_fixed.xlsx')
STRATEGY_FILE = os.path.join(BT_PATH, 'input_sheets/input_tbs_fixed_exits.xlsx')
OUTPUT_DIR = '/srv/samba/shared/Trades'

# Create a timestamp string for unique filenames
TIMESTAMP = datetime.now().strftime("%Y%m%d_%H%M%S")

# SL/TP values to test - using the standard combination
SELL_SL = 500
SELL_TP = 100
BUY_SL = 50
BUY_TP = 100

def create_test_files():
    """Create the test strategy and portfolio files"""
    logger.info("Creating test strategy and portfolio files")
    
    # Create output directory if it doesn't exist
    os.makedirs(OUTPUT_DIR, exist_ok=True)
    
    # Create strategy file with test SL/TP values
    strategy_file = os.path.join(BT_PATH, f'input_sheets/input_tbs_verify_{TIMESTAMP}.xlsx')
    shutil.copy2(STRATEGY_FILE, strategy_file)
    
    # Modify SL/TP values
    try:
        # Read LegParameter sheet
        leg_df = pd.read_excel(strategy_file, sheet_name='LegParameter')
        
        # Update SL/TP values based on transaction type
        for idx, row in leg_df.iterrows():
            transaction = row.get('Transaction', '').upper()
            
            if transaction == 'SELL':
                # Update SL/TP for SELL legs
                leg_df.at[idx, 'SLValue'] = SELL_SL
                leg_df.at[idx, 'TGTValue'] = SELL_TP
                logger.info(f"Setting SELL leg {row.get('LegID', idx)}: SL={SELL_SL}%, TP={SELL_TP}%")
            elif transaction == 'BUY':
                # Update SL/TP for BUY legs
                leg_df.at[idx, 'SLValue'] = BUY_SL
                leg_df.at[idx, 'TGTValue'] = BUY_TP
                logger.info(f"Setting BUY leg {row.get('LegID', idx)}: SL={BUY_SL}%, TP={BUY_TP}%")
        
        # Save modified file
        with pd.ExcelWriter(strategy_file, engine='openpyxl') as writer:
            leg_df.to_excel(writer, sheet_name='LegParameter', index=False)
            
            # Copy other sheets without modification
            original_df = pd.read_excel(STRATEGY_FILE, sheet_name=None)
            for sheet_name, sheet_df in original_df.items():
                if sheet_name != 'LegParameter':
                    sheet_df.to_excel(writer, sheet_name=sheet_name, index=False)
        
        logger.info(f"Created strategy file: {strategy_file}")
        
        # Create portfolio file referencing the strategy file
        portfolio_file = os.path.join(BT_PATH, f'input_sheets/input_portfolio_verify_{TIMESTAMP}.xlsx')
        
        # Read and modify StrategySetting sheet
        df = pd.read_excel(PORTFOLIO_FILE, sheet_name='StrategySetting')
        df['StrategyExcelFilePath'] = strategy_file
        
        # Save modified file
        with pd.ExcelWriter(portfolio_file, engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name='StrategySetting', index=False)
            
            # Copy other sheets without modification
            original_df = pd.read_excel(PORTFOLIO_FILE, sheet_name=None)
            for sheet_name, sheet_df in original_df.items():
                if sheet_name != 'StrategySetting':
                    sheet_df.to_excel(writer, sheet_name=sheet_name, index=False)
        
        logger.info(f"Created portfolio file: {portfolio_file}")
        
        return strategy_file, portfolio_file
    
    except Exception as e:
        logger.error(f"Error creating test files: {e}", exc_info=True)
        return None, None

def run_backtester(portfolio_file):
    """Run the backtester with the test portfolio file"""
    try:
        # Create output file path
        output_file = os.path.join(OUTPUT_DIR, f'verify_sl_tp_exit_{TIMESTAMP}.xlsx')
        
        logger.info(f"Running backtester with portfolio file: {portfolio_file}")
        logger.info(f"Output will be saved to: {output_file}")
        
        # Construct command
        cmd = [
            "python3",
            os.path.join(BT_PATH, "BTRunPortfolio_GPU.py"),
            "--portfolio-excel", portfolio_file,
            "--output-path", output_file
        ]
        
        logger.info(f"Executing command: {' '.join(cmd)}")
        
        # Run the command
        process = subprocess.run(
            cmd, 
            stdout=subprocess.PIPE, 
            stderr=subprocess.PIPE,
            text=True
        )
        
        logger.info(f"Backtester process completed with return code: {process.returncode}")
        
        if process.stdout:
            logger.debug(f"Backtester STDOUT:\n{process.stdout}")
        
        if process.stderr:
            logger.warning(f"Backtester STDERR:\n{process.stderr}")
        
        if process.returncode == 0:
            logger.info(f"Backtester completed successfully")
            return output_file
        else:
            logger.error(f"Backtester failed with return code {process.returncode}")
            return None
    
    except Exception as e:
        logger.error(f"Error running backtester: {e}", exc_info=True)
        return None

def verify_output(output_file):
    """Verify the output file for correct exit times and reasons"""
    try:
        logger.info(f"Verifying output file: {output_file}")
        
        if not os.path.exists(output_file):
            logger.error(f"Output file not found: {output_file}")
            return False
        
        # List all sheets in the Excel file
        xlsx = pd.ExcelFile(output_file)
        logger.info(f"Sheets in output file: {xlsx.sheet_names}")
        
        # Check for PORTFOLIO Trans sheet
        if 'PORTFOLIO Trans' not in xlsx.sheet_names:
            logger.error("PORTFOLIO Trans sheet not found in output file")
            return False
        
        # Read PORTFOLIO Trans sheet
        trans_df = pd.read_excel(output_file, sheet_name='PORTFOLIO Trans')
        logger.info(f"Read {len(trans_df)} rows from PORTFOLIO Trans sheet")
        
        # Print all columns for debugging
        logger.info(f"Columns in PORTFOLIO Trans: {trans_df.columns.tolist()}")
        
        # Verify exit times and reasons
        if 'exit_time' not in trans_df.columns or 'reason' not in trans_df.columns:
            logger.error("Required columns 'exit_time' or 'reason' not found in output")
            return False
        
        # Get unique exit times and reasons
        exit_times = trans_df['exit_time'].unique()
        reasons = trans_df['reason'].unique()
        
        logger.info(f"Unique exit times: {exit_times}")
        logger.info(f"Unique exit reasons: {reasons}")
        
        # Verification criteria
        all_exits_at_12 = len(exit_times) == 1 and exit_times[0] == '12:00:00'
        all_reasons_correct = len(reasons) == 1 and reasons[0] == 'Exit Time Hit'
        
        # Print sample rows for inspection
        logger.info("Sample rows from output:")
        sample_cols = ['leg_id', 'entry_time', 'exit_time', 'exit_datetime', 'reason', 'pnl']
        for _, row in trans_df.head().iterrows():
            sample_row = {col: row.get(col) for col in sample_cols if col in row}
            logger.info(f"  {sample_row}")
        
        # Check for exit_datetime consistency
        if 'exit_datetime' in trans_df.columns:
            for i, row in trans_df.iterrows():
                exit_time = row['exit_time']
                exit_datetime = row['exit_datetime']
                if exit_time not in str(exit_datetime):
                    logger.error(f"Row {i}: exit_datetime ({exit_datetime}) doesn't match exit_time ({exit_time})")
                    return False
        
        # Final verification result
        if all_exits_at_12 and all_reasons_correct:
            logger.info("✅ All trades exit at 12:00:00 with reason 'Exit Time Hit'")
            return True
        else:
            if not all_exits_at_12:
                logger.error("❌ Not all trades exit at 12:00:00")
            if not all_reasons_correct:
                logger.error("❌ Not all exit reasons are 'Exit Time Hit'")
            return False
    
    except Exception as e:
        logger.error(f"Error verifying output: {e}", exc_info=True)
        return False

def main():
    """Main function to run the verification test"""
    logger.info("=== Starting SL/TP Exit Verification Test ===")
    logger.info(f"Testing with SL/TP values - SELL: SL={SELL_SL}%, TP={SELL_TP}%, BUY: SL={BUY_SL}%, TP={BUY_TP}%")
    
    # Step 1: Create test files
    strategy_file, portfolio_file = create_test_files()
    if not strategy_file or not portfolio_file:
        logger.error("Failed to create test files")
        return 1
    
    # Step 2: Run backtester
    output_file = run_backtester(portfolio_file)
    if not output_file:
        logger.error("Backtester run failed")
        return 2
    
    # Step 3: Verify output
    if verify_output(output_file):
        logger.info("✅ VERIFICATION PASSED: SL/TP exit handling works correctly with fixed exit time")
        
        # Update refactor plan progress
        try:
            plan_path = '/srv/samba/shared/bt/memory-bank/python_refactor_plan.md'
            if os.path.exists(plan_path):
                with open(plan_path, 'r') as f:
                    plan_content = f.read()
                
                # Update Phase 1.K to 100%
                updated_content = plan_content.replace("Phase 1.K **90%**", "Phase 1.K **100%**")
                
                if updated_content != plan_content:
                    with open(plan_path, 'w') as f:
                        f.write(updated_content)
                    logger.info("✅ Updated python_refactor_plan.md with progress")
        except Exception as e:
            logger.warning(f"Could not update refactor plan: {e}")
        
        return 0
    else:
        logger.error("❌ VERIFICATION FAILED: SL/TP exit handling does not work correctly")
        return 3

if __name__ == "__main__":
    sys.exit(main()) 