#!/usr/bin/env python3
"""
GPU System Comprehensive Fixes
==============================

This script implements all critical fixes for the GPU backtesting system:
1. Trade completion logic (trades must close)
2. Multi-leg execution (all legs must execute)
3. Output format compliance (match golden standard)
4. ATM calculation with proper duplicate handling

Author: Senior Backend Expert
Date: June 9, 2025
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging
from typing import Dict, List, Tuple, Optional, Any
from pathlib import Path
import json

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# ============================================================================
# FIX 1: Trade Completion Logic
# ============================================================================

class TradeCompletionFix:
    """Fixes trade completion logic to ensure all trades are properly closed"""
    
    @staticmethod
    def fix_exit_logic(trade: Dict, current_bar: Dict, strategy_params: Dict) -> bool:
        """
        Fixed exit logic that properly closes trades
        
        Args:
            trade: Current trade object
            current_bar: Current market data bar
            strategy_params: Strategy parameters including exit conditions
            
        Returns:
            True if trade should be closed
        """
        # Check time-based exit
        exit_time = strategy_params.get('exit_time', '15:15:00')
        current_time = current_bar.get('time', '')
        
        if current_time >= exit_time:
            trade['status'] = 'CLOSED'
            trade['exit_time'] = current_time
            trade['exit_date'] = current_bar.get('date')
            trade['exit_price'] = current_bar.get('close', 0)
            trade['exit_reason'] = 'Time-based exit'
            logger.info(f"Trade {trade.get('id')} closed at {exit_time}")
            return True
        
        # Check stop loss
        if 'stop_loss' in strategy_params:
            current_pnl = calculate_pnl(trade, current_bar)
            if current_pnl <= strategy_params['stop_loss']:
                trade['status'] = 'CLOSED'
                trade['exit_time'] = current_time
                trade['exit_date'] = current_bar.get('date')
                trade['exit_price'] = current_bar.get('close', 0)
                trade['exit_reason'] = 'Stop loss hit'
                logger.info(f"Trade {trade.get('id')} stopped out")
                return True
        
        # Check target profit
        if 'target_profit' in strategy_params:
            current_pnl = calculate_pnl(trade, current_bar)
            if current_pnl >= strategy_params['target_profit']:
                trade['status'] = 'CLOSED'
                trade['exit_time'] = current_time
                trade['exit_date'] = current_bar.get('date')
                trade['exit_price'] = current_bar.get('close', 0)
                trade['exit_reason'] = 'Target hit'
                logger.info(f"Trade {trade.get('id')} target reached")
                return True
        
        return False
    
    @staticmethod
    def ensure_all_trades_closed(trades: List[Dict], final_bar: Dict) -> List[Dict]:
        """Ensure all trades are closed by end of day"""
        for trade in trades:
            if trade.get('status') != 'CLOSED':
                trade['status'] = 'CLOSED'
                trade['exit_time'] = final_bar.get('time', '15:30:00')
                trade['exit_date'] = final_bar.get('date')
                trade['exit_price'] = final_bar.get('close', trade.get('entry_price', 0))
                trade['exit_reason'] = 'Force closed at EOD'
                logger.warning(f"Force closing trade {trade.get('id')} at EOD")
        
        return trades

# ============================================================================
# FIX 2: Multi-Leg Execution
# ============================================================================

class MultiLegExecutionFix:
    """Fixes multi-leg execution to ensure all legs are processed"""
    
    @staticmethod
    def execute_all_portfolio_legs(portfolio_config: Dict, market_data: Dict) -> List[Dict]:
        """
        Execute ALL legs in a portfolio strategy
        
        Args:
            portfolio_config: Portfolio configuration with multiple legs
            market_data: Current market data
            
        Returns:
            List of executed trades for all legs
        """
        executed_trades = []
        
        # Get all legs from portfolio
        legs = portfolio_config.get('legs', {})
        
        if not legs:
            logger.error("No legs found in portfolio configuration")
            return executed_trades
        
        logger.info(f"Executing {len(legs)} legs from portfolio")
        
        # Execute each leg
        for leg_id, leg_params in legs.items():
            try:
                # Create trade for this leg
                trade = {
                    'id': f"{portfolio_config.get('name', 'Portfolio')}_{leg_id}",
                    'portfolio': portfolio_config.get('name', 'Portfolio'),
                    'strategy': leg_params.get('strategy_name', f'Leg_{leg_id}'),
                    'leg_id': leg_id,
                    'option_type': leg_params.get('option_type', 'CE'),
                    'position': leg_params.get('position', 'BUY'),
                    'quantity': leg_params.get('quantity', 1),
                    'lot_size': leg_params.get('lot_size', 50),
                    'status': 'OPEN',
                    'entry_time': market_data.get('time'),
                    'entry_date': market_data.get('date'),
                    'strike_selection': leg_params.get('strike_selection', 'ATM')
                }
                
                # Get strike and entry price
                strike, entry_price = select_strike_for_leg(leg_params, market_data)
                trade['strike'] = strike
                trade['entry_price'] = entry_price
                trade['entry_spot'] = market_data.get('spot_price', 0)
                
                executed_trades.append(trade)
                logger.info(f"Executed leg {leg_id}: {trade['option_type']} {strike} @ {entry_price}")
                
            except Exception as e:
                logger.error(f"Failed to execute leg {leg_id}: {str(e)}")
        
        logger.info(f"Successfully executed {len(executed_trades)} legs")
        return executed_trades
    
    @staticmethod
    def verify_all_legs_executed(portfolio_config: Dict, executed_trades: List[Dict]) -> bool:
        """Verify that all configured legs were executed"""
        expected_legs = len(portfolio_config.get('legs', {}))
        actual_legs = len(executed_trades)
        
        if expected_legs != actual_legs:
            logger.error(f"Leg execution mismatch: Expected {expected_legs}, Got {actual_legs}")
            return False
        
        return True

# ============================================================================
# FIX 3: Output Format Compliance
# ============================================================================

class OutputFormatFix:
    """Ensures output matches golden Excel format exactly"""
    
    def __init__(self):
        """Initialize with golden format specification"""
        # Transaction sheet columns (32 total) - exact order matters
        self.trans_columns = [
            'Portfolio', 'Strategy', 'Expiry', 'Date', 'Time', 'Option Type',
            'Moneyness', 'Entry Premium', 'Entry Spot', 'Entry Strike', 
            'Entry IV', 'Position', 'Lot Size', 'Value', 'Exit at',
            'Exit Type', 'Exit Spot', 'Exit Strike', 'Exit Premium',
            'Exit IV', 'Days', 'Exit at.1', 'PnL', 'Cum PnL', 'Strike_Entry',
            'Strike_Exit', 'Strategy_x', 'W&Type', 'Order', 'Exit Date',
            'Exit Time', 'Status'
        ]
        
        # Required sheets in order
        self.required_sheets = [
            'PortfolioParameter',
            'GeneralParameter',
            'LegParameter',
            'Metrics',
            'Max Profit and Loss',
            'PORTFOLIO Trans',
            'PORTFOLIO Results'
        ]
    
    def format_trades_to_golden_standard(self, trades: List[Dict], 
                                       portfolio_config: Dict,
                                       strategy_config: Dict) -> pd.DataFrame:
        """Convert trades to golden standard format"""
        formatted_trades = []
        cum_pnl = 0
        
        for i, trade in enumerate(trades):
            # Calculate PnL
            pnl = calculate_pnl(trade)
            cum_pnl += pnl
            
            # Format trade to match golden standard
            formatted_trade = {
                'Portfolio': trade.get('portfolio', 'Portfolio'),
                'Strategy': trade.get('strategy', 'Strategy'),
                'Expiry': trade.get('expiry', ''),
                'Date': trade.get('entry_date', ''),
                'Time': trade.get('entry_time', ''),
                'Option Type': trade.get('option_type', ''),
                'Moneyness': self._get_moneyness(trade),
                'Entry Premium': trade.get('entry_price', 0),
                'Entry Spot': trade.get('entry_spot', 0),
                'Entry Strike': trade.get('strike', 0),
                'Entry IV': trade.get('entry_iv', 0),
                'Position': trade.get('position', 'BUY'),
                'Lot Size': trade.get('lot_size', 50),
                'Value': trade.get('entry_price', 0) * trade.get('lot_size', 50) * trade.get('quantity', 1),
                'Exit at': trade.get('exit_reason', ''),
                'Exit Type': self._get_exit_type(trade),
                'Exit Spot': trade.get('exit_spot', trade.get('entry_spot', 0)),
                'Exit Strike': trade.get('strike', 0),
                'Exit Premium': trade.get('exit_price', 0),
                'Exit IV': trade.get('exit_iv', 0),
                'Days': self._calculate_days(trade),
                'Exit at.1': trade.get('exit_time', ''),
                'PnL': pnl,
                'Cum PnL': cum_pnl,
                'Strike_Entry': trade.get('strike', 0),
                'Strike_Exit': trade.get('strike', 0),
                'Strategy_x': trade.get('strategy', ''),
                'W&Type': f"{trade.get('option_type', '')}_{trade.get('position', '')}",
                'Order': i + 1,
                'Exit Date': trade.get('exit_date', trade.get('entry_date', '')),
                'Exit Time': trade.get('exit_time', '15:30:00'),
                'Status': trade.get('status', 'CLOSED')
            }
            
            formatted_trades.append(formatted_trade)
        
        return pd.DataFrame(formatted_trades, columns=self.trans_columns)
    
    def create_golden_format_excel(self, trades_df: pd.DataFrame,
                                 portfolio_config: Dict,
                                 strategy_config: Dict,
                                 output_path: str):
        """Create Excel file matching golden format exactly"""
        with pd.ExcelWriter(output_path, engine='xlsxwriter') as writer:
            # 1. PortfolioParameter sheet
            portfolio_params = pd.DataFrame([
                {'Parameter': 'Name', 'Value': portfolio_config.get('name', 'Portfolio')},
                {'Parameter': 'Capital', 'Value': portfolio_config.get('capital', 1000000)},
                {'Parameter': 'Start Date', 'Value': portfolio_config.get('start_date', '')},
                {'Parameter': 'End Date', 'Value': portfolio_config.get('end_date', '')}
            ])
            portfolio_params.to_excel(writer, sheet_name='PortfolioParameter', index=False)
            
            # 2. GeneralParameter sheet
            general_params = self._create_general_parameters(strategy_config)
            general_params.to_excel(writer, sheet_name='GeneralParameter', index=False)
            
            # 3. LegParameter sheet
            leg_params = self._create_leg_parameters(portfolio_config)
            leg_params.to_excel(writer, sheet_name='LegParameter', index=False)
            
            # 4. Metrics sheet
            metrics = self._calculate_metrics(trades_df)
            metrics.to_excel(writer, sheet_name='Metrics', index=False)
            
            # 5. Max Profit and Loss sheet
            max_pl = self._calculate_max_profit_loss(trades_df)
            max_pl.to_excel(writer, sheet_name='Max Profit and Loss', index=False)
            
            # 6. PORTFOLIO Trans sheet
            trades_df.to_excel(writer, sheet_name='PORTFOLIO Trans', index=False)
            
            # 7. PORTFOLIO Results sheet
            results = self._create_portfolio_results(trades_df)
            results.to_excel(writer, sheet_name='PORTFOLIO Results', index=False)
            
            # 8. Strategy-specific sheets (same format as PORTFOLIO Trans)
            for strategy in trades_df['Strategy'].unique():
                strategy_trades = trades_df[trades_df['Strategy'] == strategy].copy()
                # Excel sheet name limit is 31 characters
                sheet_name = f"{strategy[:28]} Trans" if len(strategy) > 23 else f"{strategy} Trans"
                strategy_trades.to_excel(writer, sheet_name=sheet_name, index=False)
        
        logger.info(f"Created golden format Excel at: {output_path}")
    
    def _get_moneyness(self, trade: Dict) -> str:
        """Determine moneyness of the trade"""
        strike_selection = trade.get('strike_selection', 'ATM')
        if 'ATM' in strike_selection:
            return 'ATM'
        elif 'ITM' in strike_selection:
            return strike_selection
        elif 'OTM' in strike_selection:
            return strike_selection
        else:
            return 'ATM'
    
    def _get_exit_type(self, trade: Dict) -> str:
        """Get exit type for the trade"""
        exit_reason = trade.get('exit_reason', '')
        if 'Stop loss' in exit_reason:
            return 'SL'
        elif 'Target' in exit_reason:
            return 'TP'
        elif 'Time' in exit_reason:
            return 'Time'
        else:
            return 'Manual'
    
    def _calculate_days(self, trade: Dict) -> int:
        """Calculate days between entry and exit"""
        try:
            entry_date = pd.to_datetime(trade.get('entry_date'))
            exit_date = pd.to_datetime(trade.get('exit_date', trade.get('entry_date')))
            return (exit_date - entry_date).days
        except:
            return 0
    
    def _create_general_parameters(self, strategy_config: Dict) -> pd.DataFrame:
        """Create GeneralParameter sheet data"""
        # This would map strategy config to general parameters
        # Placeholder implementation
        return pd.DataFrame([
            {'Parameter': 'Strategy Type', 'Value': strategy_config.get('type', 'TBS')},
            {'Parameter': 'Start Time', 'Value': strategy_config.get('start_time', '09:20:00')},
            {'Parameter': 'End Time', 'Value': strategy_config.get('end_time', '15:15:00')}
        ])
    
    def _create_leg_parameters(self, portfolio_config: Dict) -> pd.DataFrame:
        """Create LegParameter sheet data"""
        leg_data = []
        for leg_id, leg_config in portfolio_config.get('legs', {}).items():
            leg_data.append({
                'Leg ID': leg_id,
                'Option Type': leg_config.get('option_type', 'CE'),
                'Strike Selection': leg_config.get('strike_selection', 'ATM'),
                'Position': leg_config.get('position', 'BUY'),
                'Quantity': leg_config.get('quantity', 1)
            })
        return pd.DataFrame(leg_data)
    
    def _calculate_metrics(self, trades_df: pd.DataFrame) -> pd.DataFrame:
        """Calculate performance metrics"""
        total_pnl = trades_df['PnL'].sum()
        winning_trades = len(trades_df[trades_df['PnL'] > 0])
        losing_trades = len(trades_df[trades_df['PnL'] < 0])
        win_rate = winning_trades / len(trades_df) * 100 if len(trades_df) > 0 else 0
        
        metrics = [
            {'Metric': 'Total P&L', 'Value': total_pnl},
            {'Metric': 'Total Trades', 'Value': len(trades_df)},
            {'Metric': 'Winning Trades', 'Value': winning_trades},
            {'Metric': 'Losing Trades', 'Value': losing_trades},
            {'Metric': 'Win Rate %', 'Value': win_rate}
        ]
        
        return pd.DataFrame(metrics)
    
    def _calculate_max_profit_loss(self, trades_df: pd.DataFrame) -> pd.DataFrame:
        """Calculate daily max profit and loss"""
        daily_pnl = trades_df.groupby('Date')['PnL'].sum().reset_index()
        daily_pnl.columns = ['Date', 'Daily P&L']
        daily_pnl['Max Profit'] = daily_pnl['Daily P&L'].cummax()
        daily_pnl['Max Loss'] = daily_pnl['Daily P&L'].cummin()
        return daily_pnl
    
    def _create_portfolio_results(self, trades_df: pd.DataFrame) -> pd.DataFrame:
        """Create portfolio results summary"""
        # Calendar view of P&L
        if not trades_df.empty:
            trades_df['Date'] = pd.to_datetime(trades_df['Date'])
            daily_results = trades_df.groupby('Date')['PnL'].sum()
            
            # Create calendar format
            results_df = pd.DataFrame(index=pd.date_range(
                trades_df['Date'].min(),
                trades_df['Date'].max(),
                freq='D'
            ))
            results_df['P&L'] = daily_results
            results_df = results_df.fillna(0)
            results_df['Cumulative P&L'] = results_df['P&L'].cumsum()
            
            return results_df.reset_index().rename(columns={'index': 'Date'})
        else:
            return pd.DataFrame(columns=['Date', 'P&L', 'Cumulative P&L'])

# ============================================================================
# FIX 4: ATM Calculation with Duplicate Handling
# ============================================================================

class ATMCalculationFix:
    """Fixed ATM calculation with proper duplicate handling"""
    
    @staticmethod
    def calculate_synthetic_future_atm_fixed(option_chain_df: pd.DataFrame, 
                                           spot_price: float) -> Tuple[float, pd.DataFrame]:
        """
        Calculate synthetic future ATM with proper duplicate handling
        
        Args:
            option_chain_df: DataFrame with option chain data
            spot_price: Current spot price
            
        Returns:
            Tuple of (atm_strike, processed_option_data)
        """
        # Handle duplicates by averaging prices for each strike
        processed_df = option_chain_df.groupby(['strike', 'expiry_date']).agg({
            'ce_close': 'mean',
            'pe_close': 'mean',
            'ce_bid': 'mean',
            'ce_ask': 'mean',
            'pe_bid': 'mean',
            'pe_ask': 'mean',
            'ce_oi': 'mean',
            'pe_oi': 'mean'
        }).reset_index()
        
        # Calculate synthetic future for each strike
        processed_df['synthetic_future'] = (
            processed_df['strike'] + 
            processed_df['ce_close'] - 
            processed_df['pe_close']
        )
        
        # Find strike where synthetic future is closest to spot
        processed_df['diff_from_spot'] = abs(processed_df['synthetic_future'] - spot_price)
        
        # Get the strike with minimum difference
        atm_idx = processed_df['diff_from_spot'].idxmin()
        atm_strike = float(processed_df.loc[atm_idx, 'strike'])
        
        logger.info(f"Fixed ATM Calculation:")
        logger.info(f"  Spot Price: {spot_price}")
        logger.info(f"  ATM Strike: {atm_strike}")
        logger.info(f"  Synthetic Future: {processed_df.loc[atm_idx, 'synthetic_future']:.2f}")
        logger.info(f"  CE Price: {processed_df.loc[atm_idx, 'ce_close']:.2f}")
        logger.info(f"  PE Price: {processed_df.loc[atm_idx, 'pe_close']:.2f}")
        
        return atm_strike, processed_df

# ============================================================================
# Helper Functions
# ============================================================================

def calculate_pnl(trade: Dict, current_bar: Optional[Dict] = None) -> float:
    """Calculate P&L for a trade"""
    if trade.get('status') != 'CLOSED' and current_bar:
        # Use current price for open trades
        exit_price = current_bar.get('close', trade.get('entry_price', 0))
    else:
        exit_price = trade.get('exit_price', trade.get('entry_price', 0))
    
    entry_price = trade.get('entry_price', 0)
    quantity = trade.get('quantity', 1)
    lot_size = trade.get('lot_size', 50)
    position = trade.get('position', 'BUY')
    
    if position == 'BUY':
        pnl = (exit_price - entry_price) * quantity * lot_size
    else:  # SELL
        pnl = (entry_price - exit_price) * quantity * lot_size
    
    return round(pnl, 2)

def select_strike_for_leg(leg_params: Dict, market_data: Dict) -> Tuple[float, float]:
    """Select strike and get price for a leg"""
    # This would interface with the actual strike selection logic
    # Placeholder implementation
    strike = leg_params.get('strike', 22000)
    entry_price = market_data.get('option_prices', {}).get(strike, 100)
    return strike, entry_price

# ============================================================================
# Main Fix Implementation
# ============================================================================

class GPUSystemComprehensiveFix:
    """Main class that applies all fixes to the GPU system"""
    
    def __init__(self):
        self.trade_completion_fix = TradeCompletionFix()
        self.multi_leg_fix = MultiLegExecutionFix()
        self.output_format_fix = OutputFormatFix()
        self.atm_fix = ATMCalculationFix()
        
    def apply_all_fixes(self, portfolio_config: Dict, 
                       strategy_config: Dict,
                       market_data: pd.DataFrame) -> str:
        """Apply all fixes and generate compliant output"""
        logger.info("Applying comprehensive fixes to GPU system...")
        
        # Fix 1: Execute all legs
        trades = self.multi_leg_fix.execute_all_portfolio_legs(
            portfolio_config, 
            market_data.iloc[0].to_dict()
        )
        
        # Fix 2: Process trades with proper exit logic
        for _, bar in market_data.iterrows():
            bar_dict = bar.to_dict()
            for trade in trades:
                if trade.get('status') != 'CLOSED':
                    self.trade_completion_fix.fix_exit_logic(
                        trade, 
                        bar_dict, 
                        strategy_config
                    )
        
        # Fix 3: Ensure all trades are closed
        final_bar = market_data.iloc[-1].to_dict()
        trades = self.trade_completion_fix.ensure_all_trades_closed(trades, final_bar)
        
        # Fix 4: Format output to golden standard
        trades_df = self.output_format_fix.format_trades_to_golden_standard(
            trades,
            portfolio_config,
            strategy_config
        )
        
        # Generate Excel output
        output_path = f"/srv/samba/shared/test_results/gpu_fixed_output_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
        self.output_format_fix.create_golden_format_excel(
            trades_df,
            portfolio_config,
            strategy_config,
            output_path
        )
        
        logger.info(f"All fixes applied. Output saved to: {output_path}")
        return output_path
    
    def validate_fixes(self, output_path: str) -> Dict[str, bool]:
        """Validate that all fixes were properly applied"""
        validation_results = {
            'all_trades_closed': False,
            'multi_leg_complete': False,
            'format_compliant': False,
            'pnl_calculated': False
        }
        
        try:
            # Read the generated output
            df = pd.read_excel(output_path, sheet_name='PORTFOLIO Trans')
            
            # Check if all trades are closed
            open_trades = df[df['Status'] != 'CLOSED']
            validation_results['all_trades_closed'] = len(open_trades) == 0
            
            # Check if multiple legs executed
            unique_strategies = df['Strategy'].nunique()
            validation_results['multi_leg_complete'] = unique_strategies > 1
            
            # Check format compliance
            expected_columns = set(self.output_format_fix.trans_columns)
            actual_columns = set(df.columns)
            validation_results['format_compliant'] = expected_columns == actual_columns
            
            # Check PnL calculation
            zero_pnl = df[df['PnL'] == 0]
            validation_results['pnl_calculated'] = len(zero_pnl) == 0
            
        except Exception as e:
            logger.error(f"Validation failed: {str(e)}")
        
        return validation_results


def main():
    """Example usage of the comprehensive fix"""
    # Sample configuration
    portfolio_config = {
        'name': 'Test Portfolio',
        'capital': 1000000,
        'start_date': '2024-04-01',
        'end_date': '2024-04-05',
        'legs': {
            'leg1': {
                'strategy_name': 'ATM_CE_BUY',
                'option_type': 'CE',
                'strike_selection': 'ATM',
                'position': 'BUY',
                'quantity': 2,
                'lot_size': 50
            },
            'leg2': {
                'strategy_name': 'ATM_PE_BUY',
                'option_type': 'PE',
                'strike_selection': 'ATM',
                'position': 'BUY',
                'quantity': 2,
                'lot_size': 50
            },
            'leg3': {
                'strategy_name': 'OTM_CE_SELL',
                'option_type': 'CE',
                'strike_selection': 'OTM2',
                'position': 'SELL',
                'quantity': 1,
                'lot_size': 50
            },
            'leg4': {
                'strategy_name': 'OTM_PE_SELL',
                'option_type': 'PE',
                'strike_selection': 'OTM2',
                'position': 'SELL',
                'quantity': 1,
                'lot_size': 50
            }
        }
    }
    
    strategy_config = {
        'type': 'TBS',
        'start_time': '09:20:00',
        'end_time': '15:15:00',
        'exit_time': '15:15:00',
        'stop_loss': -5000,
        'target_profit': 10000
    }
    
    # Create sample market data
    dates = pd.date_range('2024-04-01', '2024-04-05', freq='D')
    market_data = pd.DataFrame({
        'date': dates,
        'time': '15:15:00',
        'spot_price': [22100, 22150, 22200, 22180, 22220],
        'close': [100, 105, 110, 108, 112]
    })
    
    # Apply fixes
    fixer = GPUSystemComprehensiveFix()
    output_path = fixer.apply_all_fixes(portfolio_config, strategy_config, market_data)
    
    # Validate fixes
    validation = fixer.validate_fixes(output_path)
    
    print("\nValidation Results:")
    for check, result in validation.items():
        status = "✅" if result else "❌"
        print(f"{check}: {status}")


if __name__ == "__main__":
    print("GPU System Comprehensive Fixes")
    print("=" * 60)
    main()