#!/usr/bin/env python3
"""
Batch load script for Nifty Option Chain data using INSERT statements
This avoids whitelist issues by loading data directly
"""

import os
import sys
import glob
import pandas as pd
import numpy as np
from datetime import datetime
import logging
import time

sys.path.append('/srv/samba/shared')

from bt.dal.heavydb_conn import get_conn

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class BatchNiftyLoader:
    def __init__(self, data_dir="/srv/samba/shared/market_data/nifty/oc_with_futures"):
        self.data_dir = data_dir
        self.conn = None
        self.cursor = None
        self.batch_size = 1000  # Insert 1000 rows at a time
        
    def connect(self):
        """Establish connection to HeavyDB"""
        self.conn = get_conn()
        self.cursor = self.conn.cursor()
        logger.info("Connected to HeavyDB")
        
    def prepare_value(self, val, col_type='str'):
        """Prepare value for SQL insert"""
        if pd.isna(val) or val is None:
            return 'NULL'
        elif col_type == 'str':
            # Escape single quotes
            val_str = str(val).replace("'", "''")
            return f"'{val_str}'"
        elif col_type == 'date':
            return f"'{val}'"
        elif col_type == 'time':
            return f"'{val}'"
        elif col_type == 'timestamp':
            return f"'{val}'"
        else:  # numeric
            return str(val)
            
    def process_and_load_file(self, csv_file):
        """Process and load a single CSV file using batch inserts"""
        logger.info(f"Processing {os.path.basename(csv_file)}...")
        
        try:
            # Count total rows first
            total_rows = sum(1 for line in open(csv_file)) - 1  # subtract header
            logger.info(f"Total rows to process: {total_rows:,}")
            
            # Read and process in chunks
            chunk_num = 0
            total_inserted = 0
            start_time = time.time()
            
            for chunk in pd.read_csv(csv_file, chunksize=self.batch_size):
                chunk_num += 1
                
                # Process dates and times
                chunk['trade_date'] = pd.to_datetime(chunk['trade_date']).dt.strftime('%Y-%m-%d')
                chunk['trade_time'] = pd.to_datetime(chunk['trade_time'], format='%H:%M:%S').dt.strftime('%H:%M:%S')
                chunk['expiry_date'] = pd.to_datetime(chunk['expiry_date']).dt.strftime('%Y-%m-%d')
                
                # Build insert statement
                values_list = []
                
                for _, row in chunk.iterrows():
                    values = []
                    
                    # Date/time columns
                    values.append(self.prepare_value(row['trade_date'], 'date'))
                    values.append(self.prepare_value(row['trade_time'], 'time'))
                    values.append(self.prepare_value(row['expiry_date'], 'date'))
                    
                    # Text columns
                    values.append(self.prepare_value(row['index_name'], 'str'))
                    
                    # Numeric columns
                    numeric_cols = ['spot', 'atm_strike', 'strike', 'dte']
                    for col in numeric_cols:
                        values.append(self.prepare_value(row[col], 'num'))
                    
                    # More text columns
                    text_cols = ['expiry_bucket', 'zone_id', 'zone_name', 'call_strike_type', 'put_strike_type']
                    for col in text_cols:
                        if col == 'zone_id':
                            values.append(self.prepare_value(row[col], 'num'))
                        else:
                            values.append(self.prepare_value(row[col], 'str'))
                    
                    # Call option columns
                    values.append(self.prepare_value(row['ce_symbol'], 'str'))
                    ce_numeric = ['ce_open', 'ce_high', 'ce_low', 'ce_close', 'ce_volume', 
                                  'ce_oi', 'ce_coi', 'ce_iv', 'ce_delta', 'ce_gamma', 
                                  'ce_theta', 'ce_vega', 'ce_rho']
                    for col in ce_numeric:
                        values.append(self.prepare_value(row[col], 'num'))
                    
                    # Put option columns
                    values.append(self.prepare_value(row['pe_symbol'], 'str'))
                    pe_numeric = ['pe_open', 'pe_high', 'pe_low', 'pe_close', 'pe_volume',
                                  'pe_oi', 'pe_coi', 'pe_iv', 'pe_delta', 'pe_gamma',
                                  'pe_theta', 'pe_vega', 'pe_rho']
                    for col in pe_numeric:
                        values.append(self.prepare_value(row[col], 'num'))
                    
                    # Future columns
                    future_numeric = ['future_open', 'future_high', 'future_low', 'future_close',
                                      'future_volume', 'future_oi', 'future_coi']
                    for col in future_numeric:
                        values.append(self.prepare_value(row[col], 'num'))
                    
                    values_list.append(f"({','.join(values)})")
                
                # Build and execute insert
                if values_list:
                    insert_sql = f"""
                    INSERT INTO nifty_option_chain (
                        trade_date, trade_time, expiry_date, index_name,
                        spot, atm_strike, strike, dte, expiry_bucket,
                        zone_id, zone_name, call_strike_type, put_strike_type,
                        ce_symbol, ce_open, ce_high, ce_low, ce_close,
                        ce_volume, ce_oi, ce_coi, ce_iv, ce_delta,
                        ce_gamma, ce_theta, ce_vega, ce_rho,
                        pe_symbol, pe_open, pe_high, pe_low, pe_close,
                        pe_volume, pe_oi, pe_coi, pe_iv, pe_delta,
                        pe_gamma, pe_theta, pe_vega, pe_rho,
                        future_open, future_high, future_low, future_close,
                        future_volume, future_oi, future_coi
                    ) VALUES {','.join(values_list)}
                    """
                    
                    self.cursor.execute(insert_sql)
                    total_inserted += len(values_list)
                    
                    # Progress update
                    elapsed = time.time() - start_time
                    rate = total_inserted / elapsed if elapsed > 0 else 0
                    eta = (total_rows - total_inserted) / rate if rate > 0 else 0
                    
                    logger.info(f"Chunk {chunk_num}: Inserted {len(values_list)} rows. "
                              f"Total: {total_inserted:,}/{total_rows:,} "
                              f"({100*total_inserted/total_rows:.1f}%) "
                              f"Rate: {rate:.0f} rows/sec, ETA: {eta/60:.1f} min")
            
            elapsed_total = time.time() - start_time
            logger.info(f"Completed loading {total_inserted:,} rows in {elapsed_total:.1f} seconds "
                       f"({total_inserted/elapsed_total:.0f} rows/sec)")
            
            return total_inserted
            
        except Exception as e:
            logger.error(f"Error processing {csv_file}: {e}")
            raise
            
    def verify_data(self):
        """Verify loaded data"""
        self.cursor.execute("""
            SELECT 
                COUNT(*) as total,
                COUNT(DISTINCT trade_date) as dates,
                MIN(trade_date) as min_date,
                MAX(trade_date) as max_date
            FROM nifty_option_chain
        """)
        
        result = self.cursor.fetchone()
        logger.info(f"\nData verification:")
        logger.info(f"Total rows: {result[0]:,}")
        logger.info(f"Date range: {result[2]} to {result[3]} ({result[1]} unique dates)")
        
    def run(self, pattern="*.csv", skip_sorted=True):
        """Run the batch loading process"""
        try:
            self.connect()
            
            # Check current state
            self.cursor.execute("SELECT COUNT(*) FROM nifty_option_chain")
            existing = self.cursor.fetchone()[0]
            logger.info(f"Starting with {existing:,} existing rows")
            
            # Get files
            csv_files = sorted(glob.glob(os.path.join(self.data_dir, pattern)))
            if skip_sorted:
                csv_files = [f for f in csv_files if not f.endswith('_sorted.csv')]
                
            logger.info(f"Found {len(csv_files)} files to process")
            
            # Process each file
            total_loaded = 0
            for i, csv_file in enumerate(csv_files, 1):
                logger.info(f"\n[{i}/{len(csv_files)}] {os.path.basename(csv_file)}")
                loaded = self.process_and_load_file(csv_file)
                total_loaded += loaded
                
            # Final verification
            self.verify_data()
            logger.info(f"\nBatch loading complete! Loaded {total_loaded:,} total rows")
            
        finally:
            if self.cursor:
                self.cursor.close()
            if self.conn:
                self.conn.close()

def main():
    import argparse
    
    parser = argparse.ArgumentParser(description='Batch load Nifty data')
    parser.add_argument('--pattern', default='*.csv', help='File pattern')
    parser.add_argument('--data-dir', help='Data directory')
    
    args = parser.parse_args()
    
    loader = BatchNiftyLoader(data_dir=args.data_dir) if args.data_dir else BatchNiftyLoader()
    loader.run(pattern=args.pattern)
    
if __name__ == "__main__":
    main()