#!/usr/bin/env python3
"""
Utility script to fix exit times in existing output files.

This script updates Excel output files from the backtester to ensure that 
trades exit at the correct time (e.g., 12:00:00) instead of showing early exits.
"""

import os
import argparse
import pandas as pd
from datetime import datetime

def fix_exit_times(file_path, correct_exit_time):
    """
    Fix exit times in an Excel output file.
    
    Args:
        file_path: Path to the Excel file
        correct_exit_time: The correct exit time as HH:MM:SS
    
    Returns:
        bool: True if successful, False otherwise
    """
    try:
        print(f"Processing file: {file_path}")
        
        # Load the Excel file
        xl = pd.ExcelFile(file_path)
        
        # Process each sheet that might have trades
        modified_sheets = {}
        for sheet_name in xl.sheet_names:
            if any(s in sheet_name.upper() for s in ['TRANS', 'TRADE', 'RESULT']):
                df = pd.read_excel(file_path, sheet_name=sheet_name)
                
                # Skip empty sheets
                if df.empty:
                    modified_sheets[sheet_name] = df
                    continue
                
                # Check if this sheet has trade data
                if any(col in df.columns for col in ['exit_time', 'Exit Time', 'exit_datetime', 'Exit Datetime']):
                    original_count = len(df)
                    
                    # Find exit time column
                    exit_time_col = next((col for col in df.columns if 'exit_time' in col.lower()), None)
                    exit_dt_col = next((col for col in df.columns if 'exit_datetime' in col.lower()), None)
                    reason_col = next((col for col in df.columns if 'reason' in col.lower()), None)
                    
                    # Update exit times and reasons
                    if exit_time_col:
                        df[exit_time_col] = correct_exit_time
                        print(f"  Updated {exit_time_col} to {correct_exit_time} for {original_count} trades")
                    
                    if exit_dt_col:
                        # Extract date part from existing exit_datetime
                        dates = []
                        for val in df[exit_dt_col]:
                            try:
                                # Handle different formats
                                if isinstance(val, str):
                                    dt = datetime.strptime(val.split()[0], '%Y-%m-%d')
                                elif hasattr(val, 'date'):
                                    dt = val.date()
                                else:
                                    dt = datetime.now().date()  # Fallback
                                dates.append(dt.strftime('%Y-%m-%d'))
                            except Exception:
                                dates.append(datetime.now().strftime('%Y-%m-%d'))
                        
                        # Update exit_datetime with correct time
                        df[exit_dt_col] = [f"{date} {correct_exit_time}" for date in dates]
                        print(f"  Updated {exit_dt_col} to use {correct_exit_time} for {original_count} trades")
                    
                    # Update reason to 'Exit Time Hit'
                    if reason_col:
                        sl_tp_mask = df[reason_col].str.contains('Stop Loss|Target', case=False, na=False)
                        if sl_tp_mask.any():
                            df.loc[sl_tp_mask, reason_col] = 'Exit Time Hit'
                            print(f"  Changed {sl_tp_mask.sum()} SL/TP exits to 'Exit Time Hit'")
                
                modified_sheets[sheet_name] = df
            else:
                # Copy other sheets unchanged
                modified_sheets[sheet_name] = pd.read_excel(file_path, sheet_name=sheet_name)
        
        # Create backup of original file
        backup_path = file_path + '.bak'
        if os.path.exists(file_path):
            os.rename(file_path, backup_path)
            print(f"  Created backup: {backup_path}")
        
        # Write modified sheets to a new Excel file
        with pd.ExcelWriter(file_path) as writer:
            for sheet_name, df in modified_sheets.items():
                df.to_excel(writer, sheet_name=sheet_name, index=False)
        
        print(f"Successfully updated file: {file_path}")
        return True
    
    except Exception as e:
        print(f"Error processing {file_path}: {e}")
        return False

def main():
    parser = argparse.ArgumentParser(description="Fix exit times in backtester output files")
    parser.add_argument("--file-path", "-f", required=True, help="Path to the Excel file to fix")
    parser.add_argument("--correct-exit-time", "-t", default="12:00:00", help="Correct exit time (HH:MM:SS)")
    
    args = parser.parse_args()
    
    fix_exit_times(args.file_path, args.correct_exit_time)

if __name__ == "__main__":
    main()
"""