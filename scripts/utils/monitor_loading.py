#!/usr/bin/env python3
"""Monitor the loading progress"""

import time
import sys
sys.path.append('/srv/samba/shared')

from bt.dal.heavydb_conn import get_conn

def get_row_count():
    """Get current row count"""
    try:
        conn = get_conn()
        cursor = conn.cursor()
        cursor.execute("SELECT COUNT(*) FROM nifty_option_chain")
        count = cursor.fetchone()[0]
        cursor.close()
        conn.close()
        return count
    except:
        return 0

def main():
    print("Monitoring nifty_option_chain loading progress...")
    print("Press Ctrl+C to stop\n")
    
    start_time = time.time()
    start_count = get_row_count()
    last_count = start_count
    
    print(f"Starting count: {start_count:,}")
    print("-" * 60)
    
    while True:
        try:
            time.sleep(30)  # Check every 30 seconds
            
            current_count = get_row_count()
            elapsed = time.time() - start_time
            
            # Calculate rates
            rows_added = current_count - last_count
            instant_rate = rows_added / 30  # rows per second in last 30s
            
            total_rows_added = current_count - start_count
            overall_rate = total_rows_added / elapsed if elapsed > 0 else 0
            
            # Estimate time remaining (assuming ~11.5M total rows)
            estimated_total = 11500000
            rows_remaining = estimated_total - current_count
            eta_seconds = rows_remaining / overall_rate if overall_rate > 0 else 0
            eta_minutes = eta_seconds / 60
            
            print(f"{time.strftime('%H:%M:%S')} | Rows: {current_count:,} | "
                  f"Added: {rows_added:,} | "
                  f"Rate: {instant_rate:.0f}/s | "
                  f"Avg: {overall_rate:.0f}/s | "
                  f"ETA: {eta_minutes:.1f} min")
            
            last_count = current_count
            
        except KeyboardInterrupt:
            break
    
    print("\n" + "-" * 60)
    final_count = get_row_count()
    total_elapsed = time.time() - start_time
    total_added = final_count - start_count
    final_rate = total_added / total_elapsed if total_elapsed > 0 else 0
    
    print(f"Final count: {final_count:,}")
    print(f"Total rows added: {total_added:,}")
    print(f"Total time: {total_elapsed/60:.1f} minutes")
    print(f"Average rate: {final_rate:.0f} rows/second")

if __name__ == "__main__":
    main()