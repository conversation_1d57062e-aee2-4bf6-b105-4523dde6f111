#!/usr/bin/env python3
"""
Apply performance indexes to nifty_option_chain table
"""
import sys
from heavydb import connect
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def apply_indexes():
    """Apply performance indexes to HeavyDB"""
    try:
        # Connect to HeavyDB
        conn = connect(
            host='localhost',
            port=6274,
            user='admin',
            password='HyperInteractive',
            dbname='heavyai',
            protocol='binary'
        )
        logger.info("Connected to HeavyDB")
        
        cursor = conn.cursor()
        
        # Create indexes
        logger.info("Creating index on index_name...")
        try:
            cursor.execute("""
                CREATE INDEX idx_nifty_option_chain_index_name 
                ON nifty_option_chain (index_name)
            """)
            logger.info("Index on index_name created successfully")
        except Exception as e:
            if "already exists" in str(e):
                logger.info("Index on index_name already exists")
            else:
                logger.error(f"Error creating index on index_name: {e}")
        
        logger.info("Creating composite index on index_name, trade_date...")
        try:
            cursor.execute("""
                CREATE INDEX idx_nifty_option_chain_index_date 
                ON nifty_option_chain (index_name, trade_date)
            """)
            logger.info("Composite index created successfully")
        except Exception as e:
            if "already exists" in str(e):
                logger.info("Composite index already exists")
            else:
                logger.error(f"Error creating composite index: {e}")
        
        # Note: HeavyDB doesn't support ANALYZE command
        # The query optimizer automatically maintains statistics
        
        cursor.close()
        conn.close()
        logger.info("Indexes applied successfully")
        
    except Exception as e:
        logger.error(f"Failed to apply indexes: {e}")
        sys.exit(1)

if __name__ == "__main__":
    apply_indexes()