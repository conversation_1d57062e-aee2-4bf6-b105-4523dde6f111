"""
Golden Output Template
This module provides a template for creating Excel outputs
that match the golden output format.
"""

import pandas as pd
from typing import Dict, List, Any


class GoldenOutputTemplate:
    """Template for creating golden output compatible Excel files."""

    SHEET_COLUMNS = {
        "PortfolioParameter": [
            "Head",
            "Value",
        ],
        "GeneralParameter": [
            "StrategyName",
            "MoveSlToCost",
            "Underlying",
            "Index",
            "Weekdays",
            "DTE",
            "StrikeSelectionTime",
            "StartTime",
            "LastEntryTime",
            "EndTime",
            "StrategyProfit",
            "StrategyLoss",
            "StrategyProfitReExecuteNo",
            "StrategyLossReExecuteNo",
            "StrategyTrailingType",
            "PnLCalTime",
            "LockPercent",
            "TrailPercent",
            "SqOff1Time",
            "SqOff1Percent",
            "SqOff2Time",
            "SqOff2Percent",
            "ProfitReaches",
            "LockMinProfitAt",
            "IncreaseInProfit",
            "TrailMinProfitBy",
            "TgtTrackingFrom",
            "TgtRegisterPriceFrom",
            "SlTrackingFrom",
            "SlRegisterPriceFrom",
            "PnLCalculationFrom",
            "ConsiderHedgePnLForStgyPnL",
            "StoplossCheckingInterval",
            "TargetCheckingInterval",
            "ReEntryCheckingInterval",
            "OnExpiryDayTradeNextExpiry",
        ],
        "LegParameter": [
            "StrategyName",
            "IsIdle",
            "LegID",
            "Instrument",
            "Transaction",
            "Expiry",
            "W&Type",
            "W&TValue",
            "TrailW&T",
            "StrikeMethod",
            "MatchPremium",
            "StrikeValue",
            "StrikePremiumCondition",
            "SLType",
            "SLValue",
            "TGTType",
            "TGTValue",
            "TrailSLType",
            "SL_TrailAt",
            "SL_TrailBy",
            "Lots",
            "ReEntryType",
            "ReEnteriesCount",
            "OnEntry_OpenTradeOn",
            "OnEntry_SqOffTradeOff",
            "OnEntry_SqOffAllLegs",
            "OnEntry_OpenTradeDelay",
            "OnEntry_SqOffDelay",
            "OnExit_OpenTradeOn",
            "OnExit_SqOffTradeOff",
            "OnExit_SqOffAllLegs",
            "OnExit_OpenAllLegs",
            "OnExit_OpenTradeDelay",
            "OnExit_SqOffDelay",
            "OpenHedge",
            "HedgeStrikeMethod",
            "HedgeStrikeValue",
            "HedgeStrikePremiumCondition",
        ],
        "Metrics": [
            "Particulars",
            "Combined",
            "RS,916-1200,ATM-SELL,OTM2-BUY WITH 100%SL",
        ],
        "Max Profit and Loss": [
            "Date",
            "Max Profit",
            "Max Profit Time",
            "Max Loss",
            "Max Loss Time",
        ],
        "PORTFOLIO Trans": [
            "Portfolio Name",
            "Strategy Name",
            "ID",
            "Entry Date",
            "Enter On",
            "Entry Day",
            "Exit Date",
            "Exit at",
            "Exit Day",
            "Index",
            "Expiry",
            "Strike",
            "CE/PE",
            "Trade",
            "Qty",
            "Entry at",
            "Exit at.1",
            "Points",
            "Points After Slippage",
            "PNL",
            "AfterSlippage",
            "Taxes",
            "Net PNL",
            "Re-entry No",
            "SL Re-entry No",
            "TGT Re-entry No",
            "Reason",
            "Strategy Entry No",
            "Index At Entry",
            "Index At Exit",
            "MaxProfit",
            "MaxLoss",
        ],
        "PORTFOLIO Results": [
            "Year",
            "Monday",
            "Tuesday",
            "Wednesday",
            "Thursday",
            "Friday",
            "Saturday",
            "Total",
            "Unnamed: 8",
            "Unnamed: 9",
            "Unnamed: 10",
            "Unnamed: 11",
            "Unnamed: 12",
            "Unnamed: 13",
        ],
        "RS,916-1200,ATM-SELL,OTM2-BUY W": [
            "Portfolio Name",
            "Strategy Name",
            "ID",
            "Entry Date",
            "Enter On",
            "Entry Day",
            "Exit Date",
            "Exit at",
            "Exit Day",
            "Index",
            "Expiry",
            "Strike",
            "CE/PE",
            "Trade",
            "Qty",
            "Entry at",
            "Exit at.1",
            "Points",
            "Points After Slippage",
            "PNL",
            "AfterSlippage",
            "Taxes",
            "Net PNL",
            "Re-entry No",
            "SL Re-entry No",
            "TGT Re-entry No",
            "Reason",
            "Strategy Entry No",
            "Index At Entry",
            "Index At Exit",
            "MaxProfit",
            "MaxLoss",
        ],
        "Recovered_Sheet1": [
            "Year",
            "Monday",
            "Tuesday",
            "Wednesday",
            "Thursday",
            "Friday",
            "Saturday",
            "Total",
            "Unnamed: 8",
            "Unnamed: 9",
            "Unnamed: 10",
            "Unnamed: 11",
            "Unnamed: 12",
            "Unnamed: 13",
        ],
    }

    @classmethod
    def create_empty_output(cls) -> Dict[str, pd.DataFrame]:
        """Create empty DataFrames with correct structure."""
        output = {}
        for sheet_name, columns in cls.SHEET_COLUMNS.items():
            output[sheet_name] = pd.DataFrame(columns=columns)
        return output

    @classmethod
    def validate_output(cls, excel_data: Dict[str, pd.DataFrame]) -> List[str]:
        """Validate that output matches golden format."""
        errors = []
        
        # Check all required sheets exist
        for required_sheet in cls.SHEET_COLUMNS:
            if required_sheet not in excel_data:
                errors.append(f"Missing required sheet: {required_sheet}")
                continue
            
            # Check columns match
            expected_cols = cls.SHEET_COLUMNS[required_sheet]
            actual_cols = list(excel_data[required_sheet].columns)
            
            if expected_cols != actual_cols:
                errors.append(f"Column mismatch in {required_sheet}")
                errors.append(f"  Expected: {expected_cols}")
                errors.append(f"  Actual: {actual_cols}")
        
        return errors
