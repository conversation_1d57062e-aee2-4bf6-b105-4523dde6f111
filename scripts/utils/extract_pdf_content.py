#!/usr/bin/env python3

import sys
import subprocess

# Try different methods to extract PDF content
pdf_file = "/srv/samba/shared/Performance Optimization Guide for HeavyDB in Financial Backtesting.pdf"

# Method 1: Try using pdfplumber
try:
    import pdfplumber
    with pdfplumber.open(pdf_file) as pdf:
        text = ""
        for page in pdf.pages:
            text += page.extract_text() + "\n"
        print(text)
        sys.exit(0)
except ImportError:
    pass

# Method 2: Try using PyPDF2
try:
    import PyPDF2
    with open(pdf_file, 'rb') as file:
        pdf_reader = PyPDF2.PdfReader(file)
        text = ""
        for page_num in range(len(pdf_reader.pages)):
            page = pdf_reader.pages[page_num]
            text += page.extract_text() + "\n"
        print(text)
        sys.exit(0)
except ImportError:
    pass

# Method 3: Try using pymupdf
try:
    import fitz  # PyMuPDF
    doc = fitz.open(pdf_file)
    text = ""
    for page in doc:
        text += page.get_text() + "\n"
    print(text)
    doc.close()
    sys.exit(0)
except ImportError:
    pass

print("No PDF processing libraries available. Installing required libraries...")

# Try to install a PDF library
try:
    subprocess.run([sys.executable, "-m", "pip", "install", "PyPDF2"], check=True)
    import PyPDF2
    with open(pdf_file, 'rb') as file:
        pdf_reader = PyPDF2.PdfReader(file)
        text = ""
        for page_num in range(len(pdf_reader.pages)):
            page = pdf_reader.pages[page_num]
            text += page.extract_text() + "\n"
        print(text)
except Exception as e:
    print(f"Error: Unable to process PDF. {e}")