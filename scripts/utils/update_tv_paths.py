#!/usr/bin/env python3
"""
Update file paths in input_tv.xlsx to use correct local file names
"""

import pandas as pd
import os

# Read the Excel file
tv_file = '/srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/tv/input_tv.xlsx'
df = pd.read_excel(tv_file, sheet_name='Setting')

print("Original paths:")
print(f"SignalFilePath: {df.iloc[0]['SignalFilePath']}")
print(f"LongPortfolioFilePath: {df.iloc[0]['LongPortfolioFilePath']}")
print(f"ShortPortfolioFilePath: {df.iloc[0]['ShortPortfolioFilePath']}")

# Update paths to use local files
df.loc[0, 'SignalFilePath'] = 'sample_nifty_list_of_trades.xlsx'
df.loc[0, 'LongPortfolioFilePath'] = 'input_portfolio_long.xlsx'
df.loc[0, 'ShortPortfolioFilePath'] = 'input_portfolio_short.xlsx'

print("\nUpdated paths:")
print(f"SignalFilePath: {df.iloc[0]['SignalFilePath']}")
print(f"LongPortfolioFilePath: {df.iloc[0]['LongPortfolioFilePath']}")
print(f"ShortPortfolioFilePath: {df.iloc[0]['ShortPortfolioFilePath']}")

# First read all sheets
all_sheets = pd.read_excel(tv_file, sheet_name=None)

# Update the Setting sheet
all_sheets['Setting'] = df

# Save back to Excel with all sheets
with pd.ExcelWriter(tv_file, engine='openpyxl') as writer:
    for sheet_name, sheet_df in all_sheets.items():
        sheet_df.to_excel(writer, sheet_name=sheet_name, index=False)

print("\n✅ Updated input_tv.xlsx with correct file paths!")