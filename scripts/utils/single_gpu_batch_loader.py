#!/usr/bin/env python3
"""
Batch loader optimized for single GPU system
Fall-back method when COPY FROM fails
"""
import sys
import csv
import time
import subprocess
from datetime import datetime

def load_csv_in_batches(csv_file, batch_size=5000):
    """Load CSV file in batches using SQL INSERT"""
    print(f"Loading {csv_file} in batches...")
    start_time = time.time()
    
    loaded = 0
    batch_sql = []
    
    with open(csv_file, 'r') as f:
        reader = csv.DictReader(f)
        
        for row in reader:
            # Build INSERT statement
            values = f"""(
                '{row['trade_date']}',
                '{row['trade_time']}',
                '{row['expiry_date']}',
                '{row.get('index_name', 'NIFTY')}',
                {row.get('spot', 0) or 0},
                {row.get('atm_strike', 0) or 0},
                {row.get('strike', 0) or 0},
                {row.get('dte', 0) or 0},
                '{row.get('expiry_bucket', '')}',
                {row.get('zone_id', 0) or 0},
                '{row.get('zone_name', '')}',
                '{row.get('call_strike_type', '')}',
                '{row.get('put_strike_type', '')}',
                '{row.get('ce_symbol', '')}',
                {row.get('ce_open', 0) or 0},
                {row.get('ce_high', 0) or 0},
                {row.get('ce_low', 0) or 0},
                {row.get('ce_close', 0) or 0},
                {row.get('ce_volume', 0) or 0},
                {row.get('ce_oi', 0) or 0},
                {row.get('ce_coi', 0) or 0},
                {row.get('ce_iv', 0) or 0},
                {row.get('ce_delta', 0) or 0},
                {row.get('ce_gamma', 0) or 0},
                {row.get('ce_theta', 0) or 0},
                {row.get('ce_vega', 0) or 0},
                {row.get('ce_rho', 0) or 0},
                '{row.get('pe_symbol', '')}',
                {row.get('pe_open', 0) or 0},
                {row.get('pe_high', 0) or 0},
                {row.get('pe_low', 0) or 0},
                {row.get('pe_close', 0) or 0},
                {row.get('pe_volume', 0) or 0},
                {row.get('pe_oi', 0) or 0},
                {row.get('pe_coi', 0) or 0},
                {row.get('pe_iv', 0) or 0},
                {row.get('pe_delta', 0) or 0},
                {row.get('pe_gamma', 0) or 0},
                {row.get('pe_theta', 0) or 0},
                {row.get('pe_vega', 0) or 0},
                {row.get('pe_rho', 0) or 0},
                {row.get('future_open', 0) or 0},
                {row.get('future_high', 0) or 0},
                {row.get('future_low', 0) or 0},
                {row.get('future_close', 0) or 0},
                {row.get('future_volume', 0) or 0},
                {row.get('future_oi', 0) or 0},
                {row.get('future_coi', 0) or 0}
            )"""
            
            batch_sql.append(values)
            
            # Execute batch when full
            if len(batch_sql) >= batch_size:
                sql = f"INSERT INTO nifty_option_chain VALUES {','.join(batch_sql)};"
                
                # Execute via heavysql
                cmd = ['/opt/heavyai/bin/heavysql', '-p', 'HyperInteractive', 
                       '-u', 'admin', '-d', 'heavyai']
                
                try:
                    process = subprocess.Popen(cmd, stdin=subprocess.PIPE, 
                                             stdout=subprocess.PIPE, 
                                             stderr=subprocess.PIPE, text=True)
                    stdout, stderr = process.communicate(input=sql)
                    
                    if process.returncode == 0:
                        loaded += len(batch_sql)
                        print(f"  Loaded {loaded} rows...", end='\r')
                except Exception as e:
                    print(f"\n  Error: {e}")
                
                batch_sql = []
    
    # Load remaining batch
    if batch_sql:
        sql = f"INSERT INTO nifty_option_chain VALUES {','.join(batch_sql)};"
        cmd = ['/opt/heavyai/bin/heavysql', '-p', 'HyperInteractive', 
               '-u', 'admin', '-d', 'heavyai']
        
        try:
            process = subprocess.Popen(cmd, stdin=subprocess.PIPE, 
                                     stdout=subprocess.PIPE, 
                                     stderr=subprocess.PIPE, text=True)
            stdout, stderr = process.communicate(input=sql)
            
            if process.returncode == 0:
                loaded += len(batch_sql)
        except Exception as e:
            print(f"\n  Error: {e}")
    
    elapsed = time.time() - start_time
    print(f"\n  Completed: {loaded} rows in {elapsed:.1f}s ({loaded/elapsed:.0f} rows/sec)")
    return loaded

if __name__ == "__main__":
    if len(sys.argv) > 1:
        csv_file = sys.argv[1]
        load_csv_in_batches(csv_file)