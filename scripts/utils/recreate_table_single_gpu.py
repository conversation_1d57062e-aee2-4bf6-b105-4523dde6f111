#!/usr/bin/env python3
"""
Recreate the nifty_option_chain table optimized for single GPU
"""
import sys
import time
from heavydb import connect

def recreate_table_for_single_gpu():
    """Drop and recreate the table without sharding for single GPU"""
    
    # Connection parameters
    conn_params = {
        'host': 'localhost',
        'port': 6274,
        'user': 'admin',
        'password': 'HyperInteractive',
        'dbname': 'heavyai'
    }
    
    print(f"Connecting to HeavyDB...")
    max_retries = 3
    retry_delay = 2
    
    for attempt in range(max_retries):
        try:
            conn = connect(**conn_params)
            cursor = conn.cursor()
            print("Successfully connected to HeavyDB")
            break
        except Exception as e:
            print(f"Connection attempt {attempt + 1} failed: {e}")
            if attempt < max_retries - 1:
                print(f"Retrying in {retry_delay} seconds...")
                time.sleep(retry_delay)
            else:
                print("Failed to connect to HeavyDB after all retries")
                sys.exit(1)
    
    try:
        # First check current row count
        print("\nChecking current row count...")
        cursor.execute("SELECT COUNT(*) FROM nifty_option_chain")
        current_count = cursor.fetchone()[0]
        print(f"Current row count: {current_count:,}")
    except Exception as e:
        print(f"Table doesn't exist or error checking count: {e}")
        current_count = 0
    
    # Drop the incorrectly sharded table
    print("\nDropping the incorrectly sharded table...")
    try:
        cursor.execute("DROP TABLE IF EXISTS nifty_option_chain")
        print("Table dropped successfully")
    except Exception as e:
        print(f"Error dropping table: {e}")
    
    # Create new table optimized for single GPU
    print("\nCreating new table optimized for single GPU...")
    
    create_table_sql = """
    CREATE TABLE nifty_option_chain (
        trade_date DATE ENCODING DAYS(16),
        trade_time TIME,
        expiry_date DATE ENCODING DAYS(16),
        dte SMALLINT,
        atm_strike INTEGER,
        strike INTEGER,
        strike_type TEXT ENCODING DICT(8),
        spot_price FLOAT,
        future_price FLOAT,
        iv FLOAT,
        ltp FLOAT,
        prev_close FLOAT,
        daily_change FLOAT,
        daily_change_percent FLOAT,
        volume BIGINT,
        value_traded DOUBLE,
        oi BIGINT,
        oi_change_daily BIGINT,
        oi_change_percent FLOAT,
        bid_qty BIGINT,
        bid_price FLOAT,
        ask_price FLOAT,
        ask_qty BIGINT,
        bid_ask_spread FLOAT,
        total_buy_qty BIGINT,
        total_sell_qty BIGINT,
        lower_circuit FLOAT,
        upper_circuit FLOAT,
        vega FLOAT,
        theta FLOAT,
        gamma FLOAT,
        delta FLOAT,
        rho FLOAT,
        datetime TIMESTAMP,
        year_value INTEGER,
        month_value SMALLINT,
        week_of_month SMALLINT,
        day_of_week SMALLINT,
        date_value DATE,
        time_value TIME,
        monthly_expiry BOOLEAN,
        instrument TEXT ENCODING DICT(8),
        moneyness TEXT ENCODING DICT(8),
        value_label TEXT ENCODING DICT(8),
        percent_label TEXT ENCODING DICT(8),
        instrument_type TEXT ENCODING DICT(8)
    ) WITH (
        fragment_size = 32000000,
        sort_column = 'trade_date'
    );
    """
    
    try:
        cursor.execute(create_table_sql)
        print("Table created successfully with single GPU optimization")
        print("Key optimizations:")
        print("- NO SHARD KEY or SHARD_COUNT (single GPU)")
        print("- Fragment size: 32M rows")
        print("- Sorted by trade_date")
        print("- Dictionary encoding for text columns")
    except Exception as e:
        print(f"Error creating table: {e}")
        conn.close()
        sys.exit(1)
    
    # Verify table creation
    print("\nVerifying table creation...")
    try:
        cursor.execute("SHOW CREATE TABLE nifty_option_chain")
        create_statement = cursor.fetchone()[0]
        print("\nTable created with following structure:")
        print(create_statement[:500] + "..." if len(create_statement) > 500 else create_statement)
    except Exception as e:
        print(f"Error verifying table: {e}")
    
    conn.close()
    print("\nTable recreation completed successfully!")
    print(f"Previous row count was: {current_count:,}")
    print("Ready to test COPY FROM with the new single-GPU optimized structure")

if __name__ == "__main__":
    recreate_table_for_single_gpu()