#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to merge the processed option chain file (with DTE, zones, etc.) with the fixed futures data.
This creates a complete file with all required columns.
"""

import pandas as pd
import time

def merge_files():
    start_time = time.time()
    
    # File paths
    processed_file = "/srv/samba/shared/market_data/nifty/oc/nifty_option_chain_complete_2023_apr.csv"
    futures_file = "/srv/samba/shared/market_data/nifty/oc/nifty_option_chain_fut_fixed_2023_apr.csv"
    output_file = "/srv/samba/shared/market_data/nifty/oc/nifty_option_chain_final_complete_2023_apr.csv"
    
    # Load both files
    print(f"Loading processed file: {processed_file}")
    processed_df = pd.read_csv(processed_file)
    print(f"Loaded {len(processed_df)} rows from processed file")
    
    print(f"Loading futures file: {futures_file}")
    futures_df = pd.read_csv(futures_file)
    print(f"Loaded {len(futures_df)} rows from futures file")
    
    # Check column names in futures file
    print("Futures file columns:")
    print(futures_df.columns.tolist())
    
    # Create join keys for both dataframes
    print("\nCreating join keys...")
    processed_df['join_key'] = processed_df['trade_date'] + "_" + processed_df['trade_time'] + "_" + processed_df['expiry_date'].astype(str) + "_" + processed_df['strike'].astype(str)
    
    # Handle expiry_date format in futures_df
    if isinstance(futures_df['expiry_date'].iloc[0], str) and len(futures_df['expiry_date'].iloc[0]) == 6:
        print("Converting expiry date format in futures file...")
        futures_df['expiry_date_fmt'] = futures_df['expiry_date'].apply(
            lambda x: f"20{x[:2]}-{x[2:4]}-{x[4:6]}" if isinstance(x, str) and len(x) == 6 else x
        )
    else:
        futures_df['expiry_date_fmt'] = futures_df['expiry_date']
    
    futures_df['join_key'] = futures_df['trade_date'] + "_" + futures_df['trade_time'] + "_" + futures_df['expiry_date_fmt'].astype(str) + "_" + futures_df['strike'].astype(str)
    
    # Extract all available futures columns
    future_column_prefixes = ['fut_']
    futures_cols = ['join_key']
    
    for col in futures_df.columns:
        for prefix in future_column_prefixes:
            if col.startswith(prefix):
                futures_cols.append(col)
    
    print(f"Found futures columns: {futures_cols}")
    
    # Check if any futures columns were found
    if len(futures_cols) <= 1:  # Only join_key
        print("No futures columns found with 'fut_' prefix. Looking for raw futures columns...")
        raw_futures_cols = ['open', 'high', 'low', 'close', 'volume', 'oi', 'coi']
        found_futures_cols = [col for col in raw_futures_cols if col in futures_df.columns]
        
        if found_futures_cols:
            print(f"Found raw futures columns: {found_futures_cols}")
            futures_subset = futures_df[['join_key'] + found_futures_cols].copy()
            
            # Rename columns to add fut_ prefix
            rename_dict = {col: f'fut_{col}' for col in found_futures_cols}
            futures_subset = futures_subset.rename(columns=rename_dict)
            futures_cols = ['join_key'] + [f'fut_{col}' for col in found_futures_cols]
        else:
            print("No futures columns found in futures file.")
            return None
    else:
        futures_subset = futures_df[futures_cols].copy()
    
    # Print sample of futures data for debugging
    print("\nSample of futures data:")
    print(futures_subset.head(3).to_string())
    
    # Merge dataframes on join_key
    print("\nMerging dataframes...")
    merged_df = pd.merge(processed_df, futures_subset, on='join_key', how='left')
    
    # Check merge results
    print(f"Merged dataframe has {len(merged_df)} rows")
    
    # Count rows with futures data
    futures_column = [col for col in futures_cols if col != 'join_key'][0]  # Get first futures column
    has_futures = merged_df[futures_column].notna()
    futures_count = has_futures.sum()
    print(f"Rows with futures data: {futures_count} ({futures_count/len(merged_df)*100:.2f}%)")
    
    # Sample merged data
    print("\nSample of merged data (first 3 rows):")
    sample_cols = ['trade_date', 'trade_time', 'expiry_date', 'strike', 'dte', 
                   'zone_name', 'call_strike_type', 'spot']
    futures_sample_col = [col for col in futures_cols if col != 'join_key'][0]
    sample_cols.append(futures_sample_col)
    print(merged_df[sample_cols].head(3).to_string())
    
    # Drop the join key
    merged_df = merged_df.drop('join_key', axis=1)
    
    # Save the merged file
    print(f"\nSaving merged file to {output_file}...")
    merged_df.to_csv(output_file, index=False)
    
    end_time = time.time()
    print(f"Processing completed in {end_time - start_time:.2f} seconds")
    
    return output_file

if __name__ == "__main__":
    output_file = merge_files()
    
    if output_file:
        print(f"\nSuccessfully created final complete file: {output_file}")
        print("This file now contains all required columns:")
        print("- DTE (Days to Expiry)")
        print("- zone_id/zone_name (time zone classification)")
        print("- expiry_bucket (CW, NW, CM, NM)")
        print("- call_strike_type/put_strike_type (ATM, ITM1-n, OTM1-n)")
        print("- fut_* columns (futures data)")
    else:
        print("\nMerge failed. Please check the error messages above.") 