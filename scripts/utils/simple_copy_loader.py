#!/usr/bin/env python3
"""
Simple COPY FROM loader for Nifty Option Chain data
Uses HeavyDB's COPY FROM command which should be much faster
"""

import os
import sys
import glob
import subprocess
import time
from datetime import datetime
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def run_sql_command(sql, timeout=1800):
    """Run SQL command via heavysql"""
    cmd = ["/opt/heavyai/bin/heavysql", "-s", "127.0.0.1", "--port", "6274", 
           "-u", "admin", "-p", "HyperInteractive", "-d", "heavyai"]
    
    try:
        process = subprocess.Popen(cmd, stdin=subprocess.PIPE, 
                                stdout=subprocess.PIPE, stderr=subprocess.PIPE,
                                text=True)
        
        stdout, stderr = process.communicate(input=sql, timeout=timeout)
        
        if process.returncode != 0:
            logger.error(f"SQL command failed with return code {process.returncode}")
            logger.error(f"STDERR: {stderr}")
            logger.error(f"STDOUT: {stdout}")
            return False
        
        if stderr and "Error" in stderr:
            logger.error(f"SQL Error: {stderr}")
            return False
        
        return True
    except subprocess.TimeoutExpired:
        logger.error(f"SQL command timed out after {timeout} seconds")
        process.kill()
        return False
    except Exception as e:
        logger.error(f"Error executing SQL: {e}")
        return False

def get_row_count():
    """Get current row count from the table"""
    cmd = ["/opt/heavyai/bin/heavysql", "-s", "127.0.0.1", "--port", "6274", 
           "-u", "admin", "-p", "HyperInteractive", "-d", "heavyai"]
    
    sql = "SELECT COUNT(*) FROM nifty_option_chain;"
    
    try:
        process = subprocess.Popen(cmd, stdin=subprocess.PIPE, 
                                stdout=subprocess.PIPE, stderr=subprocess.PIPE,
                                text=True)
        stdout, stderr = process.communicate(input=sql)
        
        # Parse the count from output
        lines = stdout.strip().split('\n')
        for line in lines:
            if line.strip().isdigit():
                return int(line.strip())
        return 0
    except:
        return 0

def load_csv_file(csv_file):
    """Load a single CSV file using COPY FROM"""
    file_name = os.path.basename(csv_file)
    file_size_mb = os.path.getsize(csv_file) / (1024 * 1024)
    
    logger.info(f"Loading {file_name} ({file_size_mb:.1f} MB)...")
    
    # Get row count before
    count_before = get_row_count()
    
    # Use absolute path for COPY FROM
    absolute_path = os.path.abspath(csv_file)
    
    copy_sql = f"""
    COPY nifty_option_chain FROM '{absolute_path}'
    WITH (header='true', delimiter=',');
    """
    
    start_time = time.time()
    success = run_sql_command(copy_sql)
    elapsed = time.time() - start_time
    
    if success:
        # Get row count after
        count_after = get_row_count()
        rows_loaded = count_after - count_before
        rate = rows_loaded / elapsed if elapsed > 0 else 0
        
        logger.info(f"✓ Successfully loaded {file_name}")
        logger.info(f"  Loaded {rows_loaded:,} rows in {elapsed:.1f} seconds ({rate:.0f} rows/sec)")
        logger.info(f"  Total rows in table: {count_after:,}")
        return True, rows_loaded
    else:
        logger.error(f"✗ Failed to load {file_name}")
        return False, 0

def main():
    data_dir = "/srv/samba/shared/market_data/nifty/oc_with_futures"
    
    logger.info("=== Simple COPY FROM Loader ===")
    logger.info(f"Starting at: {datetime.now()}")
    
    # Get initial row count
    initial_count = get_row_count()
    logger.info(f"Initial row count: {initial_count:,}")
    
    # Get list of CSV files
    csv_files = sorted(glob.glob(os.path.join(data_dir, "*.csv")))
    csv_files = [f for f in csv_files if not f.endswith('_sorted.csv')]
    
    if not csv_files:
        logger.error("No CSV files found!")
        return
    
    logger.info(f"Found {len(csv_files)} CSV files to process")
    
    # Process each file
    total_start = time.time()
    success_count = 0
    failed_count = 0
    total_rows_loaded = 0
    
    for i, csv_file in enumerate(csv_files, 1):
        logger.info(f"\n[{i}/{len(csv_files)}] Processing {os.path.basename(csv_file)}")
        
        success, rows_loaded = load_csv_file(csv_file)
        
        if success:
            success_count += 1
            total_rows_loaded += rows_loaded
        else:
            failed_count += 1
    
    # Final summary
    total_elapsed = time.time() - total_start
    final_count = get_row_count()
    overall_rate = total_rows_loaded / total_elapsed if total_elapsed > 0 else 0
    
    logger.info("\n=== Loading Complete ===")
    logger.info(f"Total files: {len(csv_files)}")
    logger.info(f"Successful: {success_count}")
    logger.info(f"Failed: {failed_count}")
    logger.info(f"Total rows loaded: {total_rows_loaded:,}")
    logger.info(f"Initial count: {initial_count:,}")
    logger.info(f"Final count: {final_count:,}")
    logger.info(f"Total time: {total_elapsed:.1f} seconds")
    logger.info(f"Overall rate: {overall_rate:.0f} rows/second")
    logger.info(f"Finished at: {datetime.now()}")

if __name__ == "__main__":
    main()