#!/usr/bin/env python3
"""
Final bulk loader - combines all strategies for fastest loading
"""

import os
import sys
import glob
import subprocess
import time
from datetime import datetime
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def get_row_count():
    """Get current row count"""
    cmd = ["/opt/heavyai/bin/heavysql", "-s", "127.0.0.1", "--port", "6274", 
           "-u", "admin", "-p", "HyperInteractive", "-d", "heavyai"]
    
    sql = "SELECT COUNT(*) FROM nifty_option_chain;"
    
    try:
        process = subprocess.Popen(cmd, stdin=subprocess.PIPE, 
                                stdout=subprocess.PIPE, stderr=subprocess.PIPE,
                                text=True)
        stdout, stderr = process.communicate(input=sql)
        
        lines = stdout.strip().split('\n')
        for line in lines:
            if line.strip().isdigit():
                return int(line.strip())
        return 0
    except:
        return 0

def execute_sql_file(sql_file):
    """Execute a single SQL file without timeout"""
    cmd = f"/opt/heavyai/bin/heavysql -s 127.0.0.1 --port 6274 -u admin -p HyperInteractive -d heavyai < {sql_file}"
    
    try:
        start_time = time.time()
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
        elapsed = time.time() - start_time
        
        if result.returncode == 0:
            return True, elapsed
        else:
            logger.error(f"Error executing {os.path.basename(sql_file)}: {result.stderr}")
            return False, elapsed
    except Exception as e:
        logger.error(f"Exception executing {os.path.basename(sql_file)}: {e}")
        return False, 0

def main():
    sql_dir = "/srv/samba/shared/sql_batches"
    
    logger.info("=== Final Bulk Loader ===")
    logger.info(f"Starting at: {datetime.now()}")
    
    # Get initial row count
    initial_count = get_row_count()
    logger.info(f"Initial row count: {initial_count:,}")
    
    # Get remaining SQL files
    sql_files = sorted(glob.glob(os.path.join(sql_dir, "*.sql")))
    sql_files = [f for f in sql_files if 'execute' not in f]
    
    # Since we've already loaded some, start from where we left off
    # Based on the error logs, we were at around batch 56
    start_from = 60  # Start a bit after to ensure we don't miss any
    sql_files = sql_files[start_from:]
    
    logger.info(f"Found {len(sql_files)} SQL files to execute (starting from batch {start_from+1})")
    
    # Process files sequentially with progress tracking
    start_time = time.time()
    success_count = 0
    failed_count = 0
    
    for i, sql_file in enumerate(sql_files):
        file_name = os.path.basename(sql_file)
        logger.info(f"[{i+1}/{len(sql_files)}] Executing {file_name}...")
        
        success, elapsed = execute_sql_file(sql_file)
        
        if success:
            success_count += 1
            logger.info(f"  ✓ Success ({elapsed:.1f}s)")
        else:
            failed_count += 1
            logger.error(f"  ✗ Failed")
        
        # Progress update every 10 files
        if (i + 1) % 10 == 0:
            current_count = get_row_count()
            rows_added = current_count - initial_count
            elapsed_total = time.time() - start_time
            rate = rows_added / elapsed_total if elapsed_total > 0 else 0
            
            logger.info(f"\nProgress Update:")
            logger.info(f"  Files: {i+1}/{len(sql_files)} ({(i+1)/len(sql_files)*100:.1f}%)")
            logger.info(f"  Success: {success_count}, Failed: {failed_count}")
            logger.info(f"  Current rows: {current_count:,} (+{rows_added:,})")
            logger.info(f"  Rate: {rate:.0f} rows/second")
            logger.info(f"  ETA: {(len(sql_files)-(i+1))*elapsed_total/(i+1)/60:.1f} minutes\n")
    
    # Final summary
    total_elapsed = time.time() - start_time
    final_count = get_row_count()
    rows_added = final_count - initial_count
    
    logger.info("\n=== Loading Complete ===")
    logger.info(f"Total files processed: {len(sql_files)}")
    logger.info(f"Successful: {success_count}")
    logger.info(f"Failed: {failed_count}")
    logger.info(f"Initial count: {initial_count:,}")
    logger.info(f"Final count: {final_count:,}")
    logger.info(f"Rows added: {rows_added:,}")
    logger.info(f"Total time: {total_elapsed/60:.1f} minutes")
    logger.info(f"Average rate: {rows_added/total_elapsed:.0f} rows/second")
    logger.info(f"Finished at: {datetime.now()}")

if __name__ == "__main__":
    main()