#!/usr/bin/env python3
import os
import sys
import glob
import csv
import subprocess
import time
import signal
import argparse
import multiprocessing
from datetime import datetime

# Configuration
CSV_DIR = "/srv/samba/shared/market_data/nifty/oc_with_futures/"
DEFAULT_BATCH_SIZE = 1000
MAX_PROCESSES = 4  # Number of parallel processes
MAX_RETRIES = 3

def parse_args():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser(description='Parallel data loader for Nifty Option Chain')
    parser.add_argument('--batch-size', type=int, default=DEFAULT_BATCH_SIZE, help='Batch size for inserts')
    parser.add_argument('--processes', type=int, default=MAX_PROCESSES, help='Number of parallel processes')
    parser.add_argument('--test', action='store_true', help='Run in test mode with limited rows')
    parser.add_argument('--timeout', type=int, default=300, help='SQL command timeout in seconds')
    return parser.parse_args()

def run_sql_command(sql, timeout=300):
    """Run SQL command via heavysql with timeout"""
    cmd = ["/opt/heavyai/bin/heavysql", "-s", "127.0.0.1", "--port", "6274", 
           "-u", "admin", "-p", "HyperInteractive", "-d", "heavyai"]
    
    try:
        process = subprocess.Popen(cmd, stdin=subprocess.PIPE, 
                                stdout=subprocess.PIPE, stderr=subprocess.PIPE,
                                text=True)
        
        stdout, stderr = process.communicate(input=sql, timeout=timeout)
        
        if stderr and "Error" in stderr:
            print(f"SQL Error: {stderr}")
            return False
        
        return True
    except subprocess.TimeoutExpired:
        print(f"SQL command timed out after {timeout} seconds")
        # Kill the process
        process.kill()
        return False
    except Exception as e:
        print(f"Unexpected error: {e}")
        return False

def ensure_table_exists():
    """Ensure the target table exists with correct schema"""
    print("Creating table nifty_option_chain...")
    create_table_sql = """
    DROP TABLE IF EXISTS nifty_option_chain;
    CREATE TABLE nifty_option_chain (
        trade_date       DATE,
        trade_time       TIME,
        expiry_date      DATE,
        index_name       TEXT,
        spot             DOUBLE,
        atm_strike       DOUBLE,
        strike           DOUBLE,
        dte              INT,
        expiry_bucket    TEXT,
        zone_id          SMALLINT,
        zone_name        TEXT,
        call_strike_type TEXT,
        put_strike_type  TEXT,
        ce_symbol        TEXT,
        ce_open          DOUBLE,
        ce_high          DOUBLE,
        ce_low           DOUBLE,
        ce_close         DOUBLE,
        ce_volume        BIGINT,
        ce_oi            BIGINT,
        ce_coi           BIGINT,
        ce_iv            DOUBLE,
        ce_delta         DOUBLE,
        ce_gamma         DOUBLE,
        ce_theta         DOUBLE,
        ce_vega          DOUBLE,
        ce_rho           DOUBLE,
        pe_symbol        TEXT,
        pe_open          DOUBLE,
        pe_high          DOUBLE,
        pe_low           DOUBLE,
        pe_close         DOUBLE,
        pe_volume        BIGINT,
        pe_oi            BIGINT,
        pe_coi           BIGINT,
        pe_iv            DOUBLE,
        pe_delta         DOUBLE,
        pe_gamma         DOUBLE,
        pe_theta         DOUBLE,
        pe_vega          DOUBLE,
        pe_rho           DOUBLE,
        future_open      DOUBLE,
        future_high      DOUBLE,
        future_low       DOUBLE,
        future_close     DOUBLE,
        future_volume    BIGINT,
        future_oi        BIGINT,
        future_coi       BIGINT
    );
    """
    
    if run_sql_command(create_table_sql, timeout=120):
        print("Table created successfully")
        return True
    else:
        print("Failed to create table")
        return False

def get_csv_files():
    """Get a list of all CSV files to process"""
    return sorted(glob.glob(os.path.join(CSV_DIR, "*.csv")))

def count_lines(file_path):
    """Count lines in a file"""
    with open(file_path, 'r') as f:
        return sum(1 for _ in f)

def process_file_chunk(file_path, start_row, end_row, batch_size, queue, timeout=300):
    """Process a chunk of rows from a file"""
    file_name = os.path.basename(file_path)
    process_id = os.getpid()
    print(f"[Process {process_id}] Processing {file_name} rows {start_row}-{end_row}")
    
    # Open the file and skip to start_row
    with open(file_path, 'r') as f:
        reader = csv.reader(f)
        header = next(reader)  # Skip header
        
        # Skip to start_row
        for _ in range(1, start_row):
            next(reader)
        
        rows_to_process = end_row - start_row + 1
        batch_rows = []
        total_processed = 0
        total_inserted = 0
        batch_count = 0
        
        for row_num, row in enumerate(reader, start=start_row):
            if row_num > end_row:
                break
                
            if len(row) != len(header):
                print(f"[Process {process_id}] Warning: Row {row_num} has {len(row)} columns, expected {len(header)}")
                continue
                
            batch_rows.append(row)
            
            # Process batch when it reaches the batch size
            if len(batch_rows) >= batch_size:
                batch_count += 1
                success = insert_batch(batch_rows, batch_count, file_name, process_id, timeout)
                total_processed += len(batch_rows)
                if success:
                    total_inserted += len(batch_rows)
                
                # Show progress
                progress = (total_processed / rows_to_process) * 100
                print(f"[Process {process_id}] Progress: {progress:.2f}% - Inserted {total_inserted}/{total_processed} rows (Batch {batch_count})")
                
                # Clear batch
                batch_rows = []
        
        # Process any remaining rows
        if batch_rows:
            batch_count += 1
            success = insert_batch(batch_rows, batch_count, file_name, process_id, timeout)
            total_processed += len(batch_rows)
            if success:
                total_inserted += len(batch_rows)
            print(f"[Process {process_id}] Final batch: Inserted {len(batch_rows)} rows")
    
    print(f"[Process {process_id}] Completed chunk. Inserted {total_inserted}/{rows_to_process} rows.")
    # Put results in the queue
    queue.put((file_name, total_inserted))
    return total_inserted

def insert_batch(rows, batch_num, file_name, process_id, timeout=300):
    """Insert a batch of rows into the database"""
    insert_statements = []
    
    for i, row in enumerate(rows):
        # Clean and format values properly
        values = []
        try:
            for j, val in enumerate(row):
                # Handle different data types
                if j in [0, 2]:  # DATE fields (trade_date, expiry_date)
                    values.append(f"'{val}'")
                elif j == 1:  # TIME field (trade_time)
                    values.append(f"'{val}'")
                elif j in [3, 10, 11, 12, 13, 27]:  # TEXT fields
                    values.append(f"'{val}'")
                elif j == 8:  # expiry_bucket TEXT field
                    values.append(f"'{val}'")
                elif val == '':  # Handle empty values
                    values.append("NULL")
                else:
                    values.append(val)
            
            # Create INSERT statement
            insert_sql = f"INSERT INTO nifty_option_chain VALUES ({', '.join(values)});"
            insert_statements.append(insert_sql)
        except Exception as e:
            print(f"[Process {process_id}] Error processing row {i}, column {j if 'j' in locals() else 'unknown'}: {e}")
            # Skip this problematic row
            continue
    
    # Combine all inserts and execute
    all_inserts = "\n".join(insert_statements)
    
    # Retry logic
    for attempt in range(MAX_RETRIES):
        success = run_sql_command(all_inserts, timeout=timeout)
        
        if success:
            return True
        else:
            print(f"[Process {process_id}] Batch {batch_num} failed on attempt {attempt+1}/{MAX_RETRIES}. Retrying...")
            time.sleep(2)  # Wait before retry
    
    print(f"[Process {process_id}] Batch {batch_num} failed after {MAX_RETRIES} attempts.")
    return False

def process_file_parallel(file_path, num_processes, batch_size, test_mode=False, timeout=300):
    """Process a file using multiple processes"""
    file_name = os.path.basename(file_path)
    print(f"\nProcessing {file_name} with {num_processes} processes...")
    
    # Count lines and determine chunks
    total_lines = count_lines(file_path)
    print(f"Total lines in file: {total_lines}")
    
    # Account for header
    data_lines = total_lines - 1
    
    # In test mode, limit to a small number of rows
    if test_mode:
        test_rows = min(10000, data_lines)
        print(f"Test mode: limiting to {test_rows} rows")
        data_lines = test_rows
    
    # Calculate lines per process
    lines_per_process = data_lines // num_processes
    print(f"Each process will handle approximately {lines_per_process} rows")
    
    # Create chunks
    chunks = []
    for i in range(num_processes):
        start_row = i * lines_per_process + 1  # +1 for header offset
        end_row = start_row + lines_per_process - 1
        
        # Last process gets any remainder
        if i == num_processes - 1:
            if test_mode:
                end_row = test_rows
            else:
                end_row = data_lines
                
        chunks.append((start_row, end_row))
    
    # Create a queue for results
    queue = multiprocessing.Queue()
    
    # Create and start processes
    processes = []
    for i, (start_row, end_row) in enumerate(chunks):
        p = multiprocessing.Process(
            target=process_file_chunk,
            args=(file_path, start_row, end_row, batch_size, queue, timeout)
        )
        processes.append(p)
        p.start()
        print(f"Started process {i+1} for rows {start_row}-{end_row}")
    
    # Wait for all processes to finish
    for p in processes:
        p.join()
    
    # Collect results
    total_inserted = 0
    while not queue.empty():
        file_name, inserted = queue.get()
        total_inserted += inserted
    
    print(f"File {file_name} completed. Total rows inserted: {total_inserted}")
    return total_inserted

def verify_loaded_data():
    """Verify the data was loaded correctly"""
    # Get total count
    print("\nVerifying loaded data...")
    count_sql = "SELECT COUNT(*) FROM nifty_option_chain;"
    cmd = ["/opt/heavyai/bin/heavysql", "-s", "127.0.0.1", "--port", "6274", 
            "-u", "admin", "-p", "HyperInteractive", "-d", "heavyai", "-q", count_sql]
    
    try:
        process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
        stdout, stderr = process.communicate()
        if stderr and "Error" in stderr:
            print(f"Error verifying data: {stderr}")
            return
        
        # Parse the count
        count_line = [line for line in stdout.split('\n') if line and not line.startswith('User') and not line.startswith('Database')]
        if count_line:
            count = count_line[-1].strip()
            print(f"Total rows in nifty_option_chain: {count}")
    except Exception as e:
        print(f"Error during verification: {e}")

def main():
    args = parse_args()
    num_processes = args.processes
    batch_size = args.batch_size
    test_mode = args.test
    timeout = args.timeout
    
    # Print configuration
    print("=== Parallel Nifty Option Chain Loader ===")
    print(f"Starting at: {datetime.now()}")
    print(f"Processes: {num_processes}")
    print(f"Batch size: {batch_size}")
    print(f"Test mode: {test_mode}")
    
    # Make sure multiprocessing is available
    if num_processes > 1 and not multiprocessing.get_start_method(allow_none=True):
        multiprocessing.set_start_method('spawn')
    
    # Ensure table exists
    if not ensure_table_exists():
        sys.exit(1)
    
    # Get all CSV files
    csv_files = get_csv_files()
    print(f"Found {len(csv_files)} CSV files to process")
    
    # Process each file in parallel
    start_time = datetime.now()
    total_inserted = 0
    
    for i, file_path in enumerate(csv_files):
        file_name = os.path.basename(file_path)
        print(f"[{i+1}/{len(csv_files)}] Processing file: {file_name}")
        
        file_start = datetime.now()
        inserted = process_file_parallel(
            file_path, 
            num_processes, 
            batch_size, 
            test_mode, 
            timeout
        )
        total_inserted += inserted
        file_duration = datetime.now() - file_start
        
        print(f"Completed file {i+1}/{len(csv_files)}: {file_name} in {file_duration}")
        print(f"Total rows loaded so far: {total_inserted}")
        
        # Exit after first file in test mode
        if test_mode and i == 0:
            print("Test mode: exiting after first file")
            break
    
    # Verify loaded data
    verify_loaded_data()
    
    # Calculate statistics
    end_time = datetime.now()
    duration = end_time - start_time
    rows_per_second = total_inserted / max(1, duration.total_seconds())
    
    print("\n=== Load Summary ===")
    print(f"Start time: {start_time}")
    print(f"End time: {end_time}")
    print(f"Duration: {duration}")
    print(f"Files processed: {len(csv_files) if not test_mode else 1}")
    print(f"Total rows inserted: {total_inserted}")
    print(f"Average throughput: {rows_per_second:.2f} rows/second")

if __name__ == "__main__":
    main() 