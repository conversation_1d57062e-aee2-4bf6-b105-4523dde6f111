"""
Main FastAPI application for Enterprise GPU Backtester v2
Test implementation with real HeavyDB data
"""
from fastapi import FastAP<PERSON>, Request, WebSocket
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import logging
import uvicorn
from datetime import datetime
from typing import Dict

from api.v2.routers import backtest
from api.v2.contracts import HealthStatus
from api.v2.websocket import ws_job_updates
from config.database import get_db_connection, DatabaseConfig

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Create FastAPI app
app = FastAPI(
    title="Enterprise GPU Backtester API v2",
    description="Refactored backtesting system with modular architecture",
    version="2.0.0",
    docs_url="/api/v2/docs",
    redoc_url="/api/v2/redoc"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include routers
app.include_router(backtest.router)

# Include v1 compatibility routes
try:
    from api_v1_compatibility import router as v1_router
    app.include_router(v1_router)
    logger.info("V1 compatibility routes loaded")
except ImportError:
    logger.warning("V1 compatibility routes not available")

@app.on_event("startup")
async def startup_event():
    """Initialize application on startup"""
    logger.info("Starting Enterprise GPU Backtester v2")
    
    # Test database connection
    try:
        with get_db_connection(test_mode=True) as db:
            cursor = db.execute(f"""
                SELECT COUNT(*) FROM {DatabaseConfig.OPTION_CHAIN_TABLE} 
                WHERE {DatabaseConfig.TEST_DATE_FILTER}
            """)
            count = cursor.fetchone()[0]
            logger.info(f"Database connected. Test data: {count:,} rows for {DatabaseConfig.TEST_DATE}")
    except Exception as e:
        logger.error(f"Database connection failed: {e}")

@app.on_event("shutdown")
async def shutdown_event():
    """Cleanup on shutdown"""
    logger.info("Shutting down Enterprise GPU Backtester v2")

@app.get("/", response_model=Dict[str, str])
async def root():
    """Root endpoint"""
    return {
        "message": "Enterprise GPU Backtester API v2",
        "version": "2.0.0",
        "docs": "/api/v2/docs",
        "health": "/api/v2/health"
    }

@app.get("/api/v2/health", response_model=HealthStatus)
async def health_check():
    """Health check endpoint"""
    start_time = datetime.now()
    
    # Check components
    components = {}
    
    # API health
    components["api"] = {
        "status": "healthy",
        "response_time_ms": 0
    }
    
    # Database health
    try:
        db_start = datetime.now()
        with get_db_connection(test_mode=True) as db:
            cursor = db.execute("SELECT 1")
            cursor.fetchone()
        db_time = (datetime.now() - db_start).total_seconds() * 1000
        
        components["heavydb"] = {
            "status": "healthy",
            "connection": "active",
            "query_time_ms": round(db_time, 2)
        }
    except Exception as e:
        components["heavydb"] = {
            "status": "unhealthy",
            "error": str(e)
        }
    
    # GPU status (placeholder)
    components["gpu"] = {
        "status": "healthy",
        "utilization": 0,
        "memory_used_gb": 0
    }
    
    # WebSocket status (placeholder)
    components["websocket"] = {
        "status": "healthy",
        "active_connections": 0
    }
    
    # Calculate overall status
    unhealthy_components = [k for k, v in components.items() if v.get("status") != "healthy"]
    overall_status = "unhealthy" if unhealthy_components else "healthy"
    
    return HealthStatus(
        status=overall_status,
        version="2.0.0",
        uptime_seconds=0,  # Placeholder
        components=components
    )

@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    """Global exception handler"""
    logger.error(f"Unhandled exception: {exc}", exc_info=True)
    return JSONResponse(
        status_code=500,
        content={
            "error": "InternalServerError",
            "message": "An unexpected error occurred",
            "timestamp": datetime.now().isoformat()
        }
    )

# WebSocket endpoint for job updates
@app.websocket("/ws/jobs/{job_id}")
async def websocket_job_updates(websocket: WebSocket, job_id: str):
    """WebSocket endpoint for real-time job updates"""
    await ws_job_updates(websocket, job_id)

# Test endpoint for quick verification
@app.get("/api/v2/test")
async def test_endpoint():
    """Quick test endpoint"""
    try:
        with get_db_connection(test_mode=True) as db:
            # Test query
            query = f"""
            SELECT 
                MIN(trade_date) as min_date,
                MAX(trade_date) as max_date,
                COUNT(*) as total_rows,
                COUNT(DISTINCT strike) as unique_strikes
            FROM {DatabaseConfig.OPTION_CHAIN_TABLE}
            WHERE {DatabaseConfig.TEST_DATE_FILTER}
            """
            
            cursor = db.execute(query)
            result = cursor.fetchone()
            
            return {
                "status": "success",
                "test_mode": True,
                "test_date": str(DatabaseConfig.TEST_DATE),
                "data": {
                    "date_range": f"{result[0]} to {result[1]}",
                    "total_rows": result[2],
                    "unique_strikes": result[3]
                }
            }
    except Exception as e:
        return {
            "status": "error",
            "message": str(e)
        }

if __name__ == "__main__":
    # Run the application
    uvicorn.run(
        "main_v2:app",
        host="0.0.0.0",
        port=8000,  # Changed to default port 8000
        reload=True,
        log_level="info"
    )