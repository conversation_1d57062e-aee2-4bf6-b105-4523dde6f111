#!/usr/bin/env python3
"""
Archive System with Synthetic Future ATM Implementation
======================================================

This module implements synthetic future-based ATM calculation for the archive system
to ensure proper comparison with the GPU/HeavyDB system.

Author: Senior Backend Expert
Date: June 9, 2025
"""

import pandas as pd
import numpy as np
import mysql.connector
from datetime import datetime, timedelta
import logging
from typing import Dict, List, Tuple, Optional

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class SyntheticFutureATMCalculator:
    """Implements synthetic future-based ATM calculation for archive system"""
    
    def __init__(self, mysql_config: Dict):
        """Initialize with MySQL connection config"""
        self.mysql_config = mysql_config
        self.strike_increment = 50  # NIFTY strike increment
        
    def get_option_chain_data(self, symbol: str, trade_date: str, expiry: str) -> pd.DataFrame:
        """Fetch option chain data from MySQL"""
        conn = mysql.connector.connect(**self.mysql_config)
        cursor = conn.cursor()
        
        query = """
        SELECT DISTINCT
            strike,
            AVG(CASE WHEN option_type = 'CE' THEN close ELSE NULL END) as ce_close,
            AVG(CASE WHEN option_type = 'PE' THEN close ELSE NULL END) as pe_close,
            AVG(CASE WHEN option_type = 'CE' THEN bid ELSE NULL END) as ce_bid,
            AVG(CASE WHEN option_type = 'CE' THEN ask ELSE NULL END) as ce_ask,
            AVG(CASE WHEN option_type = 'PE' THEN bid ELSE NULL END) as pe_bid,
            AVG(CASE WHEN option_type = 'PE' THEN ask ELSE NULL END) as pe_ask
        FROM nifty_option_chain
        WHERE symbol = %s
            AND trade_date = %s
            AND expiry_date = %s
            AND time = '09:20:00'  -- Use opening time for ATM calculation
        GROUP BY strike
        HAVING ce_close IS NOT NULL AND pe_close IS NOT NULL
        ORDER BY strike
        """
        
        cursor.execute(query, (symbol, trade_date, expiry))
        columns = [desc[0] for desc in cursor.description]
        data = cursor.fetchall()
        
        cursor.close()
        conn.close()
        
        if not data:
            logger.warning(f"No option chain data found for {symbol} on {trade_date} expiry {expiry}")
            return pd.DataFrame()
        
        df = pd.DataFrame(data, columns=columns)
        return df
    
    def calculate_synthetic_future_atm(self, spot_price: float, option_data: pd.DataFrame) -> Tuple[float, float]:
        """
        Calculate ATM using synthetic future methodology
        
        Returns:
            Tuple of (atm_strike, synthetic_future_price)
        """
        if option_data.empty:
            # Fallback to spot-based ATM
            logger.warning("No option data, using spot-based ATM")
            return round(spot_price / self.strike_increment) * self.strike_increment, spot_price
        
        # Calculate synthetic future for each strike
        # Synthetic Future = Strike + CE_price - PE_price
        option_data['synthetic_future'] = (
            option_data['strike'] + 
            option_data['ce_close'] - 
            option_data['pe_close']
        )
        
        # Find strike where synthetic future is closest to spot price
        option_data['diff_from_spot'] = abs(option_data['synthetic_future'] - spot_price)
        
        # Get the strike with minimum difference
        atm_row = option_data.loc[option_data['diff_from_spot'].idxmin()]
        atm_strike = float(atm_row['strike'])
        synthetic_future_price = float(atm_row['synthetic_future'])
        
        logger.info(f"Synthetic Future ATM Calculation:")
        logger.info(f"  Spot Price: {spot_price}")
        logger.info(f"  ATM Strike: {atm_strike}")
        logger.info(f"  Synthetic Future at ATM: {synthetic_future_price:.2f}")
        logger.info(f"  Difference from Spot: {abs(synthetic_future_price - spot_price):.2f}")
        logger.info(f"  CE Price at ATM: {atm_row['ce_close']:.2f}")
        logger.info(f"  PE Price at ATM: {atm_row['pe_close']:.2f}")
        
        return atm_strike, synthetic_future_price
    
    def get_atm_strikes_for_strategy(self, symbol: str, start_date: str, end_date: str, 
                                   expiry_type: str = 'current_week') -> pd.DataFrame:
        """Get ATM strikes for a date range using synthetic future methodology"""
        conn = mysql.connector.connect(**self.mysql_config)
        cursor = conn.cursor()
        
        # Get spot prices for the date range
        spot_query = """
        SELECT DISTINCT trade_date, close as spot_price
        FROM nifty_spot
        WHERE symbol = %s
            AND trade_date BETWEEN %s AND %s
            AND time = '09:20:00'
        ORDER BY trade_date
        """
        
        cursor.execute(spot_query, (symbol, start_date, end_date))
        spot_data = cursor.fetchall()
        
        results = []
        
        for trade_date, spot_price in spot_data:
            # Get appropriate expiry based on expiry_type
            expiry = self._get_expiry_date(trade_date, expiry_type, cursor)
            
            if expiry:
                # Get option chain data
                option_data = self.get_option_chain_data(symbol, trade_date, expiry)
                
                if not option_data.empty:
                    # Calculate synthetic future ATM
                    atm_strike, synthetic_future = self.calculate_synthetic_future_atm(spot_price, option_data)
                    
                    # Also calculate spot-based ATM for comparison
                    spot_based_atm = round(spot_price / self.strike_increment) * self.strike_increment
                    
                    results.append({
                        'trade_date': trade_date,
                        'spot_price': spot_price,
                        'expiry_date': expiry,
                        'synthetic_future_atm': atm_strike,
                        'spot_based_atm': spot_based_atm,
                        'atm_difference': atm_strike - spot_based_atm,
                        'synthetic_future_price': synthetic_future
                    })
                else:
                    logger.warning(f"No option data for {trade_date}, using spot-based ATM")
                    spot_based_atm = round(spot_price / self.strike_increment) * self.strike_increment
                    results.append({
                        'trade_date': trade_date,
                        'spot_price': spot_price,
                        'expiry_date': expiry,
                        'synthetic_future_atm': spot_based_atm,
                        'spot_based_atm': spot_based_atm,
                        'atm_difference': 0,
                        'synthetic_future_price': spot_price
                    })
        
        cursor.close()
        conn.close()
        
        return pd.DataFrame(results)
    
    def _get_expiry_date(self, trade_date: str, expiry_type: str, cursor) -> Optional[str]:
        """Get expiry date based on type (current_week, next_week, current_month, etc.)"""
        if expiry_type == 'current_week':
            query = """
            SELECT MIN(expiry_date) 
            FROM (
                SELECT DISTINCT expiry_date 
                FROM nifty_option_chain 
                WHERE expiry_date >= %s 
                ORDER BY expiry_date 
                LIMIT 1
            ) t
            """
        elif expiry_type == 'next_week':
            query = """
            SELECT MIN(expiry_date)
            FROM (
                SELECT DISTINCT expiry_date 
                FROM nifty_option_chain 
                WHERE expiry_date >= %s 
                ORDER BY expiry_date 
                LIMIT 2
            ) t
            WHERE expiry_date > (
                SELECT MIN(expiry_date) 
                FROM nifty_option_chain 
                WHERE expiry_date >= %s
            )
            """
            cursor.execute(query, (trade_date, trade_date))
        else:
            # Default to current week
            query = """
            SELECT MIN(expiry_date) 
            FROM nifty_option_chain 
            WHERE expiry_date >= %s
            """
        
        if expiry_type != 'next_week':
            cursor.execute(query, (trade_date,))
        
        result = cursor.fetchone()
        return result[0] if result and result[0] else None

class ArchiveBacktesterWithSyntheticATM:
    """Enhanced archive backtester using synthetic future ATM"""
    
    def __init__(self, mysql_config: Dict):
        """Initialize with MySQL config"""
        self.mysql_config = mysql_config
        self.atm_calculator = SyntheticFutureATMCalculator(mysql_config)
        
    def select_strike_with_synthetic_atm(self, symbol: str, trade_date: str, trade_time: str,
                                       option_type: str, strike_selection: str, 
                                       strike_value: int = 0) -> Tuple[float, float]:
        """
        Select strike using synthetic future ATM methodology
        
        Args:
            symbol: Trading symbol
            trade_date: Date of trade
            trade_time: Time of trade
            option_type: CE or PE
            strike_selection: ATM, ITM1-10, OTM1-10, etc.
            strike_value: Offset value for ITM/OTM
            
        Returns:
            Tuple of (selected_strike, entry_price)
        """
        # Get spot price
        conn = mysql.connector.connect(**self.mysql_config)
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT close FROM nifty_spot 
            WHERE symbol = %s AND trade_date = %s AND time <= %s
            ORDER BY time DESC LIMIT 1
        """, (symbol, trade_date, trade_time))
        
        spot_result = cursor.fetchone()
        if not spot_result:
            cursor.close()
            conn.close()
            raise ValueError(f"No spot price found for {symbol} on {trade_date}")
        
        spot_price = float(spot_result[0])
        
        # Get expiry (simplified - using current week)
        cursor.execute("""
            SELECT MIN(expiry_date) FROM nifty_option_chain
            WHERE symbol = %s AND trade_date = %s AND expiry_date >= %s
        """, (symbol, trade_date, trade_date))
        
        expiry_result = cursor.fetchone()
        if not expiry_result or not expiry_result[0]:
            cursor.close()
            conn.close()
            raise ValueError(f"No expiry found for {symbol} on {trade_date}")
        
        expiry = expiry_result[0]
        cursor.close()
        conn.close()
        
        # Get option chain data
        option_data = self.atm_calculator.get_option_chain_data(symbol, trade_date, expiry)
        
        if option_data.empty:
            raise ValueError(f"No option chain data for {symbol} on {trade_date}")
        
        # Calculate synthetic future ATM
        atm_strike, _ = self.atm_calculator.calculate_synthetic_future_atm(spot_price, option_data)
        
        # Handle different strike selections
        if strike_selection == 'ATM':
            selected_strike = atm_strike
        elif strike_selection.startswith('ITM'):
            # ITM: Lower strikes for CE, Higher strikes for PE
            offset = int(strike_selection[3:]) if len(strike_selection) > 3 else 1
            if option_type == 'CE':
                selected_strike = atm_strike - (offset * 50)
            else:  # PE
                selected_strike = atm_strike + (offset * 50)
        elif strike_selection.startswith('OTM'):
            # OTM: Higher strikes for CE, Lower strikes for PE
            offset = int(strike_selection[3:]) if len(strike_selection) > 3 else 1
            if option_type == 'CE':
                selected_strike = atm_strike + (offset * 50)
            else:  # PE
                selected_strike = atm_strike - (offset * 50)
        else:
            # Default to ATM
            selected_strike = atm_strike
        
        # Get entry price for selected strike
        conn = mysql.connector.connect(**self.mysql_config)
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT close FROM nifty_option_chain
            WHERE symbol = %s AND trade_date = %s AND time = %s
                AND expiry_date = %s AND strike = %s AND option_type = %s
            LIMIT 1
        """, (symbol, trade_date, trade_time, expiry, selected_strike, option_type))
        
        price_result = cursor.fetchone()
        cursor.close()
        conn.close()
        
        if not price_result:
            logger.warning(f"No price found for {selected_strike} {option_type}, using 0")
            return selected_strike, 0.0
        
        return selected_strike, float(price_result[0])
    
    def run_backtest_with_synthetic_atm(self, strategy_config: Dict) -> pd.DataFrame:
        """Run backtest using synthetic future ATM for strike selection"""
        # This would be the main backtest logic using synthetic ATM
        # For now, returning a placeholder
        logger.info("Running backtest with synthetic future ATM...")
        
        # Implementation would involve:
        # 1. Parse strategy config
        # 2. For each trade signal, use select_strike_with_synthetic_atm
        # 3. Execute trades and track P&L
        # 4. Return results in archive format
        
        return pd.DataFrame()


def test_synthetic_atm_implementation():
    """Test the synthetic future ATM implementation"""
    mysql_config = {
        'host': 'localhost',
        'user': 'root',
        'password': 'your_password',
        'database': 'market_data'
    }
    
    calculator = SyntheticFutureATMCalculator(mysql_config)
    
    # Test for specific date
    test_date = '2024-04-01'
    
    logger.info(f"Testing synthetic future ATM for {test_date}")
    
    # Get ATM strikes for a date range
    atm_data = calculator.get_atm_strikes_for_strategy(
        'NIFTY', 
        test_date, 
        test_date,
        'current_week'
    )
    
    if not atm_data.empty:
        print("\nATM Comparison Results:")
        print(atm_data.to_string(index=False))
        
        avg_diff = atm_data['atm_difference'].mean()
        print(f"\nAverage ATM Difference: {avg_diff:.2f} points")
        print(f"This represents {avg_diff/50:.1f} strikes difference")
    else:
        print("No data found for testing")


if __name__ == "__main__":
    # Example usage
    print("Archive System with Synthetic Future ATM Implementation")
    print("=" * 60)
    
    # You can run the test with actual MySQL connection
    # test_synthetic_atm_implementation()
    
    print("\nImplementation ready for use in archive backtesting system")