#!/usr/bin/env python3
import os
import sys
import glob
import csv
import subprocess
import time
import signal
import argparse
import multiprocessing
from datetime import datetime
import math

# Configuration
CSV_DIR = "/srv/samba/shared/market_data/nifty/oc_with_futures/"
DEFAULT_BATCH_SIZE = 20000
DEFAULT_CPU_WORKERS = 24  # One third of available cores (72)
DEFAULT_GPU_WORKERS = 1   # Since HeavyDB is already using GPU
MAX_RETRIES = 3
TIMEOUT = 600  # Longer timeout for bigger batches

def parse_args():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser(description='Optimized parallel data loader for Nifty Option Chain')
    parser.add_argument('--batch-size', type=int, default=DEFAULT_BATCH_SIZE, help='Batch size for inserts')
    parser.add_argument('--cpu-workers', type=int, default=DEFAULT_CPU_WORKERS, help='Number of CPU workers')
    parser.add_argument('--gpu-workers', type=int, default=DEFAULT_GPU_WORKERS, help='Number of GPU workers for COPY commands')
    parser.add_argument('--test', action='store_true', help='Run in test mode with limited rows')
    parser.add_argument('--timeout', type=int, default=TIMEOUT, help='SQL command timeout in seconds')
    parser.add_argument('--files', type=str, help='Comma-separated list of specific files to process')
    return parser.parse_args()

def run_sql_command(sql, timeout=TIMEOUT):
    """Run SQL command via heavysql with timeout"""
    cmd = ["/opt/heavyai/bin/heavysql", "-s", "127.0.0.1", "--port", "6274", 
           "-u", "admin", "-p", "HyperInteractive", "-d", "heavyai"]
    
    try:
        process = subprocess.Popen(cmd, stdin=subprocess.PIPE, 
                                stdout=subprocess.PIPE, stderr=subprocess.PIPE,
                                text=True)
        
        stdout, stderr = process.communicate(input=sql, timeout=timeout)
        
        if stderr and "Error" in stderr:
            print(f"SQL Error: {stderr}")
            return False
        
        return True
    except subprocess.TimeoutExpired:
        print(f"SQL command timed out after {timeout} seconds")
        # Kill the process
        process.kill()
        return False
    except Exception as e:
        print(f"Error executing SQL: {e}")
        return False

def process_file_via_copy(csv_file):
    """Process a file using COPY FROM command (GPU accelerated)"""
    try:
        absolute_path = os.path.abspath(csv_file)
        print(f"[GPU] Processing file {csv_file} using COPY FROM")
        
        copy_sql = f"""
        COPY nifty_option_chain FROM '{absolute_path}'
        WITH (header='true', delimiter=',');
        """
        
        if run_sql_command(copy_sql, timeout=1800):  # 30 minute timeout for COPY
            print(f"[GPU] Successfully loaded {csv_file} via COPY")
            return True
        else:
            print(f"[GPU] Failed to load {csv_file} via COPY, will try batch insert")
            return False
    except Exception as e:
        print(f"[GPU] Error in COPY process for {csv_file}: {e}")
        return False

def process_file_batch(file_path, worker_id, batch_size=DEFAULT_BATCH_SIZE, test_mode=False):
    """Process a CSV file in batches using batch INSERT"""
    try:
        print(f"[CPU-{worker_id}] Processing file: {file_path}")
        
        # Count total lines to process
        with open(file_path, 'r') as f:
            total_lines = sum(1 for _ in f)
        
        # Subtract 1 for header
        total_lines -= 1
        
        if test_mode:
            # Limit to 1000 rows in test mode
            total_lines = min(total_lines, 1000)
        
        print(f"[CPU-{worker_id}] Total lines to process: {total_lines}")
        
        # Process in batches
        with open(file_path, 'r') as f:
            # Read header
            header = next(f).strip().split(',')
            
            # Generate placeholders for the SQL INSERT
            placeholders = ", ".join(["%s"] * len(header))
            
            batch_count = 0
            row_count = 0
            batch = []
            
            for i, line in enumerate(f):
                if test_mode and i >= 1000:
                    break
                    
                values = line.strip().split(',')
                
                # Format the values for SQL
                formatted_values = []
                for val in values:
                    # Handle different data types appropriately
                    if val == '':
                        formatted_values.append('NULL')
                    elif val.replace('.', '', 1).isdigit():
                        formatted_values.append(val)
                    else:
                        # Escape single quotes and wrap in quotes
                        formatted_values.append("'" + val.replace("'", "''") + "'")
                
                row_sql = f"({', '.join(formatted_values)})"
                batch.append(row_sql)
                
                if len(batch) >= batch_size:
                    # Create and execute batch INSERT
                    insert_sql = f"INSERT INTO nifty_option_chain VALUES {', '.join(batch)};"
                    success = False
                    retries = 0
                    
                    while not success and retries < MAX_RETRIES:
                        if run_sql_command(insert_sql):
                            success = True
                        else:
                            retries += 1
                            print(f"[CPU-{worker_id}] Retry {retries}/{MAX_RETRIES} for batch {batch_count}")
                            time.sleep(2 ** retries)  # Exponential backoff
                    
                    if not success:
                        print(f"[CPU-{worker_id}] Failed to insert batch {batch_count} after {MAX_RETRIES} retries")
                        return False
                    
                    batch_count += 1
                    row_count += len(batch)
                    print(f"[CPU-{worker_id}] Inserted batch {batch_count}, progress: {row_count}/{total_lines} rows ({row_count/total_lines*100:.2f}%)")
                    batch = []
            
            # Insert any remaining rows
            if batch:
                insert_sql = f"INSERT INTO nifty_option_chain VALUES {', '.join(batch)};"
                if run_sql_command(insert_sql):
                    row_count += len(batch)
                    print(f"[CPU-{worker_id}] Inserted final batch, total: {row_count}/{total_lines} rows ({row_count/total_lines*100:.2f}%)")
                else:
                    print(f"[CPU-{worker_id}] Failed to insert final batch")
                    return False
            
            print(f"[CPU-{worker_id}] Completed processing file: {file_path}")
            return True
    except Exception as e:
        print(f"[CPU-{worker_id}] Error processing file {file_path}: {e}")
        return False

def gpu_worker(file_queue, results):
    """Worker process for GPU-based loading via COPY"""
    while not file_queue.empty():
        try:
            file_path = file_queue.get(timeout=1)
            success = process_file_via_copy(file_path)
            results.put((file_path, success, 'gpu'))
        except Exception as e:
            print(f"GPU worker error: {e}")

def cpu_worker(worker_id, file_queue, results, batch_size, test_mode):
    """Worker process for CPU-based loading via batch INSERT"""
    while not file_queue.empty():
        try:
            file_path = file_queue.get(timeout=1)
            success = process_file_batch(file_path, worker_id, batch_size, test_mode)
            results.put((file_path, success, 'cpu'))
        except Exception as e:
            print(f"CPU worker {worker_id} error: {e}")

def main():
    args = parse_args()
    
    print("=== Optimized Parallel Nifty Option Chain Loader ===")
    print(f"Starting at: {datetime.now()}")
    print(f"CPU Workers: {args.cpu_workers}")
    print(f"GPU Workers: {args.gpu_workers}")
    print(f"Batch size: {args.batch_size}")
    print(f"Test mode: {args.test}")
    
    # Create table if it doesn't exist using the simpler SQL file
    print("Creating table nifty_option_chain...")
    try:
        with open('create_noc_table_simple.sql', 'r') as f:
            create_table_sql = f.read()
        
        # Split and execute each statement separately
        statements = create_table_sql.split(';')
        for stmt in statements:
            stmt = stmt.strip()
            if stmt:  # Skip empty statements
                if not run_sql_command(stmt + ';'):
                    print("Failed to execute SQL statement!")
                    return
        
        print("Table created successfully")
    except Exception as e:
        print(f"Error handling SQL file: {e}")
        return
    
    # Get list of CSV files to process
    if args.files:
        csv_files = [os.path.join(CSV_DIR, f.strip()) for f in args.files.split(',')]
    else:
        csv_files = glob.glob(os.path.join(CSV_DIR, "*.csv"))
    
    # Make sure all files exist
    csv_files = [f for f in csv_files if os.path.isfile(f)]
    
    if not csv_files:
        print("No CSV files found!")
        return
    
    print(f"Found {len(csv_files)} CSV files to process")
    
    # Estimate file sizes and sort accordingly
    file_sizes = [(f, os.path.getsize(f)) for f in csv_files]
    file_sizes.sort(key=lambda x: x[1], reverse=True)  # Sort by size, largest first
    
    # Split files between GPU and CPU workers
    gpu_files = [f for f, _ in file_sizes[:args.gpu_workers * 2]]  # Allocate twice as many files as GPU workers
    cpu_files = [f for f, _ in file_sizes[args.gpu_workers * 2:]]
    
    print(f"Files for GPU processing: {len(gpu_files)}")
    print(f"Files for CPU processing: {len(cpu_files)}")
    
    # Create queues for files and results
    gpu_file_queue = multiprocessing.Queue()
    cpu_file_queue = multiprocessing.Queue()
    results_queue = multiprocessing.Queue()
    
    # Add files to queues
    for f in gpu_files:
        gpu_file_queue.put(f)
    
    for f in cpu_files:
        cpu_file_queue.put(f)
    
    # Start GPU workers
    gpu_processes = []
    for i in range(args.gpu_workers):
        p = multiprocessing.Process(target=gpu_worker, args=(gpu_file_queue, results_queue))
        p.start()
        gpu_processes.append(p)
    
    # Start CPU workers
    cpu_processes = []
    for i in range(args.cpu_workers):
        p = multiprocessing.Process(target=cpu_worker, args=(i+1, cpu_file_queue, results_queue, args.batch_size, args.test))
        p.start()
        cpu_processes.append(p)
    
    # Wait for all processes to complete
    for p in gpu_processes:
        p.join()
    
    for p in cpu_processes:
        p.join()
    
    # Process results
    success_count = 0
    fail_count = 0
    gpu_success = 0
    cpu_success = 0
    
    while not results_queue.empty():
        file_path, success, method = results_queue.get()
        if success:
            success_count += 1
            if method == 'gpu':
                gpu_success += 1
            else:
                cpu_success += 1
        else:
            fail_count += 1
    
    print("\n=== Loading Results ===")
    print(f"Total files: {len(csv_files)}")
    print(f"Successfully loaded: {success_count}")
    print(f"  - GPU method: {gpu_success}")
    print(f"  - CPU method: {cpu_success}")
    print(f"Failed: {fail_count}")
    
    # Verify row count
    count_sql = "SELECT COUNT(*) FROM nifty_option_chain;"
    process = subprocess.Popen(["/opt/heavyai/bin/heavysql", "-s", "127.0.0.1", "--port", "6274", 
                               "-u", "admin", "-p", "HyperInteractive", "-d", "heavyai"],
                              stdin=subprocess.PIPE, stdout=subprocess.PIPE, stderr=subprocess.PIPE,
                              text=True)
    
    stdout, stderr = process.communicate(input=count_sql)
    print("\nFinal row count:")
    print(stdout)
    
    print(f"Finished at: {datetime.now()}")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\nProcess interrupted by user")
        sys.exit(1) 