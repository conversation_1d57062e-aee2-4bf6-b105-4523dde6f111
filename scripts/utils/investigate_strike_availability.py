#!/usr/bin/env python3
"""
Investigate Strike Availability in Both Databases
"""

import subprocess
import heavydb
import pandas as pd

def check_mysql_strikes(date_str):
    """Check available strikes in MySQL."""
    query = f"USE historicaldb; SELECT DISTINCT strike FROM nifty_call WHERE date='{date_str}' AND time=33300 ORDER BY strike;"
    cmd = ["sudo", "mysql", "-e", query]
    result = subprocess.run(cmd, capture_output=True, text=True)
    
    if result.returncode == 0:
        lines = result.stdout.strip().split('\n')
        if len(lines) > 1:
            strikes = [int(line) for line in lines[1:]]
            print(f"MySQL {date_str}: {len(strikes)} strikes")
            print(f"  Range: {min(strikes)} - {max(strikes)}")
            print(f"  Around 21700: {[s for s in strikes if 21000 <= s <= 22500][:10]}...")
            return strikes
    return []

def check_heavydb_strikes(date_str):
    """Check available strikes in HeavyDB."""
    try:
        year = 2000 + int(date_str[:2])
        month = int(date_str[2:4])
        day = int(date_str[4:6])
        db_date = f"{year}-{month:02d}-{day:02d}"
        
        conn = heavydb.connect(
            host='localhost',
            port=6274,
            user='admin',
            password='HyperInteractive',
            dbname='heavyai'
        )
        
        query = f"""
        SELECT DISTINCT strike 
        FROM nifty_option_chain
        WHERE trade_date = '{db_date}'
            AND EXTRACT(HOUR FROM trade_time) = 9
            AND EXTRACT(MINUTE FROM trade_time) = 20
        ORDER BY strike
        """
        
        cursor = conn.execute(query)
        strikes = [row[0] for row in cursor.fetchall()]
        
        print(f"HeavyDB {db_date}: {len(strikes)} strikes")
        if strikes:
            print(f"  Range: {min(strikes)} - {max(strikes)}")
            print(f"  Around 21700: {[s for s in strikes if 21000 <= s <= 22500][:10]}...")
        
        conn.close()
        return strikes
        
    except Exception as e:
        print(f"HeavyDB error: {str(e)}")
        return []

def main():
    print("STRIKE AVAILABILITY INVESTIGATION")
    print("="*50)
    
    dates = ['240101', '240102']
    
    for date_str in dates:
        print(f"\nDate: {date_str}")
        print("-"*30)
        
        mysql_strikes = check_mysql_strikes(date_str)
        heavydb_strikes = check_heavydb_strikes(date_str)
        
        if mysql_strikes and heavydb_strikes:
            common = set(mysql_strikes) & set(heavydb_strikes)
            print(f"\nCommon strikes: {len(common)}")
            if common:
                print(f"  Examples: {sorted(list(common))[:10]}...")

if __name__ == "__main__":
    main()