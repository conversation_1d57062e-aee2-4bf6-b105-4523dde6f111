#!/usr/bin/env python3
import sys
import os
import datetime
import argparse
import logging
import subprocess
import random

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

try:
    import heavyai
except ImportError:
    logger.error("Error: heavyai module not found. Please install it with pip install heavyai")
    sys.exit(1)

def connect_to_heavydb():
    """Connect to HeavyDB"""
    try:
        logger.info("Connecting to HeavyDB...")
        conn = heavyai.connect(
            host="127.0.0.1",
            port=6274,
            user="admin",
            password="HyperInteractive",
            dbname="heavyai",
            protocol='binary'
        )
        logger.info("Connected to HeavyDB!")
        return conn
    except Exception as e:
        logger.error(f"Error connecting to HeavyDB: {e}")
        return None

def get_trade_dates(conn, start_date, end_date):
    """Get list of trade dates in the given range"""
    try:
        logger.info(f"Getting trade dates from {start_date} to {end_date}...")
        cursor = conn.cursor()
        query = f"SELECT DISTINCT trade_date FROM nifty_greeks WHERE trade_date BETWEEN '{start_date}' AND '{end_date}' ORDER BY trade_date"
        cursor.execute(query)
        dates = [row[0] for row in cursor.fetchall()]
        cursor.close()
        logger.info(f"Found {len(dates)} trade dates")
        return dates
    except Exception as e:
        logger.error(f"Error getting trade dates: {e}")
        return []

def process_date(conn, trade_date, batch_size=100):
    """Process a single trade date"""
    try:
        logger.info(f"Processing date: {trade_date} (batch size: {batch_size})")
        
        # Get count of rows for this date
        cursor = conn.cursor()
        cursor.execute(f"SELECT COUNT(*) FROM nifty_greeks WHERE trade_date = '{trade_date}'")
        total_rows = cursor.fetchone()[0]
        cursor.close()
        logger.info(f"Found {total_rows} rows for date {trade_date}")
        
        # Process in batches
        offset = 0
        rows_processed = 0
        
        while offset < total_rows:
            # Get a batch of data
            cursor = conn.cursor()
            query = f"""
            SELECT 
                trade_date, trade_time, expiry_date, underlying_price, strike,
                ce_symbol, ce_open, ce_high, ce_low, ce_close, ce_volume, ce_oi, ce_coi, 
                ce_iv, ce_delta, ce_gamma, ce_theta, ce_vega, ce_rho,
                pe_symbol, pe_open, pe_high, pe_low, pe_close, pe_volume, pe_oi, pe_coi, 
                pe_iv, pe_delta, pe_gamma, pe_theta, pe_vega, pe_rho
            FROM nifty_greeks 
            WHERE trade_date = '{trade_date}'
            LIMIT {batch_size} OFFSET {offset}
            """
            cursor.execute(query)
            batch_data = cursor.fetchall()
            cursor.close()
            
            if not batch_data:
                break
                
            # Generate INSERT statements for this batch
            insert_statements = []
            for row in batch_data:
                # Extract all fields
                trade_date, trade_time, expiry_date, underlying_price, strike = row[0:5]
                ce_symbol, ce_open, ce_high, ce_low, ce_close = row[5:10]
                ce_volume, ce_oi, ce_coi, ce_iv, ce_delta = row[10:15]
                ce_gamma, ce_theta, ce_vega, ce_rho = row[15:19]
                pe_symbol, pe_open, pe_high, pe_low, pe_close = row[19:24]
                pe_volume, pe_oi, pe_coi, pe_iv, pe_delta = row[24:29]
                pe_gamma, pe_theta, pe_vega, pe_rho = row[29:33]
                
                # Calculate derived fields
                atm_strike = round(underlying_price / 50) * 50 if underlying_price < 10000 else round(underlying_price / 100) * 100
                
                # Determine zone_id and zone_name
                hour = int(str(trade_time).split(':')[0])
                minute = int(str(trade_time).split(':')[1])
                time_value = hour * 60 + minute
                
                if time_value >= 9*60+15 and time_value <= 10*60+30:
                    zone_id, zone_name = 1, 'OPEN'
                elif time_value > 10*60+30 and time_value <= 12*60:
                    zone_id, zone_name = 2, 'MID_MORN'
                elif time_value > 12*60 and time_value <= 13*60+30:
                    zone_id, zone_name = 3, 'LUNCH'
                elif time_value > 13*60+30 and time_value <= 15*60:
                    zone_id, zone_name = 4, 'AFTERNOON'
                else:
                    zone_id, zone_name = 5, 'CLOSE'
                
                # Calculate strike types
                if strike == atm_strike:
                    call_strike_type = put_strike_type = 'ATM'
                elif strike < atm_strike:
                    distance = int(abs(strike - atm_strike) / (50 if underlying_price < 10000 else 100))
                    call_strike_type = f'ITM{distance}'
                    put_strike_type = f'OTM{distance}'
                else:
                    distance = int(abs(strike - atm_strike) / (50 if underlying_price < 10000 else 100))
                    call_strike_type = f'OTM{distance}'
                    put_strike_type = f'ITM{distance}'
                
                # Calculate DTE (simple approximation)
                dte = (expiry_date - trade_date).days
                
                # Generate expiry bucket (simple assignment)
                if dte <= 7:
                    expiry_bucket = 'CW'
                elif dte <= 14:
                    expiry_bucket = 'NW'
                elif expiry_date.weekday() == 3 and dte <= 30:  # Thursday within a month
                    expiry_bucket = 'CM'
                else:
                    expiry_bucket = 'NM'
                
                # Generate future data (synthetic)
                future_open = underlying_price + 50
                future_high = underlying_price + 100
                future_low = underlying_price - 10
                future_close = underlying_price + 25
                future_volume = 5000 + (int(str(trade_date).replace('-', '')) % 100) * 20
                future_oi = 10000 + (int(str(trade_date).replace('-', '')) % 100) * 100
                future_coi = -200 + (int(str(trade_date).replace('-', '')) % 100)
                
                # Format the INSERT statement
                values = (
                    f"'{trade_date}', '{trade_time}', '{expiry_date}', 'NIFTY', {underlying_price}, {atm_strike}, {strike}, "
                    f"{dte}, '{expiry_bucket}', {zone_id}, '{zone_name}', '{call_strike_type}', '{put_strike_type}', "
                    f"'{ce_symbol}', {ce_open}, {ce_high}, {ce_low}, {ce_close}, {ce_volume}, {ce_oi}, {ce_coi}, "
                    f"{ce_iv}, {ce_delta}, {ce_gamma}, {ce_theta}, {ce_vega}, {ce_rho}, "
                    f"'{pe_symbol}', {pe_open}, {pe_high}, {pe_low}, {pe_close}, {pe_volume}, {pe_oi}, {pe_coi}, "
                    f"{pe_iv}, {pe_delta}, {pe_gamma}, {pe_theta}, {pe_vega}, {pe_rho}, "
                    f"{future_open}, {future_high}, {future_low}, {future_close}, {future_volume}, {future_oi}, {future_coi}"
                )
                
                insert_statements.append(f"INSERT INTO nifty_option_chain VALUES ({values});")
            
            # Execute the INSERT statements
            cursor = conn.cursor()
            for stmt in insert_statements:
                cursor.execute(stmt)
            conn.commit()
            cursor.close()
            
            # Update counters
            rows_processed += len(batch_data)
            offset += batch_size
            
            logger.info(f"Processed {rows_processed}/{total_rows} rows for date {trade_date}")
        
        return rows_processed
        
    except Exception as e:
        logger.error(f"Error processing date {trade_date}: {e}")
        return 0

def run_sql(sql):
    """Run SQL command via heavysql"""
    cmd = ["/opt/heavyai/bin/heavysql", "-s", "127.0.0.1", "--port", "6274", 
           "-u", "admin", "-p", "HyperInteractive", "-d", "heavyai"]
    
    process = subprocess.Popen(cmd, stdin=subprocess.PIPE, 
                             stdout=subprocess.PIPE, stderr=subprocess.PIPE,
                             text=True)
    
    stdout, stderr = process.communicate(input=sql)
    
    if stderr and "Error" in stderr:
        print(f"Error executing SQL: {stderr}")
        return False
    
    return True

def generate_test_data(num_records=100):
    """Generate test data for options chain"""
    # Set seed for reproducibility
    random.seed(42)
    
    # Bases for test data
    base_date = datetime.date(2023, 1, 1)
    end_date = datetime.date(2025, 4, 30)
    time_slots = ["09:15:00", "10:30:00", "12:00:00", "13:30:00", "15:00:00"]
    zone_names = ["OPEN", "MID_MORN", "LUNCH", "AFTERNOON", "CLOSE"]
    strike_types = ["ATM", "ITM1", "ITM2", "ITM3", "OTM1", "OTM2", "OTM3"]
    buckets = ["CW", "NW", "CM", "NM"]
    
    # SQL for insertion
    sql_inserts = []
    
    # Generate data
    current_date = base_date
    while current_date <= end_date:
        # Skip weekends
        if current_date.weekday() >= 5:  # 5=Saturday, 6=Sunday
            current_date += datetime.timedelta(days=1)
            continue
            
        # For each trade date, generate entries for different expirations
        for month_offset in range(0, 6):  # Generate expirations up to 6 months out
            for week_offset in range(0, 4):  # Generate weekly expirations
                expiry_date = current_date + datetime.timedelta(days=7*week_offset + 30*month_offset)
                
                if expiry_date.weekday() != 3:  # Not a Thursday
                    # Adjust to the next Thursday
                    days_until_thursday = (3 - expiry_date.weekday()) % 7
                    expiry_date += datetime.timedelta(days=days_until_thursday)
                
                # Skip if in the past
                if expiry_date < current_date:
                    continue
                
                # Only take a sample (1 in 8) to avoid too much data
                if random.randint(1, 8) != 1:
                    continue
                
                # Calculate DTE (simple for test data)
                dte = (expiry_date - current_date).days
                
                # Select appropriate bucket
                if dte <= 7:
                    expiry_bucket = "CW"
                elif dte <= 14:
                    expiry_bucket = "NW"
                elif month_offset == 0:
                    expiry_bucket = "CM"
                elif month_offset == 1:
                    expiry_bucket = "NM"
                else:
                    expiry_bucket = "OTHER"
                
                # Generate data for different time slots within the day
                for time_idx, time_slot in enumerate(time_slots):
                    # Base underlying price around 18000 with some variation
                    base_price = 18000 + random.randint(-500, 500)
                    
                    # Round to nearest 50 or 100 based on level
                    if base_price < 10000:
                        atm_strike = round(base_price / 50) * 50
                    else:
                        atm_strike = round(base_price / 100) * 100
                        
                    # Generate data for different strike prices
                    strike_offsets = [-100, -50, 0, 50, 100]
                    strike_prices = [atm_strike + offset for offset in strike_offsets]
                    
                    # Only take one random strike per time slot to limit data
                    strike = random.choice(strike_prices)
                    
                    # Generate call/put strike types based on relation to ATM
                    if strike == atm_strike:
                        call_strike_type = put_strike_type = "ATM"
                    else:
                        # For calls: lower = ITM, higher = OTM
                        call_offset = abs((strike - atm_strike) // 50) if base_price < 10000 else abs((strike - atm_strike) // 100)
                        call_strike_type = f"{'ITM' if strike < atm_strike else 'OTM'}{call_offset}"
                        
                        # For puts: higher = ITM, lower = OTM
                        put_offset = abs((strike - atm_strike) // 50) if base_price < 10000 else abs((strike - atm_strike) // 100)
                        put_strike_type = f"{'ITM' if strike > atm_strike else 'OTM'}{put_offset}"
                    
                    # Generate option symbol
                    expiry_month_code = ["JAN", "FEB", "MAR", "APR", "MAY", "JUN", "JUL", "AUG", "SEP", "OCT", "NOV", "DEC"][expiry_date.month - 1]
                    ce_symbol = f"NIFTY{expiry_date.strftime('%y')}{expiry_month_code}{strike}CE"
                    pe_symbol = f"NIFTY{expiry_date.strftime('%y')}{expiry_month_code}{strike}PE"
                    
                    # Generate prices
                    ce_base = 50 + random.randint(0, 200) if strike <= base_price else max(10, 250 - (strike - base_price) * 0.5 + random.randint(-20, 20))
                    pe_base = 50 + random.randint(0, 200) if strike >= base_price else max(10, 250 - (base_price - strike) * 0.5 + random.randint(-20, 20))
                    
                    # OHLC for call
                    ce_open = ce_base * (1 + random.uniform(-0.05, 0.05))
                    ce_high = ce_open * (1 + random.uniform(0, 0.1))
                    ce_low = ce_open * (1 - random.uniform(0, 0.1))
                    ce_close = ce_low + random.uniform(0, ce_high - ce_low)
                    
                    # OHLC for put
                    pe_open = pe_base * (1 + random.uniform(-0.05, 0.05))
                    pe_high = pe_open * (1 + random.uniform(0, 0.1))
                    pe_low = pe_open * (1 - random.uniform(0, 0.1))
                    pe_close = pe_low + random.uniform(0, pe_high - pe_low)
                    
                    # Volume and OI
                    ce_volume = random.randint(500, 10000)
                    pe_volume = random.randint(500, 10000)
                    ce_oi = random.randint(5000, 20000)
                    pe_oi = random.randint(5000, 20000)
                    ce_coi = random.randint(-2000, 2000)
                    pe_coi = random.randint(-2000, 2000)
                    
                    # Greeks
                    ce_iv = random.uniform(0.1, 0.4)
                    pe_iv = random.uniform(0.1, 0.4)
                    
                    ce_delta = 0.5 + (atm_strike - strike) * 0.01 + random.uniform(-0.1, 0.1)
                    pe_delta = -0.5 + (atm_strike - strike) * 0.01 + random.uniform(-0.1, 0.1)
                    
                    ce_gamma = random.uniform(0.005, 0.02)
                    pe_gamma = random.uniform(0.005, 0.02)
                    
                    ce_theta = -random.uniform(5, 15)
                    pe_theta = -random.uniform(5, 15)
                    
                    ce_vega = random.uniform(50, 80)
                    pe_vega = random.uniform(50, 80)
                    
                    ce_rho = random.uniform(8, 15)
                    pe_rho = -random.uniform(8, 15)
                    
                    # Futures data
                    future_base = base_price * (1 + random.uniform(-0.002, 0.004))
                    future_open = future_base * (1 + random.uniform(-0.005, 0.005))
                    future_high = future_open * (1 + random.uniform(0, 0.01))
                    future_low = future_open * (1 - random.uniform(0, 0.01))
                    future_close = future_low + random.uniform(0, future_high - future_low)
                    future_volume = random.randint(50000, 200000)
                    future_oi = random.randint(20000, 100000)
                    future_coi = random.randint(-5000, 5000)
                    
                    # Create INSERT statement
                    sql_inserts.append(f"""
                    INSERT INTO nifty_option_chain VALUES (
                        '{current_date}', 
                        '{time_slot}', 
                        '{expiry_date}', 
                        'NIFTY', 
                        {base_price}, 
                        {atm_strike}, 
                        {strike}, 
                        {dte}, 
                        '{expiry_bucket}', 
                        {time_idx + 1}, 
                        '{zone_names[time_idx]}', 
                        '{call_strike_type}', 
                        '{put_strike_type}', 
                        '{ce_symbol}', 
                        {ce_open:.2f}, 
                        {ce_high:.2f}, 
                        {ce_low:.2f}, 
                        {ce_close:.2f}, 
                        {ce_volume}, 
                        {ce_oi}, 
                        {ce_coi}, 
                        {ce_iv:.2f}, 
                        {ce_delta:.2f}, 
                        {ce_gamma:.4f}, 
                        {ce_theta:.2f}, 
                        {ce_vega:.2f}, 
                        {ce_rho:.2f}, 
                        '{pe_symbol}', 
                        {pe_open:.2f}, 
                        {pe_high:.2f}, 
                        {pe_low:.2f}, 
                        {pe_close:.2f}, 
                        {pe_volume}, 
                        {pe_oi}, 
                        {pe_coi}, 
                        {pe_iv:.2f}, 
                        {pe_delta:.2f}, 
                        {pe_gamma:.4f}, 
                        {pe_theta:.2f}, 
                        {pe_vega:.2f}, 
                        {pe_rho:.2f}, 
                        {future_open:.2f}, 
                        {future_high:.2f}, 
                        {future_low:.2f}, 
                        {future_close:.2f}, 
                        {future_volume}, 
                        {future_oi}, 
                        {future_coi}
                    );""")
        
        # Move to next trading day
        current_date += datetime.timedelta(days=1)
    
    return sql_inserts

def main():
    # Generate test data
    print("Generating test data...")
    sql_inserts = generate_test_data()
    print(f"Generated {len(sql_inserts)} test records")
    
    # Insert in batches
    batch_size = 50
    total_success = 0
    
    print(f"Inserting in batches of {batch_size}...")
    for i in range(0, len(sql_inserts), batch_size):
        batch = sql_inserts[i:i+batch_size]
        sql_batch = "\n".join(batch)
        
        if run_sql(sql_batch):
            total_success += len(batch)
            print(f"Batch {i//batch_size + 1}/{(len(sql_inserts)-1)//batch_size + 1} inserted successfully ({len(batch)} records)")
        else:
            print(f"Failed to insert batch {i//batch_size + 1}")
    
    print(f"Successfully inserted {total_success} out of {len(sql_inserts)} records")
    
    # Verify count
    run_sql("SELECT COUNT(*) FROM nifty_option_chain;")

if __name__ == "__main__":
    main() 