#!/usr/bin/env python3
"""
Parallel SQL batch executor for fast loading
"""

import os
import sys
import glob
import subprocess
import time
from datetime import datetime
import logging
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - [%(threadName)s] - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Global counters
success_count = 0
failed_count = 0
lock = threading.Lock()

def execute_sql_file(sql_file, worker_id):
    """Execute a single SQL file"""
    global success_count, failed_count
    
    file_name = os.path.basename(sql_file)
    
    cmd = ["/opt/heavyai/bin/heavysql", "-s", "127.0.0.1", "--port", "6274", 
           "-u", "admin", "-p", "HyperInteractive", "-d", "heavyai"]
    
    try:
        with open(sql_file, 'r') as f:
            start_time = time.time()
            process = subprocess.Popen(cmd, stdin=f, 
                                    stdout=subprocess.PIPE, stderr=subprocess.PIPE,
                                    text=True)
            
            stdout, stderr = process.communicate(timeout=30)  # 30 second timeout per file
            elapsed = time.time() - start_time
            
            if process.returncode == 0:
                with lock:
                    success_count += 1
                logger.info(f"Worker {worker_id}: ✓ {file_name} ({elapsed:.2f}s)")
                return True, file_name, elapsed
            else:
                with lock:
                    failed_count += 1
                logger.error(f"Worker {worker_id}: ✗ {file_name} - Error: {stderr}")
                return False, file_name, elapsed
                
    except subprocess.TimeoutExpired:
        with lock:
            failed_count += 1
        logger.error(f"Worker {worker_id}: ✗ {file_name} - Timeout after 30s")
        process.kill()
        return False, file_name, 30.0
    except Exception as e:
        with lock:
            failed_count += 1
        logger.error(f"Worker {worker_id}: ✗ {file_name} - Exception: {e}")
        return False, file_name, 0.0

def get_row_count():
    """Get current row count"""
    cmd = ["/opt/heavyai/bin/heavysql", "-s", "127.0.0.1", "--port", "6274", 
           "-u", "admin", "-p", "HyperInteractive", "-d", "heavyai"]
    
    sql = "SELECT COUNT(*) FROM nifty_option_chain;"
    
    try:
        process = subprocess.Popen(cmd, stdin=subprocess.PIPE, 
                                stdout=subprocess.PIPE, stderr=subprocess.PIPE,
                                text=True)
        stdout, stderr = process.communicate(input=sql)
        
        # Parse the count from output
        lines = stdout.strip().split('\n')
        for line in lines:
            if line.strip().isdigit():
                return int(line.strip())
        return 0
    except:
        return 0

def main():
    sql_dir = "/srv/samba/shared/sql_batches"
    num_workers = 8  # Number of parallel workers
    
    logger.info("=== Parallel SQL Batch Executor ===")
    logger.info(f"Workers: {num_workers}")
    logger.info(f"Starting at: {datetime.now()}")
    
    # Get initial row count
    initial_count = get_row_count()
    logger.info(f"Initial row count: {initial_count:,}")
    
    # Get list of SQL files
    sql_files = sorted(glob.glob(os.path.join(sql_dir, "*.sql")))
    sql_files = [f for f in sql_files if 'execute' not in f]  # Skip execute script
    
    if not sql_files:
        logger.error("No SQL files found!")
        return
    
    logger.info(f"Found {len(sql_files)} SQL files to execute")
    
    # Process files in parallel
    start_time = time.time()
    total_execution_time = 0
    
    with ThreadPoolExecutor(max_workers=num_workers) as executor:
        # Submit all tasks
        future_to_file = {
            executor.submit(execute_sql_file, sql_file, i % num_workers): sql_file 
            for i, sql_file in enumerate(sql_files)
        }
        
        # Process results
        completed = 0
        for future in as_completed(future_to_file):
            completed += 1
            success, file_name, elapsed = future.result()
            total_execution_time += elapsed
            
            # Progress update every 10 files
            if completed % 10 == 0:
                progress = (completed / len(sql_files)) * 100
                current_count = get_row_count()
                rows_added = current_count - initial_count
                
                logger.info(f"Progress: {completed}/{len(sql_files)} ({progress:.1f}%) | "
                          f"Success: {success_count} | Failed: {failed_count} | "
                          f"Rows: {current_count:,} (+{rows_added:,})")
    
    # Final summary
    total_elapsed = time.time() - start_time
    final_count = get_row_count()
    rows_added = final_count - initial_count
    
    logger.info("\n=== Execution Complete ===")
    logger.info(f"Total files: {len(sql_files)}")
    logger.info(f"Successful: {success_count}")
    logger.info(f"Failed: {failed_count}")
    logger.info(f"Initial count: {initial_count:,}")
    logger.info(f"Final count: {final_count:,}")
    logger.info(f"Rows added: {rows_added:,}")
    logger.info(f"Total time: {total_elapsed:.1f} seconds")
    logger.info(f"Average time per file: {total_elapsed/len(sql_files):.2f} seconds")
    logger.info(f"Effective rate: {rows_added/total_elapsed:.0f} rows/second")
    logger.info(f"Finished at: {datetime.now()}")

if __name__ == "__main__":
    main()