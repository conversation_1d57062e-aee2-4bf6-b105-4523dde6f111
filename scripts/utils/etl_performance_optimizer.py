#!/usr/bin/env python3
"""
ETL Performance Optimizer
Analyzes performance metrics and implements optimizations
"""

import os
import sys
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional
import pandas as pd
import numpy as np
from heavydb import connect

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class ETLPerformanceOptimizer:
    """Optimize ETL performance based on metrics"""
    
    def __init__(self):
        self.conn = self._connect_db()
        self.optimizations_applied = []
        
    def _connect_db(self):
        """Connect to HeavyDB"""
        try:
            return connect(
                host='localhost',
                port=6274,
                user='admin',
                password='HyperInteractive',
                dbname='heavyai'
            )
        except Exception as e:
            logger.error(f"Failed to connect to HeavyDB: {e}")
            raise
            
    def analyze_bottlenecks(self) -> Dict[str, any]:
        """Analyze ETL bottlenecks"""
        bottlenecks = {
            'disk_io': self._analyze_disk_io(),
            'memory': self._analyze_memory_usage(),
            'gpu': self._analyze_gpu_usage(),
            'query_performance': self._analyze_query_performance(),
            'data_distribution': self._analyze_data_distribution()
        }
        
        return bottlenecks
        
    def _analyze_disk_io(self) -> Dict[str, any]:
        """Analyze disk I/O patterns"""
        try:
            query = """
                SELECT 
                    index_name,
                    operation,
                    AVG(disk_read_mb) as avg_read_mb,
                    AVG(disk_write_mb) as avg_write_mb,
                    AVG(rows_per_second) as avg_speed,
                    COUNT(*) as sample_count
                FROM etl_performance_metrics
                WHERE metric_time >= NOW() - INTERVAL '7' DAY
                GROUP BY index_name, operation
                ORDER BY avg_read_mb + avg_write_mb DESC
            """
            
            df = pd.read_sql(query, self.conn)
            
            # Identify high I/O operations
            high_io_ops = df[(df['avg_read_mb'] + df['avg_write_mb']) > 1000]
            
            recommendations = []
            if not high_io_ops.empty:
                recommendations.append("High disk I/O detected for:")
                for _, row in high_io_ops.iterrows():
                    recommendations.append(f"  - {row['index_name']} {row['operation']}: "
                                        f"{row['avg_read_mb']:.0f}MB read, "
                                        f"{row['avg_write_mb']:.0f}MB write")
                recommendations.append("Consider: Increasing batch sizes, using SSD storage")
                
            return {
                'high_io_operations': high_io_ops.to_dict('records'),
                'recommendations': recommendations
            }
            
        except Exception as e:
            logger.error(f"Failed to analyze disk I/O: {e}")
            return {'error': str(e)}
            
    def _analyze_memory_usage(self) -> Dict[str, any]:
        """Analyze memory usage patterns"""
        try:
            query = """
                SELECT 
                    DATE_TRUNC('hour', metric_time) as hour,
                    AVG(memory_mb) as avg_memory,
                    MAX(memory_mb) as max_memory,
                    AVG(rows_per_second) as avg_speed
                FROM etl_performance_metrics
                WHERE metric_time >= NOW() - INTERVAL '24' HOUR
                GROUP BY hour
                ORDER BY hour
            """
            
            df = pd.read_sql(query, self.conn)
            
            if df.empty:
                return {'status': 'no_data'}
                
            # Calculate memory efficiency (rows/sec per GB of memory)
            df['memory_efficiency'] = df['avg_speed'] / (df['avg_memory'] / 1024)
            
            recommendations = []
            avg_efficiency = df['memory_efficiency'].mean()
            
            if avg_efficiency < 100000:  # Less than 100K rows/sec per GB
                recommendations.append(f"Low memory efficiency: {avg_efficiency:.0f} rows/sec per GB")
                recommendations.append("Consider: Optimizing data structures, increasing batch processing")
                
            if df['max_memory'].max() > 32000:  # Over 32GB
                recommendations.append(f"High memory usage detected: {df['max_memory'].max()/1024:.1f}GB")
                recommendations.append("Consider: Implementing streaming processing, reducing concurrent operations")
                
            return {
                'avg_memory_mb': df['avg_memory'].mean(),
                'max_memory_mb': df['max_memory'].max(),
                'memory_efficiency': avg_efficiency,
                'recommendations': recommendations
            }
            
        except Exception as e:
            logger.error(f"Failed to analyze memory: {e}")
            return {'error': str(e)}
            
    def _analyze_gpu_usage(self) -> Dict[str, any]:
        """Analyze GPU utilization"""
        try:
            query = """
                SELECT 
                    index_name,
                    AVG(gpu_utilization) as avg_gpu_util,
                    AVG(gpu_memory_mb) as avg_gpu_mem,
                    AVG(rows_per_second) as avg_speed,
                    COUNT(*) as operations
                FROM etl_performance_metrics
                WHERE metric_time >= NOW() - INTERVAL '24' HOUR
                    AND gpu_utilization > 0
                GROUP BY index_name
            """
            
            df = pd.read_sql(query, self.conn)
            
            recommendations = []
            
            if not df.empty:
                # Check for underutilization
                underutilized = df[df['avg_gpu_util'] < 30]
                if not underutilized.empty:
                    recommendations.append("GPU underutilized for indices:")
                    for _, row in underutilized.iterrows():
                        recommendations.append(f"  - {row['index_name']}: {row['avg_gpu_util']:.1f}% utilization")
                    recommendations.append("Consider: Increasing batch sizes, enabling more GPU workers")
                    
                # Check for GPU memory usage
                low_gpu_mem = df[df['avg_gpu_mem'] < 1000]  # Less than 1GB
                if not low_gpu_mem.empty:
                    recommendations.append("Low GPU memory usage detected")
                    recommendations.append("Consider: Increasing fragment size, loading more data to GPU")
                    
            else:
                recommendations.append("No GPU usage detected")
                recommendations.append("Consider: Enabling GPU acceleration for better performance")
                
            return {
                'gpu_enabled': not df.empty,
                'avg_utilization': df['avg_gpu_util'].mean() if not df.empty else 0,
                'avg_gpu_memory_mb': df['avg_gpu_mem'].mean() if not df.empty else 0,
                'recommendations': recommendations
            }
            
        except Exception as e:
            logger.error(f"Failed to analyze GPU: {e}")
            return {'error': str(e)}
            
    def _analyze_query_performance(self) -> Dict[str, any]:
        """Analyze query performance patterns"""
        try:
            # Get operation timing patterns
            query = """
                SELECT 
                    operation,
                    AVG(duration_seconds) as avg_duration,
                    AVG(rows_processed) as avg_rows,
                    AVG(rows_per_second) as avg_speed,
                    STDDEV(rows_per_second) as speed_stddev,
                    COUNT(*) as operation_count
                FROM etl_performance_metrics
                WHERE metric_time >= NOW() - INTERVAL '7' DAY
                    AND status = 'success'
                GROUP BY operation
                HAVING COUNT(*) > 10
            """
            
            df = pd.read_sql(query, self.conn)
            
            recommendations = []
            
            # Check for high variance operations
            if not df.empty:
                df['cv'] = df['speed_stddev'] / df['avg_speed']  # Coefficient of variation
                high_variance = df[df['cv'] > 0.5]
                
                if not high_variance.empty:
                    recommendations.append("High performance variance detected for:")
                    for _, row in high_variance.iterrows():
                        recommendations.append(f"  - {row['operation']}: CV={row['cv']:.2f}")
                    recommendations.append("Consider: Identifying cause of variance, implementing caching")
                    
                # Check for slow operations
                slow_ops = df[df['avg_speed'] < 10000]  # Less than 10K rows/sec
                if not slow_ops.empty:
                    recommendations.append("Slow operations detected:")
                    for _, row in slow_ops.iterrows():
                        recommendations.append(f"  - {row['operation']}: {row['avg_speed']:.0f} rows/sec")
                    recommendations.append("Consider: Query optimization, indexing, parallel processing")
                    
            return {
                'operation_stats': df.to_dict('records'),
                'recommendations': recommendations
            }
            
        except Exception as e:
            logger.error(f"Failed to analyze queries: {e}")
            return {'error': str(e)}
            
    def _analyze_data_distribution(self) -> Dict[str, any]:
        """Analyze data distribution across indices"""
        try:
            # Check table statistics
            query = """
                SELECT 
                    index_name,
                    COUNT(DISTINCT trade_date) as trading_days,
                    COUNT(*) as total_rows,
                    MIN(trade_date) as min_date,
                    MAX(trade_date) as max_date
                FROM nifty_option_chain
                GROUP BY index_name
            """
            
            df = pd.read_sql(query, self.conn)
            
            recommendations = []
            
            if not df.empty:
                # Calculate data density
                df['days_range'] = (df['max_date'] - df['min_date']).dt.days + 1
                df['density'] = df['trading_days'] / df['days_range']
                df['rows_per_day'] = df['total_rows'] / df['trading_days']
                
                # Check for data imbalance
                max_rows = df['total_rows'].max()
                min_rows = df['total_rows'].min()
                
                if max_rows > 10 * min_rows:
                    recommendations.append(f"Significant data imbalance: {max_rows/min_rows:.1f}x difference")
                    recommendations.append("Consider: Partitioning by index, separate tables for large indices")
                    
                # Check for sparse data
                sparse_indices = df[df['density'] < 0.7]
                if not sparse_indices.empty:
                    recommendations.append("Sparse data detected for:")
                    for _, row in sparse_indices.iterrows():
                        recommendations.append(f"  - {row['index_name']}: {row['density']*100:.1f}% density")
                    recommendations.append("Consider: Data validation, filling missing dates")
                    
            return {
                'distribution_stats': df.to_dict('records'),
                'recommendations': recommendations
            }
            
        except Exception as e:
            logger.error(f"Failed to analyze distribution: {e}")
            return {'error': str(e)}
            
    def generate_optimization_script(self) -> str:
        """Generate optimization script based on analysis"""
        analysis = self.analyze_bottlenecks()
        
        script = []
        script.append("#!/bin/bash")
        script.append("# ETL Performance Optimization Script")
        script.append(f"# Generated: {datetime.now()}")
        script.append("")
        
        # Collect all recommendations
        all_recommendations = []
        for component, results in analysis.items():
            if isinstance(results, dict) and 'recommendations' in results:
                all_recommendations.extend(results.get('recommendations', []))
                
        # Generate optimization commands
        if any('batch size' in r.lower() for r in all_recommendations):
            script.append("# Increase batch size for better GPU utilization")
            script.append("echo 'Updating batch size configuration...'")
            script.append("sed -i 's/batch_size = 10000/batch_size = 50000/g' /srv/samba/shared/enhanced_daily_etl.py")
            script.append("")
            
        if any('gpu worker' in r.lower() for r in all_recommendations):
            script.append("# Enable more GPU workers")
            script.append("echo 'Updating GPU worker configuration...'")
            script.append("# Add GPU worker configuration to ETL scripts")
            script.append("")
            
        if any('fragment size' in r.lower() for r in all_recommendations):
            script.append("# Optimize table fragment size")
            script.append("echo 'Table fragmentation optimization needed'")
            script.append("# Consider recreating tables with optimal fragment_size")
            script.append("")
            
        if any('caching' in r.lower() for r in all_recommendations):
            script.append("# Implement query result caching")
            script.append("echo 'Setting up Redis for query caching...'")
            script.append("# sudo apt-get install redis-server")
            script.append("# pip install redis")
            script.append("")
            
        # Add monitoring enhancement
        script.append("# Enhanced monitoring")
        script.append("echo 'Setting up performance monitoring cron...'")
        script.append("(crontab -l 2>/dev/null; echo '0 */6 * * * /usr/bin/python3 /srv/samba/shared/etl_performance_monitor.py >> /srv/samba/shared/logs/performance_monitor.log 2>&1') | crontab -")
        script.append("")
        
        script.append("echo 'Optimization script completed!'")
        
        # Save script
        script_path = "/srv/samba/shared/apply_etl_optimizations.sh"
        script_content = '\n'.join(script)
        
        try:
            with open(script_path, 'w') as f:
                f.write(script_content)
            os.chmod(script_path, 0o755)
            logger.info(f"Optimization script generated: {script_path}")
        except Exception as e:
            logger.error(f"Failed to save optimization script: {e}")
            
        return script_content
        
    def auto_tune_parameters(self) -> Dict[str, any]:
        """Automatically tune ETL parameters based on performance data"""
        try:
            # Get recent performance data
            query = """
                SELECT 
                    index_name,
                    AVG(rows_per_second) as avg_speed,
                    AVG(gpu_utilization) as avg_gpu,
                    AVG(memory_mb) as avg_memory,
                    COUNT(*) as samples
                FROM etl_performance_metrics
                WHERE metric_time >= NOW() - INTERVAL '24' HOUR
                    AND status = 'success'
                GROUP BY index_name
                HAVING COUNT(*) > 5
            """
            
            df = pd.read_sql(query, self.conn)
            
            if df.empty:
                return {'status': 'insufficient_data'}
                
            tuning_params = {}
            
            for _, row in df.iterrows():
                index = row['index_name']
                params = {}
                
                # Tune batch size based on memory and speed
                if row['avg_memory'] < 8000:  # Less than 8GB
                    params['batch_size'] = 50000
                elif row['avg_memory'] < 16000:  # Less than 16GB
                    params['batch_size'] = 100000
                else:
                    params['batch_size'] = 200000
                    
                # Tune GPU workers based on utilization
                if row['avg_gpu'] < 30:
                    params['gpu_workers'] = 'auto'
                    params['max_gpu_usage'] = True
                elif row['avg_gpu'] > 90:
                    params['gpu_workers'] = '2'
                    params['max_gpu_usage'] = False
                else:
                    params['gpu_workers'] = '4'
                    params['max_gpu_usage'] = True
                    
                # Tune based on speed
                if row['avg_speed'] < 100000:
                    params['optimization_level'] = 'aggressive'
                    params['parallel_loads'] = True
                else:
                    params['optimization_level'] = 'balanced'
                    params['parallel_loads'] = False
                    
                tuning_params[index] = params
                
            # Save tuning parameters
            tuning_file = "/srv/samba/shared/etl_tuning_params.json"
            import json
            with open(tuning_file, 'w') as f:
                json.dump(tuning_params, f, indent=2)
                
            logger.info(f"Auto-tuning parameters saved to: {tuning_file}")
            
            return {
                'status': 'success',
                'tuning_params': tuning_params,
                'message': f"Tuned parameters for {len(tuning_params)} indices"
            }
            
        except Exception as e:
            logger.error(f"Failed to auto-tune: {e}")
            return {'status': 'error', 'message': str(e)}
            
    def generate_optimization_report(self) -> str:
        """Generate comprehensive optimization report"""
        report = []
        report.append("=" * 80)
        report.append(f"ETL OPTIMIZATION REPORT - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report.append("=" * 80)
        
        # Run analysis
        analysis = self.analyze_bottlenecks()
        
        # Disk I/O Analysis
        report.append("\n## DISK I/O ANALYSIS")
        report.append("-" * 40)
        disk_io = analysis.get('disk_io', {})
        if 'recommendations' in disk_io:
            for rec in disk_io['recommendations']:
                report.append(f"• {rec}")
        else:
            report.append("No disk I/O issues detected")
            
        # Memory Analysis
        report.append("\n## MEMORY USAGE ANALYSIS")
        report.append("-" * 40)
        memory = analysis.get('memory', {})
        if 'avg_memory_mb' in memory:
            report.append(f"Average Memory: {memory['avg_memory_mb']/1024:.1f} GB")
            report.append(f"Peak Memory: {memory['max_memory_mb']/1024:.1f} GB")
            report.append(f"Memory Efficiency: {memory.get('memory_efficiency', 0):.0f} rows/sec per GB")
        if 'recommendations' in memory:
            for rec in memory['recommendations']:
                report.append(f"• {rec}")
                
        # GPU Analysis
        report.append("\n## GPU UTILIZATION ANALYSIS")
        report.append("-" * 40)
        gpu = analysis.get('gpu', {})
        if gpu.get('gpu_enabled'):
            report.append(f"GPU Enabled: Yes")
            report.append(f"Average Utilization: {gpu.get('avg_utilization', 0):.1f}%")
            report.append(f"Average GPU Memory: {gpu.get('avg_gpu_memory_mb', 0)/1024:.1f} GB")
        else:
            report.append("GPU Enabled: No")
        if 'recommendations' in gpu:
            for rec in gpu['recommendations']:
                report.append(f"• {rec}")
                
        # Query Performance
        report.append("\n## QUERY PERFORMANCE ANALYSIS")
        report.append("-" * 40)
        queries = analysis.get('query_performance', {})
        if 'operation_stats' in queries and queries['operation_stats']:
            report.append("Operation Performance:")
            for op in queries['operation_stats']:
                report.append(f"  {op['operation']}: {op['avg_speed']:.0f} rows/sec "
                            f"(±{op['speed_stddev']:.0f})")
        if 'recommendations' in queries:
            for rec in queries['recommendations']:
                report.append(f"• {rec}")
                
        # Data Distribution
        report.append("\n## DATA DISTRIBUTION ANALYSIS")
        report.append("-" * 40)
        distribution = analysis.get('data_distribution', {})
        if 'distribution_stats' in distribution and distribution['distribution_stats']:
            report.append("Index Distribution:")
            for idx in distribution['distribution_stats']:
                report.append(f"  {idx['index_name']}: {idx['total_rows']:,} rows, "
                            f"{idx['trading_days']} days")
        if 'recommendations' in distribution:
            for rec in distribution['recommendations']:
                report.append(f"• {rec}")
                
        # Auto-tuning Results
        report.append("\n## AUTO-TUNING RESULTS")
        report.append("-" * 40)
        tuning = self.auto_tune_parameters()
        if tuning.get('status') == 'success':
            report.append("Auto-tuning completed successfully")
            for index, params in tuning.get('tuning_params', {}).items():
                report.append(f"\n{index}:")
                for param, value in params.items():
                    report.append(f"  - {param}: {value}")
        else:
            report.append("Auto-tuning not available (insufficient data)")
            
        # Generated Scripts
        report.append("\n## GENERATED OPTIMIZATION SCRIPTS")
        report.append("-" * 40)
        report.append("1. /srv/samba/shared/apply_etl_optimizations.sh - Apply recommended optimizations")
        report.append("2. /srv/samba/shared/etl_tuning_params.json - Auto-tuned parameters")
        
        report.append("\n" + "=" * 80)
        
        return "\n".join(report)

def main():
    """Run optimization analysis"""
    optimizer = ETLPerformanceOptimizer()
    
    # Generate optimization report
    report = optimizer.generate_optimization_report()
    print(report)
    
    # Generate optimization script
    print("\nGenerating optimization script...")
    script = optimizer.generate_optimization_script()
    print("Optimization script saved to: /srv/samba/shared/apply_etl_optimizations.sh")
    
    # Save report
    report_path = f"/srv/samba/shared/logs/etl_optimization_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
    with open(report_path, 'w') as f:
        f.write(report)
    print(f"\nOptimization report saved to: {report_path}")

if __name__ == "__main__":
    main()