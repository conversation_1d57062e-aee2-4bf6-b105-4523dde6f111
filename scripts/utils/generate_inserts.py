#!/usr/bin/env python3
import os
import sys
import datetime

def generate_insert_statement(row):
    """Generate an SQL INSERT statement from a row of data."""
    fields = [
        "trade_date", "trade_time", "expiry_date", "index_name", "underlying_price",
        "atm_strike", "strike", "dte", "expiry_bucket", "zone_id", "zone_name",
        "call_strike_type", "put_strike_type",
        "ce_symbol", "ce_open", "ce_high", "ce_low", "ce_close",
        "ce_volume", "ce_oi", "ce_coi", "ce_iv", "ce_delta", "ce_gamma", "ce_theta", "ce_vega", "ce_rho",
        "pe_symbol", "pe_open", "pe_high", "pe_low", "pe_close",
        "pe_volume", "pe_oi", "pe_coi", "pe_iv", "pe_delta", "pe_gamma", "pe_theta", "pe_vega", "pe_rho"
    ]
    
    # Extract values from the row
    trade_date = row["trade_date"]
    trade_time = row["trade_time"]
    expiry_date = row["expiry_date"]
    underlying_price = row["underlying_price"]
    strike = row["strike"]
    
    # Calculate derived fields
    if underlying_price < 10000:
        atm_strike = round(underlying_price / 50) * 50
    else:
        atm_strike = round(underlying_price / 100) * 100
    
    # Determine zone
    time_parts = [int(x) for x in trade_time.split(':')]
    time_obj = datetime.time(time_parts[0], time_parts[1], time_parts[2])
    
    if datetime.time(9, 15) <= time_obj <= datetime.time(10, 30):
        zone_id = 1
        zone_name = "OPEN"
    elif datetime.time(10, 30, 1) <= time_obj <= datetime.time(12, 0):
        zone_id = 2
        zone_name = "MID_MORN"
    elif datetime.time(12, 0, 1) <= time_obj <= datetime.time(13, 30):
        zone_id = 3
        zone_name = "LUNCH"
    elif datetime.time(13, 30, 1) <= time_obj <= datetime.time(15, 0):
        zone_id = 4
        zone_name = "AFTERNOON"
    elif datetime.time(15, 0, 1) <= time_obj <= datetime.time(15, 30):
        zone_id = 5
        zone_name = "CLOSE"
    else:
        zone_id = 1
        zone_name = "OPEN"
    
    # Simple DTE calculation (not considering holidays)
    start_date = datetime.datetime.strptime(trade_date, "%Y-%m-%d").date()
    end_date = datetime.datetime.strptime(expiry_date, "%Y-%m-%d").date()
    dte = (end_date - start_date).days
    
    # Moneyness classification (simplified)
    if strike == atm_strike:
        call_strike_type = "ATM"
        put_strike_type = "ATM"
    elif strike < atm_strike:
        call_strike_type = "ITM1"
        put_strike_type = "OTM1"
    else:
        call_strike_type = "OTM1"
        put_strike_type = "ITM1"
    
    # Build values list
    values = [
        f"'{trade_date}'", f"'{trade_time}'", f"'{expiry_date}'", "'NIFTY'", str(underlying_price),
        str(atm_strike), str(strike), str(dte), "'CW'", str(zone_id), f"'{zone_name}'",
        f"'{call_strike_type}'", f"'{put_strike_type}'",
        f"'{row['ce_symbol']}'", str(row['ce_open']), str(row['ce_high']), str(row['ce_low']), str(row['ce_close']),
        str(row['ce_volume']), str(row['ce_oi']), str(row['ce_coi']), str(row['ce_iv']), str(row['ce_delta']),
        str(row['ce_gamma']), str(row['ce_theta']), str(row['ce_vega']), str(row['ce_rho']),
        f"'{row['pe_symbol']}'", str(row['pe_open']), str(row['pe_high']), str(row['pe_low']), str(row['pe_close']),
        str(row['pe_volume']), str(row['pe_oi']), str(row['pe_coi']), str(row['pe_iv']), str(row['pe_delta']),
        str(row['pe_gamma']), str(row['pe_theta']), str(row['pe_vega']), str(row['pe_rho'])
    ]
    
    # Build and return the INSERT statement
    return f"INSERT INTO nifty_option_chain ({', '.join(fields)}) VALUES ({', '.join(values)});"

def main():
    """Main function to generate INSERT statements for sample data."""
    # Sample data from nifty_greeks (from the dump we saw earlier)
    sample_data = [
        {
            "trade_date": "2023-01-04", "trade_time": "13:08:00", "expiry_date": "2023-01-12", "strike": 17950, "underlying_price": 18077.75,
            "ce_symbol": "NIFTY12JAN2317950CE", "ce_open": 236.1, "ce_high": 236.85, "ce_low": 235, "ce_close": 236.85,
            "ce_volume": 100, "ce_oi": 23250, "ce_coi": 0, "ce_iv": 12.23, "ce_delta": 0.67, "ce_gamma": 0.001, "ce_theta": -10.27, "ce_vega": 9.73, "ce_rho": 292.114,
            "pe_symbol": "NIFTY12JAN2317950PE", "pe_open": 88.5, "pe_high": 88.5, "pe_low": 86.85, "pe_close": 88,
            "pe_volume": 1350, "pe_oi": 166900, "pe_coi": 0, "pe_iv": 14.38, "pe_delta": -0.33, "pe_gamma": 0.0009, "pe_theta": -6.73, "pe_vega": 10.08, "pe_rho": -143.4972
        },
        {
            "trade_date": "2023-01-04", "trade_time": "14:56:00", "expiry_date": "2023-01-25", "strike": 17500, "underlying_price": 18054.9,
            "ce_symbol": "NIFTY25JAN2317500CE", "ce_open": 668.55, "ce_high": 672.05, "ce_low": 668.55, "ce_close": 669.5,
            "ce_volume": 700, "ce_oi": 280750, "ce_coi": 0, "ce_iv": 9.86, "ce_delta": 0.83, "ce_gamma": 0.0003, "ce_theta": -5.65, "ce_vega": 5.2, "ce_rho": 966.6382,
            "pe_symbol": "NIFTY25JAN2317500PE", "pe_open": 66.25, "pe_high": 66.3, "pe_low": 64.95, "pe_close": 65.8,
            "pe_volume": 4950, "pe_oi": 3077000, "pe_coi": 0, "pe_iv": 16.28, "pe_delta": -0.17, "pe_gamma": 0.0004, "pe_theta": -3.32, "pe_vega": 11.09, "pe_rho": -184.9569
        }
    ]
    
    # Generate and print INSERT statements
    with open("sample_inserts.sql", "w") as f:
        for row in sample_data:
            insert_stmt = generate_insert_statement(row)
            f.write(insert_stmt + "\n")
    
    print(f"Generated INSERT statements saved to sample_inserts.sql")

if __name__ == "__main__":
    main() 