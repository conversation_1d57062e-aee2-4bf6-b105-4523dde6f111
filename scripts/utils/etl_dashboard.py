#!/usr/bin/env python3
"""
ETL Dashboard - Real-time monitoring interface for ETL operations
"""

import os
import sys
import json
import logging
from datetime import datetime, timedelta
from flask import Flask, render_template, jsonify, request
import pandas as pd
from heavydb import connect
import plotly.graph_objs as go
import plotly.utils

# Add path for performance monitor
sys.path.append('/srv/samba/shared')
from etl_performance_monitor import ETLPerformanceMonitor

app = Flask(__name__)
monitor = ETLPerformanceMonitor()

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def format_number(num):
    """Format large numbers with K/M/B suffixes"""
    if num >= 1e9:
        return f"{num/1e9:.1f}B"
    elif num >= 1e6:
        return f"{num/1e6:.1f}M"
    elif num >= 1e3:
        return f"{num/1e3:.1f}K"
    else:
        return str(int(num))

@app.route('/')
def dashboard():
    """Main dashboard page"""
    return '''
    <!DOCTYPE html>
    <html>
    <head>
        <title>ETL Performance Dashboard</title>
        <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
        <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
        <style>
            body {
                font-family: Arial, sans-serif;
                margin: 0;
                padding: 20px;
                background-color: #f5f5f5;
            }
            .container {
                max-width: 1400px;
                margin: 0 auto;
            }
            .header {
                background-color: #2c3e50;
                color: white;
                padding: 20px;
                border-radius: 5px;
                margin-bottom: 20px;
            }
            .metrics-grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
                gap: 20px;
                margin-bottom: 30px;
            }
            .metric-card {
                background: white;
                padding: 20px;
                border-radius: 5px;
                box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            }
            .metric-value {
                font-size: 2em;
                font-weight: bold;
                color: #2c3e50;
            }
            .metric-label {
                color: #7f8c8d;
                margin-top: 5px;
            }
            .status-current { color: #27ae60; }
            .status-recent { color: #f39c12; }
            .status-stale { color: #e74c3c; }
            .status-nodata { color: #95a5a6; }
            .chart-container {
                background: white;
                padding: 20px;
                border-radius: 5px;
                box-shadow: 0 2px 4px rgba(0,0,0,0.1);
                margin-bottom: 20px;
            }
            .alert {
                padding: 15px;
                margin-bottom: 10px;
                border-radius: 5px;
            }
            .alert-error {
                background-color: #e74c3c;
                color: white;
            }
            .alert-warning {
                background-color: #f39c12;
                color: white;
            }
            .refresh-info {
                text-align: right;
                color: #7f8c8d;
                font-size: 0.9em;
            }
            .loading {
                text-align: center;
                padding: 20px;
                color: #7f8c8d;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>ETL Performance Dashboard</h1>
                <div class="refresh-info">
                    Auto-refresh: <span id="countdown">30</span>s | 
                    Last update: <span id="last-update">-</span>
                </div>
            </div>
            
            <div id="alerts-container"></div>
            
            <h2>Data Freshness</h2>
            <div id="freshness-grid" class="metrics-grid">
                <div class="loading">Loading data freshness...</div>
            </div>
            
            <h2>Performance Metrics (Last 24 Hours)</h2>
            <div id="performance-grid" class="metrics-grid">
                <div class="loading">Loading performance metrics...</div>
            </div>
            
            <div class="chart-container">
                <h3>Loading Speed Trend (Last 7 Days)</h3>
                <div id="speed-chart"></div>
            </div>
            
            <div class="chart-container">
                <h3>System Resource Usage</h3>
                <div id="resource-chart"></div>
            </div>
            
            <div class="chart-container">
                <h3>Operations by Index</h3>
                <div id="operations-chart"></div>
            </div>
        </div>
        
        <script>
            let countdown = 30;
            
            function updateDashboard() {
                // Update freshness with timeout
                $.ajax({
                    url: '/api/freshness',
                    method: 'GET',
                    timeout: 10000,  // 10 second timeout
                    success: function(data) {
                        $('#freshness-grid').html('');
                        data.forEach(function(index) {
                            const statusClass = 'status-' + index.status.toLowerCase().replace(' ', '');
                            const card = `
                                <div class="metric-card">
                                    <div class="metric-value ${statusClass}">${index.status}</div>
                                    <div class="metric-label">${index.index_name}</div>
                                    <div style="font-size: 0.9em; color: #7f8c8d; margin-top: 10px;">
                                        ${index.row_count > 0 ? 
                                            `${index.row_count_formatted} rows<br>
                                             Latest: ${index.latest_date}<br>
                                             ${index.days_behind} days behind` : 
                                            'No data loaded'}
                                    </div>
                                </div>
                            `;
                            $('#freshness-grid').append(card);
                        });
                    },
                    error: function(xhr, status, error) {
                        if (status === 'timeout') {
                            $('#freshness-grid').html('<div class="loading">Request timed out. Data may be loading...</div>');
                        } else {
                            $('#freshness-grid').html('<div class="loading">Error loading data freshness</div>');
                        }
                    }
                });
                
                // Update performance metrics
                $.get('/api/performance', function(data) {
                    $('#performance-grid').html('');
                    Object.keys(data).forEach(function(key) {
                        const metric = data[key];
                        const card = `
                            <div class="metric-card">
                                <div class="metric-value">${metric.value}</div>
                                <div class="metric-label">${metric.label}</div>
                                ${metric.detail ? `<div style="font-size: 0.9em; color: #7f8c8d; margin-top: 5px;">${metric.detail}</div>` : ''}
                            </div>
                        `;
                        $('#performance-grid').append(card);
                    });
                });
                
                // Update charts
                $.get('/api/charts/speed', function(data) {
                    Plotly.newPlot('speed-chart', data.data, data.layout);
                });
                
                $.get('/api/charts/resources', function(data) {
                    Plotly.newPlot('resource-chart', data.data, data.layout);
                });
                
                $.get('/api/charts/operations', function(data) {
                    Plotly.newPlot('operations-chart', data.data, data.layout);
                });
                
                // Update alerts
                $.get('/api/alerts', function(data) {
                    $('#alerts-container').html('');
                    data.forEach(function(alert) {
                        const alertDiv = `
                            <div class="alert alert-${alert.level}">
                                <strong>${alert.type.replace(/_/g, ' ').toUpperCase()}:</strong> 
                                ${alert.message}
                            </div>
                        `;
                        $('#alerts-container').append(alertDiv);
                    });
                });
                
                // Update last update time
                $('#last-update').text(new Date().toLocaleTimeString());
                countdown = 30;
            }
            
            // Initial load
            updateDashboard();
            
            // Auto-refresh
            setInterval(function() {
                countdown--;
                $('#countdown').text(countdown);
                if (countdown <= 0) {
                    updateDashboard();
                }
            }, 1000);
        </script>
    </body>
    </html>
    '''

@app.route('/api/freshness')
def api_freshness():
    """Get data freshness for all indices"""
    try:
        df = monitor.get_data_freshness()
        data = []
        for _, row in df.iterrows():
            data.append({
                'index_name': row['index_name'],
                'status': row['status'],
                'row_count': int(row['row_count']),
                'row_count_formatted': format_number(row['row_count']),
                'latest_date': str(row['latest_date']) if row['latest_date'] else 'N/A',
                'days_behind': int(row['days_behind']) if row['days_behind'] >= 0 else 'N/A'
            })
        return jsonify(data)
    except Exception as e:
        logger.error(f"Error getting freshness: {e}")
        return jsonify([
            {'index_name': 'NIFTY', 'status': 'Unknown', 'row_count': 0, 'row_count_formatted': '0', 'latest_date': 'Error', 'days_behind': 'N/A'},
            {'index_name': 'BANKNIFTY', 'status': 'Unknown', 'row_count': 0, 'row_count_formatted': '0', 'latest_date': 'Error', 'days_behind': 'N/A'},
            {'index_name': 'MIDCAPNIFTY', 'status': 'Unknown', 'row_count': 0, 'row_count_formatted': '0', 'latest_date': 'Error', 'days_behind': 'N/A'},
            {'index_name': 'SENSEX', 'status': 'Unknown', 'row_count': 0, 'row_count_formatted': '0', 'latest_date': 'Error', 'days_behind': 'N/A'}
        ])

@app.route('/api/performance')
def api_performance():
    """Get performance metrics summary"""
    try:
        df = monitor.get_performance_summary(24)
        
        if df.empty:
            return jsonify({
                'total_operations': {'value': '0', 'label': 'Total Operations'},
                'avg_speed': {'value': '0', 'label': 'Avg Speed'},
                'total_rows': {'value': '0', 'label': 'Total Rows Processed'},
                'active_indices': {'value': '0', 'label': 'Active Indices'}
            })
            
        metrics = {
            'total_operations': {
                'value': str(int(df['operation_count'].sum())),
                'label': 'Total Operations',
                'detail': 'Last 24 hours'
            },
            'avg_speed': {
                'value': f"{df['avg_rows_per_sec'].mean():,.0f}",
                'label': 'Avg Speed (rows/sec)',
                'detail': f"Peak: {df['max_rows_per_sec'].max():,.0f}"
            },
            'total_rows': {
                'value': format_number(df['total_rows'].sum()),
                'label': 'Total Rows Processed',
                'detail': 'Last 24 hours'
            },
            'active_indices': {
                'value': str(df['index_name'].nunique()),
                'label': 'Active Indices',
                'detail': ', '.join(df['index_name'].unique())
            },
            'error_rate': {
                'value': f"{(df['total_errors'].sum() / df['operation_count'].sum() * 100):.1f}%",
                'label': 'Error Rate',
                'detail': f"{df['total_errors'].sum()} errors"
            },
            'avg_duration': {
                'value': f"{df['avg_duration'].mean():.1f}s",
                'label': 'Avg Operation Time',
                'detail': f"Total: {df['avg_duration'].sum():.0f}s"
            }
        }
        
        return jsonify(metrics)
    except Exception as e:
        logger.error(f"Error getting performance: {e}")
        return jsonify({
            'total_operations': {'value': '0', 'label': 'Total Operations'},
            'avg_speed': {'value': '0', 'label': 'Avg Speed'},
            'total_rows': {'value': '0', 'label': 'Total Rows Processed'},
            'active_indices': {'value': '0', 'label': 'Active Indices'}
        })

@app.route('/api/charts/speed')
def api_chart_speed():
    """Get speed trend chart data"""
    try:
        # Since we have no historical data yet, return empty chart
        return jsonify({
            'data': [],
            'layout': {
                'title': 'No data available yet',
                'xaxis': {'title': 'Time'},
                'yaxis': {'title': 'Rows per Second'},
                'showlegend': True
            }
        })
        
    except Exception as e:
        logger.error(f"Error generating speed chart: {e}")
        return jsonify({'data': [], 'layout': {}})

@app.route('/api/charts/resources')
def api_chart_resources():
    """Get resource usage chart data"""
    try:
        # Since we have no historical data yet, return empty chart
        return jsonify({
            'data': [],
            'layout': {
                'title': 'No data available yet',
                'xaxis': {'title': 'Time'},
                'yaxis': {'title': 'Usage %', 'side': 'left'},
                'yaxis2': {'title': 'Memory (GB)', 'side': 'right', 'overlaying': 'y'},
                'showlegend': True
            }
        })
        
    except Exception as e:
        logger.error(f"Error generating resource chart: {e}")
        return jsonify({'data': [], 'layout': {}})

@app.route('/api/charts/operations')
def api_chart_operations():
    """Get operations by index chart data"""
    try:
        # Since we have no historical data yet, return empty chart
        return jsonify({
            'data': [],
            'layout': {
                'title': 'No data available yet',
                'xaxis': {'title': 'Index'},
                'yaxis': {'title': 'Number of Operations'},
                'showlegend': False
            }
        })
        
    except Exception as e:
        logger.error(f"Error generating operations chart: {e}")
        return jsonify({'data': [], 'layout': {}})

@app.route('/api/alerts')
def api_alerts():
    """Get current alerts"""
    try:
        alerts = monitor.check_alerts()
        return jsonify(alerts)
    except Exception as e:
        logger.error(f"Error getting alerts: {e}")
        return jsonify([])

@app.route('/api/report')
def api_report():
    """Get performance report"""
    try:
        report = monitor.generate_performance_report()
        return jsonify({'report': report})
    except Exception as e:
        logger.error(f"Error generating report: {e}")
        return jsonify({'report': 'Error generating report'})

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=8001, debug=False)
