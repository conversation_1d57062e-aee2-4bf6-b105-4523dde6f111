#!/usr/bin/env python3
import sys
import logging
import pandas as pd
import subprocess

try:
    # Try importing heavydb
    from heavydb import connect
except ImportError:
    try:
        # Fallback to pymapd
        from pymapd import connect
    except ImportError:
        print("Error: Neither heavydb nor pymapd module is available. Please install one of them.")
        sys.exit(1)

# Configure logging
logging.basicConfig(level=logging.INFO, 
                   format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def get_heavydb_connection():
    """Establishes a connection to the HeavyDB database"""
    try:
        conn = connect(
            host='127.0.0.1',
            port=6274,
            user='admin',
            password='HyperInteractive',
            dbname='heavyai'
        )
        logger.info("Successfully connected to HeavyDB")
        return conn
    except Exception as e:
        logger.error(f"Failed to connect to HeavyDB: {str(e)}")
        return None

def verify_nifty_option_chain(conn):
    """Verifies the nifty_option_chain table and returns basic info"""
    try:
        cursor = conn.cursor()
        
        # Simpler approach - directly try to query count
        try:
            count_query = "SELECT COUNT(*) FROM nifty_option_chain;"
            cursor.execute(count_query)
            count = cursor.fetchone()[0]
            logger.info(f"nifty_option_chain table exists and has {count} rows")
            
            # If count succeeds, the table exists
            # Try to get a sample row if there are any rows
            if count > 0:
                sample_query = "SELECT * FROM nifty_option_chain LIMIT 1;"
                cursor.execute(sample_query)
                sample = cursor.fetchone()
                logger.info(f"Sample row: {sample}")
                
            # Try to get column info by running an empty query
            desc_query = "SELECT * FROM nifty_option_chain LIMIT 0;"
            cursor.execute(desc_query)
            columns = [desc[0] for desc in cursor.description]
            logger.info(f"Table has {len(columns)} columns:")
            logger.info(f"Columns: {', '.join(columns)}")
            
        except Exception as e:
            logger.error(f"Error querying nifty_option_chain: {str(e)}")
            logger.error("The table might not exist or has a different structure")
        
        return True
    except Exception as e:
        logger.error(f"Error verifying table: {str(e)}")
        return False

def run_sql_command(sql_command):
    """Run a SQL command using heavysql and return the output"""
    cmd = [
        "/opt/heavyai/bin/heavysql",
        "-s", "127.0.0.1",
        "--port", "6274",
        "-u", "admin",
        "-p", "HyperInteractive",
        "-d", "heavyai"
    ]
    
    process = subprocess.Popen(
        cmd,
        stdin=subprocess.PIPE,
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE,
        text=True
    )
    
    stdout, stderr = process.communicate(input=sql_command)
    
    if process.returncode != 0:
        print(f"Error executing SQL command: {stderr}")
        return None
    
    return stdout

def main():
    # Connect to HeavyDB
    conn = get_heavydb_connection()
    if not conn:
        sys.exit(1)

    # Verify the table
    if not verify_nifty_option_chain(conn):
        conn.close()
        sys.exit(1)

    logger.info("Verification completed")
    conn.close()

    # Try to show tables
    print("Checking tables...")
    tables_output = run_sql_command("SHOW TABLES;")
    print("Tables output:", tables_output)

    # Check if nifty_option_chain exists
    print("\nChecking nifty_option_chain schema...")
    schema_output = run_sql_command("DESCRIBE nifty_option_chain;")
    print("Schema output:", schema_output)

    # Count rows
    print("\nCounting rows in nifty_option_chain...")
    count_output = run_sql_command("SELECT COUNT(*) FROM nifty_option_chain;")
    print("Count output:", count_output)

if __name__ == "__main__":
    main() 