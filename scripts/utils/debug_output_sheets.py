#!/usr/bin/env python3
"""
Debug all sheets in the output Excel file to identify and fix metric calculation issues
"""

import pandas as pd
import numpy as np
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

def debug_output_file(file_path):
    """Debug all sheets in the output file"""
    
    print("DEBUGGING OUTPUT FILE SHEETS")
    print("="*80)
    print(f"File: {file_path}\n")
    
    try:
        # Read all sheets
        xl_file = pd.ExcelFile(file_path)
        print(f"Sheets found: {xl_file.sheet_names}\n")
        
        issues_found = []
        
        # 1. Debug Portfolio Trans sheet
        print("1. DEBUGGING PORTFOLIO TRANS SHEET")
        print("-"*60)
        if 'PORTFOLIO Trans' in xl_file.sheet_names:
            trans_df = pd.read_excel(file_path, sheet_name='PORTFOLIO Trans')
            print(f"   Rows: {len(trans_df)}")
            print(f"   Columns: {len(trans_df.columns)}")
            
            # Check for missing values
            print("\n   Missing Values Check:")
            missing_cols = []
            for col in trans_df.columns:
                missing_count = trans_df[col].isna().sum()
                if missing_count > 0:
                    missing_cols.append((col, missing_count))
                    print(f"   - {col}: {missing_count} missing values")
                    issues_found.append(f"PORTFOLIO Trans: {col} has {missing_count} missing values")
            
            # Specific checks
            if 'Expiry' in trans_df.columns:
                expiry_issues = trans_df['Expiry'].isna().sum()
                if expiry_issues > 0:
                    print(f"\n   ❌ EXPIRY DATES MISSING: {expiry_issues} rows")
                    # Show sample of missing expiry rows
                    missing_expiry = trans_df[trans_df['Expiry'].isna()].head(3)
                    print("   Sample rows with missing expiry:")
                    print(missing_expiry[['Strategy Name', 'Strike', 'CE/PE', 'Entry Date', 'Expiry']].to_string())
            
            # Check calculations
            if all(col in trans_df.columns for col in ['Points', 'Points After Slippage', 'PNL', 'AfterSlippage']):
                # Check if slippage is calculated correctly
                print("\n   Slippage Calculation Check:")
                non_zero_slippage = trans_df[trans_df['Points After Slippage'] != trans_df['Points']]
                if len(non_zero_slippage) == 0:
                    print("   ❌ NO SLIPPAGE APPLIED - Points After Slippage equals Points")
                    issues_found.append("PORTFOLIO Trans: Slippage not being applied")
                else:
                    print(f"   ✓ Slippage applied to {len(non_zero_slippage)} trades")
            
            # Check P&L calculations
            if 'Net PNL' in trans_df.columns and 'Taxes' in trans_df.columns:
                print("\n   P&L Calculation Check:")
                # Net PNL should be AfterSlippage - Taxes
                if 'AfterSlippage' in trans_df.columns:
                    calc_net_pnl = trans_df['AfterSlippage'] - trans_df['Taxes']
                    pnl_diff = abs(trans_df['Net PNL'] - calc_net_pnl).sum()
                    if pnl_diff > 0.01:
                        print(f"   ❌ Net PNL calculation error: Total difference = {pnl_diff:.2f}")
                        issues_found.append(f"PORTFOLIO Trans: Net PNL calculation error = {pnl_diff:.2f}")
                    else:
                        print("   ✓ Net PNL calculations correct")
        
        # 2. Debug Metrics sheet
        print("\n\n2. DEBUGGING METRICS SHEET")
        print("-"*60)
        if 'Metrics' in xl_file.sheet_names:
            metrics_df = pd.read_excel(file_path, sheet_name='Metrics')
            print(f"   Rows: {len(metrics_df)}")
            
            # Check key metrics
            print("\n   Key Metrics Check:")
            expected_metrics = ['Total PnL', 'Win Rate', 'Average PnL per Trade', 'Max Profit', 
                              'Max Loss', 'Sharpe Ratio', 'Max Drawdown']
            
            if 'Metric' in metrics_df.columns:
                available_metrics = metrics_df['Metric'].tolist()
                for metric in expected_metrics:
                    if metric not in available_metrics:
                        print(f"   ❌ Missing metric: {metric}")
                        issues_found.append(f"Metrics: Missing {metric}")
                    else:
                        # Get the value
                        value_row = metrics_df[metrics_df['Metric'] == metric]
                        if not value_row.empty:
                            # Check which columns have the values
                            for col in metrics_df.columns:
                                if col != 'Metric' and not pd.isna(value_row[col].iloc[0]):
                                    print(f"   - {metric}: {value_row[col].iloc[0]}")
                                    break
        
        # 3. Debug Max Profit and Loss sheet
        print("\n\n3. DEBUGGING MAX PROFIT AND LOSS SHEET")
        print("-"*60)
        if 'Max Profit and Loss' in xl_file.sheet_names:
            max_pl_df = pd.read_excel(file_path, sheet_name='Max Profit and Loss')
            print(f"   Rows: {len(max_pl_df)}")
            
            if len(max_pl_df) > 0:
                print("\n   Daily Max P&L Check:")
                # Check columns
                expected_cols = ['Date', 'Max Profit', 'Max Loss', 'Max Profit Time', 'Max Loss Time']
                for col in expected_cols:
                    if col not in max_pl_df.columns:
                        print(f"   ❌ Missing column: {col}")
                        issues_found.append(f"Max Profit and Loss: Missing column {col}")
                
                # Check if values are calculated
                if 'Max Profit' in max_pl_df.columns and 'Max Loss' in max_pl_df.columns:
                    zero_profit = (max_pl_df['Max Profit'] == 0).sum()
                    zero_loss = (max_pl_df['Max Loss'] == 0).sum()
                    if zero_profit == len(max_pl_df):
                        print("   ❌ All Max Profit values are 0")
                        issues_found.append("Max Profit and Loss: All Max Profit values are 0")
                    if zero_loss == len(max_pl_df):
                        print("   ❌ All Max Loss values are 0")
                        issues_found.append("Max Profit and Loss: All Max Loss values are 0")
                    
                    print(f"\n   Sample data:")
                    print(max_pl_df.head(3).to_string())
        
        # 4. Debug PORTFOLIO Results sheet
        print("\n\n4. DEBUGGING PORTFOLIO RESULTS SHEET")
        print("-"*60)
        if 'PORTFOLIO Results' in xl_file.sheet_names:
            results_df = pd.read_excel(file_path, sheet_name='PORTFOLIO Results')
            print(f"   Shape: {results_df.shape}")
            
            if len(results_df) > 0:
                print("\n   Day-wise P&L Check:")
                # Should have weekday columns
                weekdays = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday']
                missing_days = [day for day in weekdays if day not in results_df.columns]
                if missing_days:
                    print(f"   ❌ Missing weekday columns: {missing_days}")
                    issues_found.append(f"PORTFOLIO Results: Missing weekday columns {missing_days}")
                
                # Check if Total column exists and is calculated correctly
                if 'Total' in results_df.columns:
                    # Verify total calculation
                    day_cols = [col for col in results_df.columns if col in weekdays]
                    if day_cols:
                        calc_total = results_df[day_cols].sum(axis=1)
                        total_diff = abs(results_df['Total'] - calc_total).sum()
                        if total_diff > 0.01:
                            print(f"   ❌ Total calculation error: {total_diff:.2f}")
                            issues_found.append(f"PORTFOLIO Results: Total calculation error = {total_diff:.2f}")
                        else:
                            print("   ✓ Total calculations correct")
        
        # 5. Check other sheets
        print("\n\n5. CHECKING OTHER SHEETS")
        print("-"*60)
        for sheet in xl_file.sheet_names:
            if sheet not in ['PORTFOLIO Trans', 'Metrics', 'Max Profit and Loss', 'PORTFOLIO Results']:
                df = pd.read_excel(file_path, sheet_name=sheet)
                print(f"\n   {sheet}: {df.shape}")
                # Check for empty sheets
                if len(df) == 0:
                    print(f"   ⚠️  Sheet is empty")
                    issues_found.append(f"{sheet}: Empty sheet")
        
        # Summary
        print("\n\nSUMMARY OF ISSUES FOUND")
        print("="*80)
        if issues_found:
            print(f"Total issues: {len(issues_found)}\n")
            for i, issue in enumerate(issues_found, 1):
                print(f"{i}. {issue}")
        else:
            print("✓ No major issues found!")
        
        return issues_found
        
    except Exception as e:
        print(f"Error reading file: {e}")
        return []

def check_golden_format_compliance(output_file, golden_file):
    """Compare output with golden format"""
    print("\n\nGOLDEN FORMAT COMPLIANCE CHECK")
    print("="*80)
    
    try:
        output_trans = pd.read_excel(output_file, sheet_name='PORTFOLIO Trans')
        golden_trans = pd.read_excel(golden_file, sheet_name='PORTFOLIO Trans')
        
        print("Column comparison:")
        output_cols = set(output_trans.columns)
        golden_cols = set(golden_trans.columns)
        
        missing_cols = golden_cols - output_cols
        extra_cols = output_cols - golden_cols
        
        if missing_cols:
            print(f"\n❌ Missing columns: {missing_cols}")
        if extra_cols:
            print(f"\n⚠️  Extra columns: {extra_cols}")
        
        if not missing_cols and not extra_cols:
            print("\n✓ All columns match golden format!")
            
        # Check data types
        print("\n\nData type comparison for key columns:")
        key_cols = ['Expiry', 'Strike', 'MaxProfit', 'MaxLoss', 'ID', 'Qty']
        for col in key_cols:
            if col in output_trans.columns and col in golden_trans.columns:
                output_dtype = output_trans[col].dtype
                golden_dtype = golden_trans[col].dtype
                match = '✓' if output_dtype == golden_dtype else '❌'
                print(f"   {match} {col}: Output={output_dtype}, Golden={golden_dtype}")
                
    except Exception as e:
        print(f"Error comparing with golden format: {e}")

if __name__ == "__main__":
    # Debug the latest output file
    output_file = "/srv/samba/shared/test_final_fixes.xlsx"
    golden_file = "/srv/samba/shared/Nifty_Golden_Ouput.xlsx"
    
    print(f"Debugging: {output_file}")
    issues = debug_output_file(output_file)
    
    # Check golden format compliance
    check_golden_format_compliance(output_file, golden_file)