#!/usr/bin/env python3
"""Final verification of golden format compliance"""

import pandas as pd

def verify_final_output():
    output_file = 'final_golden_output.xlsx'
    golden_file = 'Nifty_Golden_Ouput.xlsx'
    
    print('COMPREHENSIVE FINAL VERIFICATION')
    print('='*80)
    
    # Read files
    golden_xl = pd.ExcelFile(golden_file)
    output_xl = pd.ExcelFile(output_file)
    
    # Compare sheets
    print(f'Golden sheets: {golden_xl.sheet_names}')
    print(f'Output sheets: {output_xl.sheet_names}')
    
    # Check transaction sheet
    output_trans = pd.read_excel(output_file, sheet_name='PORTFOLIO Trans')
    golden_trans = pd.read_excel(golden_file, sheet_name='PORTFOLIO Trans')
    
    # Column comparison
    golden_cols = list(golden_trans.columns)
    output_cols = list(output_trans.columns)
    
    print(f'\nColumn count: Golden={len(golden_cols)}, Output={len(output_cols)}')
    
    # Check all columns match
    all_match = True
    for i, col in enumerate(golden_cols):
        if col in output_cols:
            print(f'  {i+1:2d}. ✅ {col}')
        else:
            print(f'  {i+1:2d}. ❌ {col} - MISSING!')
            all_match = False
    
    # Check data types
    print('\nData Type Check:')
    for col in ['MaxProfit', 'MaxLoss', 'Expiry', 'SL Re-entry No', 'TGT Re-entry No']:
        if col in golden_trans.columns and col in output_trans.columns:
            golden_type = golden_trans[col].dtype
            output_type = output_trans[col].dtype
            match = '✅' if golden_type == output_type else '❌'
            print(f'  {match} {col}: Golden={golden_type}, Output={output_type}')
    
    # Check values
    print('\nValue Check:')
    
    # MaxProfit/MaxLoss
    if 'MaxProfit' in output_trans.columns:
        non_zero_profit = output_trans[output_trans['MaxProfit'] != 0]
        print(f'  MaxProfit non-zero count: {len(non_zero_profit)}')
        if len(non_zero_profit) > 0:
            print(f'  MaxProfit sample values: {non_zero_profit["MaxProfit"].head(3).tolist()}')
    
    if 'MaxLoss' in output_trans.columns:
        non_zero_loss = output_trans[output_trans['MaxLoss'] != 0]
        print(f'  MaxLoss non-zero count: {len(non_zero_loss)}')
        if len(non_zero_loss) > 0:
            print(f'  MaxLoss sample values: {non_zero_loss["MaxLoss"].head(3).tolist()}')
    
    # Slippage columns
    slippage_cols = ['Points After Slippage', 'AfterSlippage']
    for col in slippage_cols:
        if col in output_trans.columns:
            print(f'\n  {col}:')
            print(f'    Sample values: {output_trans[col].head(3).tolist()}')
            print(f'    Non-zero count: {(output_trans[col] != 0).sum()}')
    
    # Final verdict
    print('\n' + '='*80)
    if all_match and len(golden_cols) == len(output_cols):
        print('✅ PERFECT MATCH! All columns match golden format exactly!')
    else:
        print('❌ Column mismatch detected - needs fixing')
    
    # Check Max Profit and Loss sheet
    print('\nMax Profit and Loss Sheet:')
    max_pl = pd.read_excel(output_file, sheet_name='Max Profit and Loss')
    print(max_pl)

if __name__ == "__main__":
    verify_final_output()