#!/usr/bin/env python3
"""
Multi-process bulk loader for Nifty Option Chain data
Uses multiple parallel workers to significantly speed up data loading
"""

import os
import sys
import glob
import pandas as pd
import numpy as np
from datetime import datetime
import logging
import time
import multiprocessing
from concurrent.futures import ProcessPoolExecutor, as_completed
import signal

sys.path.append('/srv/samba/shared')

from bt.dal.heavydb_conn import get_conn

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - [%(processName)s] - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Configuration
DEFAULT_BATCH_SIZE = 20000  # Larger batch size for better performance
DEFAULT_WORKERS = min(24, multiprocessing.cpu_count() // 2)  # Use half available CPUs
MAX_RETRIES = 3

class MultiProcessLoader:
    def __init__(self, data_dir="/srv/samba/shared/market_data/nifty/oc_with_futures", 
                 batch_size=DEFAULT_BATCH_SIZE, num_workers=DEFAULT_WORKERS):
        self.data_dir = data_dir
        self.batch_size = batch_size
        self.num_workers = num_workers
        self.total_rows_processed = multiprocessing.Value('i', 0)
        self.files_completed = multiprocessing.Value('i', 0)
        
    def prepare_value(self, val, col_type='str'):
        """Prepare value for SQL insert"""
        if pd.isna(val) or val is None:
            return 'NULL'
        elif col_type == 'str':
            # Escape single quotes
            val_str = str(val).replace("'", "''")
            return f"'{val_str}'"
        elif col_type == 'date':
            return f"'{val}'"
        elif col_type == 'time':
            return f"'{val}'"
        else:  # numeric
            return str(val)
    
    @staticmethod
    def process_file_chunk(args):
        """Process a chunk of a CSV file - static method for multiprocessing"""
        file_path, start_row, end_row, batch_size, worker_id = args
        
        # Each worker creates its own connection
        conn = get_conn()
        cursor = conn.cursor()
        
        try:
            # Read only the specified chunk
            chunk_df = pd.read_csv(file_path, skiprows=range(1, start_row+1), nrows=end_row-start_row)
            
            # Process dates
            chunk_df['trade_date'] = pd.to_datetime(chunk_df['trade_date']).dt.strftime('%Y-%m-%d')
            chunk_df['trade_time'] = pd.to_datetime(chunk_df['trade_time'], format='%H:%M:%S').dt.strftime('%H:%M:%S')
            chunk_df['expiry_date'] = pd.to_datetime(chunk_df['expiry_date']).dt.strftime('%Y-%m-%d')
            
            rows_processed = 0
            batch_num = 0
            
            # Process in batches
            for batch_start in range(0, len(chunk_df), batch_size):
                batch_end = min(batch_start + batch_size, len(chunk_df))
                batch = chunk_df.iloc[batch_start:batch_end]
                
                values_list = []
                for _, row in batch.iterrows():
                    values = []
                    
                    # Date/time columns
                    values.append(MultiProcessLoader.prepare_value_static(row['trade_date'], 'date'))
                    values.append(MultiProcessLoader.prepare_value_static(row['trade_time'], 'time'))
                    values.append(MultiProcessLoader.prepare_value_static(row['expiry_date'], 'date'))
                    
                    # Text columns
                    values.append(MultiProcessLoader.prepare_value_static(row['index_name'], 'str'))
                    
                    # Numeric columns
                    numeric_cols = ['spot', 'atm_strike', 'strike', 'dte']
                    for col in numeric_cols:
                        values.append(MultiProcessLoader.prepare_value_static(row[col], 'num'))
                    
                    # More text columns
                    text_cols = ['expiry_bucket', 'zone_id', 'zone_name', 'call_strike_type', 'put_strike_type']
                    for col in text_cols:
                        if col == 'zone_id':
                            values.append(MultiProcessLoader.prepare_value_static(row[col], 'num'))
                        else:
                            values.append(MultiProcessLoader.prepare_value_static(row[col], 'str'))
                    
                    # Option columns
                    option_cols = ['ce_symbol', 'ce_open', 'ce_high', 'ce_low', 'ce_close', 'ce_volume', 
                                  'ce_oi', 'ce_coi', 'ce_iv', 'ce_delta', 'ce_gamma', 'ce_theta', 
                                  'ce_vega', 'ce_rho', 'pe_symbol', 'pe_open', 'pe_high', 'pe_low', 
                                  'pe_close', 'pe_volume', 'pe_oi', 'pe_coi', 'pe_iv', 'pe_delta', 
                                  'pe_gamma', 'pe_theta', 'pe_vega', 'pe_rho']
                    
                    for col in option_cols:
                        if col in ['ce_symbol', 'pe_symbol']:
                            values.append(MultiProcessLoader.prepare_value_static(row[col], 'str'))
                        else:
                            values.append(MultiProcessLoader.prepare_value_static(row[col], 'num'))
                    
                    # Future columns
                    future_cols = ['future_open', 'future_high', 'future_low', 'future_close',
                                  'future_volume', 'future_oi', 'future_coi']
                    for col in future_cols:
                        values.append(MultiProcessLoader.prepare_value_static(row[col], 'num'))
                    
                    values_list.append(f"({','.join(values)})")
                
                # Execute batch insert
                if values_list:
                    insert_sql = f"""
                    INSERT INTO nifty_option_chain (
                        trade_date, trade_time, expiry_date, index_name,
                        spot, atm_strike, strike, dte, expiry_bucket,
                        zone_id, zone_name, call_strike_type, put_strike_type,
                        ce_symbol, ce_open, ce_high, ce_low, ce_close,
                        ce_volume, ce_oi, ce_coi, ce_iv, ce_delta,
                        ce_gamma, ce_theta, ce_vega, ce_rho,
                        pe_symbol, pe_open, pe_high, pe_low, pe_close,
                        pe_volume, pe_oi, pe_coi, pe_iv, pe_delta,
                        pe_gamma, pe_theta, pe_vega, pe_rho,
                        future_open, future_high, future_low, future_close,
                        future_volume, future_oi, future_coi
                    ) VALUES {','.join(values_list)}
                    """
                    
                    retries = 0
                    while retries < MAX_RETRIES:
                        try:
                            cursor.execute(insert_sql)
                            rows_processed += len(values_list)
                            batch_num += 1
                            logger.info(f"Worker {worker_id}: Batch {batch_num} - Inserted {len(values_list)} rows from {os.path.basename(file_path)}")
                            break
                        except Exception as e:
                            retries += 1
                            if retries >= MAX_RETRIES:
                                logger.error(f"Worker {worker_id}: Failed to insert batch after {MAX_RETRIES} retries: {e}")
                                raise
                            time.sleep(2 ** retries)
            
            cursor.close()
            conn.close()
            
            return rows_processed, os.path.basename(file_path)
            
        except Exception as e:
            logger.error(f"Worker {worker_id}: Error processing chunk: {e}")
            if cursor:
                cursor.close()
            if conn:
                conn.close()
            raise
    
    @staticmethod
    def prepare_value_static(val, col_type='str'):
        """Static version of prepare_value for multiprocessing"""
        if pd.isna(val) or val is None:
            return 'NULL'
        elif col_type == 'str':
            val_str = str(val).replace("'", "''")
            return f"'{val_str}'"
        elif col_type in ['date', 'time']:
            return f"'{val}'"
        else:  # numeric
            return str(val)
    
    def split_file_into_chunks(self, file_path, num_chunks):
        """Split a file into chunks for parallel processing"""
        # Count total rows
        with open(file_path, 'r') as f:
            total_rows = sum(1 for _ in f) - 1  # Subtract header
        
        chunk_size = total_rows // num_chunks
        chunks = []
        
        for i in range(num_chunks):
            start_row = i * chunk_size
            if i == num_chunks - 1:
                # Last chunk gets all remaining rows
                end_row = total_rows
            else:
                end_row = (i + 1) * chunk_size
            
            chunks.append((file_path, start_row, end_row, self.batch_size, i))
        
        return chunks
    
    def process_files(self, csv_files):
        """Process multiple CSV files using multiprocessing"""
        total_start_time = time.time()
        
        logger.info(f"Starting multi-process bulk loading with {self.num_workers} workers")
        logger.info(f"Batch size: {self.batch_size} rows")
        logger.info(f"Files to process: {len(csv_files)}")
        
        all_tasks = []
        file_chunks_map = {}
        
        # Create chunks for each file
        for csv_file in csv_files:
            file_size = os.path.getsize(csv_file)
            file_size_mb = file_size / (1024 * 1024)
            
            # Determine number of chunks based on file size
            if file_size_mb < 50:
                num_chunks = 1
            elif file_size_mb < 200:
                num_chunks = 4
            else:
                num_chunks = 8
            
            chunks = self.split_file_into_chunks(csv_file, num_chunks)
            file_chunks_map[csv_file] = len(chunks)
            all_tasks.extend(chunks)
            
            logger.info(f"File: {os.path.basename(csv_file)} ({file_size_mb:.1f} MB) - Split into {num_chunks} chunks")
        
        # Process all chunks in parallel
        total_rows = 0
        completed_files = set()
        
        with ProcessPoolExecutor(max_workers=self.num_workers) as executor:
            # Submit all tasks
            future_to_task = {executor.submit(self.process_file_chunk, task): task for task in all_tasks}
            
            # Process completed tasks
            for future in as_completed(future_to_task):
                try:
                    rows_processed, file_name = future.result()
                    total_rows += rows_processed
                    
                    # Track file completion
                    original_file = future_to_task[future][0]
                    if original_file not in completed_files:
                        # Check if all chunks for this file are done
                        completed_chunks = sum(1 for f, t in future_to_task.items() 
                                             if f.done() and t[0] == original_file)
                        if completed_chunks == file_chunks_map[original_file]:
                            completed_files.add(original_file)
                            with self.files_completed.get_lock():
                                self.files_completed.value += 1
                            logger.info(f"Completed file {self.files_completed.value}/{len(csv_files)}: {os.path.basename(original_file)}")
                    
                    # Update total rows
                    with self.total_rows_processed.get_lock():
                        self.total_rows_processed.value += rows_processed
                    
                    # Progress update
                    elapsed = time.time() - total_start_time
                    rate = self.total_rows_processed.value / elapsed if elapsed > 0 else 0
                    logger.info(f"Total progress: {self.total_rows_processed.value:,} rows loaded at {rate:.0f} rows/sec")
                    
                except Exception as e:
                    logger.error(f"Task failed: {e}")
        
        total_elapsed = time.time() - total_start_time
        final_rate = self.total_rows_processed.value / total_elapsed if total_elapsed > 0 else 0
        
        logger.info(f"\n=== Multi-Process Loading Complete ===")
        logger.info(f"Total rows loaded: {self.total_rows_processed.value:,}")
        logger.info(f"Total time: {total_elapsed:.1f} seconds")
        logger.info(f"Average rate: {final_rate:.0f} rows/second")
        logger.info(f"Files completed: {self.files_completed.value}/{len(csv_files)}")
        
        return self.total_rows_processed.value

def main():
    import argparse
    
    parser = argparse.ArgumentParser(description='Multi-process bulk loader for Nifty data')
    parser.add_argument('--data-dir', default='/srv/samba/shared/market_data/nifty/oc_with_futures',
                        help='Directory containing CSV files')
    parser.add_argument('--batch-size', type=int, default=DEFAULT_BATCH_SIZE,
                        help='Batch size for inserts')
    parser.add_argument('--workers', type=int, default=DEFAULT_WORKERS,
                        help='Number of parallel workers')
    parser.add_argument('--pattern', default='*.csv', help='File pattern to match')
    parser.add_argument('--skip-sorted', action='store_true', 
                        help='Skip _sorted.csv files')
    
    args = parser.parse_args()
    
    # Get CSV files
    csv_files = sorted(glob.glob(os.path.join(args.data_dir, args.pattern)))
    
    if args.skip_sorted:
        csv_files = [f for f in csv_files if not f.endswith('_sorted.csv')]
    
    if not csv_files:
        logger.error("No CSV files found!")
        return
    
    logger.info(f"Found {len(csv_files)} CSV files to process")
    
    # Create loader and process files
    loader = MultiProcessLoader(
        data_dir=args.data_dir,
        batch_size=args.batch_size,
        num_workers=args.workers
    )
    
    # Verify table exists
    try:
        conn = get_conn()
        cursor = conn.cursor()
        cursor.execute("SELECT COUNT(*) FROM nifty_option_chain")
        initial_count = cursor.fetchone()[0]
        logger.info(f"Starting with {initial_count:,} rows in table")
        cursor.close()
        conn.close()
    except Exception as e:
        logger.error(f"Error checking table: {e}")
        return
    
    # Process files
    rows_loaded = loader.process_files(csv_files)
    
    # Final verification
    try:
        conn = get_conn()
        cursor = conn.cursor()
        cursor.execute("SELECT COUNT(*) FROM nifty_option_chain")
        final_count = cursor.fetchone()[0]
        logger.info(f"\nFinal row count: {final_count:,}")
        logger.info(f"Rows added in this session: {final_count - initial_count:,}")
        cursor.close()
        conn.close()
    except Exception as e:
        logger.error(f"Error verifying final count: {e}")

if __name__ == "__main__":
    main()