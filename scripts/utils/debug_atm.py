#!/usr/bin/env python3
"""
Debug script for ATM strike calculation issues
"""
import pandas as pd
import numpy as np

# Load data
input_file = "/srv/samba/shared/market_data/nifty/IV_2025_apr_nifty_cleaned.csv"
print(f"Loading {input_file}...")
df = pd.read_csv(input_file)

# Get the first group
date = df["trade_date"].iloc[0]
time = df["trade_time"].iloc[0]
expiry = df["expiry_date"].iloc[0]
print(f"Checking group: Date={date}, Time={time}, Expiry={expiry}")

# Filter rows for this group
group = df[(df["trade_date"] == date) & (df["trade_time"] == time) & (df["expiry_date"] == expiry)].copy()
print(f"Group has {len(group)} rows")

# Filter for valid CE/PE pairs
valid_rows = group[(group["ce_close"] > 0) & (group["pe_close"] > 0)].copy()
print(f"Found {len(valid_rows)} rows with valid CE/PE pairs")

# Calculate synthetic futures and absolute difference from spot
spot_value = group["underlying_price"].iloc[0]
print(f"Spot value: {spot_value}")

valid_rows["synthetic_future"] = valid_rows["strike"] + valid_rows["ce_close"] - valid_rows["pe_close"]
valid_rows["abs_diff"] = abs(valid_rows["synthetic_future"] - spot_value)

# Show the top 5 rows by abs_diff
print("\nTop 5 strikes by smallest abs_diff:")
top5 = valid_rows.sort_values("abs_diff").head(5)
print(top5[["strike", "ce_close", "pe_close", "synthetic_future", "abs_diff"]].to_string())

# Find the strike with minimum absolute difference
min_idx = valid_rows["abs_diff"].idxmin()
min_strike = valid_rows.loc[min_idx, "strike"]
min_synthetic = valid_rows.loc[min_idx, "synthetic_future"]
min_diff = valid_rows.loc[min_idx, "abs_diff"]

print(f"\nATM strike should be: {min_strike}")
print(f"With synthetic future: {min_synthetic}")
print(f"Absolute difference from spot: {min_diff}")

# Check if strike 24100 exists
has_24100 = 24100 in valid_rows["strike"].values
print(f"\nStrike 24100 exists in valid rows: {has_24100}")

if has_24100:
    strike_24100 = valid_rows[valid_rows["strike"] == 24100]
    synthetic_24100 = strike_24100["synthetic_future"].iloc[0]
    diff_24100 = strike_24100["abs_diff"].iloc[0]
    print(f"Strike 24100 synthetic future: {synthetic_24100}")
    print(f"Strike 24100 absolute difference: {diff_24100}") 