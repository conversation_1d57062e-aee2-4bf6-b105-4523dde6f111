# Directory Reorganization Summary

## Date: June 12, 2025

### Overview
The codebase has been reorganized for better clarity and maintainability. All core modules have been preserved without any changes.

### New Directory Structure

```
/srv/samba/shared/
├── bt/                     # PRESERVED - Core backtester module
├── docs/                   # PRESERVED - Documentation (with added subdirectories)
│   ├── guides/            # Implementation guides and architecture docs
│   ├── images/            # Screenshots and diagrams
│   ├── pdfs/              # PDF documentation
│   └── project-status/    # Status reports and completion summaries
├── market_data/           # PRESERVED - Market data files
├── archive_verification/  # PRESERVED - Verification scripts
│
├── etl/                   # NEW - ETL and data processing
│   ├── scripts/          # ETL Python scripts (load_*.py, process_*.py)
│   ├── sql/              # SQL files for data operations
│   └── configs/          # ETL configurations
│
├── tests/                 # EXISTING - Consolidated testing
│   ├── unit/             # Unit tests
│   ├── integration/      # Integration tests (added test files)
│   ├── e2e/              # End-to-end tests
│   ├── fixtures/         # Test fixtures and data
│   └── reports/          # Test reports
│
├── scripts/               # NEW - Utility scripts
│   ├── analysis/         # Analysis scripts (analyze_*.py)
│   ├── validation/       # Validation scripts (check_*.py, validate_*.py)
│   ├── fixes/            # Fix scripts (fix_*.py)
│   └── utils/            # General utilities and runners
│
├── configs/               # NEW - Configuration files
│   ├── database/         # Database configs
│   ├── strategies/       # Strategy configs
│   └── server/           # Server configs
│
├── logs/                  # NEW - All log files
│   ├── backtest/         # Backtest logs
│   ├── server/           # Server logs
│   └── etl/              # ETL logs
│
├── reports/               # NEW - All reports
│   ├── analysis/         # Analysis reports and JSON files
│   ├── optimization/     # Optimization reports
│   └── validation/       # Validation reports
│
├── templates/             # NEW - Excel/YAML templates
│   ├── excel/            # Excel templates
│   └── yaml/             # YAML templates
│
├── data/                  # NEW - Data files
│   ├── excel/            # Excel data files
│   ├── csv/              # CSV data files
│   ├── json/             # JSON data files
│   └── archives/         # ZIP archives
│
├── outputs/               # NEW - Output directories
│   └── [various output and results directories]
│
├── temp/                  # NEW - Temporary files
│   └── .gitignore        # Ignores all temp files
│
└── legacy/                # NEW - Legacy/deprecated code
    ├── scripts/          # Old scripts
    └── backups/          # Backup files

## Files Remaining in Root (Intentionally)
- BTRunPortfolio.py       # Core module
- BTRunPortfolio_GPU_Fixed.py  # Core module
- Util.py                 # Core utility
- CLAUDE.md              # Project instructions
- README.md              # Main documentation
- QUICK_REFERENCE.md     # Quick reference guide
- requirements.txt       # Python dependencies
- docker-compose.yml     # Docker configuration
- .gitignore            # Git ignore rules
- Logs/, Trades/, Merge/ # Existing directories (preserved)

## What Was Moved

### ETL Scripts (→ etl/scripts/)
- All load_*.py scripts
- All process_*.py scripts
- ETL-related Python files

### SQL Files (→ etl/sql/)
- All *.sql files for database operations

### Analysis Scripts (→ scripts/analysis/)
- All analyze_*.py scripts

### Validation Scripts (→ scripts/validation/)
- All check_*.py scripts
- All validate_*.py scripts

### Fix Scripts (→ scripts/fixes/)
- All fix_*.py scripts

### Utility Scripts (→ scripts/utils/)
- All create_*.py scripts
- All run_*.py scripts
- All *.sh shell scripts
- Debug scripts (debug_*.py)
- Other utility Python scripts

### Log Files (→ logs/)
- 586 log files organized into:
  - logs/backtest/ (backtest logs)
  - logs/server/ (server logs)
  - logs/etl/ (ETL logs)
  - logs/ (other logs)

### Test Files (→ tests/integration/)
- All *test*.py files

### Documentation (→ docs/)
- Status reports → docs/project-status/
- Guides → docs/guides/
- Images → docs/images/
- PDFs → docs/pdfs/

### Data Files (→ data/)
- Excel files → data/excel/
- CSV files → data/csv/
- JSON files → data/json/
- ZIP files → data/archives/

### Templates (→ templates/excel/)
- All *TEMPLATE*.xlsx files

### Reports (→ reports/)
- Analysis reports → reports/analysis/
- Optimization reports → reports/optimization/
- JSON report files → reports/analysis/

### Output Directories (→ outputs/)
- All *output* directories
- All *results* directories

## Benefits Achieved

1. **Cleaner Root Directory**: Reduced from 1000+ files to essential files only
2. **Logical Organization**: Related files are now grouped together
3. **Easy Navigation**: Clear directory structure for finding files
4. **Preserved Core**: All critical modules remain untouched
5. **Better Maintenance**: Clear locations for adding new files

## Next Steps

1. Update any scripts that reference moved files
2. Update .gitignore to exclude temp/ and logs/ appropriately
3. Consider creating symbolic links for frequently accessed files
4. Document any path changes in relevant configuration files

## Notes

- All moves were done preserving file permissions and ownership
- No files were deleted, only reorganized
- Core modules (bt/, docs/, market_data/, archive_verification/) were not modified
- The reorganization can be reversed if needed using git history