# TradingView (TV) Input Sheet Column Mapping and Valid Options

This document provides a comprehensive reference for TradingView (TV) backtesting input sheets, including field mappings, valid options, and usage patterns.

## Overview of TradingView Backtesting

TradingView backtesting allows users to test strategies based on external signals (from TradingView or other sources) rather than strategy parameter-driven entry/exit conditions. TV backtesting takes signal dates, times, and types from a separate signal sheet and applies portfolio strategy parameters to those signals.

## TV Setting Sheet

| Column | Description | Valid Options | Notes |
|--------|-------------|--------------|-------|
| Name | Output file name | Any string | Used for identifying backtest results |
| Enabled | Enable this TV setting | YES/NO | Row filter, only YES rows are processed |
| SignalFilePath | Path to signal Excel | Valid file path | Contains the list of trade signals |
| StartDate | Backtest start date | DD_MM_YYYY format | Filter signals outside this range |
| EndDate | Backtest end date | DD_MM_YYYY format | Filter signals outside this range |
| SignalDateFormat | Signal timestamp format | %Y%m%d %H%M%S, etc. | Format for parsing signal datetimes |
| IntradaySqOffApplicable | Use intraday square off | YES/NO | Force exit at IntradayExitTime |
| IntradayExitTime | Intraday exit time | HHMMSS format | Format: 153000 = 15:30:00 |
| TvExitApplicable | Use TV exit signals | YES/NO | If NO, use IntradayExitTime or EndTime |
| DoRollover | Roll positions | YES/NO | Continue trade across contract expiry |
| RolloverTime | Rollover time | HHMMSS format | Time to roll on expiry day |
| ManualTradeEntryTime | Manual trade entry time | HHMMSS format | Time for synthetic manual entries |
| ManualTradeLots | Manual trade lot count | Integer | Lot size for synthetic manual trades |
| FirstTradeEntryTime | First entry time | HHMMSS format | Override first signal timing |
| IncreaseEntrySignalTimeBy | Entry time offset | Integer (seconds) | Add delay to entry signals |
| IncreaseExitSignalTimeBy | Exit time offset | Integer (seconds) | Add delay to exit signals |
| ExpiryDayExitTime | Expiry day exit time | HHMMSS format | Force exit on expiry day |
| SlippagePercent | Slippage percentage | Float (e.g., 0.1) | Slippage applied to trade execution |
| LongPortfolioFilePath | LONG strategy Excel | Valid file path | Portfolio for LONG signals |
| ShortPortfolioFilePath | SHORT strategy Excel | Valid file path | Portfolio for SHORT signals |
| ManualPortfolioFilePath | MANUAL strategy Excel | Valid file path | Portfolio for MANUAL signals |
| UseDbExitTiming | Enable database exit timing | YES/NO | Use database price movements for precise exit timing |
| ExitSearchInterval | Database search window | Integer (minutes) | Time window around TV signal to search for precise exit (default: 5) |
| ExitPriceSource | Price source for exit detection | SPOT/FUTURE | SPOT uses close price, FUTURE uses OHLC data (default: SPOT) |

## TV Signals Sheet (List of trades)

| Column | Description | Valid Options | Notes |
|--------|-------------|--------------|-------|
| Trade # | Trade identifier | Any unique ID | Used to match entry/exit pairs |
| Type | Signal type | Entry Long, Exit Long, Entry Short, Exit Short, Manual Entry, Manual Exit | Determines trade direction and action |
| Date/Time | Signal timestamp | Date/time format matching SignalDateFormat | When signal occurs |
| Contracts | Number of contracts | Integer > 0 | Lot size to use |

## Signal Type Mapping

Signal types in the TV Signals sheet are mapped to internal directions for portfolio selection:

| Signal Type | Internal Direction | Used For |
|-------------|-------------------|---------|
| Entry Long | LONG | LongPortfolioFilePath |
| Exit Long | LONG_EXIT | Paired with Entry Long |
| Entry Short | SHORT | ShortPortfolioFilePath |
| Exit Short | SHORT_EXIT | Paired with Entry Short | 
| Manual Entry | MANUAL | ManualPortfolioFilePath |
| Manual Exit | MANUAL_EXIT | Paired with Manual Entry |

## Synthetic Manual Trades

When ManualTradeEntryTime and ManualTradeLots are specified, the system creates synthetic manual trades for each trading day in the backtest range:

1. Create "Manual Entry" signal at ManualTradeEntryTime for each day
2. Create matching "Manual Exit" signal at IntradayExitTime for each day
3. Use ManualTradeLots for position sizing
4. Process these signals using the strategy from ManualPortfolioFilePath

## Time Adjustment Logic

Several time adjustments can be applied to signals:

1. **FirstTradeEntryTime**: If specified, the first trade each day will start at this time
2. **IncreaseEntrySignalTimeBy**: Add this many seconds to entry signal times
3. **IncreaseExitSignalTimeBy**: Add this many seconds to exit signal times
4. **IntradayExitTime**: When IntradaySqOffApplicable=YES and TvExitApplicable=NO, force exit at this time
5. **RolloverTime**: When DoRollover=YES, use this time for rollovers on expiry days

## Exit Priority Logic

Exit timing follows this priority:

1. **Signal Exit Time**: When TvExitApplicable=YES, use exit signal's time
2. **IntradayExitTime**: When IntradaySqOffApplicable=YES, use this if earlier than signal exit
3. **ExpiryDayExitTime**: On expiry days, use this if applicable
4. **RolloverTime**: When DoRollover=YES, exit at this time on expiry days and re-enter next day

## Database Exit Timing

The Database Exit Timing feature allows the system to use precise exit timing based on actual price movements in the HeavyDB database, rather than relying solely on TV signal timestamps.

### Configuration Parameters

| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| UseDbExitTiming | YES/NO | NO | Enable/disable database exit timing |
| ExitSearchInterval | Integer | 5 | Search window in minutes around TV signal |
| ExitPriceSource | SPOT/FUTURE | SPOT | Price source for exit detection |

### Exit Detection Logic

The system uses different logic based on trade direction and price source:

#### SPOT Price Source
- **Short Trades**: Exit when `spot >= target_price` (price moves up against position)
- **Long Trades**: Exit when `spot <= target_price` (price moves down against position)

#### FUTURE Price Source  
- **Short Trades**: Exit when `future_high >= target_price` (intraday high touches target)
- **Long Trades**: Exit when `future_low <= target_price` (intraday low touches target)

### Target Price Calculation

Since TV signals don't contain explicit target prices, the system uses percentage-based movement detection:

1. Get baseline spot price at TV signal time
2. Calculate small movement threshold (0.1% for testing)
3. For short trades: `target_price = baseline + threshold`
4. For long trades: `target_price = baseline - threshold`

### Search Window

The system searches within a configurable time window around the TV signal:
- Default: ±5 minutes around TV signal time
- Configurable via `ExitSearchInterval` parameter
- Always uses the FIRST matching price movement

### Fallback Behavior

If no precise exit time is found:
- System falls back to original TV signal time
- No error is thrown - graceful degradation
- Logged as informational message

### SQL Implementation

```sql
-- Get baseline spot price at TV signal time
SELECT spot
FROM nifty_option_chain
WHERE trade_date = DATE '2024-01-19'
    AND trade_time = TIME '09:20:00'
    AND spot IS NOT NULL
LIMIT 1;

-- Find precise exit time based on price movement
SELECT trade_time, future_high as exit_price
FROM nifty_option_chain
WHERE trade_date = DATE '2024-01-19'
    AND trade_time BETWEEN TIME '09:15:00' AND TIME '09:25:00'
    AND future_high >= 21628.0  -- Target price for short trade
    AND future_high IS NOT NULL
ORDER BY trade_time ASC
LIMIT 1;
```

### Usage Example

```
-- Configuration in TV Setting sheet:
UseDbExitTiming: YES
ExitSearchInterval: 5  
ExitPriceSource: FUTURE

-- Result:
Original TV signal time: 09:20:00
Precise database exit time: 09:18:00
Time improvement: 2 minutes earlier
```

## Rollover Handling

When DoRollover=YES, trades spanning multiple expiries are split into segments:

1. First segment: Entry time to RolloverTime on current expiry
2. Next segment: RolloverTime on next trading day to RolloverTime on next expiry or original exit time
3. Process continues until original exit time is reached
4. Each segment is treated as a separate trade for P&L calculation

## HeavyDB Implementation Details

### TV Setting Field Mappings

| Excel Column | Model Field | HeavyDB Treatment | SQL Pattern/Transformation |
|--------------|-------------|-------------------|----------------------------|
| Name | name | String identifier | Used in result tracking and file naming |
| SignalFilePath | signal_file_path | File path string | Used to load signals, not used in SQL |
| StartDate | start_date | DATE conversion | DD_MM_YYYY → DATE '2025-04-01' |
| EndDate | end_date | DATE conversion | DD_MM_YYYY → DATE '2025-04-01' |
| SignalDateFormat | signal_date_format | Format string | Used for datetime parsing, not in SQL |
| IntradaySqOffApplicable | intraday_sqoff_applicable | Boolean conversion | YES/yes → true, NO/no → false |
| IntradayExitTime | intraday_exit_time | TIME conversion | HHMMSS → TIME '15:30:00' |
| TvExitApplicable | tv_exit_applicable | Boolean conversion | YES/yes → true, NO/no → false |
| DoRollover | do_rollover | Boolean conversion | YES/yes → true, NO/no → false |
| RolloverTime | rollover_time | TIME conversion | HHMMSS → TIME '15:30:00' |
| ExpiryDayExitTime | expiry_day_exit_time | TIME conversion | HHMMSS → TIME '15:30:00' |
| ManualTradeEntryTime | manual_trade_entry_time | TIME conversion | HHMMSS → TIME '09:30:00' |
| ManualTradeLots | manual_trade_lots | Integer conversion | Direct numeric value |
| FirstTradeEntryTime | first_trade_entry_time | TIME conversion | HHMMSS → TIME '09:15:00' |
| IncreaseEntrySignalTimeBy | increase_entry_signal_time_by | Integer (seconds) | Used in datetime addition |
| IncreaseExitSignalTimeBy | increase_exit_signal_time_by | Integer (seconds) | Used in datetime addition |
| SlippagePercent | slippage_percent | Float conversion | Direct numeric value for trade execution |
| LongPortfolioFilePath | long_portfolio_file_path | File path string | Used to load portfolio for LONG signals |
| ShortPortfolioFilePath | short_portfolio_file_path | File path string | Used to load portfolio for SHORT signals |
| ManualPortfolioFilePath | manual_portfolio_file_path | File path string | Used to load portfolio for MANUAL signals |
| UseDbExitTiming | use_db_exit_timing | Boolean conversion | YES/yes → true, NO/no → false |
| ExitSearchInterval | exit_search_interval | Integer conversion | Direct numeric value |
| ExitPriceSource | exit_price_source | Enum conversion | Maps to SPOT/FUTURE for trade execution. SPOT uses `nifty_spot` table (open/high/low/close columns) |

### TV Signal Field Mappings

| Excel Column | Model Field | HeavyDB Treatment | SQL Pattern/Transformation |
|--------------|-------------|-------------------|----------------------------|
| Trade # | trade_no | String identifier | Used for signal pairing, not directly in SQL |
| Type | signal | Enum conversion | Maps to LONG/SHORT/MANUAL for portfolio selection |
| Date/Time | datetime | TIMESTAMP conversion | Parsed using SignalDateFormat |
| Contracts | lots | Integer conversion | Used for position sizing in SQL |

### Processed Signal Model to SQL Mapping

After signals are processed into the ProcessedTvSignalModel:

| Model Field | HeavyDB Treatment | SQL Pattern/Transformation |
|-------------|-------------------|----------------------------|
| entrydate | DATE conversion | YYMMDD → DATE '2025-04-01' |
| entrytime | TIME conversion | HHMMSS → TIME '09:30:00' |
| exitdate | DATE conversion | YYMMDD → DATE '2025-04-01' |
| exittime | TIME conversion | HHMMSS → TIME '15:30:00' |
| lots | Integer value | Used in query: `lots * lot_size` |
| isrollovertrade | Boolean flag | Affects contract selection |
| signal_direction | String enum | Determines portfolio selection |
| original_tradeno | String identifier | Used for result tracking |

## Integration with Strategy Models

Each processed TV signal is applied to a portfolio model loaded from the corresponding strategy Excel file:

1. Signal direction determines which strategy file to use:
   - LONG signals → LongPortfolioFilePath
   - SHORT signals → ShortPortfolioFilePath
   - MANUAL signals → ManualPortfolioFilePath

2. The portfolio model is modified to apply signal-specific parameters:
   - Entry time from signal's entrytime
   - Exit time from signal's exittime
   - Lot size from signal's lots
   - Date range from signal's entrydate to exitdate

3. Strategy parameters in the selected portfolio apply:
   - Strike selection methods (see Strike Selection Logic below)
   - Risk management settings
   - Option legs and their relationships

## Strike Selection Logic in TV Backtesting

TV backtesting inherits strike selection logic from the referenced strategy files. The strike selection follows the standardized ATM offset logic:

### ATM Strike Selection with Standardized Offset

When the referenced strategy uses `StrikeMethod = 'ATM'` with `StrikeValue`:

| StrikeValue | CE Selection | PE Selection | Description |
|-------------|--------------|--------------|-------------|
| 0 | ATM | ATM | At-The-Money strike |
| 1 | OTM1 (ATM + 1 step) | OTM1 (ATM - 1 step) | 1 step Out-of-The-Money |
| 2 | OTM2 (ATM + 2 steps) | OTM2 (ATM - 2 steps) | 2 steps Out-of-The-Money |
| -1 | ITM1 (ATM - 1 step) | ITM1 (ATM + 1 step) | 1 step In-The-Money |
| -2 | ITM2 (ATM - 2 steps) | ITM2 (ATM + 2 steps) | 2 steps In-The-Money |

### ATM Width (Straddle Width) Calculation

The ATM Width method calculates strikes based on ATM straddle premium:

**Formula:**
- **Positive Multiplier (OTM Selection):**
  - CE Strike = ATM + (Straddle Premium × Multiplier)
  - PE Strike = ATM - (Straddle Premium × Multiplier)
  
- **Negative Multiplier (ITM Selection):**
  - CE Strike = ATM - (Straddle Premium × |Multiplier|)
  - PE Strike = ATM + (Straddle Premium × |Multiplier|)

**Example (BANKNIFTY):**
- Spot: 35520, ATM: 35500
- ATM CE Premium: 310, ATM PE Premium: 465
- Straddle Premium: 775

With `StrikeValue = 0.5`:
- CE Strike = 35500 + (775 × 0.5) = 35887.5 → 35900 (OTM)
- PE Strike = 35500 - (775 × 0.5) = 35112.5 → 35100 (OTM)

With `StrikeValue = -0.5`:
- CE Strike = 35500 - (775 × 0.5) = 35112.5 → 35100 (ITM)
- PE Strike = 35500 + (775 × 0.5) = 35887.5 → 35900 (ITM)

### Other Strike Methods

TV backtesting also supports other strike selection methods defined in the strategy files:
- **ITM1, ITM2, OTM1, OTM2**: Fixed distance from ATM
- **FIXED**: Specific strike price
- **PREMIUM**: Premium-based selection
- **ATM WIDTH/STRADDLE WIDTH**: Straddle premium-based offset
- **DELTA**: Delta-based selection

For complete details on strike selection methods, refer to the strategy-specific documentation:
- TBS: `column_mapping_ml_tbs.md`
- ORB: `column_mapping_ml_orb.md`
- OI: `column_mapping_ml_oi.md`
- Indicator: `column_mapping_ml_indicator.md`

## TV SQL Query Patterns

### Contract Selection for Rollover Trades

```sql
-- Get current and next expiry dates for rollover logic
WITH expiry_dates AS (
  SELECT 
    MIN(expiry_date) AS current_expiry, 
    MIN(CASE WHEN expiry_date > MIN(expiry_date) THEN expiry_date END) AS next_expiry
  FROM nifty_option_chain
  WHERE expiry_date >= DATE '2025-04-01'
    AND expiry_bucket IN ('CW', 'NW')
),
-- Check if trade date is on expiry date
is_expiry_day AS (
  SELECT 
    DATE '2025-04-01' AS trade_date,
    e.current_expiry = DATE '2025-04-01' AS is_expiry
  FROM expiry_dates e
)
-- Select appropriate expiry based on rollover logic
SELECT 
  oc.*,
  CASE 
    WHEN i.is_expiry AND '15:30:00' >= TIME '15:15:00' AND TRUE -- DoRollover = true
    THEN e.next_expiry
    ELSE e.current_expiry
  END AS target_expiry
FROM nifty_option_chain oc, expiry_dates e, is_expiry_day i
WHERE oc.trade_date = i.trade_date
  AND oc.trade_time = TIME '15:30:00'
  AND oc.expiry_date = CASE 
                          WHEN i.is_expiry AND '15:30:00' >= TIME '15:15:00' AND TRUE
                          THEN e.next_expiry
                          ELSE e.current_expiry
                        END
```

### Holiday Calendar Access for Rollover Planning

```sql
-- Get holidays between current date and far future date for rollover planning
SELECT holiday_date
FROM nse_holidays
WHERE holiday_date BETWEEN DATE '2025-04-01' AND DATE '2025-06-01'
ORDER BY holiday_date
```

### Trading Day Calculation for Next Day After Expiry

```sql
-- Find next trading day after expiry
WITH dates_to_check AS (
  SELECT 
    expiry_date + INTERVAL '1 day' AS check_date,
    ROW_NUMBER() OVER (ORDER BY expiry_date) AS rn
  FROM (
    SELECT DISTINCT expiry_date
    FROM nifty_option_chain
    WHERE expiry_date >= DATE '2025-04-01'
    LIMIT 10
  ) e
),
is_trading_day AS (
  SELECT 
    d.check_date,
    d.rn,
    EXTRACT(DOW FROM d.check_date) NOT IN (0, 6) -- 0=Sunday, 6=Saturday
    AND NOT EXISTS (
      SELECT 1 FROM nse_holidays h
      WHERE h.holiday_date = d.check_date
    ) AS is_trading
  FROM dates_to_check d
)
SELECT 
  check_date,
  CASE WHEN is_trading THEN check_date
       ELSE LEAD(check_date) OVER (ORDER BY rn) 
  END AS next_trading_day
FROM is_trading_day
WHERE is_trading OR rn = 1
ORDER BY check_date
LIMIT 1
```

## Common Validation Rules for TV Settings

1. **StartDate** must be before **EndDate**
2. **IntradaySqOffApplicable** must be YES if **TvExitApplicable** is NO, otherwise trades won't exit
3. **DoRollover** and **IntradaySqOffApplicable** cannot both be YES
4. **DoRollover** requires **RolloverTime** to be set
5. **FirstTradeEntryTime** and **ManualTradeEntryTime** cannot both be set
6. Signal CSV/Excel must have signals in date range with proper signal type pairs (Entry/Exit)

## Performance Considerations

1. **Signal Count Management**:
   - Large signal files (1000+ signals) can cause performance issues
   - Filter signals by date range in Excel before backtest if possible

2. **Rollover Optimization**:
   - Rollover processing creates multiple segments per trade
   - Each segment requires independent processing
   - Use targeted date ranges for better performance

3. **Portfolio Complexity**:
   - Simpler portfolios (fewer legs) process faster
   - Complex multi-leg strategies with many conditions are slower

4. **Database Exit Timing**:
   - Adds 2 additional HeavyDB queries per exit signal (baseline + search)
   - Performance impact proportional to search window size
   - Consider disabling for large signal volumes if precision isn't critical
   - FUTURE price source queries are slightly more complex than SPOT

## Common Issues and Resolutions

1. **Problem**: Signals not exiting at expected times
   - **Cause**: TvExitApplicable=NO but no IntradayExitTime
   - **Fix**: Set IntradayExitTime or make TvExitApplicable=YES

2. **Problem**: Rollover not continuing trades
   - **Cause**: Missing expiry dates or holidays in database
   - **Fix**: Update holiday calendar and verify expiry dates in option chain

3. **Problem**: Signal direction not matching portfolio
   - **Cause**: SignalType format mismatch or missing portfolio path
   - **Fix**: Check Signal Type values match expected format and all portfolio paths are valid

4. **Problem**: Manual trades not appearing
   - **Cause**: ManualTradeEntryTime and ManualTradeLots must both be set
   - **Fix**: Set both parameters and ensure ManualPortfolioFilePath is valid

## API Integration Notes

TV backtesting can be used with external signal sources by:

1. Converting API signals to Excel format with proper columns
2. Setting correct SignalDateFormat to parse API timestamps
3. Using ManualTradeEntryTime for consistent entry timing
4. Using system-generated exit times with TvExitApplicable=NO

## Signal File Requirements

The signal file (referenced by SignalFilePath) must have these characteristics:

1. **Format**: Excel workbook with a sheet named "List of trades"
2. **Column Names**: Must include "Trade #", "Type", "Date/Time", and "Contracts"
3. **Signal Pairs**: For each Trade #, there must be exactly one entry and one exit signal
4. **Timestamp Format**: Must match SignalDateFormat, typically YYYYMMDD HHMMSS
5. **Signal Types**: Must be one of the valid signal types (Entry Long, Exit Long, etc.) 