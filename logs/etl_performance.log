2025-06-04 09:21:20,443 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-04 09:21:20,613 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-04 09:21:20,664 - __main__ - INFO - Connected to HeavyDB
2025-06-04 09:21:20,838 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-04 09:21:20,838 - __main__ - INFO - Metrics table ready
2025-06-04 09:21:21,105 - etl_performance_monitor - INFO - Metrics table ready
2025-06-04 09:21:21,121 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:8001
 * Running on http://**************:8001
2025-06-04 09:21:21,121 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-04 09:21:21,122 - werkzeug - INFO -  * Restarting with stat
2025-06-04 09:21:21,607 - __main__ - ERROR - Failed to analyze trends: Execution failed on sql: 
                SELECT 
                    DATE_TRUNC('hour', metric_time) as hour,
                    AVG(rows_per_second) as avg_speed,
                    AVG(cpu_percent) as avg_cpu,
                    AVG(gpu_utilization) as avg_gpu,
                    SUM(rows_processed) as total_rows,
                    COUNT(*) as operations
                FROM etl_performance_metrics
                WHERE metric_time >= NOW() - INTERVAL '7' DAY
                    AND status = 'success'
                GROUP BY hour
                ORDER BY hour
            
SQL Error: Encountered "hour" at line 2, column 59.
Was expecting one of:
    <QUOTED_STRING> ...
    <BRACKET_QUOTED_IDENTIFIER> ...
    <QUOTED_IDENTIFIER> ...
    <BACK_QUOTED_IDENTIFIER> ...
    <IDENTIFIER> ...
    <UNICODE_QUOTED_IDENTIFIER> ...
    
unable to rollback
2025-06-04 09:21:21,934 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-04 09:21:22,151 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-04 09:21:22,156 - etl_performance_monitor - INFO - Metrics table ready
2025-06-04 09:21:22,168 - werkzeug - WARNING -  * Debugger is active!
2025-06-04 09:21:22,273 - werkzeug - INFO -  * Debugger PIN: 129-519-026
2025-06-04 09:21:34,547 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-04 09:21:34,786 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-04 09:21:34,792 - etl_performance_monitor - INFO - Metrics table ready
2025-06-04 09:30:02,072 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-04 09:30:02,287 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-04 09:30:02,292 - etl_performance_monitor - INFO - Metrics table ready
2025-06-04 09:36:40,158 - werkzeug - INFO - ************** - - [04/Jun/2025 09:36:40] "GET / HTTP/1.1" 200 -
2025-06-04 09:36:40,991 - __main__ - ERROR - Error generating resource chart: Execution failed on sql: 
            SELECT 
                DATE_TRUNC('hour', metric_time) as hour,
                AVG(cpu_percent) as avg_cpu,
                AVG(gpu_utilization) as avg_gpu,
                AVG(memory_mb/1024) as avg_memory_gb
            FROM etl_performance_metrics
            WHERE metric_time >= NOW() - INTERVAL '24' HOUR
            GROUP BY hour
            ORDER BY hour
        
Error while skipping unknown field
unable to rollback
2025-06-04 09:36:40,992 - werkzeug - INFO - ************** - - [04/Jun/2025 09:36:40] "GET /api/charts/resources HTTP/1.1" 200 -
2025-06-04 09:36:40,996 - werkzeug - INFO - ************** - - [04/Jun/2025 09:36:40] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-06-04 09:36:41,048 - etl_performance_monitor - ERROR - Failed to get performance summary: Execution failed on sql: 
                SELECT 
                    index_name,
                    operation,
                    COUNT(*) as operation_count,
                    SUM(rows_processed) as total_rows,
                    AVG(rows_per_second) as avg_rows_per_sec,
                    MAX(rows_per_second) as max_rows_per_sec,
                    MIN(rows_per_second) as min_rows_per_sec,
                    AVG(duration_seconds) as avg_duration,
                    AVG(cpu_percent) as avg_cpu,
                    AVG(memory_mb) as avg_memory_mb,
                    AVG(gpu_utilization) as avg_gpu_util,
                    SUM(error_count) as total_errors
                FROM etl_performance_metrics
                WHERE metric_time >= NOW() - INTERVAL '168' HOUR
                GROUP BY index_name, operation
                ORDER BY index_name, operation
            
SQL Error: Encountered "hour" at line 2, column 55.
Was expecting one of:
    <QUOTED_STRING> ...
    <BRACKET_QUOTED_IDENTIFIER> ...
    <QUOTED_IDENTIFIER> ...
    <BACK_QUOTED_IDENTIFIER> ...
    <IDENTIFIER> ...
    <UNICODE_QUOTED_IDENTIFIER> ...
    
unable to rollback
2025-06-04 09:36:41,051 - werkzeug - INFO - ************** - - [04/Jun/2025 09:36:41] "GET /api/charts/operations HTTP/1.1" 200 -
2025-06-04 09:37:00,879 - werkzeug - INFO - ************** - - [04/Jun/2025 09:37:00] "GET / HTTP/1.1" 200 -
2025-06-04 09:37:01,175 - etl_performance_monitor - ERROR - Failed to check alerts: sql_execute failed: unknown result
2025-06-04 09:37:01,175 - werkzeug - INFO - ************** - - [04/Jun/2025 09:37:01] "GET /api/alerts HTTP/1.1" 200 -
2025-06-04 09:37:01,180 - etl_performance_monitor - ERROR - Failed to get performance summary: Execution failed on sql: 
                SELECT 
                    index_name,
                    operation,
                    COUNT(*) as operation_count,
                    SUM(rows_processed) as total_rows,
                    AVG(rows_per_second) as avg_rows_per_sec,
                    MAX(rows_per_second) as max_rows_per_sec,
                    MIN(rows_per_second) as min_rows_per_sec,
                    AVG(duration_seconds) as avg_duration,
                    AVG(cpu_percent) as avg_cpu,
                    AVG(memory_mb) as avg_memory_mb,
                    AVG(gpu_utilization) as avg_gpu_util,
                    SUM(error_count) as total_errors
                FROM etl_performance_metrics
                WHERE metric_time >= NOW() - INTERVAL '24' HOUR
                GROUP BY index_name, operation
                ORDER BY index_name, operation
            
SQL Error: Encountered "hour" at line 2, column 55.
Was expecting one of:
    <QUOTED_STRING> ...
    <BRACKET_QUOTED_IDENTIFIER> ...
    <QUOTED_IDENTIFIER> ...
    <BACK_QUOTED_IDENTIFIER> ...
    <IDENTIFIER> ...
    <UNICODE_QUOTED_IDENTIFIER> ...
    
unable to rollback
2025-06-04 09:37:01,182 - werkzeug - INFO - ************** - - [04/Jun/2025 09:37:01] "GET /api/performance HTTP/1.1" 200 -
2025-06-04 09:37:31,145 - etl_performance_monitor - ERROR - Failed to get performance summary: Execution failed on sql: 
                SELECT 
                    index_name,
                    operation,
                    COUNT(*) as operation_count,
                    SUM(rows_processed) as total_rows,
                    AVG(rows_per_second) as avg_rows_per_sec,
                    MAX(rows_per_second) as max_rows_per_sec,
                    MIN(rows_per_second) as min_rows_per_sec,
                    AVG(duration_seconds) as avg_duration,
                    AVG(cpu_percent) as avg_cpu,
                    AVG(memory_mb) as avg_memory_mb,
                    AVG(gpu_utilization) as avg_gpu_util,
                    SUM(error_count) as total_errors
                FROM etl_performance_metrics
                WHERE metric_time >= NOW() - INTERVAL '24' HOUR
                GROUP BY index_name, operation
                ORDER BY index_name, operation
            
sql_execute failed: unknown result
unable to rollback
2025-06-04 09:37:31,146 - werkzeug - INFO - ************** - - [04/Jun/2025 09:37:31] "GET /api/performance HTTP/1.1" 200 -
2025-06-04 09:38:40,852 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-04 09:38:41,071 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-04 09:38:41,076 - etl_performance_monitor - INFO - Metrics table ready
2025-06-04 09:38:41,089 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:8001
 * Running on http://**************:8001
2025-06-04 09:38:41,089 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-04 09:38:41,089 - werkzeug - INFO -  * Restarting with stat
2025-06-04 09:38:41,887 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-04 09:38:42,102 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-04 09:38:42,108 - etl_performance_monitor - INFO - Metrics table ready
2025-06-04 09:38:42,120 - werkzeug - WARNING -  * Debugger is active!
2025-06-04 09:38:42,120 - werkzeug - INFO -  * Debugger PIN: 129-519-026
2025-06-04 09:38:46,555 - werkzeug - INFO - ************** - - [04/Jun/2025 09:38:46] "GET / HTTP/1.1" 200 -
2025-06-04 09:38:46,762 - werkzeug - INFO - ************** - - [04/Jun/2025 09:38:46] "GET /api/performance HTTP/1.1" 200 -
2025-06-04 09:38:46,770 - __main__ - ERROR - Error generating resource chart: Execution failed on sql: 
            SELECT 
                CAST(metric_time AS DATE) as day, EXTRACT(HOUR FROM metric_time) as hour_of_day,
                AVG(cpu_percent) as avg_cpu,
                AVG(gpu_utilization) as avg_gpu,
                AVG(memory_mb/1024) as avg_memory_gb
            FROM etl_performance_metrics
            WHERE metric_time >= NOW() - INTERVAL '24' HOUR
            GROUP BY day, hour_of_day
ORDER BY day, hour_of_day
        
SQL Error: From line 3, column 34 to line 3, column 52: Interval field value 168 exceeds precision of HOUR(2) field
unable to rollback
2025-06-04 09:38:46,771 - werkzeug - INFO - ************** - - [04/Jun/2025 09:38:46] "GET /api/charts/resources HTTP/1.1" 200 -
2025-06-04 09:38:46,811 - __main__ - ERROR - Error generating speed chart: Execution failed on sql: 
            SELECT 
                CAST(metric_time AS DATE) as day, EXTRACT(HOUR FROM metric_time) as hour_of_day,
                index_name,
                AVG(rows_per_second) as avg_speed
            FROM etl_performance_metrics
            WHERE metric_time >= NOW() - INTERVAL '7' DAY
                AND status = 'success'
                AND rows_per_second > 0
            GROUP BY day, hour_of_day, index_name
ORDER BY day, hour_of_day
        
Error while skipping unknown field
unable to rollback
2025-06-04 09:38:46,811 - werkzeug - INFO - ************** - - [04/Jun/2025 09:38:46] "GET /api/charts/speed HTTP/1.1" 200 -
2025-06-04 09:38:46,819 - etl_performance_monitor - ERROR - Failed to get performance summary: Execution failed on sql: 
                SELECT 
                    index_name,
                    operation,
                    COUNT(*) as operation_count,
                    SUM(rows_processed) as total_rows,
                    AVG(rows_per_second) as avg_rows_per_sec,
                    MAX(rows_per_second) as max_rows_per_sec,
                    MIN(rows_per_second) as min_rows_per_sec,
                    AVG(duration_seconds) as avg_duration,
                    AVG(cpu_percent) as avg_cpu,
                    AVG(memory_mb) as avg_memory_mb,
                    AVG(gpu_utilization) as avg_gpu_util,
                    SUM(error_count) as total_errors
                FROM etl_performance_metrics
                WHERE metric_time >= NOW() - INTERVAL '168' HOUR
                GROUP BY index_name, operation
                ORDER BY index_name, operation
            
SQL Error: Encountered "day" at line 2, column 46.
Was expecting one of:
    <QUOTED_STRING> ...
    <BRACKET_QUOTED_IDENTIFIER> ...
    <QUOTED_IDENTIFIER> ...
    <BACK_QUOTED_IDENTIFIER> ...
    <IDENTIFIER> ...
    <UNICODE_QUOTED_IDENTIFIER> ...
    
unable to rollback
2025-06-04 09:38:46,821 - werkzeug - INFO - ************** - - [04/Jun/2025 09:38:46] "GET /api/charts/operations HTTP/1.1" 200 -
2025-06-04 09:39:16,764 - etl_performance_monitor - WARNING - Could not check freshness for NIFTY: SQL Error: Encountered "day" at line 2, column 46.
Was expecting one of:
    <QUOTED_STRING> ...
    <BRACKET_QUOTED_IDENTIFIER> ...
    <QUOTED_IDENTIFIER> ...
    <BACK_QUOTED_IDENTIFIER> ...
    <IDENTIFIER> ...
    <UNICODE_QUOTED_IDENTIFIER> ...
    
2025-06-04 09:39:16,791 - __main__ - ERROR - Error generating speed chart: Execution failed on sql: 
            SELECT 
                CAST(metric_time AS DATE) as day, EXTRACT(HOUR FROM metric_time) as hour_of_day,
                index_name,
                AVG(rows_per_second) as avg_speed
            FROM etl_performance_metrics
            WHERE metric_time >= NOW() - INTERVAL '7' DAY
                AND status = 'success'
                AND rows_per_second > 0
            GROUP BY day, hour_of_day, index_name
ORDER BY day, hour_of_day
        
Error while skipping unknown field
unable to rollback
2025-06-04 09:39:16,792 - werkzeug - INFO - ************** - - [04/Jun/2025 09:39:16] "GET /api/charts/speed HTTP/1.1" 200 -
2025-06-04 09:55:55,471 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-04 09:55:55,692 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-04 09:55:55,702 - etl_performance_monitor - INFO - Metrics table ready
2025-06-04 09:55:55,717 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:8001
 * Running on http://**************:8001
2025-06-04 09:55:55,717 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-04 09:55:55,717 - werkzeug - INFO -  * Restarting with stat
2025-06-04 09:55:56,518 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-04 09:55:56,741 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-04 09:55:56,745 - etl_performance_monitor - INFO - Metrics table ready
2025-06-04 09:55:56,756 - werkzeug - WARNING -  * Debugger is active!
2025-06-04 09:55:56,757 - werkzeug - INFO -  * Debugger PIN: 129-519-026
2025-06-04 09:56:00,560 - werkzeug - INFO - ************** - - [04/Jun/2025 09:56:00] "GET / HTTP/1.1" 200 -
2025-06-04 09:56:00,649 - werkzeug - INFO - ************** - - [04/Jun/2025 09:56:00] "GET /api/charts/speed HTTP/1.1" 200 -
2025-06-04 09:56:00,650 - werkzeug - INFO - ************** - - [04/Jun/2025 09:56:00] "GET /api/charts/operations HTTP/1.1" 200 -
2025-06-04 09:56:00,652 - werkzeug - INFO - ************** - - [04/Jun/2025 09:56:00] "GET /api/charts/resources HTTP/1.1" 200 -
2025-06-04 09:56:00,679 - __main__ - ERROR - Error getting performance: 'operation_count'
2025-06-04 09:56:00,679 - werkzeug - INFO - ************** - - [04/Jun/2025 09:56:00] "GET /api/performance HTTP/1.1" 200 -
2025-06-04 09:56:00,931 - etl_performance_monitor - WARNING - Could not check freshness for MIDCAPNIFTY: Error while skipping unknown field
2025-06-04 09:56:30,654 - werkzeug - INFO - ************** - - [04/Jun/2025 09:56:30] "GET /api/charts/resources HTTP/1.1" 200 -
2025-06-04 09:56:30,655 - werkzeug - INFO - ************** - - [04/Jun/2025 09:56:30] "GET /api/charts/operations HTTP/1.1" 200 -
2025-06-04 09:56:30,656 - werkzeug - INFO - ************** - - [04/Jun/2025 09:56:30] "GET /api/charts/speed HTTP/1.1" 200 -
2025-06-04 09:56:30,688 - werkzeug - INFO - ************** - - [04/Jun/2025 09:56:30] "GET /api/performance HTTP/1.1" 200 -
2025-06-04 09:57:00,654 - werkzeug - INFO - ************** - - [04/Jun/2025 09:57:00] "GET /api/charts/speed HTTP/1.1" 200 -
2025-06-04 09:57:00,657 - werkzeug - INFO - ************** - - [04/Jun/2025 09:57:00] "GET /api/charts/resources HTTP/1.1" 200 -
2025-06-04 09:57:00,659 - werkzeug - INFO - ************** - - [04/Jun/2025 09:57:00] "GET /api/charts/operations HTTP/1.1" 200 -
2025-06-04 10:00:02,385 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-04 10:00:02,603 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-04 10:00:02,607 - etl_performance_monitor - INFO - Metrics table ready
2025-06-04 10:24:01,397 - werkzeug - INFO -  * Detected change in '/srv/samba/shared/bt/backtester_stable/BTRUN/BTRunPortfolio_GPU.py', reloading
2025-06-04 10:24:01,515 - werkzeug - INFO -  * Restarting with stat
2025-06-04 10:24:02,286 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-04 10:24:02,502 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-04 10:24:02,507 - etl_performance_monitor - INFO - Metrics table ready
2025-06-04 10:24:02,518 - werkzeug - WARNING -  * Debugger is active!
2025-06-04 10:24:02,518 - werkzeug - INFO -  * Debugger PIN: 129-519-026
2025-06-04 10:30:02,682 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-04 10:30:02,898 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-04 10:30:02,903 - etl_performance_monitor - INFO - Metrics table ready
2025-06-04 11:00:01,906 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-04 11:00:02,122 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-04 11:00:02,127 - etl_performance_monitor - INFO - Metrics table ready
2025-06-04 11:26:38,566 - werkzeug - INFO -  * Detected change in '/srv/samba/shared/etl_performance_monitor.py', reloading
2025-06-04 11:26:38,681 - werkzeug - INFO -  * Restarting with stat
2025-06-04 11:29:57,603 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-04 11:29:57,828 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-04 11:29:57,834 - etl_performance_monitor - INFO - Metrics table ready
2025-06-04 11:29:57,849 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:8001
 * Running on http://**************:8001
2025-06-04 11:29:57,849 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-04 11:29:57,849 - werkzeug - INFO -  * Restarting with stat
2025-06-04 11:29:58,670 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-04 11:29:58,897 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-04 11:29:58,901 - etl_performance_monitor - INFO - Metrics table ready
2025-06-04 11:29:58,913 - werkzeug - WARNING -  * Debugger is active!
2025-06-04 11:29:58,914 - werkzeug - INFO -  * Debugger PIN: 129-519-026
2025-06-04 11:30:02,157 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-04 11:30:02,385 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-04 11:30:02,389 - etl_performance_monitor - INFO - Metrics table ready
2025-06-04 11:30:02,450 - etl_performance_monitor - ERROR - Error in optimized freshness query: SQL Error: Encountered "query_timeout_ms" at line 1, column 13.
Was expecting one of:
    "=" ...
    "." ...
    
2025-06-04 11:30:02,452 - etl_performance_monitor - WARNING - Could not check freshness for NIFTY: SQL Error: Encountered "query_timeout_ms" at line 1, column 13.
Was expecting one of:
    "=" ...
    "." ...
    
2025-06-04 11:30:02,455 - etl_performance_monitor - WARNING - Could not check freshness for BANKNIFTY: SQL Error: Encountered "query_timeout_ms" at line 1, column 13.
Was expecting one of:
    "=" ...
    "." ...
    
2025-06-04 11:30:02,458 - etl_performance_monitor - WARNING - Could not check freshness for MIDCAPNIFTY: SQL Error: Encountered "query_timeout_ms" at line 1, column 13.
Was expecting one of:
    "=" ...
    "." ...
    
2025-06-04 11:30:02,461 - etl_performance_monitor - WARNING - Could not check freshness for SENSEX: SQL Error: Encountered "query_timeout_ms" at line 1, column 13.
Was expecting one of:
    "=" ...
    "." ...
    
2025-06-04 11:30:02,463 - etl_performance_monitor - WARNING - Could not check freshness for FINNIFTY: SQL Error: Encountered "query_timeout_ms" at line 1, column 13.
Was expecting one of:
    "=" ...
    "." ...
    
2025-06-04 11:30:02,466 - etl_performance_monitor - WARNING - Could not check freshness for BANKEX: SQL Error: Encountered "query_timeout_ms" at line 1, column 13.
Was expecting one of:
    "=" ...
    "." ...
    
2025-06-04 11:30:25,161 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-04 11:30:25,387 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-04 11:30:25,392 - etl_performance_monitor - INFO - Metrics table ready
2025-06-04 11:30:52,336 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-04 11:30:52,562 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-04 11:30:52,567 - etl_performance_monitor - INFO - Metrics table ready
2025-06-04 11:30:52,581 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:8001
 * Running on http://**************:8001
2025-06-04 11:30:52,581 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-04 11:30:52,582 - werkzeug - INFO -  * Restarting with stat
2025-06-04 11:30:53,376 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-04 11:30:53,602 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-04 11:30:53,606 - etl_performance_monitor - INFO - Metrics table ready
2025-06-04 11:30:53,618 - werkzeug - WARNING -  * Debugger is active!
2025-06-04 11:30:53,618 - werkzeug - INFO -  * Debugger PIN: 129-519-026
2025-06-04 11:34:49,293 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-04 11:34:49,522 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-04 11:34:49,527 - etl_performance_monitor - INFO - Metrics table ready
2025-06-04 11:35:29,340 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-04 11:35:29,566 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-04 11:35:29,571 - etl_performance_monitor - INFO - Metrics table ready
2025-06-04 11:35:29,585 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:8001
 * Running on http://**************:8001
2025-06-04 11:35:29,585 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-04 11:35:29,586 - werkzeug - INFO -  * Restarting with stat
2025-06-04 11:35:30,388 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-04 11:35:30,614 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-04 11:35:30,619 - etl_performance_monitor - INFO - Metrics table ready
2025-06-04 11:35:30,630 - werkzeug - WARNING -  * Debugger is active!
2025-06-04 11:35:30,631 - werkzeug - INFO -  * Debugger PIN: 129-519-026
2025-06-04 11:36:12,215 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-04 11:36:12,447 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-04 11:36:12,451 - etl_performance_monitor - INFO - Metrics table ready
2025-06-04 11:36:43,882 - etl_performance_monitor - ERROR - Error in optimized freshness query: SQL Error: Encountered "query_timeout_ms" at line 1, column 13.
Was expecting one of:
    "=" ...
    "." ...
    
2025-06-04 11:36:43,884 - etl_performance_monitor - WARNING - Could not check freshness for NIFTY: SQL Error: Encountered "query_timeout_ms" at line 1, column 13.
Was expecting one of:
    "=" ...
    "." ...
    
2025-06-04 11:36:43,886 - etl_performance_monitor - WARNING - Could not check freshness for BANKNIFTY: SQL Error: Encountered "query_timeout_ms" at line 1, column 13.
Was expecting one of:
    "=" ...
    "." ...
    
2025-06-04 11:36:43,888 - etl_performance_monitor - WARNING - Could not check freshness for MIDCAPNIFTY: SQL Error: Encountered "query_timeout_ms" at line 1, column 13.
Was expecting one of:
    "=" ...
    "." ...
    
2025-06-04 11:36:43,890 - etl_performance_monitor - WARNING - Could not check freshness for SENSEX: SQL Error: Encountered "query_timeout_ms" at line 1, column 13.
Was expecting one of:
    "=" ...
    "." ...
    
2025-06-04 11:36:43,892 - etl_performance_monitor - WARNING - Could not check freshness for FINNIFTY: SQL Error: Encountered "query_timeout_ms" at line 1, column 13.
Was expecting one of:
    "=" ...
    "." ...
    
2025-06-04 11:36:43,894 - etl_performance_monitor - WARNING - Could not check freshness for BANKEX: SQL Error: Encountered "query_timeout_ms" at line 1, column 13.
Was expecting one of:
    "=" ...
    "." ...
    
2025-06-04 11:36:43,899 - werkzeug - INFO - 127.0.0.1 - - [04/Jun/2025 11:36:43] "GET /api/freshness HTTP/1.1" 200 -
2025-06-04 11:36:49,856 - etl_performance_monitor - ERROR - Error in optimized freshness query: SQL Error: Encountered "query_timeout_ms" at line 1, column 13.
Was expecting one of:
    "=" ...
    "." ...
    
2025-06-04 11:36:49,858 - etl_performance_monitor - WARNING - Could not check freshness for NIFTY: SQL Error: Encountered "query_timeout_ms" at line 1, column 13.
Was expecting one of:
    "=" ...
    "." ...
    
2025-06-04 11:36:49,861 - etl_performance_monitor - WARNING - Could not check freshness for BANKNIFTY: SQL Error: Encountered "query_timeout_ms" at line 1, column 13.
Was expecting one of:
    "=" ...
    "." ...
    
2025-06-04 11:36:49,863 - etl_performance_monitor - WARNING - Could not check freshness for MIDCAPNIFTY: SQL Error: Encountered "query_timeout_ms" at line 1, column 13.
Was expecting one of:
    "=" ...
    "." ...
    
2025-06-04 11:36:49,866 - etl_performance_monitor - WARNING - Could not check freshness for SENSEX: SQL Error: Encountered "query_timeout_ms" at line 1, column 13.
Was expecting one of:
    "=" ...
    "." ...
    
2025-06-04 11:36:49,869 - etl_performance_monitor - WARNING - Could not check freshness for FINNIFTY: SQL Error: Encountered "query_timeout_ms" at line 1, column 13.
Was expecting one of:
    "=" ...
    "." ...
    
2025-06-04 11:36:49,871 - etl_performance_monitor - WARNING - Could not check freshness for BANKEX: SQL Error: Encountered "query_timeout_ms" at line 1, column 13.
Was expecting one of:
    "=" ...
    "." ...
    
2025-06-04 11:36:49,873 - werkzeug - INFO - 127.0.0.1 - - [04/Jun/2025 11:36:49] "GET /api/freshness HTTP/1.1" 200 -
2025-06-04 11:37:12,039 - werkzeug - INFO -  * Detected change in '/srv/samba/shared/etl_performance_monitor.py', reloading
2025-06-04 11:37:12,159 - werkzeug - INFO -  * Restarting with stat
2025-06-04 11:37:12,960 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-04 11:37:13,184 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-04 11:37:13,189 - etl_performance_monitor - INFO - Metrics table ready
2025-06-04 11:37:13,201 - werkzeug - WARNING -  * Debugger is active!
2025-06-04 11:37:13,201 - werkzeug - INFO -  * Debugger PIN: 129-519-026
2025-06-04 11:37:18,998 - werkzeug - INFO -  * Detected change in '/srv/samba/shared/etl_performance_monitor.py', reloading
2025-06-04 11:37:19,116 - werkzeug - INFO -  * Restarting with stat
2025-06-04 11:37:19,919 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-04 11:37:20,145 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-04 11:37:20,148 - etl_performance_monitor - INFO - Metrics table ready
2025-06-04 11:37:20,160 - werkzeug - WARNING -  * Debugger is active!
2025-06-04 11:37:20,161 - werkzeug - INFO -  * Debugger PIN: 129-519-026
2025-06-04 11:37:23,884 - werkzeug - INFO - 127.0.0.1 - - [04/Jun/2025 11:37:23] "GET /api/freshness HTTP/1.1" 200 -
2025-06-04 11:37:34,308 - werkzeug - INFO - 127.0.0.1 - - [04/Jun/2025 11:37:34] "GET /api/freshness HTTP/1.1" 200 -
2025-06-04 11:37:51,945 - werkzeug - INFO -  * Detected change in '/srv/samba/shared/etl_dashboard.py', reloading
2025-06-04 11:37:52,068 - werkzeug - INFO -  * Restarting with stat
2025-06-04 11:37:52,894 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-04 11:37:53,119 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-04 11:37:53,123 - etl_performance_monitor - INFO - Metrics table ready
2025-06-04 12:00:02,336 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-04 12:00:02,340 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-04 12:00:02,569 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-04 12:00:02,574 - __main__ - INFO - Connected to HeavyDB
2025-06-04 12:00:02,574 - etl_performance_monitor - INFO - Metrics table ready
2025-06-04 12:00:02,597 - __main__ - INFO - Metrics table ready
2025-06-04 12:00:02,746 - __main__ - ERROR - Failed to analyze trends: Execution failed on sql: 
                SELECT 
                    CAST(metric_time AS DATE) as day, EXTRACT(HOUR FROM metric_time) as hour_of_day,
                    AVG(rows_per_second) as avg_speed,
                    AVG(cpu_percent) as avg_cpu,
                    AVG(gpu_utilization) as avg_gpu,
                    SUM(rows_processed) as total_rows,
                    COUNT(*) as operations
                FROM etl_performance_metrics
                WHERE metric_time >= NOW() - INTERVAL '7' DAY
                    AND status = 'success'
                GROUP BY day, hour_of_day
ORDER BY day, hour_of_day
            
SQL Error: Encountered "day" at line 2, column 50.
Was expecting one of:
    <QUOTED_STRING> ...
    <BRACKET_QUOTED_IDENTIFIER> ...
    <QUOTED_IDENTIFIER> ...
    <BACK_QUOTED_IDENTIFIER> ...
    <IDENTIFIER> ...
    <UNICODE_QUOTED_IDENTIFIER> ...
    
unable to rollback
2025-06-04 12:30:01,798 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-04 12:30:02,024 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-04 12:30:02,028 - etl_performance_monitor - INFO - Metrics table ready
2025-06-04 13:00:02,029 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-04 13:00:02,261 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-04 13:00:02,266 - etl_performance_monitor - INFO - Metrics table ready
2025-06-04 13:27:36,769 - etl_performance_monitor - ERROR - Error in optimized freshness query: Session not valid or expired.
2025-06-04 13:27:36,769 - etl_performance_monitor - WARNING - Could not check freshness for NIFTY: Session not valid or expired.
2025-06-04 13:27:36,770 - etl_performance_monitor - WARNING - Could not check freshness for BANKNIFTY: Session not valid or expired.
2025-06-04 13:27:36,770 - etl_performance_monitor - WARNING - Could not check freshness for MIDCAPNIFTY: Session not valid or expired.
2025-06-04 13:27:36,770 - etl_performance_monitor - WARNING - Could not check freshness for SENSEX: Session not valid or expired.
2025-06-04 13:27:36,770 - etl_performance_monitor - WARNING - Could not check freshness for FINNIFTY: Session not valid or expired.
2025-06-04 13:27:36,770 - etl_performance_monitor - WARNING - Could not check freshness for BANKEX: Session not valid or expired.
2025-06-04 13:27:36,774 - werkzeug - INFO - 127.0.0.1 - - [04/Jun/2025 13:27:36] "GET /api/freshness HTTP/1.1" 200 -
2025-06-04 13:30:02,378 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-04 13:30:02,603 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-04 13:30:02,607 - etl_performance_monitor - INFO - Metrics table ready
2025-06-04 13:37:05,677 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-04 13:37:05,902 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-04 13:37:06,128 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-04 13:37:06,352 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-04 13:37:06,576 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-04 13:37:06,805 - __main__ - INFO - Metrics table ready
2025-06-04 13:37:06,825 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:8001
 * Running on http://**************:8001
2025-06-04 13:37:06,825 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-04 13:39:19,983 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-04 13:39:20,212 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-04 13:39:20,436 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-04 13:39:20,662 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-04 13:39:20,891 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-04 13:39:21,122 - __main__ - INFO - Metrics table ready
2025-06-04 13:40:39,026 - werkzeug - INFO - 127.0.0.1 - - [04/Jun/2025 13:40:39] "GET /api/freshness HTTP/1.1" 200 -
2025-06-04 13:43:13,788 - werkzeug - INFO - 127.0.0.1 - - [04/Jun/2025 13:43:13] "GET /api/performance HTTP/1.1" 200 -
2025-06-04 13:43:56,675 - werkzeug - INFO - 127.0.0.1 - - [04/Jun/2025 13:43:56] "GET /api/dropbox/files?path=/ HTTP/1.1" 200 -
2025-06-04 13:44:04,538 - werkzeug - INFO - ************** - - [04/Jun/2025 13:44:04] "GET / HTTP/1.1" 200 -
2025-06-04 13:44:04,724 - werkzeug - INFO - ************** - - [04/Jun/2025 13:44:04] "GET /api/charts/speed HTTP/1.1" 200 -
2025-06-04 13:44:04,725 - werkzeug - INFO - ************** - - [04/Jun/2025 13:44:04] "GET /api/charts/resources HTTP/1.1" 200 -
2025-06-04 13:44:04,740 - werkzeug - INFO - ************** - - [04/Jun/2025 13:44:04] "GET /api/alerts HTTP/1.1" 200 -
2025-06-04 13:44:04,749 - werkzeug - INFO - ************** - - [04/Jun/2025 13:44:04] "GET /api/dropbox/files?path=/ HTTP/1.1" 200 -
2025-06-04 13:44:04,848 - werkzeug - INFO - ************** - - [04/Jun/2025 13:44:04] "GET /api/freshness HTTP/1.1" 200 -
2025-06-04 13:44:04,872 - werkzeug - INFO - ************** - - [04/Jun/2025 13:44:04] "GET /api/performance HTTP/1.1" 200 -
2025-06-04 13:44:04,902 - werkzeug - INFO - ************** - - [04/Jun/2025 13:44:04] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-06-04 13:44:17,725 - werkzeug - INFO - ************** - - [04/Jun/2025 13:44:17] "GET /api/dropbox/files?path=/2025 HTTP/1.1" 200 -
2025-06-04 13:44:34,754 - werkzeug - INFO - ************** - - [04/Jun/2025 13:44:34] "GET /api/charts/speed HTTP/1.1" 200 -
2025-06-04 13:44:34,756 - werkzeug - INFO - ************** - - [04/Jun/2025 13:44:34] "GET /api/charts/resources HTTP/1.1" 200 -
2025-06-04 13:44:34,771 - werkzeug - INFO - ************** - - [04/Jun/2025 13:44:34] "GET /api/alerts HTTP/1.1" 200 -
2025-06-04 13:44:34,875 - werkzeug - INFO - ************** - - [04/Jun/2025 13:44:34] "GET /api/freshness HTTP/1.1" 200 -
2025-06-04 13:44:34,893 - werkzeug - INFO - ************** - - [04/Jun/2025 13:44:34] "GET /api/performance HTTP/1.1" 200 -
2025-06-04 13:45:04,756 - werkzeug - INFO - ************** - - [04/Jun/2025 13:45:04] "GET /api/charts/speed HTTP/1.1" 200 -
2025-06-04 13:45:04,757 - werkzeug - INFO - ************** - - [04/Jun/2025 13:45:04] "GET /api/charts/resources HTTP/1.1" 200 -
2025-06-04 13:45:04,775 - werkzeug - INFO - ************** - - [04/Jun/2025 13:45:04] "GET /api/alerts HTTP/1.1" 200 -
2025-06-04 13:45:04,877 - werkzeug - INFO - ************** - - [04/Jun/2025 13:45:04] "GET /api/freshness HTTP/1.1" 200 -
2025-06-04 13:45:04,896 - werkzeug - INFO - ************** - - [04/Jun/2025 13:45:04] "GET /api/performance HTTP/1.1" 200 -
2025-06-04 13:45:34,754 - werkzeug - INFO - ************** - - [04/Jun/2025 13:45:34] "GET /api/charts/speed HTTP/1.1" 200 -
2025-06-04 13:45:34,755 - werkzeug - INFO - ************** - - [04/Jun/2025 13:45:34] "GET /api/charts/resources HTTP/1.1" 200 -
2025-06-04 13:45:34,768 - werkzeug - INFO - ************** - - [04/Jun/2025 13:45:34] "GET /api/alerts HTTP/1.1" 200 -
2025-06-04 13:45:34,872 - werkzeug - INFO - ************** - - [04/Jun/2025 13:45:34] "GET /api/freshness HTTP/1.1" 200 -
2025-06-04 13:45:34,893 - werkzeug - INFO - ************** - - [04/Jun/2025 13:45:34] "GET /api/performance HTTP/1.1" 200 -
2025-06-04 13:46:04,754 - werkzeug - INFO - ************** - - [04/Jun/2025 13:46:04] "GET /api/charts/speed HTTP/1.1" 200 -
2025-06-04 13:46:04,755 - werkzeug - INFO - ************** - - [04/Jun/2025 13:46:04] "GET /api/charts/resources HTTP/1.1" 200 -
2025-06-04 13:46:04,770 - werkzeug - INFO - ************** - - [04/Jun/2025 13:46:04] "GET /api/alerts HTTP/1.1" 200 -
2025-06-04 13:46:04,874 - werkzeug - INFO - ************** - - [04/Jun/2025 13:46:04] "GET /api/freshness HTTP/1.1" 200 -
2025-06-04 13:46:04,895 - werkzeug - INFO - ************** - - [04/Jun/2025 13:46:04] "GET /api/performance HTTP/1.1" 200 -
2025-06-04 13:46:34,754 - werkzeug - INFO - ************** - - [04/Jun/2025 13:46:34] "GET /api/charts/speed HTTP/1.1" 200 -
2025-06-04 13:46:34,755 - werkzeug - INFO - ************** - - [04/Jun/2025 13:46:34] "GET /api/charts/resources HTTP/1.1" 200 -
2025-06-04 13:46:34,765 - werkzeug - INFO - ************** - - [04/Jun/2025 13:46:34] "GET /api/alerts HTTP/1.1" 200 -
2025-06-04 13:46:34,872 - werkzeug - INFO - ************** - - [04/Jun/2025 13:46:34] "GET /api/freshness HTTP/1.1" 200 -
2025-06-04 13:46:34,893 - werkzeug - INFO - ************** - - [04/Jun/2025 13:46:34] "GET /api/performance HTTP/1.1" 200 -
2025-06-04 13:47:04,755 - werkzeug - INFO - ************** - - [04/Jun/2025 13:47:04] "GET /api/charts/speed HTTP/1.1" 200 -
2025-06-04 13:47:04,756 - werkzeug - INFO - ************** - - [04/Jun/2025 13:47:04] "GET /api/charts/resources HTTP/1.1" 200 -
2025-06-04 13:47:04,772 - werkzeug - INFO - ************** - - [04/Jun/2025 13:47:04] "GET /api/alerts HTTP/1.1" 200 -
2025-06-04 13:47:04,874 - werkzeug - INFO - ************** - - [04/Jun/2025 13:47:04] "GET /api/freshness HTTP/1.1" 200 -
2025-06-04 13:47:04,891 - werkzeug - INFO - ************** - - [04/Jun/2025 13:47:04] "GET /api/performance HTTP/1.1" 200 -
2025-06-04 13:47:34,754 - werkzeug - INFO - ************** - - [04/Jun/2025 13:47:34] "GET /api/charts/speed HTTP/1.1" 200 -
2025-06-04 13:47:34,755 - werkzeug - INFO - ************** - - [04/Jun/2025 13:47:34] "GET /api/charts/resources HTTP/1.1" 200 -
2025-06-04 13:47:34,767 - werkzeug - INFO - ************** - - [04/Jun/2025 13:47:34] "GET /api/alerts HTTP/1.1" 200 -
2025-06-04 13:47:34,874 - werkzeug - INFO - ************** - - [04/Jun/2025 13:47:34] "GET /api/freshness HTTP/1.1" 200 -
2025-06-04 13:47:34,896 - werkzeug - INFO - ************** - - [04/Jun/2025 13:47:34] "GET /api/performance HTTP/1.1" 200 -
2025-06-04 13:48:04,755 - werkzeug - INFO - ************** - - [04/Jun/2025 13:48:04] "GET /api/charts/speed HTTP/1.1" 200 -
2025-06-04 13:48:04,756 - werkzeug - INFO - ************** - - [04/Jun/2025 13:48:04] "GET /api/charts/resources HTTP/1.1" 200 -
2025-06-04 13:48:04,770 - werkzeug - INFO - ************** - - [04/Jun/2025 13:48:04] "GET /api/alerts HTTP/1.1" 200 -
2025-06-04 13:48:04,876 - werkzeug - INFO - ************** - - [04/Jun/2025 13:48:04] "GET /api/freshness HTTP/1.1" 200 -
2025-06-04 13:48:04,897 - werkzeug - INFO - ************** - - [04/Jun/2025 13:48:04] "GET /api/performance HTTP/1.1" 200 -
2025-06-04 13:48:34,755 - werkzeug - INFO - ************** - - [04/Jun/2025 13:48:34] "GET /api/charts/speed HTTP/1.1" 200 -
2025-06-04 13:48:34,756 - werkzeug - INFO - ************** - - [04/Jun/2025 13:48:34] "GET /api/charts/resources HTTP/1.1" 200 -
2025-06-04 13:48:34,771 - werkzeug - INFO - ************** - - [04/Jun/2025 13:48:34] "GET /api/alerts HTTP/1.1" 200 -
2025-06-04 13:48:34,873 - werkzeug - INFO - ************** - - [04/Jun/2025 13:48:34] "GET /api/freshness HTTP/1.1" 200 -
2025-06-04 13:48:34,894 - werkzeug - INFO - ************** - - [04/Jun/2025 13:48:34] "GET /api/performance HTTP/1.1" 200 -
2025-06-04 13:49:04,754 - werkzeug - INFO - ************** - - [04/Jun/2025 13:49:04] "GET /api/charts/speed HTTP/1.1" 200 -
2025-06-04 13:49:04,755 - werkzeug - INFO - ************** - - [04/Jun/2025 13:49:04] "GET /api/charts/resources HTTP/1.1" 200 -
2025-06-04 13:49:04,772 - werkzeug - INFO - ************** - - [04/Jun/2025 13:49:04] "GET /api/alerts HTTP/1.1" 200 -
2025-06-04 13:49:04,876 - werkzeug - INFO - ************** - - [04/Jun/2025 13:49:04] "GET /api/freshness HTTP/1.1" 200 -
2025-06-04 13:49:04,898 - werkzeug - INFO - ************** - - [04/Jun/2025 13:49:04] "GET /api/performance HTTP/1.1" 200 -
2025-06-04 13:49:34,754 - werkzeug - INFO - ************** - - [04/Jun/2025 13:49:34] "GET /api/charts/speed HTTP/1.1" 200 -
2025-06-04 13:49:34,755 - werkzeug - INFO - ************** - - [04/Jun/2025 13:49:34] "GET /api/charts/resources HTTP/1.1" 200 -
2025-06-04 13:49:34,771 - werkzeug - INFO - ************** - - [04/Jun/2025 13:49:34] "GET /api/alerts HTTP/1.1" 200 -
2025-06-04 13:49:34,873 - werkzeug - INFO - ************** - - [04/Jun/2025 13:49:34] "GET /api/freshness HTTP/1.1" 200 -
2025-06-04 13:49:34,897 - werkzeug - INFO - ************** - - [04/Jun/2025 13:49:34] "GET /api/performance HTTP/1.1" 200 -
2025-06-04 13:50:04,754 - werkzeug - INFO - ************** - - [04/Jun/2025 13:50:04] "GET /api/charts/speed HTTP/1.1" 200 -
2025-06-04 13:50:04,755 - werkzeug - INFO - ************** - - [04/Jun/2025 13:50:04] "GET /api/charts/resources HTTP/1.1" 200 -
2025-06-04 13:50:04,771 - werkzeug - INFO - ************** - - [04/Jun/2025 13:50:04] "GET /api/alerts HTTP/1.1" 200 -
2025-06-04 13:50:04,876 - werkzeug - INFO - ************** - - [04/Jun/2025 13:50:04] "GET /api/freshness HTTP/1.1" 200 -
2025-06-04 13:50:04,897 - werkzeug - INFO - ************** - - [04/Jun/2025 13:50:04] "GET /api/performance HTTP/1.1" 200 -
2025-06-04 13:50:34,754 - werkzeug - INFO - ************** - - [04/Jun/2025 13:50:34] "GET /api/charts/speed HTTP/1.1" 200 -
2025-06-04 13:50:34,755 - werkzeug - INFO - ************** - - [04/Jun/2025 13:50:34] "GET /api/charts/resources HTTP/1.1" 200 -
2025-06-04 13:50:34,771 - werkzeug - INFO - ************** - - [04/Jun/2025 13:50:34] "GET /api/alerts HTTP/1.1" 200 -
2025-06-04 13:50:34,873 - werkzeug - INFO - ************** - - [04/Jun/2025 13:50:34] "GET /api/freshness HTTP/1.1" 200 -
2025-06-04 13:50:34,896 - werkzeug - INFO - ************** - - [04/Jun/2025 13:50:34] "GET /api/performance HTTP/1.1" 200 -
2025-06-04 13:51:04,754 - werkzeug - INFO - ************** - - [04/Jun/2025 13:51:04] "GET /api/charts/speed HTTP/1.1" 200 -
2025-06-04 13:51:04,755 - werkzeug - INFO - ************** - - [04/Jun/2025 13:51:04] "GET /api/charts/resources HTTP/1.1" 200 -
2025-06-04 13:51:04,772 - werkzeug - INFO - ************** - - [04/Jun/2025 13:51:04] "GET /api/alerts HTTP/1.1" 200 -
2025-06-04 13:51:04,875 - werkzeug - INFO - ************** - - [04/Jun/2025 13:51:04] "GET /api/freshness HTTP/1.1" 200 -
2025-06-04 13:51:04,896 - werkzeug - INFO - ************** - - [04/Jun/2025 13:51:04] "GET /api/performance HTTP/1.1" 200 -
2025-06-04 13:51:34,754 - werkzeug - INFO - ************** - - [04/Jun/2025 13:51:34] "GET /api/charts/speed HTTP/1.1" 200 -
2025-06-04 13:51:34,755 - werkzeug - INFO - ************** - - [04/Jun/2025 13:51:34] "GET /api/charts/resources HTTP/1.1" 200 -
2025-06-04 13:51:34,771 - werkzeug - INFO - ************** - - [04/Jun/2025 13:51:34] "GET /api/alerts HTTP/1.1" 200 -
2025-06-04 13:51:34,872 - werkzeug - INFO - ************** - - [04/Jun/2025 13:51:34] "GET /api/freshness HTTP/1.1" 200 -
2025-06-04 13:51:34,893 - werkzeug - INFO - ************** - - [04/Jun/2025 13:51:34] "GET /api/performance HTTP/1.1" 200 -
2025-06-04 13:52:04,754 - werkzeug - INFO - ************** - - [04/Jun/2025 13:52:04] "GET /api/charts/speed HTTP/1.1" 200 -
2025-06-04 13:52:04,756 - werkzeug - INFO - ************** - - [04/Jun/2025 13:52:04] "GET /api/charts/resources HTTP/1.1" 200 -
2025-06-04 13:52:04,771 - werkzeug - INFO - ************** - - [04/Jun/2025 13:52:04] "GET /api/alerts HTTP/1.1" 200 -
2025-06-04 13:52:04,877 - werkzeug - INFO - ************** - - [04/Jun/2025 13:52:04] "GET /api/freshness HTTP/1.1" 200 -
2025-06-04 13:52:04,900 - werkzeug - INFO - ************** - - [04/Jun/2025 13:52:04] "GET /api/performance HTTP/1.1" 200 -
2025-06-04 13:52:34,755 - werkzeug - INFO - ************** - - [04/Jun/2025 13:52:34] "GET /api/charts/speed HTTP/1.1" 200 -
2025-06-04 13:52:34,756 - werkzeug - INFO - ************** - - [04/Jun/2025 13:52:34] "GET /api/charts/resources HTTP/1.1" 200 -
2025-06-04 13:52:34,771 - werkzeug - INFO - ************** - - [04/Jun/2025 13:52:34] "GET /api/alerts HTTP/1.1" 200 -
2025-06-04 13:52:34,876 - werkzeug - INFO - ************** - - [04/Jun/2025 13:52:34] "GET /api/freshness HTTP/1.1" 200 -
2025-06-04 13:52:34,897 - werkzeug - INFO - ************** - - [04/Jun/2025 13:52:34] "GET /api/performance HTTP/1.1" 200 -
2025-06-04 13:53:04,754 - werkzeug - INFO - ************** - - [04/Jun/2025 13:53:04] "GET /api/charts/speed HTTP/1.1" 200 -
2025-06-04 13:53:04,755 - werkzeug - INFO - ************** - - [04/Jun/2025 13:53:04] "GET /api/charts/resources HTTP/1.1" 200 -
2025-06-04 13:53:04,770 - werkzeug - INFO - ************** - - [04/Jun/2025 13:53:04] "GET /api/alerts HTTP/1.1" 200 -
2025-06-04 13:53:04,877 - werkzeug - INFO - ************** - - [04/Jun/2025 13:53:04] "GET /api/freshness HTTP/1.1" 200 -
2025-06-04 13:53:04,900 - werkzeug - INFO - ************** - - [04/Jun/2025 13:53:04] "GET /api/performance HTTP/1.1" 200 -
2025-06-04 13:53:34,754 - werkzeug - INFO - ************** - - [04/Jun/2025 13:53:34] "GET /api/charts/resources HTTP/1.1" 200 -
2025-06-04 13:53:34,756 - werkzeug - INFO - ************** - - [04/Jun/2025 13:53:34] "GET /api/charts/speed HTTP/1.1" 200 -
2025-06-04 13:53:34,770 - werkzeug - INFO - ************** - - [04/Jun/2025 13:53:34] "GET /api/alerts HTTP/1.1" 200 -
2025-06-04 13:53:34,875 - werkzeug - INFO - ************** - - [04/Jun/2025 13:53:34] "GET /api/freshness HTTP/1.1" 200 -
2025-06-04 13:53:34,898 - werkzeug - INFO - ************** - - [04/Jun/2025 13:53:34] "GET /api/performance HTTP/1.1" 200 -
2025-06-04 13:54:04,755 - werkzeug - INFO - ************** - - [04/Jun/2025 13:54:04] "GET /api/charts/speed HTTP/1.1" 200 -
2025-06-04 13:54:04,756 - werkzeug - INFO - ************** - - [04/Jun/2025 13:54:04] "GET /api/charts/resources HTTP/1.1" 200 -
2025-06-04 13:54:04,766 - werkzeug - INFO - ************** - - [04/Jun/2025 13:54:04] "GET /api/alerts HTTP/1.1" 200 -
2025-06-04 13:54:04,872 - werkzeug - INFO - ************** - - [04/Jun/2025 13:54:04] "GET /api/freshness HTTP/1.1" 200 -
2025-06-04 13:54:04,893 - werkzeug - INFO - ************** - - [04/Jun/2025 13:54:04] "GET /api/performance HTTP/1.1" 200 -
2025-06-04 13:54:34,754 - werkzeug - INFO - ************** - - [04/Jun/2025 13:54:34] "GET /api/charts/resources HTTP/1.1" 200 -
2025-06-04 13:54:34,755 - werkzeug - INFO - ************** - - [04/Jun/2025 13:54:34] "GET /api/charts/speed HTTP/1.1" 200 -
2025-06-04 13:54:34,771 - werkzeug - INFO - ************** - - [04/Jun/2025 13:54:34] "GET /api/alerts HTTP/1.1" 200 -
2025-06-04 13:54:34,873 - werkzeug - INFO - ************** - - [04/Jun/2025 13:54:34] "GET /api/freshness HTTP/1.1" 200 -
2025-06-04 13:54:34,896 - werkzeug - INFO - ************** - - [04/Jun/2025 13:54:34] "GET /api/performance HTTP/1.1" 200 -
2025-06-04 13:55:04,754 - werkzeug - INFO - ************** - - [04/Jun/2025 13:55:04] "GET /api/charts/speed HTTP/1.1" 200 -
2025-06-04 13:55:04,756 - werkzeug - INFO - ************** - - [04/Jun/2025 13:55:04] "GET /api/charts/resources HTTP/1.1" 200 -
2025-06-04 13:55:04,770 - werkzeug - INFO - ************** - - [04/Jun/2025 13:55:04] "GET /api/alerts HTTP/1.1" 200 -
2025-06-04 13:55:04,873 - werkzeug - INFO - ************** - - [04/Jun/2025 13:55:04] "GET /api/freshness HTTP/1.1" 200 -
2025-06-04 13:55:04,896 - werkzeug - INFO - ************** - - [04/Jun/2025 13:55:04] "GET /api/performance HTTP/1.1" 200 -
2025-06-04 13:55:34,754 - werkzeug - INFO - ************** - - [04/Jun/2025 13:55:34] "GET /api/charts/speed HTTP/1.1" 200 -
2025-06-04 13:55:34,755 - werkzeug - INFO - ************** - - [04/Jun/2025 13:55:34] "GET /api/charts/resources HTTP/1.1" 200 -
2025-06-04 13:55:34,771 - werkzeug - INFO - ************** - - [04/Jun/2025 13:55:34] "GET /api/alerts HTTP/1.1" 200 -
2025-06-04 13:55:34,872 - werkzeug - INFO - ************** - - [04/Jun/2025 13:55:34] "GET /api/freshness HTTP/1.1" 200 -
2025-06-04 13:55:34,893 - werkzeug - INFO - ************** - - [04/Jun/2025 13:55:34] "GET /api/performance HTTP/1.1" 200 -
2025-06-04 13:56:04,755 - werkzeug - INFO - ************** - - [04/Jun/2025 13:56:04] "GET /api/charts/resources HTTP/1.1" 200 -
2025-06-04 13:56:04,756 - werkzeug - INFO - ************** - - [04/Jun/2025 13:56:04] "GET /api/charts/speed HTTP/1.1" 200 -
2025-06-04 13:56:04,768 - werkzeug - INFO - ************** - - [04/Jun/2025 13:56:04] "GET /api/alerts HTTP/1.1" 200 -
2025-06-04 13:56:04,885 - werkzeug - INFO - ************** - - [04/Jun/2025 13:56:04] "GET /api/freshness HTTP/1.1" 200 -
2025-06-04 13:56:04,906 - werkzeug - INFO - ************** - - [04/Jun/2025 13:56:04] "GET /api/performance HTTP/1.1" 200 -
2025-06-04 13:56:34,755 - werkzeug - INFO - ************** - - [04/Jun/2025 13:56:34] "GET /api/charts/speed HTTP/1.1" 200 -
2025-06-04 13:56:34,755 - werkzeug - INFO - ************** - - [04/Jun/2025 13:56:34] "GET /api/charts/resources HTTP/1.1" 200 -
2025-06-04 13:56:34,771 - werkzeug - INFO - ************** - - [04/Jun/2025 13:56:34] "GET /api/alerts HTTP/1.1" 200 -
2025-06-04 13:56:34,878 - werkzeug - INFO - ************** - - [04/Jun/2025 13:56:34] "GET /api/freshness HTTP/1.1" 200 -
2025-06-04 13:56:34,899 - werkzeug - INFO - ************** - - [04/Jun/2025 13:56:34] "GET /api/performance HTTP/1.1" 200 -
2025-06-04 13:57:04,754 - werkzeug - INFO - ************** - - [04/Jun/2025 13:57:04] "GET /api/charts/speed HTTP/1.1" 200 -
2025-06-04 13:57:04,756 - werkzeug - INFO - ************** - - [04/Jun/2025 13:57:04] "GET /api/charts/resources HTTP/1.1" 200 -
2025-06-04 13:57:04,767 - werkzeug - INFO - ************** - - [04/Jun/2025 13:57:04] "GET /api/alerts HTTP/1.1" 200 -
2025-06-04 13:57:04,873 - werkzeug - INFO - ************** - - [04/Jun/2025 13:57:04] "GET /api/freshness HTTP/1.1" 200 -
2025-06-04 13:57:04,891 - werkzeug - INFO - ************** - - [04/Jun/2025 13:57:04] "GET /api/performance HTTP/1.1" 200 -
2025-06-04 13:57:34,754 - werkzeug - INFO - ************** - - [04/Jun/2025 13:57:34] "GET /api/charts/speed HTTP/1.1" 200 -
2025-06-04 13:57:34,755 - werkzeug - INFO - ************** - - [04/Jun/2025 13:57:34] "GET /api/charts/resources HTTP/1.1" 200 -
2025-06-04 13:57:34,766 - werkzeug - INFO - ************** - - [04/Jun/2025 13:57:34] "GET /api/alerts HTTP/1.1" 200 -
2025-06-04 13:57:34,876 - werkzeug - INFO - ************** - - [04/Jun/2025 13:57:34] "GET /api/freshness HTTP/1.1" 200 -
2025-06-04 13:57:34,893 - werkzeug - INFO - ************** - - [04/Jun/2025 13:57:34] "GET /api/performance HTTP/1.1" 200 -
2025-06-04 13:58:04,754 - werkzeug - INFO - ************** - - [04/Jun/2025 13:58:04] "GET /api/charts/resources HTTP/1.1" 200 -
2025-06-04 13:58:04,755 - werkzeug - INFO - ************** - - [04/Jun/2025 13:58:04] "GET /api/charts/speed HTTP/1.1" 200 -
2025-06-04 13:58:04,767 - werkzeug - INFO - ************** - - [04/Jun/2025 13:58:04] "GET /api/alerts HTTP/1.1" 200 -
2025-06-04 13:58:04,874 - werkzeug - INFO - ************** - - [04/Jun/2025 13:58:04] "GET /api/freshness HTTP/1.1" 200 -
2025-06-04 13:58:04,891 - werkzeug - INFO - ************** - - [04/Jun/2025 13:58:04] "GET /api/performance HTTP/1.1" 200 -
2025-06-04 13:58:34,754 - werkzeug - INFO - ************** - - [04/Jun/2025 13:58:34] "GET /api/charts/speed HTTP/1.1" 200 -
2025-06-04 13:58:34,755 - werkzeug - INFO - ************** - - [04/Jun/2025 13:58:34] "GET /api/charts/resources HTTP/1.1" 200 -
2025-06-04 13:58:34,767 - werkzeug - INFO - ************** - - [04/Jun/2025 13:58:34] "GET /api/alerts HTTP/1.1" 200 -
2025-06-04 13:58:34,877 - werkzeug - INFO - ************** - - [04/Jun/2025 13:58:34] "GET /api/freshness HTTP/1.1" 200 -
2025-06-04 13:58:34,894 - werkzeug - INFO - ************** - - [04/Jun/2025 13:58:34] "GET /api/performance HTTP/1.1" 200 -
2025-06-04 13:59:04,754 - werkzeug - INFO - ************** - - [04/Jun/2025 13:59:04] "GET /api/charts/speed HTTP/1.1" 200 -
2025-06-04 13:59:04,755 - werkzeug - INFO - ************** - - [04/Jun/2025 13:59:04] "GET /api/charts/resources HTTP/1.1" 200 -
2025-06-04 13:59:04,767 - werkzeug - INFO - ************** - - [04/Jun/2025 13:59:04] "GET /api/alerts HTTP/1.1" 200 -
2025-06-04 13:59:04,879 - werkzeug - INFO - ************** - - [04/Jun/2025 13:59:04] "GET /api/freshness HTTP/1.1" 200 -
2025-06-04 13:59:04,901 - werkzeug - INFO - ************** - - [04/Jun/2025 13:59:04] "GET /api/performance HTTP/1.1" 200 -
2025-06-04 13:59:34,754 - werkzeug - INFO - ************** - - [04/Jun/2025 13:59:34] "GET /api/charts/speed HTTP/1.1" 200 -
2025-06-04 13:59:34,755 - werkzeug - INFO - ************** - - [04/Jun/2025 13:59:34] "GET /api/charts/resources HTTP/1.1" 200 -
2025-06-04 13:59:34,771 - werkzeug - INFO - ************** - - [04/Jun/2025 13:59:34] "GET /api/alerts HTTP/1.1" 200 -
2025-06-04 13:59:34,874 - werkzeug - INFO - ************** - - [04/Jun/2025 13:59:34] "GET /api/freshness HTTP/1.1" 200 -
2025-06-04 13:59:34,892 - werkzeug - INFO - ************** - - [04/Jun/2025 13:59:34] "GET /api/performance HTTP/1.1" 200 -
2025-06-04 14:00:02,612 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-04 14:00:02,838 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-04 14:00:02,843 - etl_performance_monitor - INFO - Metrics table ready
2025-06-04 14:00:04,754 - werkzeug - INFO - ************** - - [04/Jun/2025 14:00:04] "GET /api/charts/speed HTTP/1.1" 200 -
2025-06-04 14:00:04,755 - werkzeug - INFO - ************** - - [04/Jun/2025 14:00:04] "GET /api/charts/resources HTTP/1.1" 200 -
2025-06-04 14:00:04,770 - werkzeug - INFO - ************** - - [04/Jun/2025 14:00:04] "GET /api/alerts HTTP/1.1" 200 -
2025-06-04 14:00:04,852 - werkzeug - INFO - ************** - - [04/Jun/2025 14:00:04] "GET /api/freshness HTTP/1.1" 200 -
2025-06-04 14:00:04,875 - werkzeug - INFO - ************** - - [04/Jun/2025 14:00:04] "GET /api/performance HTTP/1.1" 200 -
2025-06-04 14:00:34,754 - werkzeug - INFO - ************** - - [04/Jun/2025 14:00:34] "GET /api/charts/resources HTTP/1.1" 200 -
2025-06-04 14:00:34,755 - werkzeug - INFO - ************** - - [04/Jun/2025 14:00:34] "GET /api/charts/speed HTTP/1.1" 200 -
2025-06-04 14:00:34,770 - werkzeug - INFO - ************** - - [04/Jun/2025 14:00:34] "GET /api/alerts HTTP/1.1" 200 -
2025-06-04 14:00:34,875 - werkzeug - INFO - ************** - - [04/Jun/2025 14:00:34] "GET /api/freshness HTTP/1.1" 200 -
2025-06-04 14:00:34,893 - werkzeug - INFO - ************** - - [04/Jun/2025 14:00:34] "GET /api/performance HTTP/1.1" 200 -
2025-06-04 14:01:04,754 - werkzeug - INFO - ************** - - [04/Jun/2025 14:01:04] "GET /api/charts/speed HTTP/1.1" 200 -
2025-06-04 14:01:04,755 - werkzeug - INFO - ************** - - [04/Jun/2025 14:01:04] "GET /api/charts/resources HTTP/1.1" 200 -
2025-06-04 14:01:04,772 - werkzeug - INFO - ************** - - [04/Jun/2025 14:01:04] "GET /api/alerts HTTP/1.1" 200 -
2025-06-04 14:01:04,876 - werkzeug - INFO - ************** - - [04/Jun/2025 14:01:04] "GET /api/freshness HTTP/1.1" 200 -
2025-06-04 14:01:04,897 - werkzeug - INFO - ************** - - [04/Jun/2025 14:01:04] "GET /api/performance HTTP/1.1" 200 -
2025-06-04 14:01:34,754 - werkzeug - INFO - ************** - - [04/Jun/2025 14:01:34] "GET /api/charts/speed HTTP/1.1" 200 -
2025-06-04 14:01:34,755 - werkzeug - INFO - ************** - - [04/Jun/2025 14:01:34] "GET /api/charts/resources HTTP/1.1" 200 -
2025-06-04 14:01:34,765 - werkzeug - INFO - ************** - - [04/Jun/2025 14:01:34] "GET /api/alerts HTTP/1.1" 200 -
2025-06-04 14:01:34,874 - werkzeug - INFO - ************** - - [04/Jun/2025 14:01:34] "GET /api/freshness HTTP/1.1" 200 -
2025-06-04 14:01:34,892 - werkzeug - INFO - ************** - - [04/Jun/2025 14:01:34] "GET /api/performance HTTP/1.1" 200 -
2025-06-04 14:02:04,754 - werkzeug - INFO - ************** - - [04/Jun/2025 14:02:04] "GET /api/charts/speed HTTP/1.1" 200 -
2025-06-04 14:02:04,755 - werkzeug - INFO - ************** - - [04/Jun/2025 14:02:04] "GET /api/charts/resources HTTP/1.1" 200 -
2025-06-04 14:02:04,771 - werkzeug - INFO - ************** - - [04/Jun/2025 14:02:04] "GET /api/alerts HTTP/1.1" 200 -
2025-06-04 14:02:04,872 - werkzeug - INFO - ************** - - [04/Jun/2025 14:02:04] "GET /api/freshness HTTP/1.1" 200 -
2025-06-04 14:02:04,893 - werkzeug - INFO - ************** - - [04/Jun/2025 14:02:04] "GET /api/performance HTTP/1.1" 200 -
2025-06-04 14:02:34,754 - werkzeug - INFO - ************** - - [04/Jun/2025 14:02:34] "GET /api/charts/resources HTTP/1.1" 200 -
2025-06-04 14:02:34,755 - werkzeug - INFO - ************** - - [04/Jun/2025 14:02:34] "GET /api/charts/speed HTTP/1.1" 200 -
2025-06-04 14:02:34,769 - werkzeug - INFO - ************** - - [04/Jun/2025 14:02:34] "GET /api/alerts HTTP/1.1" 200 -
2025-06-04 14:02:34,874 - werkzeug - INFO - ************** - - [04/Jun/2025 14:02:34] "GET /api/freshness HTTP/1.1" 200 -
2025-06-04 14:02:34,892 - werkzeug - INFO - ************** - - [04/Jun/2025 14:02:34] "GET /api/performance HTTP/1.1" 200 -
2025-06-04 14:03:04,754 - werkzeug - INFO - ************** - - [04/Jun/2025 14:03:04] "GET /api/charts/resources HTTP/1.1" 200 -
2025-06-04 14:03:04,755 - werkzeug - INFO - ************** - - [04/Jun/2025 14:03:04] "GET /api/charts/speed HTTP/1.1" 200 -
2025-06-04 14:03:04,770 - werkzeug - INFO - ************** - - [04/Jun/2025 14:03:04] "GET /api/alerts HTTP/1.1" 200 -
2025-06-04 14:03:04,874 - werkzeug - INFO - ************** - - [04/Jun/2025 14:03:04] "GET /api/freshness HTTP/1.1" 200 -
2025-06-04 14:03:04,895 - werkzeug - INFO - ************** - - [04/Jun/2025 14:03:04] "GET /api/performance HTTP/1.1" 200 -
2025-06-04 14:03:34,754 - werkzeug - INFO - ************** - - [04/Jun/2025 14:03:34] "GET /api/charts/speed HTTP/1.1" 200 -
2025-06-04 14:03:34,756 - werkzeug - INFO - ************** - - [04/Jun/2025 14:03:34] "GET /api/charts/resources HTTP/1.1" 200 -
2025-06-04 14:03:34,771 - werkzeug - INFO - ************** - - [04/Jun/2025 14:03:34] "GET /api/alerts HTTP/1.1" 200 -
2025-06-04 14:03:34,874 - werkzeug - INFO - ************** - - [04/Jun/2025 14:03:34] "GET /api/freshness HTTP/1.1" 200 -
2025-06-04 14:03:34,897 - werkzeug - INFO - ************** - - [04/Jun/2025 14:03:34] "GET /api/performance HTTP/1.1" 200 -
2025-06-04 14:04:04,754 - werkzeug - INFO - ************** - - [04/Jun/2025 14:04:04] "GET /api/charts/speed HTTP/1.1" 200 -
2025-06-04 14:04:04,755 - werkzeug - INFO - ************** - - [04/Jun/2025 14:04:04] "GET /api/charts/resources HTTP/1.1" 200 -
2025-06-04 14:04:04,770 - werkzeug - INFO - ************** - - [04/Jun/2025 14:04:04] "GET /api/alerts HTTP/1.1" 200 -
2025-06-04 14:04:04,873 - werkzeug - INFO - ************** - - [04/Jun/2025 14:04:04] "GET /api/freshness HTTP/1.1" 200 -
2025-06-04 14:04:04,891 - werkzeug - INFO - ************** - - [04/Jun/2025 14:04:04] "GET /api/performance HTTP/1.1" 200 -
2025-06-04 14:04:34,754 - werkzeug - INFO - ************** - - [04/Jun/2025 14:04:34] "GET /api/charts/speed HTTP/1.1" 200 -
2025-06-04 14:04:34,755 - werkzeug - INFO - ************** - - [04/Jun/2025 14:04:34] "GET /api/charts/resources HTTP/1.1" 200 -
2025-06-04 14:04:34,770 - werkzeug - INFO - ************** - - [04/Jun/2025 14:04:34] "GET /api/alerts HTTP/1.1" 200 -
2025-06-04 14:04:34,871 - werkzeug - INFO - ************** - - [04/Jun/2025 14:04:34] "GET /api/freshness HTTP/1.1" 200 -
2025-06-04 14:04:34,892 - werkzeug - INFO - ************** - - [04/Jun/2025 14:04:34] "GET /api/performance HTTP/1.1" 200 -
2025-06-04 14:05:04,754 - werkzeug - INFO - ************** - - [04/Jun/2025 14:05:04] "GET /api/charts/resources HTTP/1.1" 200 -
2025-06-04 14:05:04,756 - werkzeug - INFO - ************** - - [04/Jun/2025 14:05:04] "GET /api/charts/speed HTTP/1.1" 200 -
2025-06-04 14:05:04,770 - werkzeug - INFO - ************** - - [04/Jun/2025 14:05:04] "GET /api/alerts HTTP/1.1" 200 -
2025-06-04 14:05:04,874 - werkzeug - INFO - ************** - - [04/Jun/2025 14:05:04] "GET /api/freshness HTTP/1.1" 200 -
2025-06-04 14:05:04,891 - werkzeug - INFO - ************** - - [04/Jun/2025 14:05:04] "GET /api/performance HTTP/1.1" 200 -
2025-06-04 14:05:34,755 - werkzeug - INFO - ************** - - [04/Jun/2025 14:05:34] "GET /api/charts/speed HTTP/1.1" 200 -
2025-06-04 14:05:34,756 - werkzeug - INFO - ************** - - [04/Jun/2025 14:05:34] "GET /api/charts/resources HTTP/1.1" 200 -
2025-06-04 14:05:34,870 - werkzeug - INFO - ************** - - [04/Jun/2025 14:05:34] "GET /api/freshness HTTP/1.1" 200 -
2025-06-04 14:05:34,874 - werkzeug - INFO - ************** - - [04/Jun/2025 14:05:34] "GET /api/alerts HTTP/1.1" 200 -
2025-06-04 14:05:34,892 - werkzeug - INFO - ************** - - [04/Jun/2025 14:05:34] "GET /api/performance HTTP/1.1" 200 -
2025-06-04 14:06:04,754 - werkzeug - INFO - ************** - - [04/Jun/2025 14:06:04] "GET /api/charts/speed HTTP/1.1" 200 -
2025-06-04 14:06:04,755 - werkzeug - INFO - ************** - - [04/Jun/2025 14:06:04] "GET /api/charts/resources HTTP/1.1" 200 -
2025-06-04 14:06:04,771 - werkzeug - INFO - ************** - - [04/Jun/2025 14:06:04] "GET /api/alerts HTTP/1.1" 200 -
2025-06-04 14:06:04,872 - werkzeug - INFO - ************** - - [04/Jun/2025 14:06:04] "GET /api/freshness HTTP/1.1" 200 -
2025-06-04 14:06:04,894 - werkzeug - INFO - ************** - - [04/Jun/2025 14:06:04] "GET /api/performance HTTP/1.1" 200 -
2025-06-04 14:06:34,754 - werkzeug - INFO - ************** - - [04/Jun/2025 14:06:34] "GET /api/charts/speed HTTP/1.1" 200 -
2025-06-04 14:06:34,756 - werkzeug - INFO - ************** - - [04/Jun/2025 14:06:34] "GET /api/charts/resources HTTP/1.1" 200 -
2025-06-04 14:06:34,875 - werkzeug - INFO - ************** - - [04/Jun/2025 14:06:34] "GET /api/freshness HTTP/1.1" 200 -
2025-06-04 14:06:34,877 - werkzeug - INFO - ************** - - [04/Jun/2025 14:06:34] "GET /api/alerts HTTP/1.1" 200 -
2025-06-04 14:06:34,900 - werkzeug - INFO - ************** - - [04/Jun/2025 14:06:34] "GET /api/performance HTTP/1.1" 200 -
2025-06-04 14:07:04,756 - werkzeug - INFO - ************** - - [04/Jun/2025 14:07:04] "GET /api/charts/speed HTTP/1.1" 200 -
2025-06-04 14:07:04,757 - werkzeug - INFO - ************** - - [04/Jun/2025 14:07:04] "GET /api/charts/resources HTTP/1.1" 200 -
2025-06-04 14:07:04,772 - werkzeug - INFO - ************** - - [04/Jun/2025 14:07:04] "GET /api/alerts HTTP/1.1" 200 -
2025-06-04 14:07:04,876 - werkzeug - INFO - ************** - - [04/Jun/2025 14:07:04] "GET /api/freshness HTTP/1.1" 200 -
2025-06-04 14:07:04,897 - werkzeug - INFO - ************** - - [04/Jun/2025 14:07:04] "GET /api/performance HTTP/1.1" 200 -
2025-06-04 14:07:34,754 - werkzeug - INFO - ************** - - [04/Jun/2025 14:07:34] "GET /api/charts/speed HTTP/1.1" 200 -
2025-06-04 14:07:34,755 - werkzeug - INFO - ************** - - [04/Jun/2025 14:07:34] "GET /api/charts/resources HTTP/1.1" 200 -
2025-06-04 14:07:34,770 - werkzeug - INFO - ************** - - [04/Jun/2025 14:07:34] "GET /api/alerts HTTP/1.1" 200 -
2025-06-04 14:07:34,873 - werkzeug - INFO - ************** - - [04/Jun/2025 14:07:34] "GET /api/freshness HTTP/1.1" 200 -
2025-06-04 14:07:34,893 - werkzeug - INFO - ************** - - [04/Jun/2025 14:07:34] "GET /api/performance HTTP/1.1" 200 -
2025-06-04 14:08:04,755 - werkzeug - INFO - ************** - - [04/Jun/2025 14:08:04] "GET /api/charts/speed HTTP/1.1" 200 -
2025-06-04 14:08:04,756 - werkzeug - INFO - ************** - - [04/Jun/2025 14:08:04] "GET /api/charts/resources HTTP/1.1" 200 -
2025-06-04 14:08:04,772 - werkzeug - INFO - ************** - - [04/Jun/2025 14:08:04] "GET /api/alerts HTTP/1.1" 200 -
2025-06-04 14:08:04,874 - werkzeug - INFO - ************** - - [04/Jun/2025 14:08:04] "GET /api/freshness HTTP/1.1" 200 -
2025-06-04 14:08:04,887 - werkzeug - INFO - ************** - - [04/Jun/2025 14:08:04] "GET /api/performance HTTP/1.1" 200 -
2025-06-04 14:08:34,753 - werkzeug - INFO - ************** - - [04/Jun/2025 14:08:34] "GET /api/charts/speed HTTP/1.1" 200 -
2025-06-04 14:08:34,754 - werkzeug - INFO - ************** - - [04/Jun/2025 14:08:34] "GET /api/charts/resources HTTP/1.1" 200 -
2025-06-04 14:08:34,766 - werkzeug - INFO - ************** - - [04/Jun/2025 14:08:34] "GET /api/alerts HTTP/1.1" 200 -
2025-06-04 14:08:34,872 - werkzeug - INFO - ************** - - [04/Jun/2025 14:08:34] "GET /api/freshness HTTP/1.1" 200 -
2025-06-04 14:08:34,893 - werkzeug - INFO - ************** - - [04/Jun/2025 14:08:34] "GET /api/performance HTTP/1.1" 200 -
2025-06-04 14:09:04,755 - werkzeug - INFO - ************** - - [04/Jun/2025 14:09:04] "GET /api/charts/speed HTTP/1.1" 200 -
2025-06-04 14:09:04,756 - werkzeug - INFO - ************** - - [04/Jun/2025 14:09:04] "GET /api/charts/resources HTTP/1.1" 200 -
2025-06-04 14:09:04,771 - werkzeug - INFO - ************** - - [04/Jun/2025 14:09:04] "GET /api/alerts HTTP/1.1" 200 -
2025-06-04 14:09:04,875 - werkzeug - INFO - ************** - - [04/Jun/2025 14:09:04] "GET /api/freshness HTTP/1.1" 200 -
2025-06-04 14:09:04,895 - werkzeug - INFO - ************** - - [04/Jun/2025 14:09:04] "GET /api/performance HTTP/1.1" 200 -
2025-06-04 14:09:34,755 - werkzeug - INFO - ************** - - [04/Jun/2025 14:09:34] "GET /api/charts/speed HTTP/1.1" 200 -
2025-06-04 14:09:34,756 - werkzeug - INFO - ************** - - [04/Jun/2025 14:09:34] "GET /api/charts/resources HTTP/1.1" 200 -
2025-06-04 14:09:34,768 - werkzeug - INFO - ************** - - [04/Jun/2025 14:09:34] "GET /api/alerts HTTP/1.1" 200 -
2025-06-04 14:09:34,874 - werkzeug - INFO - ************** - - [04/Jun/2025 14:09:34] "GET /api/freshness HTTP/1.1" 200 -
2025-06-04 14:09:34,894 - werkzeug - INFO - ************** - - [04/Jun/2025 14:09:34] "GET /api/performance HTTP/1.1" 200 -
2025-06-04 14:10:04,755 - werkzeug - INFO - ************** - - [04/Jun/2025 14:10:04] "GET /api/charts/speed HTTP/1.1" 200 -
2025-06-04 14:10:04,756 - werkzeug - INFO - ************** - - [04/Jun/2025 14:10:04] "GET /api/charts/resources HTTP/1.1" 200 -
2025-06-04 14:10:04,772 - werkzeug - INFO - ************** - - [04/Jun/2025 14:10:04] "GET /api/alerts HTTP/1.1" 200 -
2025-06-04 14:10:04,878 - werkzeug - INFO - ************** - - [04/Jun/2025 14:10:04] "GET /api/freshness HTTP/1.1" 200 -
2025-06-04 14:10:04,901 - werkzeug - INFO - ************** - - [04/Jun/2025 14:10:04] "GET /api/performance HTTP/1.1" 200 -
2025-06-04 14:10:34,755 - werkzeug - INFO - ************** - - [04/Jun/2025 14:10:34] "GET /api/charts/speed HTTP/1.1" 200 -
2025-06-04 14:10:34,755 - werkzeug - INFO - ************** - - [04/Jun/2025 14:10:34] "GET /api/charts/resources HTTP/1.1" 200 -
2025-06-04 14:10:34,770 - werkzeug - INFO - ************** - - [04/Jun/2025 14:10:34] "GET /api/alerts HTTP/1.1" 200 -
2025-06-04 14:10:34,871 - werkzeug - INFO - ************** - - [04/Jun/2025 14:10:34] "GET /api/freshness HTTP/1.1" 200 -
2025-06-04 14:10:34,881 - werkzeug - INFO - ************** - - [04/Jun/2025 14:10:34] "GET /api/performance HTTP/1.1" 200 -
2025-06-04 14:11:04,755 - werkzeug - INFO - ************** - - [04/Jun/2025 14:11:04] "GET /api/charts/speed HTTP/1.1" 200 -
2025-06-04 14:11:04,756 - werkzeug - INFO - ************** - - [04/Jun/2025 14:11:04] "GET /api/charts/resources HTTP/1.1" 200 -
2025-06-04 14:11:04,769 - werkzeug - INFO - ************** - - [04/Jun/2025 14:11:04] "GET /api/alerts HTTP/1.1" 200 -
2025-06-04 14:11:04,876 - werkzeug - INFO - ************** - - [04/Jun/2025 14:11:04] "GET /api/freshness HTTP/1.1" 200 -
2025-06-04 14:11:04,885 - werkzeug - INFO - ************** - - [04/Jun/2025 14:11:04] "GET /api/performance HTTP/1.1" 200 -
2025-06-04 14:11:34,754 - werkzeug - INFO - ************** - - [04/Jun/2025 14:11:34] "GET /api/charts/speed HTTP/1.1" 200 -
2025-06-04 14:11:34,756 - werkzeug - INFO - ************** - - [04/Jun/2025 14:11:34] "GET /api/charts/resources HTTP/1.1" 200 -
2025-06-04 14:11:34,766 - werkzeug - INFO - ************** - - [04/Jun/2025 14:11:34] "GET /api/alerts HTTP/1.1" 200 -
2025-06-04 14:11:34,872 - werkzeug - INFO - ************** - - [04/Jun/2025 14:11:34] "GET /api/freshness HTTP/1.1" 200 -
2025-06-04 14:11:34,884 - werkzeug - INFO - ************** - - [04/Jun/2025 14:11:34] "GET /api/performance HTTP/1.1" 200 -
2025-06-04 14:12:04,755 - werkzeug - INFO - ************** - - [04/Jun/2025 14:12:04] "GET /api/charts/speed HTTP/1.1" 200 -
2025-06-04 14:12:04,756 - werkzeug - INFO - ************** - - [04/Jun/2025 14:12:04] "GET /api/charts/resources HTTP/1.1" 200 -
2025-06-04 14:12:04,769 - werkzeug - INFO - ************** - - [04/Jun/2025 14:12:04] "GET /api/alerts HTTP/1.1" 200 -
2025-06-04 14:12:04,874 - werkzeug - INFO - ************** - - [04/Jun/2025 14:12:04] "GET /api/freshness HTTP/1.1" 200 -
2025-06-04 14:12:04,885 - werkzeug - INFO - ************** - - [04/Jun/2025 14:12:04] "GET /api/performance HTTP/1.1" 200 -
2025-06-04 14:12:34,754 - werkzeug - INFO - ************** - - [04/Jun/2025 14:12:34] "GET /api/charts/speed HTTP/1.1" 200 -
2025-06-04 14:12:34,755 - werkzeug - INFO - ************** - - [04/Jun/2025 14:12:34] "GET /api/charts/resources HTTP/1.1" 200 -
2025-06-04 14:12:34,770 - werkzeug - INFO - ************** - - [04/Jun/2025 14:12:34] "GET /api/alerts HTTP/1.1" 200 -
2025-06-04 14:12:34,875 - werkzeug - INFO - ************** - - [04/Jun/2025 14:12:34] "GET /api/freshness HTTP/1.1" 200 -
2025-06-04 14:12:34,896 - werkzeug - INFO - ************** - - [04/Jun/2025 14:12:34] "GET /api/performance HTTP/1.1" 200 -
2025-06-04 14:13:04,754 - werkzeug - INFO - ************** - - [04/Jun/2025 14:13:04] "GET /api/charts/speed HTTP/1.1" 200 -
2025-06-04 14:13:04,755 - werkzeug - INFO - ************** - - [04/Jun/2025 14:13:04] "GET /api/charts/resources HTTP/1.1" 200 -
2025-06-04 14:13:04,767 - werkzeug - INFO - ************** - - [04/Jun/2025 14:13:04] "GET /api/alerts HTTP/1.1" 200 -
2025-06-04 14:13:04,872 - werkzeug - INFO - ************** - - [04/Jun/2025 14:13:04] "GET /api/freshness HTTP/1.1" 200 -
2025-06-04 14:13:04,890 - werkzeug - INFO - ************** - - [04/Jun/2025 14:13:04] "GET /api/performance HTTP/1.1" 200 -
2025-06-04 14:13:34,754 - werkzeug - INFO - ************** - - [04/Jun/2025 14:13:34] "GET /api/charts/speed HTTP/1.1" 200 -
2025-06-04 14:13:34,755 - werkzeug - INFO - ************** - - [04/Jun/2025 14:13:34] "GET /api/charts/resources HTTP/1.1" 200 -
2025-06-04 14:13:34,768 - werkzeug - INFO - ************** - - [04/Jun/2025 14:13:34] "GET /api/alerts HTTP/1.1" 200 -
2025-06-04 14:13:34,873 - werkzeug - INFO - ************** - - [04/Jun/2025 14:13:34] "GET /api/freshness HTTP/1.1" 200 -
2025-06-04 14:13:34,893 - werkzeug - INFO - ************** - - [04/Jun/2025 14:13:34] "GET /api/performance HTTP/1.1" 200 -
2025-06-04 14:14:04,755 - werkzeug - INFO - ************** - - [04/Jun/2025 14:14:04] "GET /api/charts/speed HTTP/1.1" 200 -
2025-06-04 14:14:04,756 - werkzeug - INFO - ************** - - [04/Jun/2025 14:14:04] "GET /api/charts/resources HTTP/1.1" 200 -
2025-06-04 14:14:04,769 - werkzeug - INFO - ************** - - [04/Jun/2025 14:14:04] "GET /api/alerts HTTP/1.1" 200 -
2025-06-04 14:14:04,878 - werkzeug - INFO - ************** - - [04/Jun/2025 14:14:04] "GET /api/freshness HTTP/1.1" 200 -
2025-06-04 14:14:04,897 - werkzeug - INFO - ************** - - [04/Jun/2025 14:14:04] "GET /api/performance HTTP/1.1" 200 -
2025-06-04 14:14:34,755 - werkzeug - INFO - ************** - - [04/Jun/2025 14:14:34] "GET /api/charts/speed HTTP/1.1" 200 -
2025-06-04 14:14:34,757 - werkzeug - INFO - ************** - - [04/Jun/2025 14:14:34] "GET /api/charts/resources HTTP/1.1" 200 -
2025-06-04 14:14:34,767 - werkzeug - INFO - ************** - - [04/Jun/2025 14:14:34] "GET /api/alerts HTTP/1.1" 200 -
2025-06-04 14:14:34,872 - werkzeug - INFO - ************** - - [04/Jun/2025 14:14:34] "GET /api/freshness HTTP/1.1" 200 -
2025-06-04 14:14:34,895 - werkzeug - INFO - ************** - - [04/Jun/2025 14:14:34] "GET /api/performance HTTP/1.1" 200 -
2025-06-04 14:15:04,754 - werkzeug - INFO - ************** - - [04/Jun/2025 14:15:04] "GET /api/charts/speed HTTP/1.1" 200 -
2025-06-04 14:15:04,755 - werkzeug - INFO - ************** - - [04/Jun/2025 14:15:04] "GET /api/charts/resources HTTP/1.1" 200 -
2025-06-04 14:15:04,771 - werkzeug - INFO - ************** - - [04/Jun/2025 14:15:04] "GET /api/alerts HTTP/1.1" 200 -
2025-06-04 14:15:04,889 - werkzeug - INFO - ************** - - [04/Jun/2025 14:15:04] "GET /api/freshness HTTP/1.1" 200 -
2025-06-04 14:15:04,912 - werkzeug - INFO - ************** - - [04/Jun/2025 14:15:04] "GET /api/performance HTTP/1.1" 200 -
2025-06-04 14:15:34,754 - werkzeug - INFO - ************** - - [04/Jun/2025 14:15:34] "GET /api/charts/speed HTTP/1.1" 200 -
2025-06-04 14:15:34,756 - werkzeug - INFO - ************** - - [04/Jun/2025 14:15:34] "GET /api/charts/resources HTTP/1.1" 200 -
2025-06-04 14:15:34,774 - werkzeug - INFO - ************** - - [04/Jun/2025 14:15:34] "GET /api/alerts HTTP/1.1" 200 -
2025-06-04 14:15:34,877 - werkzeug - INFO - ************** - - [04/Jun/2025 14:15:34] "GET /api/freshness HTTP/1.1" 200 -
2025-06-04 14:15:34,891 - werkzeug - INFO - ************** - - [04/Jun/2025 14:15:34] "GET /api/performance HTTP/1.1" 200 -
2025-06-04 14:16:04,754 - werkzeug - INFO - ************** - - [04/Jun/2025 14:16:04] "GET /api/charts/speed HTTP/1.1" 200 -
2025-06-04 14:16:04,755 - werkzeug - INFO - ************** - - [04/Jun/2025 14:16:04] "GET /api/charts/resources HTTP/1.1" 200 -
2025-06-04 14:16:04,770 - werkzeug - INFO - ************** - - [04/Jun/2025 14:16:04] "GET /api/alerts HTTP/1.1" 200 -
2025-06-04 14:16:04,872 - werkzeug - INFO - ************** - - [04/Jun/2025 14:16:04] "GET /api/freshness HTTP/1.1" 200 -
2025-06-04 14:16:04,895 - werkzeug - INFO - ************** - - [04/Jun/2025 14:16:04] "GET /api/performance HTTP/1.1" 200 -
2025-06-04 14:16:34,754 - werkzeug - INFO - ************** - - [04/Jun/2025 14:16:34] "GET /api/charts/speed HTTP/1.1" 200 -
2025-06-04 14:16:34,755 - werkzeug - INFO - ************** - - [04/Jun/2025 14:16:34] "GET /api/charts/resources HTTP/1.1" 200 -
2025-06-04 14:16:34,770 - werkzeug - INFO - ************** - - [04/Jun/2025 14:16:34] "GET /api/alerts HTTP/1.1" 200 -
2025-06-04 14:16:34,872 - werkzeug - INFO - ************** - - [04/Jun/2025 14:16:34] "GET /api/freshness HTTP/1.1" 200 -
2025-06-04 14:16:34,893 - werkzeug - INFO - ************** - - [04/Jun/2025 14:16:34] "GET /api/performance HTTP/1.1" 200 -
2025-06-04 14:17:04,754 - werkzeug - INFO - ************** - - [04/Jun/2025 14:17:04] "GET /api/charts/speed HTTP/1.1" 200 -
2025-06-04 14:17:04,755 - werkzeug - INFO - ************** - - [04/Jun/2025 14:17:04] "GET /api/charts/resources HTTP/1.1" 200 -
2025-06-04 14:17:04,770 - werkzeug - INFO - ************** - - [04/Jun/2025 14:17:04] "GET /api/alerts HTTP/1.1" 200 -
2025-06-04 14:17:04,876 - werkzeug - INFO - ************** - - [04/Jun/2025 14:17:04] "GET /api/freshness HTTP/1.1" 200 -
2025-06-04 14:17:04,893 - werkzeug - INFO - ************** - - [04/Jun/2025 14:17:04] "GET /api/performance HTTP/1.1" 200 -
2025-06-04 14:17:34,754 - werkzeug - INFO - ************** - - [04/Jun/2025 14:17:34] "GET /api/charts/speed HTTP/1.1" 200 -
2025-06-04 14:17:34,755 - werkzeug - INFO - ************** - - [04/Jun/2025 14:17:34] "GET /api/charts/resources HTTP/1.1" 200 -
2025-06-04 14:17:34,770 - werkzeug - INFO - ************** - - [04/Jun/2025 14:17:34] "GET /api/alerts HTTP/1.1" 200 -
2025-06-04 14:17:34,873 - werkzeug - INFO - ************** - - [04/Jun/2025 14:17:34] "GET /api/freshness HTTP/1.1" 200 -
2025-06-04 14:17:34,890 - werkzeug - INFO - ************** - - [04/Jun/2025 14:17:34] "GET /api/performance HTTP/1.1" 200 -
2025-06-04 14:18:04,755 - werkzeug - INFO - ************** - - [04/Jun/2025 14:18:04] "GET /api/charts/speed HTTP/1.1" 200 -
2025-06-04 14:18:04,756 - werkzeug - INFO - ************** - - [04/Jun/2025 14:18:04] "GET /api/charts/resources HTTP/1.1" 200 -
2025-06-04 14:18:04,769 - werkzeug - INFO - ************** - - [04/Jun/2025 14:18:04] "GET /api/alerts HTTP/1.1" 200 -
2025-06-04 14:18:04,873 - werkzeug - INFO - ************** - - [04/Jun/2025 14:18:04] "GET /api/freshness HTTP/1.1" 200 -
2025-06-04 14:18:04,896 - werkzeug - INFO - ************** - - [04/Jun/2025 14:18:04] "GET /api/performance HTTP/1.1" 200 -
2025-06-04 14:18:34,754 - werkzeug - INFO - ************** - - [04/Jun/2025 14:18:34] "GET /api/charts/speed HTTP/1.1" 200 -
2025-06-04 14:18:34,755 - werkzeug - INFO - ************** - - [04/Jun/2025 14:18:34] "GET /api/charts/resources HTTP/1.1" 200 -
2025-06-04 14:18:34,771 - werkzeug - INFO - ************** - - [04/Jun/2025 14:18:34] "GET /api/alerts HTTP/1.1" 200 -
2025-06-04 14:18:34,872 - werkzeug - INFO - ************** - - [04/Jun/2025 14:18:34] "GET /api/freshness HTTP/1.1" 200 -
2025-06-04 14:18:34,889 - werkzeug - INFO - ************** - - [04/Jun/2025 14:18:34] "GET /api/performance HTTP/1.1" 200 -
2025-06-04 14:19:04,754 - werkzeug - INFO - ************** - - [04/Jun/2025 14:19:04] "GET /api/charts/speed HTTP/1.1" 200 -
2025-06-04 14:19:04,756 - werkzeug - INFO - ************** - - [04/Jun/2025 14:19:04] "GET /api/charts/resources HTTP/1.1" 200 -
2025-06-04 14:19:04,767 - werkzeug - INFO - ************** - - [04/Jun/2025 14:19:04] "GET /api/alerts HTTP/1.1" 200 -
2025-06-04 14:19:04,874 - werkzeug - INFO - ************** - - [04/Jun/2025 14:19:04] "GET /api/freshness HTTP/1.1" 200 -
2025-06-04 14:19:04,895 - werkzeug - INFO - ************** - - [04/Jun/2025 14:19:04] "GET /api/performance HTTP/1.1" 200 -
2025-06-04 14:19:34,755 - werkzeug - INFO - ************** - - [04/Jun/2025 14:19:34] "GET /api/charts/speed HTTP/1.1" 200 -
2025-06-04 14:19:34,756 - werkzeug - INFO - ************** - - [04/Jun/2025 14:19:34] "GET /api/charts/resources HTTP/1.1" 200 -
2025-06-04 14:19:34,768 - werkzeug - INFO - ************** - - [04/Jun/2025 14:19:34] "GET /api/alerts HTTP/1.1" 200 -
2025-06-04 14:19:34,877 - werkzeug - INFO - ************** - - [04/Jun/2025 14:19:34] "GET /api/freshness HTTP/1.1" 200 -
2025-06-04 14:19:34,893 - werkzeug - INFO - ************** - - [04/Jun/2025 14:19:34] "GET /api/performance HTTP/1.1" 200 -
2025-06-04 14:20:04,754 - werkzeug - INFO - ************** - - [04/Jun/2025 14:20:04] "GET /api/charts/speed HTTP/1.1" 200 -
2025-06-04 14:20:04,755 - werkzeug - INFO - ************** - - [04/Jun/2025 14:20:04] "GET /api/charts/resources HTTP/1.1" 200 -
2025-06-04 14:20:04,768 - werkzeug - INFO - ************** - - [04/Jun/2025 14:20:04] "GET /api/alerts HTTP/1.1" 200 -
2025-06-04 14:20:04,872 - werkzeug - INFO - ************** - - [04/Jun/2025 14:20:04] "GET /api/freshness HTTP/1.1" 200 -
2025-06-04 14:20:04,893 - werkzeug - INFO - ************** - - [04/Jun/2025 14:20:04] "GET /api/performance HTTP/1.1" 200 -
2025-06-04 14:20:34,754 - werkzeug - INFO - ************** - - [04/Jun/2025 14:20:34] "GET /api/charts/speed HTTP/1.1" 200 -
2025-06-04 14:20:34,755 - werkzeug - INFO - ************** - - [04/Jun/2025 14:20:34] "GET /api/charts/resources HTTP/1.1" 200 -
2025-06-04 14:20:34,769 - werkzeug - INFO - ************** - - [04/Jun/2025 14:20:34] "GET /api/alerts HTTP/1.1" 200 -
2025-06-04 14:20:34,873 - werkzeug - INFO - ************** - - [04/Jun/2025 14:20:34] "GET /api/freshness HTTP/1.1" 200 -
2025-06-04 14:20:34,896 - werkzeug - INFO - ************** - - [04/Jun/2025 14:20:34] "GET /api/performance HTTP/1.1" 200 -
2025-06-04 14:21:04,755 - werkzeug - INFO - ************** - - [04/Jun/2025 14:21:04] "GET /api/charts/speed HTTP/1.1" 200 -
2025-06-04 14:21:04,755 - werkzeug - INFO - ************** - - [04/Jun/2025 14:21:04] "GET /api/charts/resources HTTP/1.1" 200 -
2025-06-04 14:21:04,771 - werkzeug - INFO - ************** - - [04/Jun/2025 14:21:04] "GET /api/alerts HTTP/1.1" 200 -
2025-06-04 14:21:04,873 - werkzeug - INFO - ************** - - [04/Jun/2025 14:21:04] "GET /api/freshness HTTP/1.1" 200 -
2025-06-04 14:21:04,897 - werkzeug - INFO - ************** - - [04/Jun/2025 14:21:04] "GET /api/performance HTTP/1.1" 200 -
2025-06-04 14:21:34,754 - werkzeug - INFO - ************** - - [04/Jun/2025 14:21:34] "GET /api/charts/speed HTTP/1.1" 200 -
2025-06-04 14:21:34,755 - werkzeug - INFO - ************** - - [04/Jun/2025 14:21:34] "GET /api/charts/resources HTTP/1.1" 200 -
2025-06-04 14:21:34,767 - werkzeug - INFO - ************** - - [04/Jun/2025 14:21:34] "GET /api/alerts HTTP/1.1" 200 -
2025-06-04 14:21:34,873 - werkzeug - INFO - ************** - - [04/Jun/2025 14:21:34] "GET /api/freshness HTTP/1.1" 200 -
2025-06-04 14:21:34,894 - werkzeug - INFO - ************** - - [04/Jun/2025 14:21:34] "GET /api/performance HTTP/1.1" 200 -
2025-06-04 14:22:04,754 - werkzeug - INFO - ************** - - [04/Jun/2025 14:22:04] "GET /api/charts/speed HTTP/1.1" 200 -
2025-06-04 14:22:04,756 - werkzeug - INFO - ************** - - [04/Jun/2025 14:22:04] "GET /api/charts/resources HTTP/1.1" 200 -
2025-06-04 14:22:04,772 - werkzeug - INFO - ************** - - [04/Jun/2025 14:22:04] "GET /api/alerts HTTP/1.1" 200 -
2025-06-04 14:22:04,874 - werkzeug - INFO - ************** - - [04/Jun/2025 14:22:04] "GET /api/freshness HTTP/1.1" 200 -
2025-06-04 14:22:04,888 - werkzeug - INFO - ************** - - [04/Jun/2025 14:22:04] "GET /api/performance HTTP/1.1" 200 -
2025-06-04 14:22:34,754 - werkzeug - INFO - ************** - - [04/Jun/2025 14:22:34] "GET /api/charts/speed HTTP/1.1" 200 -
2025-06-04 14:22:34,755 - werkzeug - INFO - ************** - - [04/Jun/2025 14:22:34] "GET /api/charts/resources HTTP/1.1" 200 -
2025-06-04 14:22:34,767 - werkzeug - INFO - ************** - - [04/Jun/2025 14:22:34] "GET /api/alerts HTTP/1.1" 200 -
2025-06-04 14:22:34,873 - werkzeug - INFO - ************** - - [04/Jun/2025 14:22:34] "GET /api/freshness HTTP/1.1" 200 -
2025-06-04 14:22:34,895 - werkzeug - INFO - ************** - - [04/Jun/2025 14:22:34] "GET /api/performance HTTP/1.1" 200 -
2025-06-04 14:23:04,754 - werkzeug - INFO - ************** - - [04/Jun/2025 14:23:04] "GET /api/charts/resources HTTP/1.1" 200 -
2025-06-04 14:23:04,755 - werkzeug - INFO - ************** - - [04/Jun/2025 14:23:04] "GET /api/charts/speed HTTP/1.1" 200 -
2025-06-04 14:23:04,766 - werkzeug - INFO - ************** - - [04/Jun/2025 14:23:04] "GET /api/alerts HTTP/1.1" 200 -
2025-06-04 14:23:04,870 - werkzeug - INFO - ************** - - [04/Jun/2025 14:23:04] "GET /api/freshness HTTP/1.1" 200 -
2025-06-04 14:23:04,885 - werkzeug - INFO - ************** - - [04/Jun/2025 14:23:04] "GET /api/performance HTTP/1.1" 200 -
2025-06-04 14:23:34,754 - werkzeug - INFO - ************** - - [04/Jun/2025 14:23:34] "GET /api/charts/speed HTTP/1.1" 200 -
2025-06-04 14:23:34,755 - werkzeug - INFO - ************** - - [04/Jun/2025 14:23:34] "GET /api/charts/resources HTTP/1.1" 200 -
2025-06-04 14:23:34,768 - werkzeug - INFO - ************** - - [04/Jun/2025 14:23:34] "GET /api/alerts HTTP/1.1" 200 -
2025-06-04 14:23:34,871 - werkzeug - INFO - ************** - - [04/Jun/2025 14:23:34] "GET /api/freshness HTTP/1.1" 200 -
2025-06-04 14:23:34,894 - werkzeug - INFO - ************** - - [04/Jun/2025 14:23:34] "GET /api/performance HTTP/1.1" 200 -
2025-06-04 14:24:04,755 - werkzeug - INFO - ************** - - [04/Jun/2025 14:24:04] "GET /api/charts/speed HTTP/1.1" 200 -
2025-06-04 14:24:04,756 - werkzeug - INFO - ************** - - [04/Jun/2025 14:24:04] "GET /api/charts/resources HTTP/1.1" 200 -
2025-06-04 14:24:04,771 - werkzeug - INFO - ************** - - [04/Jun/2025 14:24:04] "GET /api/alerts HTTP/1.1" 200 -
2025-06-04 14:24:04,872 - werkzeug - INFO - ************** - - [04/Jun/2025 14:24:04] "GET /api/freshness HTTP/1.1" 200 -
2025-06-04 14:24:04,894 - werkzeug - INFO - ************** - - [04/Jun/2025 14:24:04] "GET /api/performance HTTP/1.1" 200 -
2025-06-04 14:24:34,755 - werkzeug - INFO - ************** - - [04/Jun/2025 14:24:34] "GET /api/charts/speed HTTP/1.1" 200 -
2025-06-04 14:24:34,756 - werkzeug - INFO - ************** - - [04/Jun/2025 14:24:34] "GET /api/charts/resources HTTP/1.1" 200 -
2025-06-04 14:24:34,772 - werkzeug - INFO - ************** - - [04/Jun/2025 14:24:34] "GET /api/alerts HTTP/1.1" 200 -
2025-06-04 14:24:34,878 - werkzeug - INFO - ************** - - [04/Jun/2025 14:24:34] "GET /api/freshness HTTP/1.1" 200 -
2025-06-04 14:24:34,903 - werkzeug - INFO - ************** - - [04/Jun/2025 14:24:34] "GET /api/performance HTTP/1.1" 200 -
2025-06-04 14:25:04,755 - werkzeug - INFO - ************** - - [04/Jun/2025 14:25:04] "GET /api/charts/speed HTTP/1.1" 200 -
2025-06-04 14:25:04,756 - werkzeug - INFO - ************** - - [04/Jun/2025 14:25:04] "GET /api/charts/resources HTTP/1.1" 200 -
2025-06-04 14:25:04,771 - werkzeug - INFO - ************** - - [04/Jun/2025 14:25:04] "GET /api/alerts HTTP/1.1" 200 -
2025-06-04 14:25:04,873 - werkzeug - INFO - ************** - - [04/Jun/2025 14:25:04] "GET /api/freshness HTTP/1.1" 200 -
2025-06-04 14:25:04,896 - werkzeug - INFO - ************** - - [04/Jun/2025 14:25:04] "GET /api/performance HTTP/1.1" 200 -
2025-06-04 14:25:34,755 - werkzeug - INFO - ************** - - [04/Jun/2025 14:25:34] "GET /api/charts/speed HTTP/1.1" 200 -
2025-06-04 14:25:34,756 - werkzeug - INFO - ************** - - [04/Jun/2025 14:25:34] "GET /api/charts/resources HTTP/1.1" 200 -
2025-06-04 14:25:34,772 - werkzeug - INFO - ************** - - [04/Jun/2025 14:25:34] "GET /api/alerts HTTP/1.1" 200 -
2025-06-04 14:25:34,877 - werkzeug - INFO - ************** - - [04/Jun/2025 14:25:34] "GET /api/freshness HTTP/1.1" 200 -
2025-06-04 14:25:34,901 - werkzeug - INFO - ************** - - [04/Jun/2025 14:25:34] "GET /api/performance HTTP/1.1" 200 -
2025-06-04 14:26:04,755 - werkzeug - INFO - ************** - - [04/Jun/2025 14:26:04] "GET /api/charts/speed HTTP/1.1" 200 -
2025-06-04 14:26:04,756 - werkzeug - INFO - ************** - - [04/Jun/2025 14:26:04] "GET /api/charts/resources HTTP/1.1" 200 -
2025-06-04 14:26:04,772 - werkzeug - INFO - ************** - - [04/Jun/2025 14:26:04] "GET /api/alerts HTTP/1.1" 200 -
2025-06-04 14:26:04,885 - werkzeug - INFO - ************** - - [04/Jun/2025 14:26:04] "GET /api/freshness HTTP/1.1" 200 -
2025-06-04 14:26:04,900 - werkzeug - INFO - ************** - - [04/Jun/2025 14:26:04] "GET /api/performance HTTP/1.1" 200 -
2025-06-04 14:26:34,754 - werkzeug - INFO - ************** - - [04/Jun/2025 14:26:34] "GET /api/charts/speed HTTP/1.1" 200 -
2025-06-04 14:26:34,756 - werkzeug - INFO - ************** - - [04/Jun/2025 14:26:34] "GET /api/charts/resources HTTP/1.1" 200 -
2025-06-04 14:26:34,770 - werkzeug - INFO - ************** - - [04/Jun/2025 14:26:34] "GET /api/alerts HTTP/1.1" 200 -
2025-06-04 14:26:34,879 - werkzeug - INFO - ************** - - [04/Jun/2025 14:26:34] "GET /api/freshness HTTP/1.1" 200 -
2025-06-04 14:26:34,895 - werkzeug - INFO - ************** - - [04/Jun/2025 14:26:34] "GET /api/performance HTTP/1.1" 200 -
2025-06-04 14:27:04,754 - werkzeug - INFO - ************** - - [04/Jun/2025 14:27:04] "GET /api/charts/speed HTTP/1.1" 200 -
2025-06-04 14:27:04,755 - werkzeug - INFO - ************** - - [04/Jun/2025 14:27:04] "GET /api/charts/resources HTTP/1.1" 200 -
2025-06-04 14:27:04,770 - werkzeug - INFO - ************** - - [04/Jun/2025 14:27:04] "GET /api/alerts HTTP/1.1" 200 -
2025-06-04 14:27:04,872 - werkzeug - INFO - ************** - - [04/Jun/2025 14:27:04] "GET /api/freshness HTTP/1.1" 200 -
2025-06-04 14:27:04,895 - werkzeug - INFO - ************** - - [04/Jun/2025 14:27:04] "GET /api/performance HTTP/1.1" 200 -
2025-06-04 14:27:34,755 - werkzeug - INFO - ************** - - [04/Jun/2025 14:27:34] "GET /api/charts/resources HTTP/1.1" 200 -
2025-06-04 14:27:34,756 - werkzeug - INFO - ************** - - [04/Jun/2025 14:27:34] "GET /api/charts/speed HTTP/1.1" 200 -
2025-06-04 14:27:34,771 - werkzeug - INFO - ************** - - [04/Jun/2025 14:27:34] "GET /api/alerts HTTP/1.1" 200 -
2025-06-04 14:27:34,875 - werkzeug - INFO - ************** - - [04/Jun/2025 14:27:34] "GET /api/freshness HTTP/1.1" 200 -
2025-06-04 14:27:34,891 - werkzeug - INFO - ************** - - [04/Jun/2025 14:27:34] "GET /api/performance HTTP/1.1" 200 -
2025-06-04 14:28:04,755 - werkzeug - INFO - ************** - - [04/Jun/2025 14:28:04] "GET /api/charts/speed HTTP/1.1" 200 -
2025-06-04 14:28:04,756 - werkzeug - INFO - ************** - - [04/Jun/2025 14:28:04] "GET /api/charts/resources HTTP/1.1" 200 -
2025-06-04 14:28:04,771 - werkzeug - INFO - ************** - - [04/Jun/2025 14:28:04] "GET /api/alerts HTTP/1.1" 200 -
2025-06-04 14:28:04,885 - werkzeug - INFO - ************** - - [04/Jun/2025 14:28:04] "GET /api/freshness HTTP/1.1" 200 -
2025-06-04 14:28:04,902 - werkzeug - INFO - ************** - - [04/Jun/2025 14:28:04] "GET /api/performance HTTP/1.1" 200 -
2025-06-04 14:28:34,755 - werkzeug - INFO - ************** - - [04/Jun/2025 14:28:34] "GET /api/charts/speed HTTP/1.1" 200 -
2025-06-04 14:28:34,756 - werkzeug - INFO - ************** - - [04/Jun/2025 14:28:34] "GET /api/charts/resources HTTP/1.1" 200 -
2025-06-04 14:28:34,772 - werkzeug - INFO - ************** - - [04/Jun/2025 14:28:34] "GET /api/alerts HTTP/1.1" 200 -
2025-06-04 14:28:34,874 - werkzeug - INFO - ************** - - [04/Jun/2025 14:28:34] "GET /api/freshness HTTP/1.1" 200 -
2025-06-04 14:28:34,897 - werkzeug - INFO - ************** - - [04/Jun/2025 14:28:34] "GET /api/performance HTTP/1.1" 200 -
2025-06-04 14:29:04,755 - werkzeug - INFO - ************** - - [04/Jun/2025 14:29:04] "GET /api/charts/speed HTTP/1.1" 200 -
2025-06-04 14:29:04,756 - werkzeug - INFO - ************** - - [04/Jun/2025 14:29:04] "GET /api/charts/resources HTTP/1.1" 200 -
2025-06-04 14:29:04,768 - werkzeug - INFO - ************** - - [04/Jun/2025 14:29:04] "GET /api/alerts HTTP/1.1" 200 -
2025-06-04 14:29:04,873 - werkzeug - INFO - ************** - - [04/Jun/2025 14:29:04] "GET /api/freshness HTTP/1.1" 200 -
2025-06-04 14:29:04,890 - werkzeug - INFO - ************** - - [04/Jun/2025 14:29:04] "GET /api/performance HTTP/1.1" 200 -
2025-06-04 14:29:34,755 - werkzeug - INFO - ************** - - [04/Jun/2025 14:29:34] "GET /api/charts/speed HTTP/1.1" 200 -
2025-06-04 14:29:34,756 - werkzeug - INFO - ************** - - [04/Jun/2025 14:29:34] "GET /api/charts/resources HTTP/1.1" 200 -
2025-06-04 14:29:34,767 - werkzeug - INFO - ************** - - [04/Jun/2025 14:29:34] "GET /api/alerts HTTP/1.1" 200 -
2025-06-04 14:29:34,874 - werkzeug - INFO - ************** - - [04/Jun/2025 14:29:34] "GET /api/freshness HTTP/1.1" 200 -
2025-06-04 14:29:34,897 - werkzeug - INFO - ************** - - [04/Jun/2025 14:29:34] "GET /api/performance HTTP/1.1" 200 -
2025-06-04 14:30:01,856 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-04 14:30:02,081 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-04 14:30:02,086 - etl_performance_monitor - INFO - Metrics table ready
2025-06-04 14:30:04,754 - werkzeug - INFO - ************** - - [04/Jun/2025 14:30:04] "GET /api/charts/speed HTTP/1.1" 200 -
2025-06-04 14:30:04,755 - werkzeug - INFO - ************** - - [04/Jun/2025 14:30:04] "GET /api/charts/resources HTTP/1.1" 200 -
2025-06-04 14:30:04,768 - werkzeug - INFO - ************** - - [04/Jun/2025 14:30:04] "GET /api/alerts HTTP/1.1" 200 -
2025-06-04 14:30:04,875 - werkzeug - INFO - ************** - - [04/Jun/2025 14:30:04] "GET /api/freshness HTTP/1.1" 200 -
2025-06-04 14:30:04,888 - werkzeug - INFO - ************** - - [04/Jun/2025 14:30:04] "GET /api/performance HTTP/1.1" 200 -
2025-06-04 14:30:34,755 - werkzeug - INFO - ************** - - [04/Jun/2025 14:30:34] "GET /api/charts/speed HTTP/1.1" 200 -
2025-06-04 14:30:34,756 - werkzeug - INFO - ************** - - [04/Jun/2025 14:30:34] "GET /api/charts/resources HTTP/1.1" 200 -
2025-06-04 14:30:34,769 - werkzeug - INFO - ************** - - [04/Jun/2025 14:30:34] "GET /api/alerts HTTP/1.1" 200 -
2025-06-04 14:30:34,873 - werkzeug - INFO - ************** - - [04/Jun/2025 14:30:34] "GET /api/freshness HTTP/1.1" 200 -
2025-06-04 14:30:34,895 - werkzeug - INFO - ************** - - [04/Jun/2025 14:30:34] "GET /api/performance HTTP/1.1" 200 -
2025-06-04 14:31:04,755 - werkzeug - INFO - ************** - - [04/Jun/2025 14:31:04] "GET /api/charts/speed HTTP/1.1" 200 -
2025-06-04 14:31:04,757 - werkzeug - INFO - ************** - - [04/Jun/2025 14:31:04] "GET /api/charts/resources HTTP/1.1" 200 -
2025-06-04 14:31:04,768 - werkzeug - INFO - ************** - - [04/Jun/2025 14:31:04] "GET /api/alerts HTTP/1.1" 200 -
2025-06-04 14:31:04,877 - werkzeug - INFO - ************** - - [04/Jun/2025 14:31:04] "GET /api/freshness HTTP/1.1" 200 -
2025-06-04 14:31:04,898 - werkzeug - INFO - ************** - - [04/Jun/2025 14:31:04] "GET /api/performance HTTP/1.1" 200 -
2025-06-04 14:31:34,755 - werkzeug - INFO - ************** - - [04/Jun/2025 14:31:34] "GET /api/charts/speed HTTP/1.1" 200 -
2025-06-04 14:31:34,755 - werkzeug - INFO - ************** - - [04/Jun/2025 14:31:34] "GET /api/charts/resources HTTP/1.1" 200 -
2025-06-04 14:31:34,806 - werkzeug - INFO - ************** - - [04/Jun/2025 14:31:34] "GET /api/alerts HTTP/1.1" 200 -
2025-06-04 14:31:34,911 - werkzeug - INFO - ************** - - [04/Jun/2025 14:31:34] "GET /api/freshness HTTP/1.1" 200 -
2025-06-04 14:31:34,928 - werkzeug - INFO - ************** - - [04/Jun/2025 14:31:34] "GET /api/performance HTTP/1.1" 200 -
2025-06-04 14:32:04,755 - werkzeug - INFO - ************** - - [04/Jun/2025 14:32:04] "GET /api/charts/speed HTTP/1.1" 200 -
2025-06-04 14:32:04,756 - werkzeug - INFO - ************** - - [04/Jun/2025 14:32:04] "GET /api/charts/resources HTTP/1.1" 200 -
2025-06-04 14:32:04,768 - werkzeug - INFO - ************** - - [04/Jun/2025 14:32:04] "GET /api/alerts HTTP/1.1" 200 -
2025-06-04 14:32:04,876 - werkzeug - INFO - ************** - - [04/Jun/2025 14:32:04] "GET /api/freshness HTTP/1.1" 200 -
2025-06-04 14:32:04,896 - werkzeug - INFO - ************** - - [04/Jun/2025 14:32:04] "GET /api/performance HTTP/1.1" 200 -
2025-06-04 14:32:34,755 - werkzeug - INFO - ************** - - [04/Jun/2025 14:32:34] "GET /api/charts/speed HTTP/1.1" 200 -
2025-06-04 14:32:34,756 - werkzeug - INFO - ************** - - [04/Jun/2025 14:32:34] "GET /api/charts/resources HTTP/1.1" 200 -
2025-06-04 14:32:34,767 - werkzeug - INFO - ************** - - [04/Jun/2025 14:32:34] "GET /api/alerts HTTP/1.1" 200 -
2025-06-04 14:32:34,872 - werkzeug - INFO - ************** - - [04/Jun/2025 14:32:34] "GET /api/freshness HTTP/1.1" 200 -
2025-06-04 14:32:34,890 - werkzeug - INFO - ************** - - [04/Jun/2025 14:32:34] "GET /api/performance HTTP/1.1" 200 -
2025-06-04 14:33:04,755 - werkzeug - INFO - ************** - - [04/Jun/2025 14:33:04] "GET /api/charts/speed HTTP/1.1" 200 -
2025-06-04 14:33:04,756 - werkzeug - INFO - ************** - - [04/Jun/2025 14:33:04] "GET /api/charts/resources HTTP/1.1" 200 -
2025-06-04 14:33:04,770 - werkzeug - INFO - ************** - - [04/Jun/2025 14:33:04] "GET /api/alerts HTTP/1.1" 200 -
2025-06-04 14:33:04,871 - werkzeug - INFO - ************** - - [04/Jun/2025 14:33:04] "GET /api/freshness HTTP/1.1" 200 -
2025-06-04 14:33:04,892 - werkzeug - INFO - ************** - - [04/Jun/2025 14:33:04] "GET /api/performance HTTP/1.1" 200 -
2025-06-04 14:33:34,754 - werkzeug - INFO - ************** - - [04/Jun/2025 14:33:34] "GET /api/charts/speed HTTP/1.1" 200 -
2025-06-04 14:33:34,755 - werkzeug - INFO - ************** - - [04/Jun/2025 14:33:34] "GET /api/charts/resources HTTP/1.1" 200 -
2025-06-04 14:33:34,872 - werkzeug - INFO - ************** - - [04/Jun/2025 14:33:34] "GET /api/freshness HTTP/1.1" 200 -
2025-06-04 14:33:34,874 - werkzeug - INFO - ************** - - [04/Jun/2025 14:33:34] "GET /api/alerts HTTP/1.1" 200 -
2025-06-04 14:33:34,895 - werkzeug - INFO - ************** - - [04/Jun/2025 14:33:34] "GET /api/performance HTTP/1.1" 200 -
2025-06-04 14:34:04,754 - werkzeug - INFO - ************** - - [04/Jun/2025 14:34:04] "GET /api/charts/speed HTTP/1.1" 200 -
2025-06-04 14:34:04,755 - werkzeug - INFO - ************** - - [04/Jun/2025 14:34:04] "GET /api/charts/resources HTTP/1.1" 200 -
2025-06-04 14:34:04,766 - werkzeug - INFO - ************** - - [04/Jun/2025 14:34:04] "GET /api/alerts HTTP/1.1" 200 -
2025-06-04 14:34:04,870 - werkzeug - INFO - ************** - - [04/Jun/2025 14:34:04] "GET /api/freshness HTTP/1.1" 200 -
2025-06-04 14:34:04,891 - werkzeug - INFO - ************** - - [04/Jun/2025 14:34:04] "GET /api/performance HTTP/1.1" 200 -
2025-06-04 14:34:34,754 - werkzeug - INFO - ************** - - [04/Jun/2025 14:34:34] "GET /api/charts/speed HTTP/1.1" 200 -
2025-06-04 14:34:34,756 - werkzeug - INFO - ************** - - [04/Jun/2025 14:34:34] "GET /api/charts/resources HTTP/1.1" 200 -
2025-06-04 14:34:34,766 - werkzeug - INFO - ************** - - [04/Jun/2025 14:34:34] "GET /api/alerts HTTP/1.1" 200 -
2025-06-04 14:34:34,872 - werkzeug - INFO - ************** - - [04/Jun/2025 14:34:34] "GET /api/freshness HTTP/1.1" 200 -
2025-06-04 14:34:34,885 - werkzeug - INFO - ************** - - [04/Jun/2025 14:34:34] "GET /api/performance HTTP/1.1" 200 -
2025-06-04 14:35:04,755 - werkzeug - INFO - ************** - - [04/Jun/2025 14:35:04] "GET /api/charts/resources HTTP/1.1" 200 -
2025-06-04 14:35:04,756 - werkzeug - INFO - ************** - - [04/Jun/2025 14:35:04] "GET /api/charts/speed HTTP/1.1" 200 -
2025-06-04 14:35:04,768 - werkzeug - INFO - ************** - - [04/Jun/2025 14:35:04] "GET /api/alerts HTTP/1.1" 200 -
2025-06-04 14:35:04,872 - werkzeug - INFO - ************** - - [04/Jun/2025 14:35:04] "GET /api/freshness HTTP/1.1" 200 -
2025-06-04 14:35:04,893 - werkzeug - INFO - ************** - - [04/Jun/2025 14:35:04] "GET /api/performance HTTP/1.1" 200 -
2025-06-04 14:35:34,755 - werkzeug - INFO - ************** - - [04/Jun/2025 14:35:34] "GET /api/charts/speed HTTP/1.1" 200 -
2025-06-04 14:35:34,756 - werkzeug - INFO - ************** - - [04/Jun/2025 14:35:34] "GET /api/charts/resources HTTP/1.1" 200 -
2025-06-04 14:35:34,770 - werkzeug - INFO - ************** - - [04/Jun/2025 14:35:34] "GET /api/alerts HTTP/1.1" 200 -
2025-06-04 14:35:34,884 - werkzeug - INFO - ************** - - [04/Jun/2025 14:35:34] "GET /api/freshness HTTP/1.1" 200 -
2025-06-04 14:35:34,905 - werkzeug - INFO - ************** - - [04/Jun/2025 14:35:34] "GET /api/performance HTTP/1.1" 200 -
2025-06-04 14:36:04,755 - werkzeug - INFO - ************** - - [04/Jun/2025 14:36:04] "GET /api/charts/speed HTTP/1.1" 200 -
2025-06-04 14:36:04,756 - werkzeug - INFO - ************** - - [04/Jun/2025 14:36:04] "GET /api/charts/resources HTTP/1.1" 200 -
2025-06-04 14:36:04,771 - werkzeug - INFO - ************** - - [04/Jun/2025 14:36:04] "GET /api/alerts HTTP/1.1" 200 -
2025-06-04 14:36:04,873 - werkzeug - INFO - ************** - - [04/Jun/2025 14:36:04] "GET /api/freshness HTTP/1.1" 200 -
2025-06-04 14:36:04,894 - werkzeug - INFO - ************** - - [04/Jun/2025 14:36:04] "GET /api/performance HTTP/1.1" 200 -
2025-06-04 14:36:34,752 - werkzeug - INFO - ************** - - [04/Jun/2025 14:36:34] "GET /api/charts/resources HTTP/1.1" 200 -
2025-06-04 14:36:34,753 - werkzeug - INFO - ************** - - [04/Jun/2025 14:36:34] "GET /api/charts/speed HTTP/1.1" 200 -
2025-06-04 14:36:34,766 - werkzeug - INFO - ************** - - [04/Jun/2025 14:36:34] "GET /api/alerts HTTP/1.1" 200 -
2025-06-04 14:36:34,870 - werkzeug - INFO - ************** - - [04/Jun/2025 14:36:34] "GET /api/freshness HTTP/1.1" 200 -
2025-06-04 14:36:34,892 - werkzeug - INFO - ************** - - [04/Jun/2025 14:36:34] "GET /api/performance HTTP/1.1" 200 -
2025-06-04 14:37:04,754 - werkzeug - INFO - ************** - - [04/Jun/2025 14:37:04] "GET /api/charts/speed HTTP/1.1" 200 -
2025-06-04 14:37:04,755 - werkzeug - INFO - ************** - - [04/Jun/2025 14:37:04] "GET /api/charts/resources HTTP/1.1" 200 -
2025-06-04 14:37:04,767 - werkzeug - INFO - ************** - - [04/Jun/2025 14:37:04] "GET /api/alerts HTTP/1.1" 200 -
2025-06-04 14:37:04,875 - werkzeug - INFO - ************** - - [04/Jun/2025 14:37:04] "GET /api/freshness HTTP/1.1" 200 -
2025-06-04 14:37:04,892 - werkzeug - INFO - ************** - - [04/Jun/2025 14:37:04] "GET /api/performance HTTP/1.1" 200 -
2025-06-04 14:37:34,755 - werkzeug - INFO - ************** - - [04/Jun/2025 14:37:34] "GET /api/charts/speed HTTP/1.1" 200 -
2025-06-04 14:37:34,755 - werkzeug - INFO - ************** - - [04/Jun/2025 14:37:34] "GET /api/charts/resources HTTP/1.1" 200 -
2025-06-04 14:37:34,771 - werkzeug - INFO - ************** - - [04/Jun/2025 14:37:34] "GET /api/alerts HTTP/1.1" 200 -
2025-06-04 14:37:34,875 - werkzeug - INFO - ************** - - [04/Jun/2025 14:37:34] "GET /api/freshness HTTP/1.1" 200 -
2025-06-04 14:37:34,895 - werkzeug - INFO - ************** - - [04/Jun/2025 14:37:34] "GET /api/performance HTTP/1.1" 200 -
2025-06-04 14:38:04,754 - werkzeug - INFO - ************** - - [04/Jun/2025 14:38:04] "GET /api/charts/speed HTTP/1.1" 200 -
2025-06-04 14:38:04,755 - werkzeug - INFO - ************** - - [04/Jun/2025 14:38:04] "GET /api/charts/resources HTTP/1.1" 200 -
2025-06-04 14:38:04,767 - werkzeug - INFO - ************** - - [04/Jun/2025 14:38:04] "GET /api/alerts HTTP/1.1" 200 -
2025-06-04 14:38:04,872 - werkzeug - INFO - ************** - - [04/Jun/2025 14:38:04] "GET /api/freshness HTTP/1.1" 200 -
2025-06-04 14:38:04,892 - werkzeug - INFO - ************** - - [04/Jun/2025 14:38:04] "GET /api/performance HTTP/1.1" 200 -
2025-06-04 14:38:34,754 - werkzeug - INFO - ************** - - [04/Jun/2025 14:38:34] "GET /api/charts/speed HTTP/1.1" 200 -
2025-06-04 14:38:34,755 - werkzeug - INFO - ************** - - [04/Jun/2025 14:38:34] "GET /api/charts/resources HTTP/1.1" 200 -
2025-06-04 14:38:34,768 - werkzeug - INFO - ************** - - [04/Jun/2025 14:38:34] "GET /api/alerts HTTP/1.1" 200 -
2025-06-04 14:38:34,872 - werkzeug - INFO - ************** - - [04/Jun/2025 14:38:34] "GET /api/freshness HTTP/1.1" 200 -
2025-06-04 14:38:34,893 - werkzeug - INFO - ************** - - [04/Jun/2025 14:38:34] "GET /api/performance HTTP/1.1" 200 -
2025-06-04 14:39:04,754 - werkzeug - INFO - ************** - - [04/Jun/2025 14:39:04] "GET /api/charts/speed HTTP/1.1" 200 -
2025-06-04 14:39:04,755 - werkzeug - INFO - ************** - - [04/Jun/2025 14:39:04] "GET /api/charts/resources HTTP/1.1" 200 -
2025-06-04 14:39:04,767 - werkzeug - INFO - ************** - - [04/Jun/2025 14:39:04] "GET /api/alerts HTTP/1.1" 200 -
2025-06-04 14:39:04,875 - werkzeug - INFO - ************** - - [04/Jun/2025 14:39:04] "GET /api/freshness HTTP/1.1" 200 -
2025-06-04 14:39:04,896 - werkzeug - INFO - ************** - - [04/Jun/2025 14:39:04] "GET /api/performance HTTP/1.1" 200 -
2025-06-04 14:39:34,754 - werkzeug - INFO - ************** - - [04/Jun/2025 14:39:34] "GET /api/charts/speed HTTP/1.1" 200 -
2025-06-04 14:39:34,755 - werkzeug - INFO - ************** - - [04/Jun/2025 14:39:34] "GET /api/charts/resources HTTP/1.1" 200 -
2025-06-04 14:39:34,768 - werkzeug - INFO - ************** - - [04/Jun/2025 14:39:34] "GET /api/alerts HTTP/1.1" 200 -
2025-06-04 14:39:34,871 - werkzeug - INFO - ************** - - [04/Jun/2025 14:39:34] "GET /api/freshness HTTP/1.1" 200 -
2025-06-04 14:39:34,888 - werkzeug - INFO - ************** - - [04/Jun/2025 14:39:34] "GET /api/performance HTTP/1.1" 200 -
2025-06-04 14:40:04,754 - werkzeug - INFO - ************** - - [04/Jun/2025 14:40:04] "GET /api/charts/resources HTTP/1.1" 200 -
2025-06-04 14:40:04,755 - werkzeug - INFO - ************** - - [04/Jun/2025 14:40:04] "GET /api/charts/speed HTTP/1.1" 200 -
2025-06-04 14:40:04,770 - werkzeug - INFO - ************** - - [04/Jun/2025 14:40:04] "GET /api/alerts HTTP/1.1" 200 -
2025-06-04 14:40:04,885 - werkzeug - INFO - ************** - - [04/Jun/2025 14:40:04] "GET /api/freshness HTTP/1.1" 200 -
2025-06-04 14:40:04,901 - werkzeug - INFO - ************** - - [04/Jun/2025 14:40:04] "GET /api/performance HTTP/1.1" 200 -
2025-06-04 14:40:34,754 - werkzeug - INFO - ************** - - [04/Jun/2025 14:40:34] "GET /api/charts/speed HTTP/1.1" 200 -
2025-06-04 14:40:34,755 - werkzeug - INFO - ************** - - [04/Jun/2025 14:40:34] "GET /api/charts/resources HTTP/1.1" 200 -
2025-06-04 14:40:34,770 - werkzeug - INFO - ************** - - [04/Jun/2025 14:40:34] "GET /api/alerts HTTP/1.1" 200 -
2025-06-04 14:40:34,872 - werkzeug - INFO - ************** - - [04/Jun/2025 14:40:34] "GET /api/freshness HTTP/1.1" 200 -
2025-06-04 14:40:34,888 - werkzeug - INFO - ************** - - [04/Jun/2025 14:40:34] "GET /api/performance HTTP/1.1" 200 -
2025-06-04 14:41:04,754 - werkzeug - INFO - ************** - - [04/Jun/2025 14:41:04] "GET /api/charts/speed HTTP/1.1" 200 -
2025-06-04 14:41:04,755 - werkzeug - INFO - ************** - - [04/Jun/2025 14:41:04] "GET /api/charts/resources HTTP/1.1" 200 -
2025-06-04 14:41:04,768 - werkzeug - INFO - ************** - - [04/Jun/2025 14:41:04] "GET /api/alerts HTTP/1.1" 200 -
2025-06-04 14:41:04,874 - werkzeug - INFO - ************** - - [04/Jun/2025 14:41:04] "GET /api/freshness HTTP/1.1" 200 -
2025-06-04 14:41:04,895 - werkzeug - INFO - ************** - - [04/Jun/2025 14:41:04] "GET /api/performance HTTP/1.1" 200 -
2025-06-04 14:41:34,754 - werkzeug - INFO - ************** - - [04/Jun/2025 14:41:34] "GET /api/charts/speed HTTP/1.1" 200 -
2025-06-04 14:41:34,755 - werkzeug - INFO - ************** - - [04/Jun/2025 14:41:34] "GET /api/charts/resources HTTP/1.1" 200 -
2025-06-04 14:41:34,768 - werkzeug - INFO - ************** - - [04/Jun/2025 14:41:34] "GET /api/alerts HTTP/1.1" 200 -
2025-06-04 14:41:34,872 - werkzeug - INFO - ************** - - [04/Jun/2025 14:41:34] "GET /api/freshness HTTP/1.1" 200 -
2025-06-04 14:41:34,889 - werkzeug - INFO - ************** - - [04/Jun/2025 14:41:34] "GET /api/performance HTTP/1.1" 200 -
2025-06-04 14:42:04,754 - werkzeug - INFO - ************** - - [04/Jun/2025 14:42:04] "GET /api/charts/speed HTTP/1.1" 200 -
2025-06-04 14:42:04,755 - werkzeug - INFO - ************** - - [04/Jun/2025 14:42:04] "GET /api/charts/resources HTTP/1.1" 200 -
2025-06-04 14:42:04,771 - werkzeug - INFO - ************** - - [04/Jun/2025 14:42:04] "GET /api/alerts HTTP/1.1" 200 -
2025-06-04 14:42:04,872 - werkzeug - INFO - ************** - - [04/Jun/2025 14:42:04] "GET /api/freshness HTTP/1.1" 200 -
2025-06-04 14:42:04,893 - werkzeug - INFO - ************** - - [04/Jun/2025 14:42:04] "GET /api/performance HTTP/1.1" 200 -
2025-06-04 14:42:34,754 - werkzeug - INFO - ************** - - [04/Jun/2025 14:42:34] "GET /api/charts/speed HTTP/1.1" 200 -
2025-06-04 14:42:34,755 - werkzeug - INFO - ************** - - [04/Jun/2025 14:42:34] "GET /api/charts/resources HTTP/1.1" 200 -
2025-06-04 14:42:34,770 - werkzeug - INFO - ************** - - [04/Jun/2025 14:42:34] "GET /api/alerts HTTP/1.1" 200 -
2025-06-04 14:42:34,884 - werkzeug - INFO - ************** - - [04/Jun/2025 14:42:34] "GET /api/freshness HTTP/1.1" 200 -
2025-06-04 14:42:34,907 - werkzeug - INFO - ************** - - [04/Jun/2025 14:42:34] "GET /api/performance HTTP/1.1" 200 -
2025-06-04 14:43:04,754 - werkzeug - INFO - ************** - - [04/Jun/2025 14:43:04] "GET /api/charts/resources HTTP/1.1" 200 -
2025-06-04 14:43:04,755 - werkzeug - INFO - ************** - - [04/Jun/2025 14:43:04] "GET /api/charts/speed HTTP/1.1" 200 -
2025-06-04 14:43:04,768 - werkzeug - INFO - ************** - - [04/Jun/2025 14:43:04] "GET /api/alerts HTTP/1.1" 200 -
2025-06-04 14:43:04,875 - werkzeug - INFO - ************** - - [04/Jun/2025 14:43:04] "GET /api/freshness HTTP/1.1" 200 -
2025-06-04 14:43:04,896 - werkzeug - INFO - ************** - - [04/Jun/2025 14:43:04] "GET /api/performance HTTP/1.1" 200 -
2025-06-04 14:43:34,755 - werkzeug - INFO - ************** - - [04/Jun/2025 14:43:34] "GET /api/charts/speed HTTP/1.1" 200 -
2025-06-04 14:43:34,756 - werkzeug - INFO - ************** - - [04/Jun/2025 14:43:34] "GET /api/charts/resources HTTP/1.1" 200 -
2025-06-04 14:43:34,774 - werkzeug - INFO - ************** - - [04/Jun/2025 14:43:34] "GET /api/alerts HTTP/1.1" 200 -
2025-06-04 14:43:34,876 - werkzeug - INFO - ************** - - [04/Jun/2025 14:43:34] "GET /api/freshness HTTP/1.1" 200 -
2025-06-04 14:43:34,893 - werkzeug - INFO - ************** - - [04/Jun/2025 14:43:34] "GET /api/performance HTTP/1.1" 200 -
2025-06-04 14:44:04,755 - werkzeug - INFO - ************** - - [04/Jun/2025 14:44:04] "GET /api/charts/speed HTTP/1.1" 200 -
2025-06-04 14:44:04,755 - werkzeug - INFO - ************** - - [04/Jun/2025 14:44:04] "GET /api/charts/resources HTTP/1.1" 200 -
2025-06-04 14:44:04,768 - werkzeug - INFO - ************** - - [04/Jun/2025 14:44:04] "GET /api/alerts HTTP/1.1" 200 -
2025-06-04 14:44:04,872 - werkzeug - INFO - ************** - - [04/Jun/2025 14:44:04] "GET /api/freshness HTTP/1.1" 200 -
2025-06-04 14:44:04,890 - werkzeug - INFO - ************** - - [04/Jun/2025 14:44:04] "GET /api/performance HTTP/1.1" 200 -
2025-06-04 14:44:34,754 - werkzeug - INFO - ************** - - [04/Jun/2025 14:44:34] "GET /api/charts/speed HTTP/1.1" 200 -
2025-06-04 14:44:34,756 - werkzeug - INFO - ************** - - [04/Jun/2025 14:44:34] "GET /api/charts/resources HTTP/1.1" 200 -
2025-06-04 14:44:34,769 - werkzeug - INFO - ************** - - [04/Jun/2025 14:44:34] "GET /api/alerts HTTP/1.1" 200 -
2025-06-04 14:44:34,873 - werkzeug - INFO - ************** - - [04/Jun/2025 14:44:34] "GET /api/freshness HTTP/1.1" 200 -
2025-06-04 14:44:34,890 - werkzeug - INFO - ************** - - [04/Jun/2025 14:44:34] "GET /api/performance HTTP/1.1" 200 -
2025-06-04 14:45:04,754 - werkzeug - INFO - ************** - - [04/Jun/2025 14:45:04] "GET /api/charts/speed HTTP/1.1" 200 -
2025-06-04 14:45:04,755 - werkzeug - INFO - ************** - - [04/Jun/2025 14:45:04] "GET /api/charts/resources HTTP/1.1" 200 -
2025-06-04 14:45:04,775 - werkzeug - INFO - ************** - - [04/Jun/2025 14:45:04] "GET /api/alerts HTTP/1.1" 200 -
2025-06-04 14:45:04,876 - werkzeug - INFO - ************** - - [04/Jun/2025 14:45:04] "GET /api/freshness HTTP/1.1" 200 -
2025-06-04 14:45:04,896 - werkzeug - INFO - ************** - - [04/Jun/2025 14:45:04] "GET /api/performance HTTP/1.1" 200 -
2025-06-04 14:45:34,755 - werkzeug - INFO - ************** - - [04/Jun/2025 14:45:34] "GET /api/charts/speed HTTP/1.1" 200 -
2025-06-04 14:45:34,756 - werkzeug - INFO - ************** - - [04/Jun/2025 14:45:34] "GET /api/charts/resources HTTP/1.1" 200 -
2025-06-04 14:45:34,771 - werkzeug - INFO - ************** - - [04/Jun/2025 14:45:34] "GET /api/alerts HTTP/1.1" 200 -
2025-06-04 14:45:34,877 - werkzeug - INFO - ************** - - [04/Jun/2025 14:45:34] "GET /api/freshness HTTP/1.1" 200 -
2025-06-04 14:45:34,898 - werkzeug - INFO - ************** - - [04/Jun/2025 14:45:34] "GET /api/performance HTTP/1.1" 200 -
2025-06-04 14:46:04,754 - werkzeug - INFO - ************** - - [04/Jun/2025 14:46:04] "GET /api/charts/speed HTTP/1.1" 200 -
2025-06-04 14:46:04,756 - werkzeug - INFO - ************** - - [04/Jun/2025 14:46:04] "GET /api/charts/resources HTTP/1.1" 200 -
2025-06-04 14:46:04,770 - werkzeug - INFO - ************** - - [04/Jun/2025 14:46:04] "GET /api/alerts HTTP/1.1" 200 -
2025-06-04 14:46:04,872 - werkzeug - INFO - ************** - - [04/Jun/2025 14:46:04] "GET /api/freshness HTTP/1.1" 200 -
2025-06-04 14:46:04,889 - werkzeug - INFO - ************** - - [04/Jun/2025 14:46:04] "GET /api/performance HTTP/1.1" 200 -
2025-06-04 14:46:34,755 - werkzeug - INFO - ************** - - [04/Jun/2025 14:46:34] "GET /api/charts/speed HTTP/1.1" 200 -
2025-06-04 14:46:34,756 - werkzeug - INFO - ************** - - [04/Jun/2025 14:46:34] "GET /api/charts/resources HTTP/1.1" 200 -
2025-06-04 14:46:34,772 - werkzeug - INFO - ************** - - [04/Jun/2025 14:46:34] "GET /api/alerts HTTP/1.1" 200 -
2025-06-04 14:46:34,874 - werkzeug - INFO - ************** - - [04/Jun/2025 14:46:34] "GET /api/freshness HTTP/1.1" 200 -
2025-06-04 14:46:34,894 - werkzeug - INFO - ************** - - [04/Jun/2025 14:46:34] "GET /api/performance HTTP/1.1" 200 -
2025-06-04 14:47:04,755 - werkzeug - INFO - ************** - - [04/Jun/2025 14:47:04] "GET /api/charts/speed HTTP/1.1" 200 -
2025-06-04 14:47:04,756 - werkzeug - INFO - ************** - - [04/Jun/2025 14:47:04] "GET /api/charts/resources HTTP/1.1" 200 -
2025-06-04 14:47:04,775 - werkzeug - INFO - ************** - - [04/Jun/2025 14:47:04] "GET /api/alerts HTTP/1.1" 200 -
2025-06-04 14:47:04,877 - werkzeug - INFO - ************** - - [04/Jun/2025 14:47:04] "GET /api/freshness HTTP/1.1" 200 -
2025-06-04 14:47:04,900 - werkzeug - INFO - ************** - - [04/Jun/2025 14:47:04] "GET /api/performance HTTP/1.1" 200 -
2025-06-04 14:47:34,755 - werkzeug - INFO - ************** - - [04/Jun/2025 14:47:34] "GET /api/charts/speed HTTP/1.1" 200 -
2025-06-04 14:47:34,757 - werkzeug - INFO - ************** - - [04/Jun/2025 14:47:34] "GET /api/charts/resources HTTP/1.1" 200 -
2025-06-04 14:47:34,768 - werkzeug - INFO - ************** - - [04/Jun/2025 14:47:34] "GET /api/alerts HTTP/1.1" 200 -
2025-06-04 14:47:34,874 - werkzeug - INFO - ************** - - [04/Jun/2025 14:47:34] "GET /api/freshness HTTP/1.1" 200 -
2025-06-04 14:47:34,895 - werkzeug - INFO - ************** - - [04/Jun/2025 14:47:34] "GET /api/performance HTTP/1.1" 200 -
2025-06-04 14:48:04,755 - werkzeug - INFO - ************** - - [04/Jun/2025 14:48:04] "GET /api/charts/speed HTTP/1.1" 200 -
2025-06-04 14:48:04,756 - werkzeug - INFO - ************** - - [04/Jun/2025 14:48:04] "GET /api/charts/resources HTTP/1.1" 200 -
2025-06-04 14:48:04,772 - werkzeug - INFO - ************** - - [04/Jun/2025 14:48:04] "GET /api/alerts HTTP/1.1" 200 -
2025-06-04 14:48:04,874 - werkzeug - INFO - ************** - - [04/Jun/2025 14:48:04] "GET /api/freshness HTTP/1.1" 200 -
2025-06-04 14:48:04,895 - werkzeug - INFO - ************** - - [04/Jun/2025 14:48:04] "GET /api/performance HTTP/1.1" 200 -
2025-06-04 14:48:34,755 - werkzeug - INFO - ************** - - [04/Jun/2025 14:48:34] "GET /api/charts/speed HTTP/1.1" 200 -
2025-06-04 14:48:34,756 - werkzeug - INFO - ************** - - [04/Jun/2025 14:48:34] "GET /api/charts/resources HTTP/1.1" 200 -
2025-06-04 14:48:34,772 - werkzeug - INFO - ************** - - [04/Jun/2025 14:48:34] "GET /api/alerts HTTP/1.1" 200 -
2025-06-04 14:48:34,876 - werkzeug - INFO - ************** - - [04/Jun/2025 14:48:34] "GET /api/freshness HTTP/1.1" 200 -
2025-06-04 14:48:34,895 - werkzeug - INFO - ************** - - [04/Jun/2025 14:48:34] "GET /api/performance HTTP/1.1" 200 -
2025-06-04 15:00:02,146 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-04 15:00:02,373 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-04 15:00:02,378 - etl_performance_monitor - INFO - Metrics table ready
2025-06-04 15:30:02,404 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-04 15:30:02,629 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-04 15:30:02,635 - etl_performance_monitor - INFO - Metrics table ready
2025-06-04 16:00:02,651 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-04 16:00:02,875 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-04 16:00:02,882 - etl_performance_monitor - INFO - Metrics table ready
2025-06-04 16:30:01,913 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-04 16:30:02,137 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-04 16:30:02,142 - etl_performance_monitor - INFO - Metrics table ready
2025-06-04 17:00:02,130 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-04 17:00:02,357 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-04 17:00:02,367 - etl_performance_monitor - INFO - Metrics table ready
2025-06-04 17:30:02,407 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-04 17:30:02,632 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-04 17:30:02,637 - etl_performance_monitor - INFO - Metrics table ready
2025-06-04 18:00:02,652 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-04 18:00:02,717 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-04 18:00:02,883 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-04 18:00:02,889 - etl_performance_monitor - INFO - Metrics table ready
2025-06-04 18:00:02,950 - __main__ - INFO - Connected to HeavyDB
2025-06-04 18:00:02,961 - __main__ - INFO - Metrics table ready
2025-06-04 18:00:03,182 - __main__ - ERROR - Failed to analyze trends: Execution failed on sql: 
                SELECT 
                    CAST(metric_time AS DATE) as day, EXTRACT(HOUR FROM metric_time) as hour_of_day,
                    AVG(rows_per_second) as avg_speed,
                    AVG(cpu_percent) as avg_cpu,
                    AVG(gpu_utilization) as avg_gpu,
                    SUM(rows_processed) as total_rows,
                    COUNT(*) as operations
                FROM etl_performance_metrics
                WHERE metric_time >= NOW() - INTERVAL '7' DAY
                    AND status = 'success'
                GROUP BY day, hour_of_day
ORDER BY day, hour_of_day
            
SQL Error: Encountered "day" at line 2, column 50.
Was expecting one of:
    <QUOTED_STRING> ...
    <BRACKET_QUOTED_IDENTIFIER> ...
    <QUOTED_IDENTIFIER> ...
    <BACK_QUOTED_IDENTIFIER> ...
    <IDENTIFIER> ...
    <UNICODE_QUOTED_IDENTIFIER> ...
    
unable to rollback
2025-06-04 18:30:02,246 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-04 18:30:02,472 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-04 18:30:02,480 - etl_performance_monitor - INFO - Metrics table ready
2025-06-04 19:00:02,529 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-04 19:00:02,754 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-04 19:00:02,761 - etl_performance_monitor - INFO - Metrics table ready
2025-06-04 19:30:01,788 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-04 19:30:02,016 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-04 19:30:02,024 - etl_performance_monitor - INFO - Metrics table ready
2025-06-04 20:00:02,059 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-04 20:00:02,285 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-04 20:00:02,290 - etl_performance_monitor - INFO - Metrics table ready
2025-06-04 20:30:02,328 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-04 20:30:02,554 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-04 20:30:02,563 - etl_performance_monitor - INFO - Metrics table ready
2025-06-04 21:00:02,621 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-04 21:00:02,848 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-04 21:00:02,855 - etl_performance_monitor - INFO - Metrics table ready
2025-06-04 21:30:01,923 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-04 21:30:02,150 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-04 21:30:02,154 - etl_performance_monitor - INFO - Metrics table ready
2025-06-04 22:00:02,173 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-04 22:00:02,400 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-04 22:00:02,405 - etl_performance_monitor - INFO - Metrics table ready
2025-06-04 22:30:02,458 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-04 22:30:02,682 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-04 22:30:02,688 - etl_performance_monitor - INFO - Metrics table ready
2025-06-04 23:00:01,730 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-04 23:00:01,957 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-04 23:00:01,966 - etl_performance_monitor - INFO - Metrics table ready
2025-06-04 23:30:02,012 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-04 23:30:02,237 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-04 23:30:02,242 - etl_performance_monitor - INFO - Metrics table ready
2025-06-05 00:00:02,344 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-05 00:00:02,344 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-05 00:00:02,578 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-05 00:00:02,580 - __main__ - INFO - Connected to HeavyDB
2025-06-05 00:00:02,583 - etl_performance_monitor - INFO - Metrics table ready
2025-06-05 00:00:02,620 - __main__ - INFO - Metrics table ready
2025-06-05 00:00:02,774 - __main__ - ERROR - Failed to analyze trends: Execution failed on sql: 
                SELECT 
                    CAST(metric_time AS DATE) as day, EXTRACT(HOUR FROM metric_time) as hour_of_day,
                    AVG(rows_per_second) as avg_speed,
                    AVG(cpu_percent) as avg_cpu,
                    AVG(gpu_utilization) as avg_gpu,
                    SUM(rows_processed) as total_rows,
                    COUNT(*) as operations
                FROM etl_performance_metrics
                WHERE metric_time >= NOW() - INTERVAL '7' DAY
                    AND status = 'success'
                GROUP BY day, hour_of_day
ORDER BY day, hour_of_day
            
SQL Error: Encountered "day" at line 2, column 50.
Was expecting one of:
    <QUOTED_STRING> ...
    <BRACKET_QUOTED_IDENTIFIER> ...
    <QUOTED_IDENTIFIER> ...
    <BACK_QUOTED_IDENTIFIER> ...
    <IDENTIFIER> ...
    <UNICODE_QUOTED_IDENTIFIER> ...
    
unable to rollback
2025-06-05 00:30:01,838 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-05 00:30:02,064 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-05 00:30:02,068 - etl_performance_monitor - INFO - Metrics table ready
2025-06-05 01:00:02,109 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-05 01:00:02,334 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-05 01:00:02,342 - etl_performance_monitor - INFO - Metrics table ready
2025-06-05 01:30:02,403 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-05 01:30:02,631 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-05 01:30:02,635 - etl_performance_monitor - INFO - Metrics table ready
2025-06-05 02:00:02,656 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-05 02:00:02,889 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-05 02:00:02,897 - etl_performance_monitor - INFO - Metrics table ready
2025-06-05 02:30:02,493 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-05 02:30:02,718 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-05 02:30:02,723 - etl_performance_monitor - INFO - Metrics table ready
2025-06-05 03:00:01,742 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-05 03:00:01,968 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-05 03:00:01,972 - etl_performance_monitor - INFO - Metrics table ready
2025-06-05 03:30:02,013 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-05 03:30:02,238 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-05 03:30:02,246 - etl_performance_monitor - INFO - Metrics table ready
2025-06-05 04:00:02,292 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-05 04:00:02,520 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-05 04:00:02,524 - etl_performance_monitor - INFO - Metrics table ready
2025-06-05 04:30:02,570 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-05 04:30:02,796 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-05 04:30:02,800 - etl_performance_monitor - INFO - Metrics table ready
2025-06-05 05:00:01,820 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-05 05:00:02,046 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-05 05:00:02,050 - etl_performance_monitor - INFO - Metrics table ready
2025-06-05 05:30:02,089 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-05 05:30:02,314 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-05 05:30:02,320 - etl_performance_monitor - INFO - Metrics table ready
2025-06-05 06:00:02,395 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-05 06:00:02,405 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-05 06:00:02,629 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-05 06:00:02,633 - etl_performance_monitor - INFO - Metrics table ready
2025-06-05 06:00:02,638 - __main__ - INFO - Connected to HeavyDB
2025-06-05 06:00:02,662 - __main__ - INFO - Metrics table ready
2025-06-05 06:00:02,814 - __main__ - ERROR - Failed to analyze trends: Execution failed on sql: 
                SELECT 
                    CAST(metric_time AS DATE) as day, EXTRACT(HOUR FROM metric_time) as hour_of_day,
                    AVG(rows_per_second) as avg_speed,
                    AVG(cpu_percent) as avg_cpu,
                    AVG(gpu_utilization) as avg_gpu,
                    SUM(rows_processed) as total_rows,
                    COUNT(*) as operations
                FROM etl_performance_metrics
                WHERE metric_time >= NOW() - INTERVAL '7' DAY
                    AND status = 'success'
                GROUP BY day, hour_of_day
ORDER BY day, hour_of_day
            
SQL Error: Encountered "day" at line 2, column 50.
Was expecting one of:
    <QUOTED_STRING> ...
    <BRACKET_QUOTED_IDENTIFIER> ...
    <QUOTED_IDENTIFIER> ...
    <BACK_QUOTED_IDENTIFIER> ...
    <IDENTIFIER> ...
    <UNICODE_QUOTED_IDENTIFIER> ...
    
unable to rollback
2025-06-05 06:30:01,907 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-05 06:30:02,133 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-05 06:30:02,140 - etl_performance_monitor - INFO - Metrics table ready
2025-06-05 07:00:02,171 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-05 07:00:02,396 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-05 07:00:02,402 - etl_performance_monitor - INFO - Metrics table ready
2025-06-05 07:30:02,403 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-05 07:30:02,630 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-05 07:30:02,637 - etl_performance_monitor - INFO - Metrics table ready
2025-06-05 08:00:02,647 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-05 08:00:02,873 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-05 08:00:02,877 - etl_performance_monitor - INFO - Metrics table ready
2025-06-05 08:30:01,897 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-05 08:30:02,123 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-05 08:30:02,128 - etl_performance_monitor - INFO - Metrics table ready
2025-06-05 09:00:02,157 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-05 09:00:02,383 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-05 09:00:02,387 - etl_performance_monitor - INFO - Metrics table ready
2025-06-05 09:30:02,428 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-05 09:30:02,656 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-05 09:30:02,660 - etl_performance_monitor - INFO - Metrics table ready
2025-06-05 10:00:02,697 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-05 10:00:02,926 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-05 10:00:02,934 - etl_performance_monitor - INFO - Metrics table ready
2025-06-05 10:30:01,982 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-05 10:30:02,209 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-05 10:30:02,213 - etl_performance_monitor - INFO - Metrics table ready
2025-06-05 11:00:02,262 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-05 11:00:02,488 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-05 11:00:02,493 - etl_performance_monitor - INFO - Metrics table ready
2025-06-05 11:30:02,523 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-05 11:30:02,750 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-05 11:30:02,757 - etl_performance_monitor - INFO - Metrics table ready
2025-06-05 12:00:01,773 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-05 12:00:01,797 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-05 12:00:02,008 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-05 12:00:02,013 - etl_performance_monitor - INFO - Metrics table ready
2025-06-05 12:00:02,033 - __main__ - INFO - Connected to HeavyDB
2025-06-05 12:00:02,047 - __main__ - INFO - Metrics table ready
2025-06-05 12:00:02,206 - __main__ - ERROR - Failed to analyze trends: Execution failed on sql: 
                SELECT 
                    CAST(metric_time AS DATE) as day, EXTRACT(HOUR FROM metric_time) as hour_of_day,
                    AVG(rows_per_second) as avg_speed,
                    AVG(cpu_percent) as avg_cpu,
                    AVG(gpu_utilization) as avg_gpu,
                    SUM(rows_processed) as total_rows,
                    COUNT(*) as operations
                FROM etl_performance_metrics
                WHERE metric_time >= NOW() - INTERVAL '7' DAY
                    AND status = 'success'
                GROUP BY day, hour_of_day
ORDER BY day, hour_of_day
            
SQL Error: Encountered "day" at line 2, column 50.
Was expecting one of:
    <QUOTED_STRING> ...
    <BRACKET_QUOTED_IDENTIFIER> ...
    <QUOTED_IDENTIFIER> ...
    <BACK_QUOTED_IDENTIFIER> ...
    <IDENTIFIER> ...
    <UNICODE_QUOTED_IDENTIFIER> ...
    
unable to rollback
2025-06-05 12:30:02,242 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-05 12:30:02,461 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-05 12:30:02,465 - etl_performance_monitor - INFO - Metrics table ready
2025-06-05 13:00:02,532 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-05 13:00:02,761 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-05 13:00:02,766 - etl_performance_monitor - INFO - Metrics table ready
2025-06-05 13:30:01,745 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-05 13:30:01,970 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-05 13:30:01,974 - etl_performance_monitor - INFO - Metrics table ready
2025-06-05 14:00:01,969 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-05 14:00:02,194 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-05 14:00:02,202 - etl_performance_monitor - INFO - Metrics table ready
2025-06-05 14:30:02,235 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-05 14:30:02,448 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-05 14:30:02,452 - etl_performance_monitor - INFO - Metrics table ready
2025-06-05 15:00:02,448 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-05 15:00:02,671 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-05 15:00:02,678 - etl_performance_monitor - INFO - Metrics table ready
2025-06-05 15:30:02,680 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-05 15:30:02,895 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-05 15:30:02,900 - etl_performance_monitor - INFO - Metrics table ready
2025-06-05 16:00:01,929 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-05 16:00:02,151 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-05 16:00:02,156 - etl_performance_monitor - INFO - Metrics table ready
2025-06-05 16:30:02,183 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-05 16:30:02,402 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-05 16:30:02,408 - etl_performance_monitor - INFO - Metrics table ready
2025-06-05 17:00:02,436 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-05 17:00:02,658 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-05 17:00:02,667 - etl_performance_monitor - INFO - Metrics table ready
2025-06-05 17:30:02,697 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-05 17:30:02,918 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-05 17:30:02,924 - etl_performance_monitor - INFO - Metrics table ready
2025-06-05 18:00:01,962 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-05 18:00:01,962 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-05 18:00:02,189 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-05 18:00:02,191 - __main__ - INFO - Connected to HeavyDB
2025-06-05 18:00:02,194 - etl_performance_monitor - INFO - Metrics table ready
2025-06-05 18:00:02,225 - __main__ - INFO - Metrics table ready
2025-06-05 18:00:02,372 - __main__ - ERROR - Failed to analyze trends: Execution failed on sql: 
                SELECT 
                    CAST(metric_time AS DATE) as day, EXTRACT(HOUR FROM metric_time) as hour_of_day,
                    AVG(rows_per_second) as avg_speed,
                    AVG(cpu_percent) as avg_cpu,
                    AVG(gpu_utilization) as avg_gpu,
                    SUM(rows_processed) as total_rows,
                    COUNT(*) as operations
                FROM etl_performance_metrics
                WHERE metric_time >= NOW() - INTERVAL '7' DAY
                    AND status = 'success'
                GROUP BY day, hour_of_day
ORDER BY day, hour_of_day
            
SQL Error: Encountered "day" at line 2, column 50.
Was expecting one of:
    <QUOTED_STRING> ...
    <BRACKET_QUOTED_IDENTIFIER> ...
    <QUOTED_IDENTIFIER> ...
    <BACK_QUOTED_IDENTIFIER> ...
    <IDENTIFIER> ...
    <UNICODE_QUOTED_IDENTIFIER> ...
    
unable to rollback
2025-06-05 18:30:02,382 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-05 18:30:02,596 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-05 18:30:02,601 - etl_performance_monitor - INFO - Metrics table ready
2025-06-05 19:00:02,609 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-05 19:00:02,825 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-05 19:00:02,829 - etl_performance_monitor - INFO - Metrics table ready
2025-06-05 19:30:01,855 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-05 19:30:02,080 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-05 19:30:02,085 - etl_performance_monitor - INFO - Metrics table ready
2025-06-05 20:00:02,099 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-05 20:00:02,325 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-05 20:00:02,329 - etl_performance_monitor - INFO - Metrics table ready
2025-06-05 20:30:02,320 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-05 20:30:02,542 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-05 20:30:02,550 - etl_performance_monitor - INFO - Metrics table ready
2025-06-05 21:00:02,553 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-05 21:00:02,780 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-05 21:00:02,784 - etl_performance_monitor - INFO - Metrics table ready
2025-06-05 21:30:01,786 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-05 21:30:02,010 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-05 21:30:02,016 - etl_performance_monitor - INFO - Metrics table ready
2025-06-05 22:00:02,028 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-05 22:00:02,251 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-05 22:00:02,260 - etl_performance_monitor - INFO - Metrics table ready
2025-06-05 22:30:02,275 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-05 22:30:02,503 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-05 22:30:02,512 - etl_performance_monitor - INFO - Metrics table ready
2025-06-05 23:00:02,552 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-05 23:00:02,773 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-05 23:00:02,782 - etl_performance_monitor - INFO - Metrics table ready
2025-06-05 23:30:01,800 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-05 23:30:02,026 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-05 23:30:02,033 - etl_performance_monitor - INFO - Metrics table ready
2025-06-06 00:00:02,113 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-06 00:00:02,118 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-06 00:00:02,347 - __main__ - INFO - Connected to HeavyDB
2025-06-06 00:00:02,351 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-06 00:00:02,353 - __main__ - INFO - Metrics table ready
2025-06-06 00:00:02,475 - etl_performance_monitor - INFO - Metrics table ready
2025-06-06 00:00:02,516 - __main__ - ERROR - Failed to analyze trends: Execution failed on sql: 
                SELECT 
                    CAST(metric_time AS DATE) as day, EXTRACT(HOUR FROM metric_time) as hour_of_day,
                    AVG(rows_per_second) as avg_speed,
                    AVG(cpu_percent) as avg_cpu,
                    AVG(gpu_utilization) as avg_gpu,
                    SUM(rows_processed) as total_rows,
                    COUNT(*) as operations
                FROM etl_performance_metrics
                WHERE metric_time >= NOW() - INTERVAL '7' DAY
                    AND status = 'success'
                GROUP BY day, hour_of_day
ORDER BY day, hour_of_day
            
SQL Error: Encountered "day" at line 2, column 50.
Was expecting one of:
    <QUOTED_STRING> ...
    <BRACKET_QUOTED_IDENTIFIER> ...
    <QUOTED_IDENTIFIER> ...
    <BACK_QUOTED_IDENTIFIER> ...
    <IDENTIFIER> ...
    <UNICODE_QUOTED_IDENTIFIER> ...
    
unable to rollback
2025-06-06 00:30:02,543 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-06 00:30:02,770 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-06 00:30:02,774 - etl_performance_monitor - INFO - Metrics table ready
2025-06-06 01:00:01,783 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-06 01:00:02,005 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-06 01:00:02,012 - etl_performance_monitor - INFO - Metrics table ready
2025-06-06 01:30:02,011 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-06 01:30:02,238 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-06 01:30:02,246 - etl_performance_monitor - INFO - Metrics table ready
2025-06-06 02:00:02,291 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-06 02:00:02,522 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-06 02:00:02,565 - etl_performance_monitor - INFO - Metrics table ready
2025-06-06 02:30:01,929 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-06 02:30:02,154 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-06 02:30:02,159 - etl_performance_monitor - INFO - Metrics table ready
2025-06-06 03:00:02,164 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-06 03:00:02,391 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-06 03:00:02,395 - etl_performance_monitor - INFO - Metrics table ready
2025-06-06 03:30:02,421 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-06 03:30:02,641 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-06 03:30:02,647 - etl_performance_monitor - INFO - Metrics table ready
2025-06-06 04:00:02,641 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-06 04:00:02,863 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-06 04:00:02,870 - etl_performance_monitor - INFO - Metrics table ready
2025-06-06 04:30:01,893 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-06 04:30:02,121 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-06 04:30:02,129 - etl_performance_monitor - INFO - Metrics table ready
2025-06-06 05:00:02,119 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-06 05:00:02,337 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-06 05:00:02,342 - etl_performance_monitor - INFO - Metrics table ready
2025-06-06 05:30:02,361 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-06 05:30:02,579 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-06 05:30:02,583 - etl_performance_monitor - INFO - Metrics table ready
2025-06-06 06:00:02,630 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-06 06:00:02,635 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-06 06:00:02,862 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-06 06:00:02,865 - __main__ - INFO - Connected to HeavyDB
2025-06-06 06:00:02,867 - etl_performance_monitor - INFO - Metrics table ready
2025-06-06 06:00:02,904 - __main__ - INFO - Metrics table ready
2025-06-06 06:00:03,063 - __main__ - ERROR - Failed to analyze trends: Execution failed on sql: 
                SELECT 
                    CAST(metric_time AS DATE) as day, EXTRACT(HOUR FROM metric_time) as hour_of_day,
                    AVG(rows_per_second) as avg_speed,
                    AVG(cpu_percent) as avg_cpu,
                    AVG(gpu_utilization) as avg_gpu,
                    SUM(rows_processed) as total_rows,
                    COUNT(*) as operations
                FROM etl_performance_metrics
                WHERE metric_time >= NOW() - INTERVAL '7' DAY
                    AND status = 'success'
                GROUP BY day, hour_of_day
ORDER BY day, hour_of_day
            
SQL Error: Encountered "day" at line 2, column 50.
Was expecting one of:
    <QUOTED_STRING> ...
    <BRACKET_QUOTED_IDENTIFIER> ...
    <QUOTED_IDENTIFIER> ...
    <BACK_QUOTED_IDENTIFIER> ...
    <IDENTIFIER> ...
    <UNICODE_QUOTED_IDENTIFIER> ...
    
unable to rollback
2025-06-06 06:30:02,123 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-06 06:30:02,347 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-06 06:30:02,353 - etl_performance_monitor - INFO - Metrics table ready
2025-06-06 07:00:02,349 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-06 07:00:02,570 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-06 07:00:02,577 - etl_performance_monitor - INFO - Metrics table ready
2025-06-06 07:30:02,600 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-06 07:30:02,818 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-06 07:30:02,825 - etl_performance_monitor - INFO - Metrics table ready
2025-06-06 08:00:01,811 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-06 08:00:02,029 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-06 08:00:02,033 - etl_performance_monitor - INFO - Metrics table ready
2025-06-06 08:30:02,068 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-06 08:30:02,296 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-06 08:30:02,300 - etl_performance_monitor - INFO - Metrics table ready
2025-06-06 09:00:02,313 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-06 09:00:02,546 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-06 09:00:02,551 - etl_performance_monitor - INFO - Metrics table ready
2025-06-06 09:30:02,596 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-06 09:30:02,826 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-06 09:30:02,830 - etl_performance_monitor - INFO - Metrics table ready
2025-06-06 10:00:01,852 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-06 10:00:02,079 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-06 10:00:02,083 - etl_performance_monitor - INFO - Metrics table ready
2025-06-06 10:30:02,095 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-06 10:30:02,328 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-06 10:30:02,333 - etl_performance_monitor - INFO - Metrics table ready
2025-06-06 11:00:02,344 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-06 11:00:02,574 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-06 11:00:02,579 - etl_performance_monitor - INFO - Metrics table ready
2025-06-06 11:30:02,609 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-06 11:30:02,839 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-06 11:30:02,843 - etl_performance_monitor - INFO - Metrics table ready
2025-06-06 12:00:01,856 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-06 12:00:01,884 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-06 12:00:02,088 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-06 12:00:02,092 - etl_performance_monitor - INFO - Metrics table ready
2025-06-06 12:00:02,117 - __main__ - INFO - Connected to HeavyDB
2025-06-06 12:00:02,131 - __main__ - INFO - Metrics table ready
2025-06-06 12:00:02,274 - __main__ - ERROR - Failed to analyze trends: Execution failed on sql: 
                SELECT 
                    CAST(metric_time AS DATE) as day, EXTRACT(HOUR FROM metric_time) as hour_of_day,
                    AVG(rows_per_second) as avg_speed,
                    AVG(cpu_percent) as avg_cpu,
                    AVG(gpu_utilization) as avg_gpu,
                    SUM(rows_processed) as total_rows,
                    COUNT(*) as operations
                FROM etl_performance_metrics
                WHERE metric_time >= NOW() - INTERVAL '7' DAY
                    AND status = 'success'
                GROUP BY day, hour_of_day
ORDER BY day, hour_of_day
            
SQL Error: Encountered "day" at line 2, column 50.
Was expecting one of:
    <QUOTED_STRING> ...
    <BRACKET_QUOTED_IDENTIFIER> ...
    <QUOTED_IDENTIFIER> ...
    <BACK_QUOTED_IDENTIFIER> ...
    <IDENTIFIER> ...
    <UNICODE_QUOTED_IDENTIFIER> ...
    
unable to rollback
2025-06-06 12:30:02,315 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-06 12:30:02,546 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-06 12:30:02,550 - etl_performance_monitor - INFO - Metrics table ready
2025-06-06 13:00:02,622 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-06 13:00:02,857 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-06 13:00:02,863 - etl_performance_monitor - INFO - Metrics table ready
2025-06-06 13:30:01,896 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-06 13:30:02,126 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-06 13:30:02,130 - etl_performance_monitor - INFO - Metrics table ready
2025-06-06 14:00:02,152 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-06 14:00:02,383 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-06 14:00:02,387 - etl_performance_monitor - INFO - Metrics table ready
2025-06-06 14:30:02,392 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-06 14:30:02,623 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-06 14:30:02,631 - etl_performance_monitor - INFO - Metrics table ready
2025-06-06 15:00:02,656 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-06 15:00:02,882 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-06 15:00:02,887 - etl_performance_monitor - INFO - Metrics table ready
2025-06-06 15:30:01,875 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-06 15:30:02,104 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-06 15:30:02,108 - etl_performance_monitor - INFO - Metrics table ready
2025-06-06 16:00:02,062 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-06 16:00:02,293 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-06 16:00:02,297 - etl_performance_monitor - INFO - Metrics table ready
2025-06-06 16:30:02,295 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-06 16:30:02,521 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-06 16:30:02,528 - etl_performance_monitor - INFO - Metrics table ready
2025-06-06 17:00:02,564 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-06 17:00:02,792 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-06 17:00:02,800 - etl_performance_monitor - INFO - Metrics table ready
2025-06-06 17:30:01,834 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-06 17:30:02,063 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-06 17:30:02,071 - etl_performance_monitor - INFO - Metrics table ready
2025-06-06 18:00:02,117 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-06 18:00:02,145 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-06 18:00:02,352 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-06 18:00:02,357 - etl_performance_monitor - INFO - Metrics table ready
2025-06-06 18:00:02,382 - __main__ - INFO - Connected to HeavyDB
2025-06-06 18:00:02,388 - __main__ - INFO - Metrics table ready
2025-06-06 18:00:02,534 - __main__ - ERROR - Failed to analyze trends: Execution failed on sql: 
                SELECT 
                    CAST(metric_time AS DATE) as day, EXTRACT(HOUR FROM metric_time) as hour_of_day,
                    AVG(rows_per_second) as avg_speed,
                    AVG(cpu_percent) as avg_cpu,
                    AVG(gpu_utilization) as avg_gpu,
                    SUM(rows_processed) as total_rows,
                    COUNT(*) as operations
                FROM etl_performance_metrics
                WHERE metric_time >= NOW() - INTERVAL '7' DAY
                    AND status = 'success'
                GROUP BY day, hour_of_day
ORDER BY day, hour_of_day
            
SQL Error: Encountered "day" at line 2, column 50.
Was expecting one of:
    <QUOTED_STRING> ...
    <BRACKET_QUOTED_IDENTIFIER> ...
    <QUOTED_IDENTIFIER> ...
    <BACK_QUOTED_IDENTIFIER> ...
    <IDENTIFIER> ...
    <UNICODE_QUOTED_IDENTIFIER> ...
    
unable to rollback
2025-06-06 18:30:02,574 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-06 18:30:02,802 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-06 18:30:02,806 - etl_performance_monitor - INFO - Metrics table ready
2025-06-06 19:00:01,818 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-06 19:00:02,050 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-06 19:00:02,058 - etl_performance_monitor - INFO - Metrics table ready
2025-06-06 19:30:02,080 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-06 19:30:02,306 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-06 19:30:02,314 - etl_performance_monitor - INFO - Metrics table ready
2025-06-06 20:00:02,345 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-06 20:00:02,575 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-06 20:00:02,580 - etl_performance_monitor - INFO - Metrics table ready
2025-06-06 20:30:02,609 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-06 20:30:02,840 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-06 20:30:02,843 - etl_performance_monitor - INFO - Metrics table ready
2025-06-06 21:00:01,860 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-06 21:00:02,088 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-06 21:00:02,092 - etl_performance_monitor - INFO - Metrics table ready
2025-06-06 21:30:02,119 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-06 21:30:02,350 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-06 21:30:02,354 - etl_performance_monitor - INFO - Metrics table ready
2025-06-06 22:00:02,376 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-06 22:00:02,607 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-06 22:00:02,614 - etl_performance_monitor - INFO - Metrics table ready
2025-06-06 22:30:02,614 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-06 22:30:02,840 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-06 22:30:02,845 - etl_performance_monitor - INFO - Metrics table ready
2025-06-06 23:00:01,859 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-06 23:00:02,087 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-06 23:00:02,091 - etl_performance_monitor - INFO - Metrics table ready
2025-06-06 23:30:02,097 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-06 23:30:02,328 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-06 23:30:02,335 - etl_performance_monitor - INFO - Metrics table ready
2025-06-07 00:00:02,478 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-07 00:00:02,486 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-07 00:00:02,714 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-07 00:00:02,717 - etl_performance_monitor - INFO - Metrics table ready
2025-06-07 00:00:02,721 - __main__ - INFO - Connected to HeavyDB
2025-06-07 00:00:02,749 - __main__ - INFO - Metrics table ready
2025-06-07 00:00:02,910 - __main__ - ERROR - Failed to analyze trends: Execution failed on sql: 
                SELECT 
                    CAST(metric_time AS DATE) as day, EXTRACT(HOUR FROM metric_time) as hour_of_day,
                    AVG(rows_per_second) as avg_speed,
                    AVG(cpu_percent) as avg_cpu,
                    AVG(gpu_utilization) as avg_gpu,
                    SUM(rows_processed) as total_rows,
                    COUNT(*) as operations
                FROM etl_performance_metrics
                WHERE metric_time >= NOW() - INTERVAL '7' DAY
                    AND status = 'success'
                GROUP BY day, hour_of_day
ORDER BY day, hour_of_day
            
SQL Error: Encountered "day" at line 2, column 50.
Was expecting one of:
    <QUOTED_STRING> ...
    <BRACKET_QUOTED_IDENTIFIER> ...
    <QUOTED_IDENTIFIER> ...
    <BACK_QUOTED_IDENTIFIER> ...
    <IDENTIFIER> ...
    <UNICODE_QUOTED_IDENTIFIER> ...
    
unable to rollback
2025-06-07 00:30:01,939 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-07 00:30:02,168 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-07 00:30:02,172 - etl_performance_monitor - INFO - Metrics table ready
2025-06-07 01:00:02,176 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-07 01:00:02,403 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-07 01:00:02,407 - etl_performance_monitor - INFO - Metrics table ready
2025-06-07 01:30:02,427 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-07 01:30:02,657 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-07 01:30:02,662 - etl_performance_monitor - INFO - Metrics table ready
2025-06-07 02:00:02,726 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-07 02:00:02,964 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-07 02:00:02,996 - etl_performance_monitor - INFO - Metrics table ready
2025-06-07 02:30:02,376 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-07 02:30:02,609 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-07 02:30:02,613 - etl_performance_monitor - INFO - Metrics table ready
2025-06-07 03:00:02,606 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-07 03:00:02,837 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-07 03:00:02,841 - etl_performance_monitor - INFO - Metrics table ready
2025-06-07 03:30:01,872 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-07 03:30:02,103 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-07 03:30:02,107 - etl_performance_monitor - INFO - Metrics table ready
2025-06-07 04:00:02,114 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-07 04:00:02,347 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-07 04:00:02,352 - etl_performance_monitor - INFO - Metrics table ready
2025-06-07 04:30:02,378 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-07 04:30:02,603 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-07 04:30:02,610 - etl_performance_monitor - INFO - Metrics table ready
2025-06-07 05:00:02,660 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-07 05:00:02,888 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-07 05:00:02,892 - etl_performance_monitor - INFO - Metrics table ready
2025-06-07 05:30:01,891 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-07 05:30:02,122 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-07 05:30:02,126 - etl_performance_monitor - INFO - Metrics table ready
2025-06-07 06:00:02,143 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-07 06:00:02,160 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-07 06:00:02,378 - __main__ - INFO - Connected to HeavyDB
2025-06-07 06:00:02,382 - __main__ - INFO - Metrics table ready
2025-06-07 06:00:02,395 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-07 06:00:02,501 - etl_performance_monitor - INFO - Metrics table ready
2025-06-07 06:00:02,538 - __main__ - ERROR - Failed to analyze trends: Execution failed on sql: 
                SELECT 
                    CAST(metric_time AS DATE) as day, EXTRACT(HOUR FROM metric_time) as hour_of_day,
                    AVG(rows_per_second) as avg_speed,
                    AVG(cpu_percent) as avg_cpu,
                    AVG(gpu_utilization) as avg_gpu,
                    SUM(rows_processed) as total_rows,
                    COUNT(*) as operations
                FROM etl_performance_metrics
                WHERE metric_time >= NOW() - INTERVAL '7' DAY
                    AND status = 'success'
                GROUP BY day, hour_of_day
ORDER BY day, hour_of_day
            
SQL Error: Encountered "day" at line 2, column 50.
Was expecting one of:
    <QUOTED_STRING> ...
    <BRACKET_QUOTED_IDENTIFIER> ...
    <QUOTED_IDENTIFIER> ...
    <BACK_QUOTED_IDENTIFIER> ...
    <IDENTIFIER> ...
    <UNICODE_QUOTED_IDENTIFIER> ...
    
unable to rollback
2025-06-07 06:30:02,636 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-07 06:30:02,868 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-07 06:30:02,876 - etl_performance_monitor - INFO - Metrics table ready
2025-06-07 07:00:01,876 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-07 07:00:02,102 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-07 07:00:02,106 - etl_performance_monitor - INFO - Metrics table ready
2025-06-07 07:30:02,135 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-07 07:30:02,363 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-07 07:30:02,367 - etl_performance_monitor - INFO - Metrics table ready
2025-06-07 08:00:02,355 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-07 08:00:02,584 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-07 08:00:02,588 - etl_performance_monitor - INFO - Metrics table ready
2025-06-07 08:30:02,578 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-07 08:30:02,805 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-07 08:30:02,812 - etl_performance_monitor - INFO - Metrics table ready
2025-06-07 09:00:01,814 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-07 09:00:02,039 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-07 09:00:02,042 - etl_performance_monitor - INFO - Metrics table ready
2025-06-07 09:30:02,043 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-07 09:30:02,264 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-07 09:30:02,268 - etl_performance_monitor - INFO - Metrics table ready
2025-06-07 10:00:02,226 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-07 10:00:02,450 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-07 10:00:02,454 - etl_performance_monitor - INFO - Metrics table ready
2025-06-07 10:30:02,430 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-07 10:30:02,653 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-07 10:30:02,658 - etl_performance_monitor - INFO - Metrics table ready
2025-06-07 11:00:02,629 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-07 11:00:02,851 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-07 11:00:02,858 - etl_performance_monitor - INFO - Metrics table ready
2025-06-07 11:30:01,840 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-07 11:30:02,060 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-07 11:30:02,066 - etl_performance_monitor - INFO - Metrics table ready
2025-06-07 12:00:02,068 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-07 12:00:02,097 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-07 12:00:02,300 - __main__ - INFO - Connected to HeavyDB
2025-06-07 12:00:02,308 - __main__ - INFO - Metrics table ready
2025-06-07 12:00:02,328 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-07 12:00:02,436 - etl_performance_monitor - INFO - Metrics table ready
2025-06-07 12:00:02,470 - __main__ - ERROR - Failed to analyze trends: Execution failed on sql: 
                SELECT 
                    CAST(metric_time AS DATE) as day, EXTRACT(HOUR FROM metric_time) as hour_of_day,
                    AVG(rows_per_second) as avg_speed,
                    AVG(cpu_percent) as avg_cpu,
                    AVG(gpu_utilization) as avg_gpu,
                    SUM(rows_processed) as total_rows,
                    COUNT(*) as operations
                FROM etl_performance_metrics
                WHERE metric_time >= NOW() - INTERVAL '7' DAY
                    AND status = 'success'
                GROUP BY day, hour_of_day
ORDER BY day, hour_of_day
            
SQL Error: Encountered "day" at line 2, column 50.
Was expecting one of:
    <QUOTED_STRING> ...
    <BRACKET_QUOTED_IDENTIFIER> ...
    <QUOTED_IDENTIFIER> ...
    <BACK_QUOTED_IDENTIFIER> ...
    <IDENTIFIER> ...
    <UNICODE_QUOTED_IDENTIFIER> ...
    
unable to rollback
2025-06-07 12:30:02,462 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-07 12:30:02,679 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-07 12:30:02,687 - etl_performance_monitor - INFO - Metrics table ready
2025-06-07 13:00:01,762 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-07 13:00:01,999 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-07 13:00:02,013 - etl_performance_monitor - INFO - Metrics table ready
2025-06-07 13:30:02,034 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-07 13:30:02,261 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-07 13:30:02,266 - etl_performance_monitor - INFO - Metrics table ready
2025-06-07 14:00:02,276 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-07 14:00:02,498 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-07 14:00:02,502 - etl_performance_monitor - INFO - Metrics table ready
2025-06-07 14:30:02,486 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-07 14:30:02,701 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-07 14:30:02,708 - etl_performance_monitor - INFO - Metrics table ready
2025-06-07 15:00:01,738 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-07 15:00:01,959 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-07 15:00:01,963 - etl_performance_monitor - INFO - Metrics table ready
2025-06-07 15:30:01,984 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-07 15:30:02,201 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-07 15:30:02,206 - etl_performance_monitor - INFO - Metrics table ready
2025-06-07 16:00:02,192 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-07 16:00:02,415 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-07 16:00:02,421 - etl_performance_monitor - INFO - Metrics table ready
2025-06-07 16:30:02,400 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-07 16:30:02,625 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-07 16:30:02,631 - etl_performance_monitor - INFO - Metrics table ready
2025-06-07 17:00:02,618 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-07 17:00:02,832 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-07 17:00:02,839 - etl_performance_monitor - INFO - Metrics table ready
2025-06-07 17:30:01,828 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-07 17:30:02,041 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-07 17:30:02,046 - etl_performance_monitor - INFO - Metrics table ready
2025-06-07 18:00:02,032 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-07 18:00:02,052 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-07 18:00:02,258 - __main__ - INFO - Connected to HeavyDB
2025-06-07 18:00:02,266 - __main__ - INFO - Metrics table ready
2025-06-07 18:00:02,279 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-07 18:00:02,392 - etl_performance_monitor - INFO - Metrics table ready
2025-06-07 18:00:02,427 - __main__ - ERROR - Failed to analyze trends: Execution failed on sql: 
                SELECT 
                    CAST(metric_time AS DATE) as day, EXTRACT(HOUR FROM metric_time) as hour_of_day,
                    AVG(rows_per_second) as avg_speed,
                    AVG(cpu_percent) as avg_cpu,
                    AVG(gpu_utilization) as avg_gpu,
                    SUM(rows_processed) as total_rows,
                    COUNT(*) as operations
                FROM etl_performance_metrics
                WHERE metric_time >= NOW() - INTERVAL '7' DAY
                    AND status = 'success'
                GROUP BY day, hour_of_day
ORDER BY day, hour_of_day
            
SQL Error: Encountered "day" at line 2, column 50.
Was expecting one of:
    <QUOTED_STRING> ...
    <BRACKET_QUOTED_IDENTIFIER> ...
    <QUOTED_IDENTIFIER> ...
    <BACK_QUOTED_IDENTIFIER> ...
    <IDENTIFIER> ...
    <UNICODE_QUOTED_IDENTIFIER> ...
    
unable to rollback
2025-06-07 18:30:02,438 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-07 18:30:02,659 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-07 18:30:02,668 - etl_performance_monitor - INFO - Metrics table ready
2025-06-07 19:00:02,657 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-07 19:00:02,873 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-07 19:00:02,881 - etl_performance_monitor - INFO - Metrics table ready
2025-06-07 19:30:01,876 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-07 19:30:02,094 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-07 19:30:02,100 - etl_performance_monitor - INFO - Metrics table ready
2025-06-07 20:00:02,046 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-07 20:00:02,265 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-07 20:00:02,270 - etl_performance_monitor - INFO - Metrics table ready
2025-06-07 20:30:02,249 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-07 20:30:02,477 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-07 20:30:02,485 - etl_performance_monitor - INFO - Metrics table ready
2025-06-07 21:00:02,480 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-07 21:00:02,698 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-07 21:00:02,702 - etl_performance_monitor - INFO - Metrics table ready
2025-06-07 21:30:01,716 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-07 21:30:01,933 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-07 21:30:01,938 - etl_performance_monitor - INFO - Metrics table ready
2025-06-07 22:00:01,908 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-07 22:00:02,124 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-07 22:00:02,132 - etl_performance_monitor - INFO - Metrics table ready
2025-06-07 22:30:02,140 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-07 22:30:02,359 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-07 22:30:02,362 - etl_performance_monitor - INFO - Metrics table ready
2025-06-07 23:00:02,354 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-07 23:00:02,574 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-07 23:00:02,579 - etl_performance_monitor - INFO - Metrics table ready
2025-06-07 23:30:02,577 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-07 23:30:02,793 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-07 23:30:02,799 - etl_performance_monitor - INFO - Metrics table ready
2025-06-08 00:00:01,865 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-08 00:00:01,868 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-08 00:00:02,099 - __main__ - INFO - Connected to HeavyDB
2025-06-08 00:00:02,101 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-08 00:00:02,103 - __main__ - INFO - Metrics table ready
2025-06-08 00:00:02,228 - etl_performance_monitor - INFO - Metrics table ready
2025-06-08 00:00:02,270 - __main__ - ERROR - Failed to analyze trends: Execution failed on sql: 
                SELECT 
                    CAST(metric_time AS DATE) as day, EXTRACT(HOUR FROM metric_time) as hour_of_day,
                    AVG(rows_per_second) as avg_speed,
                    AVG(cpu_percent) as avg_cpu,
                    AVG(gpu_utilization) as avg_gpu,
                    SUM(rows_processed) as total_rows,
                    COUNT(*) as operations
                FROM etl_performance_metrics
                WHERE metric_time >= NOW() - INTERVAL '7' DAY
                    AND status = 'success'
                GROUP BY day, hour_of_day
ORDER BY day, hour_of_day
            
SQL Error: Encountered "day" at line 2, column 50.
Was expecting one of:
    <QUOTED_STRING> ...
    <BRACKET_QUOTED_IDENTIFIER> ...
    <QUOTED_IDENTIFIER> ...
    <BACK_QUOTED_IDENTIFIER> ...
    <IDENTIFIER> ...
    <UNICODE_QUOTED_IDENTIFIER> ...
    
unable to rollback
2025-06-08 00:30:02,289 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-08 00:30:02,509 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-08 00:30:02,514 - etl_performance_monitor - INFO - Metrics table ready
2025-06-08 01:00:02,494 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-08 01:00:02,712 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-08 01:00:02,719 - etl_performance_monitor - INFO - Metrics table ready
2025-06-08 01:30:01,698 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-08 01:30:01,917 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-08 01:30:01,921 - etl_performance_monitor - INFO - Metrics table ready
2025-06-08 02:00:01,963 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-08 02:00:02,191 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-08 02:00:02,254 - etl_performance_monitor - INFO - Metrics table ready
2025-06-08 02:30:02,650 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-08 02:30:02,869 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-08 02:30:02,873 - etl_performance_monitor - INFO - Metrics table ready
2025-06-08 03:00:01,868 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-08 03:00:02,085 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-08 03:00:02,089 - etl_performance_monitor - INFO - Metrics table ready
2025-06-08 03:30:02,097 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-08 03:30:02,315 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-08 03:30:02,323 - etl_performance_monitor - INFO - Metrics table ready
2025-06-08 04:00:02,312 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-08 04:00:02,529 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-08 04:00:02,534 - etl_performance_monitor - INFO - Metrics table ready
2025-06-08 04:30:02,512 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-08 04:30:02,730 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-08 04:30:02,738 - etl_performance_monitor - INFO - Metrics table ready
2025-06-08 05:00:01,748 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-08 05:00:01,966 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-08 05:00:01,970 - etl_performance_monitor - INFO - Metrics table ready
2025-06-08 05:30:01,958 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-08 05:30:02,176 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-08 05:30:02,181 - etl_performance_monitor - INFO - Metrics table ready
2025-06-08 06:00:02,169 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-08 06:00:02,212 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-08 06:00:02,399 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-08 06:00:02,406 - etl_performance_monitor - INFO - Metrics table ready
2025-06-08 06:00:02,442 - __main__ - INFO - Connected to HeavyDB
2025-06-08 06:00:02,478 - __main__ - INFO - Metrics table ready
2025-06-08 06:00:02,699 - __main__ - ERROR - Failed to analyze trends: Execution failed on sql: 
                SELECT 
                    CAST(metric_time AS DATE) as day, EXTRACT(HOUR FROM metric_time) as hour_of_day,
                    AVG(rows_per_second) as avg_speed,
                    AVG(cpu_percent) as avg_cpu,
                    AVG(gpu_utilization) as avg_gpu,
                    SUM(rows_processed) as total_rows,
                    COUNT(*) as operations
                FROM etl_performance_metrics
                WHERE metric_time >= NOW() - INTERVAL '7' DAY
                    AND status = 'success'
                GROUP BY day, hour_of_day
ORDER BY day, hour_of_day
            
SQL Error: Encountered "day" at line 2, column 50.
Was expecting one of:
    <QUOTED_STRING> ...
    <BRACKET_QUOTED_IDENTIFIER> ...
    <QUOTED_IDENTIFIER> ...
    <BACK_QUOTED_IDENTIFIER> ...
    <IDENTIFIER> ...
    <UNICODE_QUOTED_IDENTIFIER> ...
    
unable to rollback
2025-06-08 06:30:01,768 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-08 06:30:01,984 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-08 06:30:01,989 - etl_performance_monitor - INFO - Metrics table ready
2025-06-08 07:00:01,998 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-08 07:00:02,217 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-08 07:00:02,221 - etl_performance_monitor - INFO - Metrics table ready
2025-06-08 07:30:02,223 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-08 07:30:02,440 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-08 07:30:02,446 - etl_performance_monitor - INFO - Metrics table ready
2025-06-08 08:00:02,446 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-08 08:00:02,663 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-08 08:00:02,667 - etl_performance_monitor - INFO - Metrics table ready
2025-06-08 08:30:02,645 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-08 08:30:02,862 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-08 08:30:02,866 - etl_performance_monitor - INFO - Metrics table ready
2025-06-08 09:00:01,788 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-08 09:00:02,006 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-08 09:00:02,013 - etl_performance_monitor - INFO - Metrics table ready
2025-06-08 09:30:01,997 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-08 09:30:02,213 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-08 09:30:02,218 - etl_performance_monitor - INFO - Metrics table ready
2025-06-08 10:00:02,238 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-08 10:00:02,456 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-08 10:00:02,463 - etl_performance_monitor - INFO - Metrics table ready
2025-06-08 10:30:02,485 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-08 10:30:02,701 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-08 10:30:02,707 - etl_performance_monitor - INFO - Metrics table ready
2025-06-08 11:00:02,660 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-08 11:00:02,878 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-08 11:00:02,886 - etl_performance_monitor - INFO - Metrics table ready
2025-06-08 11:30:01,863 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-08 11:30:02,081 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-08 11:30:02,089 - etl_performance_monitor - INFO - Metrics table ready
2025-06-08 12:00:02,115 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-08 12:00:02,160 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-08 12:00:02,346 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-08 12:00:02,353 - etl_performance_monitor - INFO - Metrics table ready
2025-06-08 12:00:02,389 - __main__ - INFO - Connected to HeavyDB
2025-06-08 12:00:02,397 - __main__ - INFO - Metrics table ready
2025-06-08 12:00:02,553 - __main__ - ERROR - Failed to analyze trends: Execution failed on sql: 
                SELECT 
                    CAST(metric_time AS DATE) as day, EXTRACT(HOUR FROM metric_time) as hour_of_day,
                    AVG(rows_per_second) as avg_speed,
                    AVG(cpu_percent) as avg_cpu,
                    AVG(gpu_utilization) as avg_gpu,
                    SUM(rows_processed) as total_rows,
                    COUNT(*) as operations
                FROM etl_performance_metrics
                WHERE metric_time >= NOW() - INTERVAL '7' DAY
                    AND status = 'success'
                GROUP BY day, hour_of_day
ORDER BY day, hour_of_day
            
SQL Error: Encountered "day" at line 2, column 50.
Was expecting one of:
    <QUOTED_STRING> ...
    <BRACKET_QUOTED_IDENTIFIER> ...
    <QUOTED_IDENTIFIER> ...
    <BACK_QUOTED_IDENTIFIER> ...
    <IDENTIFIER> ...
    <UNICODE_QUOTED_IDENTIFIER> ...
    
unable to rollback
2025-06-08 12:30:02,553 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-08 12:30:02,771 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-08 12:30:02,779 - etl_performance_monitor - INFO - Metrics table ready
2025-06-08 13:00:01,794 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-08 13:00:02,024 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-08 13:00:02,029 - etl_performance_monitor - INFO - Metrics table ready
2025-06-08 13:30:02,063 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-08 13:30:02,278 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-08 13:30:02,282 - etl_performance_monitor - INFO - Metrics table ready
2025-06-08 14:00:02,265 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-08 14:00:02,482 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-08 14:00:02,487 - etl_performance_monitor - INFO - Metrics table ready
2025-06-08 14:30:02,478 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-08 14:30:02,694 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-08 14:30:02,700 - etl_performance_monitor - INFO - Metrics table ready
2025-06-08 15:00:02,703 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-08 15:00:02,918 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-08 15:00:02,924 - etl_performance_monitor - INFO - Metrics table ready
2025-06-08 15:30:01,905 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-08 15:30:02,122 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-08 15:30:02,130 - etl_performance_monitor - INFO - Metrics table ready
2025-06-08 16:00:02,133 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-08 16:00:02,349 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-08 16:00:02,357 - etl_performance_monitor - INFO - Metrics table ready
2025-06-08 16:30:02,360 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-08 16:30:02,578 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-08 16:30:02,582 - etl_performance_monitor - INFO - Metrics table ready
2025-06-08 17:00:02,585 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-08 17:00:02,802 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-08 17:00:02,807 - etl_performance_monitor - INFO - Metrics table ready
2025-06-08 17:30:01,801 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-08 17:30:02,019 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-08 17:30:02,024 - etl_performance_monitor - INFO - Metrics table ready
2025-06-08 18:00:02,048 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-08 18:00:02,104 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-08 18:00:02,278 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-08 18:00:02,283 - etl_performance_monitor - INFO - Metrics table ready
2025-06-08 18:00:02,334 - __main__ - INFO - Connected to HeavyDB
2025-06-08 18:00:02,370 - __main__ - INFO - Metrics table ready
2025-06-08 18:00:02,591 - __main__ - ERROR - Failed to analyze trends: Execution failed on sql: 
                SELECT 
                    CAST(metric_time AS DATE) as day, EXTRACT(HOUR FROM metric_time) as hour_of_day,
                    AVG(rows_per_second) as avg_speed,
                    AVG(cpu_percent) as avg_cpu,
                    AVG(gpu_utilization) as avg_gpu,
                    SUM(rows_processed) as total_rows,
                    COUNT(*) as operations
                FROM etl_performance_metrics
                WHERE metric_time >= NOW() - INTERVAL '7' DAY
                    AND status = 'success'
                GROUP BY day, hour_of_day
ORDER BY day, hour_of_day
            
SQL Error: Encountered "day" at line 2, column 50.
Was expecting one of:
    <QUOTED_STRING> ...
    <BRACKET_QUOTED_IDENTIFIER> ...
    <QUOTED_IDENTIFIER> ...
    <BACK_QUOTED_IDENTIFIER> ...
    <IDENTIFIER> ...
    <UNICODE_QUOTED_IDENTIFIER> ...
    
unable to rollback
2025-06-08 18:30:02,589 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-08 18:30:02,808 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-08 18:30:02,814 - etl_performance_monitor - INFO - Metrics table ready
2025-06-08 19:00:01,794 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-08 19:00:02,008 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-08 19:00:02,013 - etl_performance_monitor - INFO - Metrics table ready
2025-06-08 19:30:02,007 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-08 19:30:02,225 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-08 19:30:02,230 - etl_performance_monitor - INFO - Metrics table ready
2025-06-08 20:00:02,233 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-08 20:00:02,451 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-08 20:00:02,456 - etl_performance_monitor - INFO - Metrics table ready
2025-06-08 20:30:02,459 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-08 20:30:02,676 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-08 20:30:02,680 - etl_performance_monitor - INFO - Metrics table ready
2025-06-08 21:00:02,668 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-08 21:00:02,883 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-08 21:00:02,888 - etl_performance_monitor - INFO - Metrics table ready
2025-06-08 21:30:01,899 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-08 21:30:02,115 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-08 21:30:02,121 - etl_performance_monitor - INFO - Metrics table ready
2025-06-08 22:00:02,119 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-08 22:00:02,338 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-08 22:00:02,342 - etl_performance_monitor - INFO - Metrics table ready
2025-06-08 22:30:02,340 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-08 22:30:02,559 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-08 22:30:02,563 - etl_performance_monitor - INFO - Metrics table ready
2025-06-08 23:00:02,537 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-08 23:00:02,754 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-08 23:00:02,761 - etl_performance_monitor - INFO - Metrics table ready
2025-06-08 23:30:01,754 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-08 23:30:01,971 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-08 23:30:01,975 - etl_performance_monitor - INFO - Metrics table ready
2025-06-09 00:00:02,022 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-09 00:00:02,034 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-09 00:00:02,253 - __main__ - INFO - Connected to HeavyDB
2025-06-09 00:00:02,258 - __main__ - INFO - Metrics table ready
2025-06-09 00:00:02,261 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-09 00:00:02,384 - etl_performance_monitor - INFO - Metrics table ready
2025-06-09 00:00:02,421 - __main__ - ERROR - Failed to analyze trends: Execution failed on sql: 
                SELECT 
                    CAST(metric_time AS DATE) as day, EXTRACT(HOUR FROM metric_time) as hour_of_day,
                    AVG(rows_per_second) as avg_speed,
                    AVG(cpu_percent) as avg_cpu,
                    AVG(gpu_utilization) as avg_gpu,
                    SUM(rows_processed) as total_rows,
                    COUNT(*) as operations
                FROM etl_performance_metrics
                WHERE metric_time >= NOW() - INTERVAL '7' DAY
                    AND status = 'success'
                GROUP BY day, hour_of_day
ORDER BY day, hour_of_day
            
SQL Error: Encountered "day" at line 2, column 50.
Was expecting one of:
    <QUOTED_STRING> ...
    <BRACKET_QUOTED_IDENTIFIER> ...
    <QUOTED_IDENTIFIER> ...
    <BACK_QUOTED_IDENTIFIER> ...
    <IDENTIFIER> ...
    <UNICODE_QUOTED_IDENTIFIER> ...
    
unable to rollback
2025-06-09 00:30:02,437 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-09 00:30:02,656 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-09 00:30:02,662 - etl_performance_monitor - INFO - Metrics table ready
2025-06-09 01:00:02,675 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-09 01:00:02,894 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-09 01:00:02,899 - etl_performance_monitor - INFO - Metrics table ready
2025-06-09 01:30:01,892 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-09 01:30:02,110 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-09 01:30:02,117 - etl_performance_monitor - INFO - Metrics table ready
2025-06-09 02:00:02,152 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-09 02:00:02,381 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-09 02:00:02,431 - etl_performance_monitor - INFO - Metrics table ready
2025-06-09 02:30:01,796 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-09 02:30:02,013 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-09 02:30:02,018 - etl_performance_monitor - INFO - Metrics table ready
2025-06-09 03:00:02,021 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-09 03:00:02,238 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-09 03:00:02,245 - etl_performance_monitor - INFO - Metrics table ready
2025-06-09 03:30:02,223 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-09 03:30:02,438 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-09 03:30:02,443 - etl_performance_monitor - INFO - Metrics table ready
2025-06-09 04:00:02,415 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-09 04:00:02,633 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-09 04:00:02,639 - etl_performance_monitor - INFO - Metrics table ready
2025-06-09 04:30:02,629 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-09 04:30:02,847 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-09 04:30:02,853 - etl_performance_monitor - INFO - Metrics table ready
2025-06-09 05:00:01,836 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-09 05:00:02,053 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-09 05:00:02,057 - etl_performance_monitor - INFO - Metrics table ready
2025-06-09 05:30:02,039 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-09 05:30:02,255 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-09 05:30:02,262 - etl_performance_monitor - INFO - Metrics table ready
2025-06-09 06:00:02,278 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-09 06:00:02,285 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-09 06:00:02,505 - __main__ - INFO - Connected to HeavyDB
2025-06-09 06:00:02,511 - __main__ - INFO - Metrics table ready
2025-06-09 06:00:02,512 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-09 06:00:02,629 - etl_performance_monitor - INFO - Metrics table ready
2025-06-09 06:00:02,666 - __main__ - ERROR - Failed to analyze trends: Execution failed on sql: 
                SELECT 
                    CAST(metric_time AS DATE) as day, EXTRACT(HOUR FROM metric_time) as hour_of_day,
                    AVG(rows_per_second) as avg_speed,
                    AVG(cpu_percent) as avg_cpu,
                    AVG(gpu_utilization) as avg_gpu,
                    SUM(rows_processed) as total_rows,
                    COUNT(*) as operations
                FROM etl_performance_metrics
                WHERE metric_time >= NOW() - INTERVAL '7' DAY
                    AND status = 'success'
                GROUP BY day, hour_of_day
ORDER BY day, hour_of_day
            
SQL Error: Encountered "day" at line 2, column 50.
Was expecting one of:
    <QUOTED_STRING> ...
    <BRACKET_QUOTED_IDENTIFIER> ...
    <QUOTED_IDENTIFIER> ...
    <BACK_QUOTED_IDENTIFIER> ...
    <IDENTIFIER> ...
    <UNICODE_QUOTED_IDENTIFIER> ...
    
unable to rollback
2025-06-09 06:30:01,721 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-09 06:30:01,935 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-09 06:30:01,940 - etl_performance_monitor - INFO - Metrics table ready
2025-06-09 07:00:01,902 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-09 07:00:02,116 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-09 07:00:02,122 - etl_performance_monitor - INFO - Metrics table ready
2025-06-09 07:30:02,083 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-09 07:30:02,296 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-09 07:30:02,300 - etl_performance_monitor - INFO - Metrics table ready
2025-06-09 08:00:02,272 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-09 08:00:02,487 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-09 08:00:02,492 - etl_performance_monitor - INFO - Metrics table ready
2025-06-09 08:30:02,475 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-09 08:30:02,690 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-09 08:30:02,693 - etl_performance_monitor - INFO - Metrics table ready
2025-06-09 09:00:02,646 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-09 09:00:02,861 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-09 09:00:02,866 - etl_performance_monitor - INFO - Metrics table ready
2025-06-09 09:30:01,816 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-09 09:30:02,029 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-09 09:30:02,033 - etl_performance_monitor - INFO - Metrics table ready
2025-06-09 10:00:01,977 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-09 10:00:02,190 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-09 10:00:02,194 - etl_performance_monitor - INFO - Metrics table ready
2025-06-09 10:30:02,150 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-09 10:30:02,363 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-09 10:30:02,367 - etl_performance_monitor - INFO - Metrics table ready
2025-06-09 11:00:02,352 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-09 11:00:02,578 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-09 11:00:02,584 - etl_performance_monitor - INFO - Metrics table ready
2025-06-09 11:30:02,616 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-09 11:30:02,842 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-09 11:30:02,850 - etl_performance_monitor - INFO - Metrics table ready
2025-06-09 12:00:01,857 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-09 12:00:01,897 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-09 12:00:02,089 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-09 12:00:02,096 - etl_performance_monitor - INFO - Metrics table ready
2025-06-09 12:00:02,129 - __main__ - INFO - Connected to HeavyDB
2025-06-09 12:00:02,143 - __main__ - INFO - Metrics table ready
2025-06-09 12:00:02,399 - __main__ - ERROR - Failed to analyze trends: Execution failed on sql: 
                SELECT 
                    CAST(metric_time AS DATE) as day, EXTRACT(HOUR FROM metric_time) as hour_of_day,
                    AVG(rows_per_second) as avg_speed,
                    AVG(cpu_percent) as avg_cpu,
                    AVG(gpu_utilization) as avg_gpu,
                    SUM(rows_processed) as total_rows,
                    COUNT(*) as operations
                FROM etl_performance_metrics
                WHERE metric_time >= NOW() - INTERVAL '7' DAY
                    AND status = 'success'
                GROUP BY day, hour_of_day
ORDER BY day, hour_of_day
            
SQL Error: Encountered "day" at line 2, column 50.
Was expecting one of:
    <QUOTED_STRING> ...
    <BRACKET_QUOTED_IDENTIFIER> ...
    <QUOTED_IDENTIFIER> ...
    <BACK_QUOTED_IDENTIFIER> ...
    <IDENTIFIER> ...
    <UNICODE_QUOTED_IDENTIFIER> ...
    
unable to rollback
2025-06-09 12:30:02,392 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-09 12:30:02,619 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-09 12:30:02,623 - etl_performance_monitor - INFO - Metrics table ready
2025-06-09 13:00:02,661 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-09 13:00:02,889 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-09 13:00:02,901 - etl_performance_monitor - INFO - Metrics table ready
2025-06-09 13:30:01,904 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-09 13:30:02,117 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-09 13:30:02,120 - etl_performance_monitor - INFO - Metrics table ready
2025-06-09 14:00:02,070 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-09 14:00:02,284 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-09 14:00:02,289 - etl_performance_monitor - INFO - Metrics table ready
2025-06-09 14:30:02,324 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-09 14:30:02,553 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-09 14:30:02,557 - etl_performance_monitor - INFO - Metrics table ready
2025-06-09 15:00:02,545 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-09 15:00:02,771 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-09 15:00:02,775 - etl_performance_monitor - INFO - Metrics table ready
2025-06-09 15:30:01,773 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-09 15:30:02,002 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-09 15:30:02,009 - etl_performance_monitor - INFO - Metrics table ready
2025-06-09 16:00:02,023 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-09 16:00:02,250 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-09 16:00:02,254 - etl_performance_monitor - INFO - Metrics table ready
2025-06-09 16:30:02,247 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-09 16:30:02,462 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-09 16:30:02,468 - etl_performance_monitor - INFO - Metrics table ready
2025-06-09 17:00:02,463 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-09 17:00:02,681 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-09 17:00:02,687 - etl_performance_monitor - INFO - Metrics table ready
2025-06-09 17:30:02,680 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-09 17:30:02,894 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-09 17:30:02,898 - etl_performance_monitor - INFO - Metrics table ready
2025-06-09 18:00:01,870 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-09 18:00:01,975 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-09 18:00:02,092 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-09 18:00:02,099 - etl_performance_monitor - INFO - Metrics table ready
2025-06-09 18:00:02,201 - __main__ - INFO - Connected to HeavyDB
2025-06-09 18:00:02,284 - __main__ - INFO - Metrics table ready
2025-06-09 18:00:02,423 - __main__ - ERROR - Failed to analyze trends: Execution failed on sql: 
                SELECT 
                    CAST(metric_time AS DATE) as day, EXTRACT(HOUR FROM metric_time) as hour_of_day,
                    AVG(rows_per_second) as avg_speed,
                    AVG(cpu_percent) as avg_cpu,
                    AVG(gpu_utilization) as avg_gpu,
                    SUM(rows_processed) as total_rows,
                    COUNT(*) as operations
                FROM etl_performance_metrics
                WHERE metric_time >= NOW() - INTERVAL '7' DAY
                    AND status = 'success'
                GROUP BY day, hour_of_day
ORDER BY day, hour_of_day
            
SQL Error: Encountered "day" at line 2, column 50.
Was expecting one of:
    <QUOTED_STRING> ...
    <BRACKET_QUOTED_IDENTIFIER> ...
    <QUOTED_IDENTIFIER> ...
    <BACK_QUOTED_IDENTIFIER> ...
    <IDENTIFIER> ...
    <UNICODE_QUOTED_IDENTIFIER> ...
    
unable to rollback
2025-06-09 18:30:02,449 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-09 18:30:02,675 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-09 18:30:02,681 - etl_performance_monitor - INFO - Metrics table ready
2025-06-09 19:00:02,685 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-09 19:00:02,912 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-09 19:00:02,916 - etl_performance_monitor - INFO - Metrics table ready
2025-06-09 19:30:01,915 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-09 19:30:02,142 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-09 19:30:02,146 - etl_performance_monitor - INFO - Metrics table ready
2025-06-09 20:00:02,122 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-09 20:00:02,336 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-09 20:00:02,344 - etl_performance_monitor - INFO - Metrics table ready
2025-06-09 20:30:02,325 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-09 20:30:02,541 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-09 20:30:02,544 - etl_performance_monitor - INFO - Metrics table ready
2025-06-09 21:00:02,546 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-09 21:00:02,761 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-09 21:00:02,766 - etl_performance_monitor - INFO - Metrics table ready
2025-06-09 21:30:01,742 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-09 21:30:01,957 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-09 21:30:01,962 - etl_performance_monitor - INFO - Metrics table ready
2025-06-09 22:00:01,942 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-09 22:00:02,158 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-09 22:00:02,161 - etl_performance_monitor - INFO - Metrics table ready
2025-06-09 22:30:02,138 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-09 22:30:02,353 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-09 22:30:02,358 - etl_performance_monitor - INFO - Metrics table ready
2025-06-09 23:00:02,317 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-09 23:00:02,533 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-09 23:00:02,538 - etl_performance_monitor - INFO - Metrics table ready
2025-06-09 23:30:02,518 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-09 23:30:02,733 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-09 23:30:02,741 - etl_performance_monitor - INFO - Metrics table ready
2025-06-10 00:00:01,796 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-10 00:00:01,806 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-10 00:00:02,026 - __main__ - INFO - Connected to HeavyDB
2025-06-10 00:00:02,032 - __main__ - INFO - Metrics table ready
2025-06-10 00:00:02,034 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-10 00:00:02,157 - etl_performance_monitor - INFO - Metrics table ready
2025-06-10 00:00:02,204 - __main__ - ERROR - Failed to analyze trends: Execution failed on sql: 
                SELECT 
                    CAST(metric_time AS DATE) as day, EXTRACT(HOUR FROM metric_time) as hour_of_day,
                    AVG(rows_per_second) as avg_speed,
                    AVG(cpu_percent) as avg_cpu,
                    AVG(gpu_utilization) as avg_gpu,
                    SUM(rows_processed) as total_rows,
                    COUNT(*) as operations
                FROM etl_performance_metrics
                WHERE metric_time >= NOW() - INTERVAL '7' DAY
                    AND status = 'success'
                GROUP BY day, hour_of_day
ORDER BY day, hour_of_day
            
SQL Error: Encountered "day" at line 2, column 50.
Was expecting one of:
    <QUOTED_STRING> ...
    <BRACKET_QUOTED_IDENTIFIER> ...
    <QUOTED_IDENTIFIER> ...
    <BACK_QUOTED_IDENTIFIER> ...
    <IDENTIFIER> ...
    <UNICODE_QUOTED_IDENTIFIER> ...
    
unable to rollback
2025-06-10 00:30:02,186 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-10 00:30:02,403 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-10 00:30:02,410 - etl_performance_monitor - INFO - Metrics table ready
2025-06-10 01:00:02,377 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-10 01:00:02,594 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-10 01:00:02,601 - etl_performance_monitor - INFO - Metrics table ready
2025-06-10 01:30:02,587 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-10 01:30:02,801 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-10 01:30:02,805 - etl_performance_monitor - INFO - Metrics table ready
2025-06-10 02:00:01,805 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-10 02:00:02,033 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-10 02:00:02,088 - etl_performance_monitor - INFO - Metrics table ready
2025-06-10 02:30:02,429 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-10 02:30:02,646 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-10 02:30:02,652 - etl_performance_monitor - INFO - Metrics table ready
2025-06-10 03:00:02,651 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-10 03:00:02,868 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-10 03:00:02,872 - etl_performance_monitor - INFO - Metrics table ready
2025-06-10 03:30:01,876 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-10 03:30:02,092 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-10 03:30:02,097 - etl_performance_monitor - INFO - Metrics table ready
2025-06-10 04:00:02,081 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-10 04:00:02,296 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-10 04:00:02,302 - etl_performance_monitor - INFO - Metrics table ready
2025-06-10 04:30:02,291 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-10 04:30:02,507 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-10 04:30:02,511 - etl_performance_monitor - INFO - Metrics table ready
2025-06-10 05:00:02,495 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-10 05:00:02,708 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-10 05:00:02,716 - etl_performance_monitor - INFO - Metrics table ready
2025-06-10 05:30:01,709 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-10 05:30:01,925 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-10 05:30:01,932 - etl_performance_monitor - INFO - Metrics table ready
2025-06-10 06:00:01,964 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-10 06:00:01,965 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-10 06:00:02,191 - __main__ - INFO - Connected to HeavyDB
2025-06-10 06:00:02,193 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-10 06:00:02,195 - __main__ - INFO - Metrics table ready
2025-06-10 06:00:02,312 - etl_performance_monitor - INFO - Metrics table ready
2025-06-10 06:00:02,356 - __main__ - ERROR - Failed to analyze trends: Execution failed on sql: 
                SELECT 
                    CAST(metric_time AS DATE) as day, EXTRACT(HOUR FROM metric_time) as hour_of_day,
                    AVG(rows_per_second) as avg_speed,
                    AVG(cpu_percent) as avg_cpu,
                    AVG(gpu_utilization) as avg_gpu,
                    SUM(rows_processed) as total_rows,
                    COUNT(*) as operations
                FROM etl_performance_metrics
                WHERE metric_time >= NOW() - INTERVAL '7' DAY
                    AND status = 'success'
                GROUP BY day, hour_of_day
ORDER BY day, hour_of_day
            
SQL Error: Encountered "day" at line 2, column 50.
Was expecting one of:
    <QUOTED_STRING> ...
    <BRACKET_QUOTED_IDENTIFIER> ...
    <QUOTED_IDENTIFIER> ...
    <BACK_QUOTED_IDENTIFIER> ...
    <IDENTIFIER> ...
    <UNICODE_QUOTED_IDENTIFIER> ...
    
unable to rollback
2025-06-10 06:30:02,394 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-10 06:30:02,611 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-10 06:30:02,615 - etl_performance_monitor - INFO - Metrics table ready
2025-06-10 07:00:02,597 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-10 07:00:02,812 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-10 07:00:02,816 - etl_performance_monitor - INFO - Metrics table ready
2025-06-10 07:30:01,779 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-10 07:30:01,993 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-10 07:30:01,997 - etl_performance_monitor - INFO - Metrics table ready
2025-06-10 08:00:02,005 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-10 08:00:02,230 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-10 08:00:02,234 - etl_performance_monitor - INFO - Metrics table ready
2025-06-10 08:30:02,232 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-10 08:30:02,458 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-10 08:30:02,463 - etl_performance_monitor - INFO - Metrics table ready
2025-06-10 09:00:02,474 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-10 09:00:02,700 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-10 09:00:02,704 - etl_performance_monitor - INFO - Metrics table ready
2025-06-10 09:30:01,716 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-10 09:30:01,940 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-10 09:30:01,944 - etl_performance_monitor - INFO - Metrics table ready
2025-06-10 10:00:01,916 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-10 10:00:02,131 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-10 10:00:02,139 - etl_performance_monitor - INFO - Metrics table ready
2025-06-10 10:30:02,119 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-10 10:30:02,338 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-10 10:30:02,342 - etl_performance_monitor - INFO - Metrics table ready
2025-06-10 11:00:02,354 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-10 11:00:02,582 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-10 11:00:02,587 - etl_performance_monitor - INFO - Metrics table ready
2025-06-10 11:30:02,574 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-10 11:30:02,801 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-10 11:30:02,806 - etl_performance_monitor - INFO - Metrics table ready
2025-06-10 12:00:01,840 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-10 12:00:01,841 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-10 12:00:02,075 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-10 12:00:02,075 - __main__ - INFO - Connected to HeavyDB
2025-06-10 12:00:02,079 - __main__ - INFO - Metrics table ready
2025-06-10 12:00:02,079 - etl_performance_monitor - INFO - Metrics table ready
2025-06-10 12:00:02,227 - __main__ - ERROR - Failed to analyze trends: Execution failed on sql: 
                SELECT 
                    CAST(metric_time AS DATE) as day, EXTRACT(HOUR FROM metric_time) as hour_of_day,
                    AVG(rows_per_second) as avg_speed,
                    AVG(cpu_percent) as avg_cpu,
                    AVG(gpu_utilization) as avg_gpu,
                    SUM(rows_processed) as total_rows,
                    COUNT(*) as operations
                FROM etl_performance_metrics
                WHERE metric_time >= NOW() - INTERVAL '7' DAY
                    AND status = 'success'
                GROUP BY day, hour_of_day
ORDER BY day, hour_of_day
            
SQL Error: Encountered "day" at line 2, column 50.
Was expecting one of:
    <QUOTED_STRING> ...
    <BRACKET_QUOTED_IDENTIFIER> ...
    <QUOTED_IDENTIFIER> ...
    <BACK_QUOTED_IDENTIFIER> ...
    <IDENTIFIER> ...
    <UNICODE_QUOTED_IDENTIFIER> ...
    
unable to rollback
2025-06-10 12:30:02,235 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-10 12:30:02,463 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-10 12:30:02,467 - etl_performance_monitor - INFO - Metrics table ready
2025-06-10 13:00:02,508 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-10 13:00:02,744 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-10 13:00:02,747 - etl_performance_monitor - INFO - Metrics table ready
2025-06-10 13:30:01,692 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-10 13:30:01,908 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-10 13:30:01,914 - etl_performance_monitor - INFO - Metrics table ready
2025-06-10 14:00:01,893 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-10 14:00:02,110 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-10 14:00:02,114 - etl_performance_monitor - INFO - Metrics table ready
2025-06-10 14:30:02,101 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-10 14:30:02,316 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-10 14:30:02,320 - etl_performance_monitor - INFO - Metrics table ready
2025-06-10 15:00:02,280 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-10 15:00:02,497 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-10 15:00:02,501 - etl_performance_monitor - INFO - Metrics table ready
2025-06-10 15:30:02,512 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-10 15:30:02,728 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-10 15:30:02,733 - etl_performance_monitor - INFO - Metrics table ready
2025-06-10 16:00:01,734 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-10 16:00:01,951 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-10 16:00:01,957 - etl_performance_monitor - INFO - Metrics table ready
2025-06-10 16:30:01,959 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-10 16:30:02,175 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-10 16:30:02,179 - etl_performance_monitor - INFO - Metrics table ready
2025-06-10 17:00:02,162 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-10 17:00:02,380 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-10 17:00:02,383 - etl_performance_monitor - INFO - Metrics table ready
2025-06-10 17:30:02,378 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-10 17:30:02,596 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-10 17:30:02,601 - etl_performance_monitor - INFO - Metrics table ready
2025-06-10 18:00:02,571 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-10 18:00:02,680 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-10 18:00:02,792 - __main__ - INFO - Connected to HeavyDB
2025-06-10 18:00:02,798 - __main__ - INFO - Metrics table ready
2025-06-10 18:00:02,902 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-10 18:00:02,914 - etl_performance_monitor - INFO - Metrics table ready
2025-06-10 18:00:02,946 - __main__ - ERROR - Failed to analyze trends: Execution failed on sql: 
                SELECT 
                    CAST(metric_time AS DATE) as day, EXTRACT(HOUR FROM metric_time) as hour_of_day,
                    AVG(rows_per_second) as avg_speed,
                    AVG(cpu_percent) as avg_cpu,
                    AVG(gpu_utilization) as avg_gpu,
                    SUM(rows_processed) as total_rows,
                    COUNT(*) as operations
                FROM etl_performance_metrics
                WHERE metric_time >= NOW() - INTERVAL '7' DAY
                    AND status = 'success'
                GROUP BY day, hour_of_day
ORDER BY day, hour_of_day
            
SQL Error: Encountered "day" at line 2, column 50.
Was expecting one of:
    <QUOTED_STRING> ...
    <BRACKET_QUOTED_IDENTIFIER> ...
    <QUOTED_IDENTIFIER> ...
    <BACK_QUOTED_IDENTIFIER> ...
    <IDENTIFIER> ...
    <UNICODE_QUOTED_IDENTIFIER> ...
    
unable to rollback
2025-06-10 18:30:01,922 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-10 18:30:02,138 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-10 18:30:02,142 - etl_performance_monitor - INFO - Metrics table ready
2025-06-10 19:00:02,124 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-10 19:00:02,350 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-10 19:00:02,354 - etl_performance_monitor - INFO - Metrics table ready
2025-06-10 19:30:02,360 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-10 19:30:02,587 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-10 19:30:02,592 - etl_performance_monitor - INFO - Metrics table ready
2025-06-10 20:00:02,598 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-10 20:00:02,824 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-10 20:00:02,829 - etl_performance_monitor - INFO - Metrics table ready
2025-06-10 20:30:01,829 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-10 20:30:02,053 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-10 20:30:02,058 - etl_performance_monitor - INFO - Metrics table ready
2025-06-10 21:00:02,049 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-10 21:00:02,274 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-10 21:00:02,277 - etl_performance_monitor - INFO - Metrics table ready
2025-06-10 21:30:02,280 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-10 21:30:02,507 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-10 21:30:02,514 - etl_performance_monitor - INFO - Metrics table ready
2025-06-10 22:00:02,520 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-10 22:00:02,746 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-10 22:00:02,752 - etl_performance_monitor - INFO - Metrics table ready
2025-06-10 22:30:01,761 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-10 22:30:01,988 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-10 22:30:01,992 - etl_performance_monitor - INFO - Metrics table ready
2025-06-10 23:00:01,998 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-10 23:00:02,222 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-10 23:00:02,228 - etl_performance_monitor - INFO - Metrics table ready
2025-06-10 23:30:02,248 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-10 23:30:02,474 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-10 23:30:02,478 - etl_performance_monitor - INFO - Metrics table ready
2025-06-11 00:00:02,545 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-11 00:00:02,547 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-11 00:00:02,781 - __main__ - INFO - Connected to HeavyDB
2025-06-11 00:00:02,783 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-11 00:00:02,788 - __main__ - INFO - Metrics table ready
2025-06-11 00:00:02,788 - etl_performance_monitor - INFO - Metrics table ready
2025-06-11 00:00:02,947 - __main__ - ERROR - Failed to analyze trends: Execution failed on sql: 
                SELECT 
                    CAST(metric_time AS DATE) as day, EXTRACT(HOUR FROM metric_time) as hour_of_day,
                    AVG(rows_per_second) as avg_speed,
                    AVG(cpu_percent) as avg_cpu,
                    AVG(gpu_utilization) as avg_gpu,
                    SUM(rows_processed) as total_rows,
                    COUNT(*) as operations
                FROM etl_performance_metrics
                WHERE metric_time >= NOW() - INTERVAL '7' DAY
                    AND status = 'success'
                GROUP BY day, hour_of_day
ORDER BY day, hour_of_day
            
SQL Error: Encountered "day" at line 2, column 50.
Was expecting one of:
    <QUOTED_STRING> ...
    <BRACKET_QUOTED_IDENTIFIER> ...
    <QUOTED_IDENTIFIER> ...
    <BACK_QUOTED_IDENTIFIER> ...
    <IDENTIFIER> ...
    <UNICODE_QUOTED_IDENTIFIER> ...
    
unable to rollback
2025-06-11 00:30:01,937 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-11 00:30:02,163 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-11 00:30:02,166 - etl_performance_monitor - INFO - Metrics table ready
2025-06-11 01:00:02,140 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-11 01:00:02,363 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-11 01:00:02,367 - etl_performance_monitor - INFO - Metrics table ready
2025-06-11 01:30:02,342 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-11 01:30:02,567 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-11 01:30:02,570 - etl_performance_monitor - INFO - Metrics table ready
2025-06-11 02:00:02,611 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-11 02:00:02,844 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-11 02:00:02,881 - etl_performance_monitor - INFO - Metrics table ready
2025-06-11 02:30:02,209 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-11 02:30:02,433 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-11 02:30:02,439 - etl_performance_monitor - INFO - Metrics table ready
2025-06-11 03:00:02,438 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-11 03:00:02,662 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-11 03:00:02,666 - etl_performance_monitor - INFO - Metrics table ready
2025-06-11 03:30:02,689 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-11 03:30:02,914 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-11 03:30:02,917 - etl_performance_monitor - INFO - Metrics table ready
2025-06-11 04:00:01,897 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-11 04:00:02,122 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-11 04:00:02,126 - etl_performance_monitor - INFO - Metrics table ready
2025-06-11 04:30:02,105 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-11 04:30:02,330 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-11 04:30:02,334 - etl_performance_monitor - INFO - Metrics table ready
2025-06-11 05:00:02,332 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-11 05:00:02,558 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-11 05:00:02,562 - etl_performance_monitor - INFO - Metrics table ready
2025-06-11 05:30:02,581 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-11 05:30:02,805 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-11 05:30:02,809 - etl_performance_monitor - INFO - Metrics table ready
2025-06-11 06:00:01,821 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-11 06:00:01,823 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-11 06:00:02,053 - __main__ - INFO - Connected to HeavyDB
2025-06-11 06:00:02,056 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-11 06:00:02,059 - etl_performance_monitor - INFO - Metrics table ready
2025-06-11 06:00:02,059 - __main__ - INFO - Metrics table ready
2025-06-11 06:00:02,201 - __main__ - ERROR - Failed to analyze trends: Execution failed on sql: 
                SELECT 
                    CAST(metric_time AS DATE) as day, EXTRACT(HOUR FROM metric_time) as hour_of_day,
                    AVG(rows_per_second) as avg_speed,
                    AVG(cpu_percent) as avg_cpu,
                    AVG(gpu_utilization) as avg_gpu,
                    SUM(rows_processed) as total_rows,
                    COUNT(*) as operations
                FROM etl_performance_metrics
                WHERE metric_time >= NOW() - INTERVAL '7' DAY
                    AND status = 'success'
                GROUP BY day, hour_of_day
ORDER BY day, hour_of_day
            
SQL Error: Encountered "day" at line 2, column 50.
Was expecting one of:
    <QUOTED_STRING> ...
    <BRACKET_QUOTED_IDENTIFIER> ...
    <QUOTED_IDENTIFIER> ...
    <BACK_QUOTED_IDENTIFIER> ...
    <IDENTIFIER> ...
    <UNICODE_QUOTED_IDENTIFIER> ...
    
unable to rollback
2025-06-11 06:30:02,288 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-11 06:30:02,513 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-11 06:30:02,517 - etl_performance_monitor - INFO - Metrics table ready
2025-06-11 07:00:02,515 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-11 07:00:02,742 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-11 07:00:02,746 - etl_performance_monitor - INFO - Metrics table ready
2025-06-11 07:30:01,725 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-11 07:30:01,950 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-11 07:30:01,954 - etl_performance_monitor - INFO - Metrics table ready
2025-06-11 08:00:01,949 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-11 08:00:02,177 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-11 08:00:02,180 - etl_performance_monitor - INFO - Metrics table ready
2025-06-11 08:30:02,178 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-11 08:30:02,407 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-11 08:30:02,411 - etl_performance_monitor - INFO - Metrics table ready
2025-06-11 09:00:02,409 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-11 09:00:02,636 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-11 09:00:02,640 - etl_performance_monitor - INFO - Metrics table ready
2025-06-11 09:30:02,615 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-11 09:30:02,841 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-11 09:30:02,844 - etl_performance_monitor - INFO - Metrics table ready
2025-06-11 10:00:01,856 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-11 10:00:02,082 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-11 10:00:02,087 - etl_performance_monitor - INFO - Metrics table ready
2025-06-11 10:30:02,084 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-11 10:30:02,310 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-11 10:30:02,314 - etl_performance_monitor - INFO - Metrics table ready
2025-06-11 11:00:02,320 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-11 11:00:02,547 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-11 11:00:02,551 - etl_performance_monitor - INFO - Metrics table ready
2025-06-11 11:30:02,557 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-11 11:30:02,782 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-11 11:30:02,788 - etl_performance_monitor - INFO - Metrics table ready
2025-06-11 12:00:01,805 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-11 12:00:01,894 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-11 12:00:02,040 - __main__ - INFO - Connected to HeavyDB
2025-06-11 12:00:02,044 - __main__ - INFO - Metrics table ready
2025-06-11 12:00:02,127 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-11 12:00:02,161 - etl_performance_monitor - INFO - Metrics table ready
2025-06-11 12:00:02,191 - __main__ - ERROR - Failed to analyze trends: Execution failed on sql: 
                SELECT 
                    CAST(metric_time AS DATE) as day, EXTRACT(HOUR FROM metric_time) as hour_of_day,
                    AVG(rows_per_second) as avg_speed,
                    AVG(cpu_percent) as avg_cpu,
                    AVG(gpu_utilization) as avg_gpu,
                    SUM(rows_processed) as total_rows,
                    COUNT(*) as operations
                FROM etl_performance_metrics
                WHERE metric_time >= NOW() - INTERVAL '7' DAY
                    AND status = 'success'
                GROUP BY day, hour_of_day
ORDER BY day, hour_of_day
            
SQL Error: Encountered "day" at line 2, column 50.
Was expecting one of:
    <QUOTED_STRING> ...
    <BRACKET_QUOTED_IDENTIFIER> ...
    <QUOTED_IDENTIFIER> ...
    <BACK_QUOTED_IDENTIFIER> ...
    <IDENTIFIER> ...
    <UNICODE_QUOTED_IDENTIFIER> ...
    
unable to rollback
2025-06-11 12:30:02,195 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-11 12:30:02,420 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-11 12:30:02,423 - etl_performance_monitor - INFO - Metrics table ready
2025-06-11 13:00:02,437 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-11 13:00:02,656 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-11 13:00:02,661 - etl_performance_monitor - INFO - Metrics table ready
2025-06-11 13:30:02,649 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-11 13:30:02,867 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-11 13:30:02,871 - etl_performance_monitor - INFO - Metrics table ready
2025-06-11 14:00:01,829 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-11 14:00:02,045 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-11 14:00:02,049 - etl_performance_monitor - INFO - Metrics table ready
2025-06-11 14:30:02,045 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-11 14:30:02,265 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-11 14:30:02,269 - etl_performance_monitor - INFO - Metrics table ready
2025-06-11 15:00:02,231 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-11 15:00:02,445 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-11 15:00:02,449 - etl_performance_monitor - INFO - Metrics table ready
2025-06-11 15:30:02,422 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-11 15:30:02,640 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-11 15:30:02,644 - etl_performance_monitor - INFO - Metrics table ready
2025-06-11 16:00:02,610 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-11 16:00:02,825 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-11 16:00:02,829 - etl_performance_monitor - INFO - Metrics table ready
2025-06-11 16:30:01,803 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-11 16:30:02,017 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-11 16:30:02,021 - etl_performance_monitor - INFO - Metrics table ready
2025-06-11 17:00:01,963 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-11 17:00:02,177 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-11 17:00:02,181 - etl_performance_monitor - INFO - Metrics table ready
2025-06-11 17:30:02,124 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-11 17:30:02,339 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-11 17:30:02,343 - etl_performance_monitor - INFO - Metrics table ready
2025-06-11 18:00:02,373 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-11 18:00:02,462 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-11 18:00:02,601 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-11 18:00:02,606 - etl_performance_monitor - INFO - Metrics table ready
2025-06-11 18:00:02,689 - __main__ - INFO - Connected to HeavyDB
2025-06-11 18:00:02,790 - __main__ - INFO - Metrics table ready
2025-06-11 18:00:02,930 - __main__ - ERROR - Failed to analyze trends: Execution failed on sql: 
                SELECT 
                    CAST(metric_time AS DATE) as day, EXTRACT(HOUR FROM metric_time) as hour_of_day,
                    AVG(rows_per_second) as avg_speed,
                    AVG(cpu_percent) as avg_cpu,
                    AVG(gpu_utilization) as avg_gpu,
                    SUM(rows_processed) as total_rows,
                    COUNT(*) as operations
                FROM etl_performance_metrics
                WHERE metric_time >= NOW() - INTERVAL '7' DAY
                    AND status = 'success'
                GROUP BY day, hour_of_day
ORDER BY day, hour_of_day
            
SQL Error: Encountered "day" at line 2, column 50.
Was expecting one of:
    <QUOTED_STRING> ...
    <BRACKET_QUOTED_IDENTIFIER> ...
    <QUOTED_IDENTIFIER> ...
    <BACK_QUOTED_IDENTIFIER> ...
    <IDENTIFIER> ...
    <UNICODE_QUOTED_IDENTIFIER> ...
    
unable to rollback
2025-06-11 18:30:01,925 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-11 18:30:02,139 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-11 18:30:02,144 - etl_performance_monitor - INFO - Metrics table ready
2025-06-11 19:00:02,129 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-11 19:00:02,344 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-11 19:00:02,349 - etl_performance_monitor - INFO - Metrics table ready
2025-06-11 19:30:02,363 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-11 19:30:02,585 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-11 19:30:02,589 - etl_performance_monitor - INFO - Metrics table ready
2025-06-11 20:00:02,555 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-11 20:00:02,772 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-11 20:00:02,776 - etl_performance_monitor - INFO - Metrics table ready
2025-06-11 20:30:01,785 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-11 20:30:02,000 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-11 20:30:02,006 - etl_performance_monitor - INFO - Metrics table ready
2025-06-11 21:00:01,980 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-11 21:00:02,199 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-11 21:00:02,203 - etl_performance_monitor - INFO - Metrics table ready
2025-06-11 21:30:02,172 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-11 21:30:02,393 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-11 21:30:02,397 - etl_performance_monitor - INFO - Metrics table ready
2025-06-11 22:00:02,379 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-11 22:00:02,596 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-11 22:00:02,599 - etl_performance_monitor - INFO - Metrics table ready
2025-06-11 22:30:02,590 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-11 22:30:02,812 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-11 22:30:02,816 - etl_performance_monitor - INFO - Metrics table ready
2025-06-11 23:00:01,791 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-11 23:00:02,007 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-11 23:00:02,011 - etl_performance_monitor - INFO - Metrics table ready
2025-06-11 23:30:01,982 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-11 23:30:02,205 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-11 23:30:02,209 - etl_performance_monitor - INFO - Metrics table ready
2025-06-12 00:00:02,191 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-12 00:00:02,197 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-12 00:00:02,427 - __main__ - INFO - Connected to HeavyDB
2025-06-12 00:00:02,430 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-12 00:00:02,431 - __main__ - INFO - Metrics table ready
2025-06-12 00:00:02,549 - etl_performance_monitor - INFO - Metrics table ready
2025-06-12 00:00:02,597 - __main__ - ERROR - Failed to analyze trends: Execution failed on sql: 
                SELECT 
                    CAST(metric_time AS DATE) as day, EXTRACT(HOUR FROM metric_time) as hour_of_day,
                    AVG(rows_per_second) as avg_speed,
                    AVG(cpu_percent) as avg_cpu,
                    AVG(gpu_utilization) as avg_gpu,
                    SUM(rows_processed) as total_rows,
                    COUNT(*) as operations
                FROM etl_performance_metrics
                WHERE metric_time >= NOW() - INTERVAL '7' DAY
                    AND status = 'success'
                GROUP BY day, hour_of_day
ORDER BY day, hour_of_day
            
SQL Error: Encountered "day" at line 2, column 50.
Was expecting one of:
    <QUOTED_STRING> ...
    <BRACKET_QUOTED_IDENTIFIER> ...
    <QUOTED_IDENTIFIER> ...
    <BACK_QUOTED_IDENTIFIER> ...
    <IDENTIFIER> ...
    <UNICODE_QUOTED_IDENTIFIER> ...
    
unable to rollback
2025-06-12 00:30:02,617 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-12 00:30:02,837 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-12 00:30:02,841 - etl_performance_monitor - INFO - Metrics table ready
2025-06-12 01:00:01,811 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-12 01:00:02,030 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-12 01:00:02,033 - etl_performance_monitor - INFO - Metrics table ready
2025-06-12 01:30:02,021 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-12 01:30:02,241 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-12 01:30:02,244 - etl_performance_monitor - INFO - Metrics table ready
2025-06-12 02:00:02,241 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-12 02:00:02,464 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-12 02:00:02,521 - etl_performance_monitor - INFO - Metrics table ready
2025-06-12 02:30:01,854 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-12 02:30:02,073 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-12 02:30:02,077 - etl_performance_monitor - INFO - Metrics table ready
2025-06-12 03:00:02,036 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-12 03:00:02,257 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-12 03:00:02,261 - etl_performance_monitor - INFO - Metrics table ready
2025-06-12 03:30:02,227 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-12 03:30:02,446 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-12 03:30:02,449 - etl_performance_monitor - INFO - Metrics table ready
2025-06-12 04:00:02,379 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-12 04:00:02,604 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-12 04:00:02,609 - etl_performance_monitor - INFO - Metrics table ready
2025-06-12 04:30:02,555 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-12 04:30:02,774 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-12 04:30:02,777 - etl_performance_monitor - INFO - Metrics table ready
2025-06-12 05:00:01,742 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-12 05:00:01,959 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-12 05:00:01,964 - etl_performance_monitor - INFO - Metrics table ready
2025-06-12 05:30:01,943 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-12 05:30:02,178 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-12 05:30:02,182 - etl_performance_monitor - INFO - Metrics table ready
2025-06-12 06:00:02,190 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-12 06:00:02,198 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-12 06:00:02,420 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-12 06:00:02,424 - etl_performance_monitor - INFO - Metrics table ready
2025-06-12 06:00:02,430 - __main__ - INFO - Connected to HeavyDB
2025-06-12 06:00:02,456 - __main__ - INFO - Metrics table ready
2025-06-12 06:00:02,605 - __main__ - ERROR - Failed to analyze trends: Execution failed on sql: 
                SELECT 
                    CAST(metric_time AS DATE) as day, EXTRACT(HOUR FROM metric_time) as hour_of_day,
                    AVG(rows_per_second) as avg_speed,
                    AVG(cpu_percent) as avg_cpu,
                    AVG(gpu_utilization) as avg_gpu,
                    SUM(rows_processed) as total_rows,
                    COUNT(*) as operations
                FROM etl_performance_metrics
                WHERE metric_time >= NOW() - INTERVAL '7' DAY
                    AND status = 'success'
                GROUP BY day, hour_of_day
ORDER BY day, hour_of_day
            
SQL Error: Encountered "day" at line 2, column 50.
Was expecting one of:
    <QUOTED_STRING> ...
    <BRACKET_QUOTED_IDENTIFIER> ...
    <QUOTED_IDENTIFIER> ...
    <BACK_QUOTED_IDENTIFIER> ...
    <IDENTIFIER> ...
    <UNICODE_QUOTED_IDENTIFIER> ...
    
unable to rollback
2025-06-12 06:30:02,651 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-12 06:30:02,880 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-12 06:30:02,884 - etl_performance_monitor - INFO - Metrics table ready
2025-06-12 07:00:01,858 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-12 07:00:02,077 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-12 07:00:02,081 - etl_performance_monitor - INFO - Metrics table ready
2025-06-12 07:30:02,089 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-12 07:30:02,310 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-12 07:30:02,313 - etl_performance_monitor - INFO - Metrics table ready
2025-06-12 08:00:02,280 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-12 08:00:02,501 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-12 08:00:02,505 - etl_performance_monitor - INFO - Metrics table ready
2025-06-12 08:30:02,474 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-12 08:30:02,691 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-12 08:30:02,696 - etl_performance_monitor - INFO - Metrics table ready
2025-06-12 09:00:02,660 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-12 09:00:02,879 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-12 09:00:02,882 - etl_performance_monitor - INFO - Metrics table ready
2025-06-12 09:30:01,886 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-12 09:30:02,107 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-12 09:30:02,111 - etl_performance_monitor - INFO - Metrics table ready
2025-06-12 10:00:02,104 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-12 10:00:02,320 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-12 10:00:02,324 - etl_performance_monitor - INFO - Metrics table ready
2025-06-12 10:30:02,326 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-12 10:30:02,544 - etl_performance_monitor - INFO - Connected to HeavyDB
2025-06-12 10:30:02,548 - etl_performance_monitor - INFO - Metrics table ready
2025-06-12 12:00:02,633 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-12 12:00:02,846 - __main__ - INFO - Connected to HeavyDB
2025-06-12 12:00:02,850 - __main__ - INFO - Metrics table ready
2025-06-12 12:00:03,007 - __main__ - ERROR - Failed to analyze trends: Execution failed on sql: 
                SELECT 
                    CAST(metric_time AS DATE) as day, EXTRACT(HOUR FROM metric_time) as hour_of_day,
                    AVG(rows_per_second) as avg_speed,
                    AVG(cpu_percent) as avg_cpu,
                    AVG(gpu_utilization) as avg_gpu,
                    SUM(rows_processed) as total_rows,
                    COUNT(*) as operations
                FROM etl_performance_metrics
                WHERE metric_time >= NOW() - INTERVAL '7' DAY
                    AND status = 'success'
                GROUP BY day, hour_of_day
ORDER BY day, hour_of_day
            
SQL Error: Encountered "day" at line 2, column 50.
Was expecting one of:
    <QUOTED_STRING> ...
    <BRACKET_QUOTED_IDENTIFIER> ...
    <QUOTED_IDENTIFIER> ...
    <BACK_QUOTED_IDENTIFIER> ...
    <IDENTIFIER> ...
    <UNICODE_QUOTED_IDENTIFIER> ...
    
unable to rollback
