Defaulting to user installation because normal site-packages is not writeable
Collecting PyPDF2
  Downloading pypdf2-3.0.1-py3-none-any.whl.metadata (6.8 kB)
Downloading pypdf2-3.0.1-py3-none-any.whl (232 kB)
Installing collected packages: PyPDF2
Successfully installed PyPDF2-3.0.1
No PDF processing libraries available. Installing required libraries...
Performance Optimization Guide for HeavyDB in
Financial Backtesting
This guide provides a detailed, step-by-step approach to optimize HeavyDB for a financial backtesting
system.  We  assume  a  use  case  of  intraday  tick  and  minute-level  data  for  ~70  instruments  across
multiple exchanges, with about 10 million rows per instrument and thousands of backtests running
concurrently.  The  goal  is  to  maximize  speed  (through  GPU  acceleration  and  parallelism)  without
sacrificing accuracy. Each section below covers a key aspect of the system with best practices, example
strategies, and code snippets where applicable.
1. Schema Design
Designing an efficient schema is crucial for fast per-instrument queries and scalable cross-instrument
analysis.  HeavyDB  (formerly  OmniSci)  is  a  SQL-based,  relational,  columnar  database  designed  to
leverage modern hardware (including GPUs) for analytics . Given HeavyDB’s columnar nature and
lack of traditional indexes, the schema should be optimized for sequential scans and partitioning rather
than point lookups. Key considerations for this use case include table structure, handling multiple
exchanges/instruments, and the balance between normalization and denormalization.
Single Fact Table vs. Per-Instrument Tables:  You can store all instruments in one large fact
table (with instrument and exchange as columns), or use separate tables per instrument (and/or
per exchange). A single large table simplifies cross-instrument queries (just filter by instrument),
but each query will scan a big table (though HeavyDB’s columnar engine will skip irrelevant data
if properly sorted) . Separate per-instrument tables ensure each query only scans ~10M rows
for that instrument, which can be faster for isolated backtests and reduces contention. However ,
cross-instrument analysis would require querying/joining multiple tables. Recommendation:  If
cross-instrument queries are frequent (e.g. correlation, pair trades), use one unified table with a
column for instrument_id  or symbol; if backtests are mostly per instrument, separate tables
(possibly with a union or view for combined analysis) can improve per-query speed by limiting
scanned data.
Partitioning by Exchange/Instrument:  HeavyDB does not allow multi-column sharding keys,
but sharding by a single key  can distribute data across GPUs or cluster nodes . If running
HeavyDB on a multi-GPU server or distributed cluster , consider sharding the main table by
instrument (or exchange) as the shard key. For example: 
CREATETABLEtrades_all (
instrument TEXTENCODING DICT(32),
exchange TEXTENCODING DICT(8),
tsTIMESTAMP (3),
priceFLOAT,
volumeINT,
-- other columns --1
• 
2
• 
3
1
SHARDKEY(instrument )
)WITH(shard_count =4,sort_column ='ts');
In this example, data is partitioned by instrument  across 4 GPUs (or nodes). A query filtering a single
instrument will be routed to the corresponding partition/GPU, reducing scan load on others. Note:
Shard count should equal the number of GPUs or leaves in a cluster . Sharding can improve
concurrency by spreading queries, but be mindful of potential skew if some instruments have far more
data than others . If sharding is not used, HeavyDB by default might replicate or evenly distribute
fragments across GPUs (ensuring parallel scan on multi-GPU systems).
Handling Multiple Exchanges:  If the same instrument trades on multiple exchanges (or you
need to separate data by exchange), include an  exchange  column or incorporate exchange
into the instrument identifier . This could be a composite key like EXCHANGE_SYMBOL  stored in one
column  (which  HeavyDB  can  treat  as  a  single  text  key).  Alternatively,  include  a  separate
exchange  column (TEXT or small int code) and always filter by both instrument and exchange
in queries. HeavyDB’s columnar storage will compress repeated exchange codes well, especially
with dictionary encoding. If using separate tables, you might create tables grouped by exchange
(e.g. one table per exchange containing all its instruments), but this is usually not necessary
unless data volume per exchange is a concern. A unified table can handle multiple exchanges by
simply having an exchange  column and perhaps sorting or fragmenting by exchange as well
(for example, loading data grouped by exchange and time together).
Normalization vs. Denormalization:  For performance analytics,  denormalized schemas are
often preferred . Joins can be expensive, and HeavyDB currently does not support user-defined
indexes  (you cannot create traditional B-tree indexes) . Instead, rely on scanning and filtering
in-column. This means storing instrument and exchange identifiers directly in the fact table to
avoid joins to dimension tables. The storage overhead is minimal thanks to dictionary encoding:
HeavyDB  automatically  compresses  text  fields  by  storing  a  dictionary  of  unique  values  and
referencing them by keys . This yields the benefits of normalization (space saving) without
join cost. For example, store  instrument  as  TEXT ENCODING DICT(32)  rather than an
integer foreign key to a dimension table – HeavyDB will compress repeated instrument names
and allow fast equality filtering . Only consider normalization for extremely high-cardinality
attributes or large text fields that are seldom used; even then, joining in HeavyDB might not be
as efficient as scanning a wider table, so weigh carefully. In practice, a single large fact table with
columns for instrument metadata (like instrument name, exchange code, sector , etc.) can be
feasible since redundant text is dictionary-compressed and queries avoid the cost of joins.
Schema Example:  Below is an example schema snippet for a  unified ticks table  covering all
instruments, optimized for HeavyDB: 
CREATETABLEtick_data (
instrument TEXTENCODING DICT(32),-- instrument symbol or ID
exchange TEXTENCODING DICT(8),-- exchange code
tsTIMESTAMP (6), -- event timestamp with 
microsecond precision
priceDECIMAL(10,4), -- price with 4 decimal places
volumeINT,
-- any other fields like bid/ask, etc.
SHARDKEY(instrument ) -- distribute by instrument if 3
4
• 
• 
5
6
6
• 
2
multi-GPU
)
WITH(
fragment_size =32000000 ,
-- ~32M rows per fragment (HeavyDB default)
sort_column ='ts' -- sort data by timestamp for 
efficient time filtering
);
In this design: 
TEXT ENCODING DICT  is used for instrument and exchange for compression and performance
(HeavyDB will also allow grouping or joining on these efficiently, especially if dictionaries are 
shared  between tables or columns) .
We specify fragment_size  (though it defaults to 32 million) to ensure large tables are broken
into fragments that HeavyDB can process in parallel . Each fragment can be scanned by a
separate thread or GPU kernel, so having data in multiple fragments (the default for large tables)
enables parallel query execution.
We set  sort_column='ts'  so that data on disk is kept sorted by timestamp. This has two
benefits: (1) Most queries will specify a date/time range, so HeavyDB can skip entire chunks/
fragments that fall outside the filter (using min/max metadata). For instance, if each fragment
covers a specific time range, a  WHERE ts BETWEEN x AND y  will only load the fragments
overlapping that interval , instead of scanning everything. (2) Sequential time ranges read
from  disk  are  more  I/O  efficient  and  compressible  (time  values  often  compress  with  delta
encoding or run-length encoding when sorted).
Alternative Per-Instrument Tables:  If opting for separate tables per instrument, each table
should  have  a  similar  schema  without  the  instrument  column  (since  the  table  name  itself
indicates the instrument). Ensure consistency of schema across tables for easier management.
Use  meaningful  table  naming  (e.g.  ticks_AAPL ,  ticks_GOOG )  and  possibly  group  by
exchange (e.g. prefix with exchange code).  Best practices for separate tables:  use the same
data types and encodings for all, and consider using  shared dictionaries  for common text
fields. HeavyDB allows sharing a dictionary between tables (e.g. if all tables have an exchange
column or other categorical data, you can have them reference a common dictionary) . This
way, if you ever need to query across multiple tables (via a union or join), the dictionary-encoded
values align. To share a dictionary, one can create one table with the dictionary and have others
use  SHARED DICTIONARY  in  the  CREATE  TABLE  statement  referencing  the  original  (per
HeavyDB docs). This is an advanced optimization ensuring consistent encoding across tables.
Primary  Keys  and  Uniqueness:  HeavyDB  does  not  enforce  primary  key  constraints  in  the
traditional sense. If needed, you can maintain uniqueness via your application logic. The absence
of primary key indexing means duplicate checks on insert would be expensive; it’s usually better
to allow duplicates or pre-aggregate data as needed.
In summary,  favor a schema that minimizes join operations and exploits HeavyDB’s columnar
compression . A denormalized fact table (or tables) with encoded text columns and a sort order aligned
to common query filters will set the stage for high scan throughput and effective fragment elimination.7
• 
8
• 
7
• 
2
• 
9
• 
3
2. Columnar Storage and Data Types
HeavyDB is a  columnar storage  system, meaning data is laid out column-by-column on disk and in
memory. This is ideal for analytical workloads like backtesting, because queries often need only a few
columns (e.g. timestamp, price, volume) out of a wider table. By reading only the needed columns,
HeavyDB reduces I/O and exploits CPU/GPU vectorization. Additionally, HeavyDB’s GPU acceleration can
scan and aggregate columns of billions of values extremely fast, often 100x faster than CPU-bound
databases . To fully leverage these advantages, careful selection of data types and encodings is
necessary:
Choose Optimal Data Types:  Use the smallest data type that can accurately represent your data
range and precision . Smaller types not only save memory and storage, but also process
faster on both CPU and GPU:
Use BOOLEAN  (1 byte) for flags, tinyint if needed, or an 8-bit integer for small categorical codes.
For integer fields (e.g. trade size, volume, or an ID), select the narrowest integer type that covers
the range: HeavyDB supports 8, 16, 32, and 64-bit integers ( TINYINT , SMALLINT , INT, 
BIGINT). If an INT (32-bit) is sufficient for volumes (up to ~2 billion), avoid using BIGINT
(64-bit).
For decimal or currency values, consider the trade-offs between floating point and fixed decimal:
FLOAT (4-byte single precision) and DOUBLE (8-byte double precision) are fast on CPU
and reasonably fast on GPUs (though many GPUs have much higher throughput for 32-
bit floats than 64-bit doubles ). If your strategy tolerates floating-point rounding error
(which is usually minor for pricing data but could accumulate in summations), FLOAT
can be a performant choice. 
If accuracy is paramount (e.g. P/L calculations in backtesting require exact cents), use 
DECIMAL  types. HeavyDB supports DECIMAL(precision, scale)  which stores as 2,
4, or 8 bytes depending on needed precision . A type like DECIMAL(10,4)  can safely
store prices up to 6 digits and 4 decimal places. Internally, decimals might be handled as
scaled integers which is fine for GPU operations (though check if very high precision
decimals force software processing; moderate precision is typically okay).
Another approach is storing monetary values as integers of smallest currency unit (e.g.
cents) to avoid floating issues; e.g. price in cents as INT if that stays within range. This
avoids floating error entirely, at the cost of handling scaling in application logic.
For dates and times, use proper temporal types:
TIMESTAMP  is appropriate for intraday data. HeavyDB supports up to nanosecond
precision timestamps ( TIMESTAMP(9) ) . For most financial tick data,
microsecond ( TIMESTAMP(6) ) or millisecond ( TIMESTAMP(3) ) precision suffices.
Ensure you use a high-enough precision to differentiate unique events if needed.
You can apply fixed-length encoding to timestamps if the range of timestamps is limited.
For example, DATE ENCODING DAYS(16)  uses 2 bytes for dates within +/-90 years of
epoch . However , for a broad timeline or where time is critical, it’s usually fine to store
timestamps as 8 bytes. HeavyDB even has TIMESTAMP(0) ENCODING FIXED(32)  to
store second-precision timestamps in 4 bytes if they fall in a certain range . In our
scenario with potentially many years of tick data, we likely stick to 8-byte timestamps for
simplicity, unless memory is very tight.
Avoid using TEXT for numeric data  (store numbers in numeric types). Also avoid high-precision
types like NUMERIC  if not needed; prefer the native types which HeavyDB optimizes for GPU.
Leverage Fixed-Length Encoding:  HeavyDB allows columns to be stored in a fixed-bit-length
representation to save space (as long as values fit in that range). For example, a BIGINT column10
• 
1112
• 
• 
• 
◦ 
◦ 
13
◦ 
• 
◦ 
1415
◦ 
16
17
• 
• 
4
can be encoded as 32-bit or 16-bit if you know the values won’t exceed those limits . Use
ENCODING FIXED(N)  in the schema for integers and decimals where appropriate. Example:  If
you have a column for trade quantity that maxes out at 100,000, you can use INT ENCODING 
FIXED(16)  (2 bytes) instead of 4 bytes. Smaller encodings reduce memory footprint and can
increase query speed across the board . The HeavyDB optimizer and execution benefit from
less data movement and better cache utilization with narrower types.  Note:  Ensure that the
data’s range truly fits to avoid overflow (HeavyDB will not implicitly validate beyond type limits).
Dictionary Encoding for Categorical Data:  As mentioned, text fields like instrument symbols,
exchange codes, or trade conditions should be declared with  TEXT ENCODING DICT . This is
highly  recommended  “whenever  possible”  in  HeavyDB .  Dictionary  encoding  replaces
strings with integer keys, drastically reducing storage for repetitive values and speeding up
comparisons/joins. In our case, instrument  and exchange  will have at most a few hundred
unique  values  (70  instruments,  a  handful  of  exchanges),  so  they  compress  extremely  well.
Queries grouping or filtering on these text fields will use the integer codes internally, which is
efficient. If you plan to do joins on text (for example joining a trades table with an instruments
table by symbol), you should ensure they share dictionaries or use a shared dimension table,
otherwise  HeavyDB  will  have  to  translate  between  dictionaries .  For  our  denormalized
approach, simply using a single table with dictionary text avoids that issue. 
Avoid using TEXT ENCODING NONE  (which stores raw strings) for any high-cardinality or large
text in this scenario, as it will bloat storage and slow queries. Only use it if you have a truly
unique string per row (not our case).
Shared Dictionaries:  If using multiple tables (like per-instrument tables or separate fact and
dimension  tables),  consider  HeavyDB’s  Shared  Dictionary  feature .  This  allows  two  text
columns in different tables to use the same dictionary, which is beneficial if you ever join or
union them. For example, if each instrument table has an  exchange  column, they can all
reference a single dictionary of exchange codes to maintain consistency. This advanced setup
can be done by specifying SHARED DICTIONARY(col) REFERENCES other_table(col)  in
the CREATE TABLE statement . Shared dictionaries save memory and ensure that a value like
"NYSE" has the same internal code across tables (no runtime translation needed).
Columnar Compression:  HeavyDB inherently compresses data in columns. Beyond dictionary
encoding, it uses techniques like run-length encoding and bitpacking for integers under the
hood where beneficial. While you don’t directly control these (apart from choosing encodings
and data types), it’s useful to know that sorted data often compresses better . For instance, if your
table is sorted by timestamp, the ts column can be stored as differences (each fragment’s min
timestamp plus deltas), and columns like  price might compress if there are repeating or
slowly changing values. The takeaway:  sort order and data distribution can impact storage
size and I/O.  In general, keep frequently filtered columns sorted to exploit compression and
fragment skipping (as covered in schema and query sections).
Wide vs. Narrow Tables:  Having many columns in a table is fine, but remember that columnar
systems only read the columns you query. If you find there are a set of rarely used columns
(maybe metadata or less-used indicators), you could vertically partition them into a separate
table, to ensure most queries never touch them. However , this complicates design and is usually
not  necessary  unless  memory  is  constrained.  Given  our  use  case,  the  schema  likely  isn’t
excessively wide (tick data typically has a dozen or so fields at most). It’s acceptable to keep all
relevant fields in one table.1819
12
• 
6
20
• 
• 
9
21
• 
• 
5
Geospatial and Other Types:  If your backtesting includes spatial data (unlikely for pure financial
data, but possibly if analyzing geographic venues), HeavyDB has specialized geospatial types. For
completeness:  heavy  supports  POINT, LINESTRING, POLYGON  etc.,  but  those  would  not
appear in typical market data schema. Likewise, array types exist but probably aren’t needed
here. Stick to primitive types for performance-critical data.
In summary, use HeavyDB’s type system to your advantage : smaller encodings for speed, dictionary
encoding for strings, and appropriate numeric precision. The combination of columnar storage and
these encodings yields compact data and fast scans. Always test with real data distributions to ensure
the chosen types handle all values (no overflow or unnecessary large types). Remember that “using the
smallest  possible  encoding  increases  the  speed  of  all  aspects  of  HeavyDB,  from  initial  load  to  query
execution.”
3. Query Optimization
Optimizing  your  SQL  queries  and  data  layout  for  HeavyDB  can  drastically  improve  performance,
especially under concurrent load. HeavyDB’s query engine compiles queries to machine code (using
LLVM) and exploits the parallelism of multi-core CPUs and GPUs. While it will automatically attempt to
optimize (e.g. push down filters, use hash joins, etc.), you should design queries and data to make the
optimizer’s  job  easier .  Below  are  strategies  for  filtering,  partitioning,  pre-aggregation,  and  parallel
execution relevant to backtesting:
Effective Filtering (WHERE Clauses):  Always include selective filters in your queries to minimize
scanned data. In a backtest, you often query a specific instrument and a date/time range – make
sure to use those in the WHERE clause. For example: WHERE instrument = 'AAPL' AND ts 
>= '2025-01-01' AND ts < '2025-01-31' .  This  allows  HeavyDB  to  use  fragment
skipping : since the table is sorted by  ts (per our schema design), the engine will only read
fragments that fall in the date range . All fragments wholly outside the range are skipped
without even reading them from disk. This can reduce IO and processing dramatically (e.g.
filtering one week out of a year of data might scan ~2% of the table). If instrument data are
grouped or sharded, the filter on instrument  similarly restricts the scan to relevant partitions.
Tip: Avoid  wrapping  columns  in  functions  for  filtering  (e.g.
WHERE DATE(ts) = '2025-01-01' ), as this can prevent using the sorted order . Instead, use
range conditions or appropriate operators that allow the engine to use min/max metadata (e.g.
ts BETWEEN '2025-01-01' AND '2025-01-01 23:59:59' ). Also, leverage boolean logic to
your advantage – combine filters so that the most restrictive conditions are applied. If certain
instruments or time periods are irrelevant to a particular strategy, explicitly exclude them in the
query.
Sorting and Fragment Elimination:  Ensure that your table’s  sort order  aligns with common
query filters. As mentioned, sorting by timestamp is very effective for time-range queries .
You could also consider sorting by instrument within time (though HeavyDB only allows one sort
column,  it  actually  sorts  lexicographically  by  that  column;  timestamps  will  naturally  cluster
instrument data if you loaded one instrument at a time, but generally time order intermixes
instruments). If you frequently query one instrument at a time, an alternative approach is to
partition data by instrument (separate tables or shards). With separate tables, the query doesn’t
even see other instruments. With sharding by instrument, each instrument’s data resides largely
on one fragment or one GPU, so fragment metadata on instrument  could theoretically help
skip others. However , HeavyDB’s fragment skipping is primarily based on min/max of the sorted
column or maybe metadata of the chunk; it’s most reliable on the sort column and on deletes.• 
12
• 
2
• 
22
6
It’s safe to say:  load the data in an order that benefits your typical filters . If instrument
queries dominate, you might load data instrument by instrument so that each fragment contains
mostly one instrument’s data (for example, load all rows for instrument A, then all for B, etc.). In
such a case, fragments align with instruments and a filter on instrument can skip fragments not
containing that instrument. This is a subtle technique – you’d rely on heavy’s knowledge of min/
max of the instrument  column per fragment (if data was clustered, min and max instrument
might both be “AAPL” for fragments of only AAPL data). This approach can improve selective
scans  on  the  instrument  column.  The  downside  is  that  time-based  queries  spanning  many
instruments (e.g. “all instruments on 2025-01-01”) might not be contiguous and could touch
many fragments. Decide based on query patterns: if single-instrument backtests are the vast
majority, clustering by instrument might be worthwhile. HeavyDB’s upcoming features like multi-
column sort or automatic clustering aren’t available yet, so it’s a manual trade-off.
Partitioning & Sharding Strategies:  As discussed in Schema Design, HeavyDB can partition
tables using the SHARD KEY  on a column (like instrument) and a specified shard_count . In a
multi-GPU  environment,  sharding  ensures  that  queries  utilize  all  GPUs  in  parallel  when
scanning large ranges, and also that each GPU handles a subset of the data. This can linearly
scale throughput for big scans (e.g. a query over all 70 instruments’ data will be split among
GPUs) and also isolate smaller queries to one GPU (freeing others for other queries). Note:  If you
have an uneven distribution of data or queries (some instruments much more active), one shard/
GPU could become a hotspot. Monitor for load skew and adjust strategy if needed (maybe
shuffle which instruments map to each shard, if possible, or add GPUs for more shards). If
running on a single GPU or CPU-only, sharding is less relevant, but you might still logically
partition data by instrument in separate tables for manageability.
No  Traditional  Indexes  –  Use  Sort  and  Projections:  HeavyDB  does  not  support  explicit
indexes  on arbitrary columns . Instead, it relies on scanning and the tricks above (sorting and
fragment skipping) for performance. If you have a query pattern that isn’t well-served by the sort
order (for example, filtering by a high-cardinality field that isn’t sorted), consider if you can pre-
filter or pre-aggregate data. For instance, filtering by a specific trade condition or flag that’s
randomly distributed will cause a full scan (since each fragment likely has some of those values)
. In such cases, you could maintain a separate table of primary keys for the rows matching
that condition, or periodically compute results so queries don’t have to filter raw ticks. This is
getting into pre-aggregation.
Pre-Aggregation  and  Summary  Tables:  Given  the  large  volume  (10  million  rows  per
instrument), aggregating on the fly might be heavy if done repeatedly. HeavyDB is extremely fast
at scanning and aggregating (summing millions of records in milliseconds in ideal conditions),
but under  thousands of concurrent queries , you should consider caching or precomputing
some results:
Materialized Views (Manual):  HeavyDB doesn’t have automatic materialized views, but you can
simulate them by creating summary tables. For example, if many strategies only need minute-
bar data instead of raw ticks, maintain a minute-bars table (by instrument, by minute, with
OHLCV values). This can be generated daily at EOD (since data updates daily) using HeavyDB SQL
(e.g. CREATE TABLE minute_bars AS SELECT instrument, floor(ts to minute) as 
minute, MIN(price) as open_price, MAX(price) as high, ... GROUP BY 
instrument, floor(ts to minute) ). Querying 1,440 rows per day instead of hundreds of
thousands of ticks is obviously faster . Ensure these summary tables are updated consistently
with the tick data (maybe rebuild or append new day’s bars each night). Use appropriate sort
keys on these tables too (e.g. sort by instrument then date).• 
• 
5
23
• 
• 
7
Precomputed Metrics:  Similarly, if backtests often compute metrics (like daily P&L, moving
averages, etc.), you could precompute those in columns or separate tables. HeavyDB’s SQL can
handle window functions (for moving averages) and joins if needed, but doing it on the fly for
many concurrent tests could strain the system. By precomputing common indicators at load
time, you trade a bit of storage for faster queries.
Keep in mind accuracy: pre-aggregating minute bars from ticks is fine for strategies that don’t
need intraminute detail. But if a strategy truly needs tick-level simulation, you might not have a
choice but to query tick data.
Use of Common Table Expressions (CTEs):  Using WITH clauses (CTEs) can organize complex
queries (like multi-step calculations) into readable blocks. HeavyDB’s planner (Calcite) will usually
inline or optimize CTEs similarly to subqueries. However , be mindful that in some SQL databases
CTEs act as an optimization fence (materializing the result); it’s not clearly documented that
HeavyDB does this, but given it focuses on performance, it likely tries to optimize through them.
A good practice is to use CTEs for clarity  and maintainability, and trust HeavyDB to optimize the
final plan. For example, you might use a CTE to filter and join data for a specific backtest
scenario, then another CTE to rank or compute metrics, and finally select from it. This can often
be done in one SQL, but splitting it can clarify the intent. Test : if you notice a CTE-based query
running slow, consider rewriting it as a single query or ensuring that the CTE is not re-scanned
unnecessarily. In heavy concurrent usage, simpler queries are often better , but don’t sacrifice all
clarity – correct results and maintainable logic are important too.
Window Functions and QUALIFY:  HeavyDB supports a variety of window functions (analytic
functions) – e.g. running sums, moving averages, lag/lead – which are useful in time-series
analysis. You can use PARTITION BY  (e.g. partition by instrument) in your window functions to
compute metrics per instrument without separate queries. The QUALIFY  clause (if available in
your HeavyDB version) can be used to filter the results of window functions in the same query ,
similar to how  HAVING filters after aggregation. For instance, you could retrieve the top 5
trades by volume for each instrument by doing: 
SELECTinstrument ,ts,price,volume,
ROW_NUMBER ()OVER(PARTITION BYinstrument ORDERBYvolumeDESC)
asvol_rank
FROMtick_data
QUALIFY vol_rank <=5;
This would rank trades per instrument and immediately filter to rank <= 5 for each instrument.
Without QUALIFY , you’d need a subquery or CTE. Using such SQL features can push more work
into HeavyDB’s optimized engine rather than retrieving data to the application for post-
processing. Ensure your HeavyDB version supports the specific feature (window functions are
supported as of HeavyDB 5.x+, and QUALIFY is supported by some analytic SQL engines like
BigQuery and likely HeavyDB given its analytic focus). These constructs let you leverage the GPU
for complex logic (like sorting within partitions) instead of doing it in Python or C++ outside the
DB.
Joins Optimization:  If any of your backtests require joining data (for example, joining trades
with quotes, or joining an instrument’s data with a benchmark index), HeavyDB can perform
joins but a few tips apply:• 
• 
• 
• 
• 
8
Use Hash Joins:  HeavyDB’s optimizer will attempt to use a hash join for equi-joins, which is
efficient especially if one table is significantly smaller (dimension table) – load the smaller table
into CPU/GPU and probe it for each row of the larger . Ensure join columns have the same type
and encoding. If joining on text, share dictionaries  or use dictionary encoding referencing the
same set . If not, HeavyDB might have to translate dictionaries or even fail to do a fast join.
Fragment Skipping on Joins:  There is a config option for inner-join-fragment-skipping
. By default it’s off (false), meaning HeavyDB will scan all fragments of both tables for a join.
If enabled, it might skip some fragments if it can determine they have no matching keys (e.g. by
min/max of join key). This can help if data is partitioned similarly. For example, if you had
separate daily tables and were joining day by day, it might skip non-matching days. For our use,
if we join instrument A’s table with instrument B’s on timestamp (for a synchronized
comparison), fragment skipping could potentially skip fragments of A that have timestamps
outside B’s range. It’s an edge case optimization; enabling it could help certain join patterns –
consider testing it if you do a lot of time-based joins.
Avoid Big Cartesian Joins:  Obviously, avoid any join that isn’t keyed (no product of 10M x 10M!).
HeavyDB cannot index-join, it will brute force, so always join on restrictive conditions (like
matching timestamps or IDs).
Denormalized Data:  We previously chose denormalization to avoid joins. Stick to that for core
queries. Only join when absolutely needed (like reference data or combining results).
Parallel Execution and Concurrency:  HeavyDB excels at  parallel query execution . A single
query will utilize all available CPU cores and GPU threads to process data chunks in parallel (each
fragment is a unit of parallelism). For example, if a table has 320 million rows and fragment size
is 32M, it will have 10 fragments; HeavyDB can use ~10 threads (and GPU kernels) to scan those
in parallel . This means a query on a large table is automatically parallelized. To ensure this
works:
Keep fragment size at the default (32M) or at least not too large. The default is tuned for
performance ; if you set fragment_size equal to your entire table (i.e. one fragment), you’d
force HeavyDB to use only one thread per table scan, reducing performance. Conversely, very
small fragments (say 1000 rows each) would increase overhead and likely reduce performance
due to kernel launch overhead and metadata bloat. The default 32M is generally fine for most
cases; if your table has ~700 million rows (70 instruments * 10M each), that’s ~22 fragments.
Each query scanning the whole table could spawn tasks across threads/GPUs to process ~22
chunks concurrently.
Parallel Query Execution vs Concurrency:  HeavyDB’s scheduler will also handle multiple
queries at once. If thousands of backtests query simultaneously, HeavyDB will intermix their
execution depending on resources. By default, HeavyDB will try to use all CPU cores for a single
query up to a point, but there are settings to limit any one query from monopolizing resources.
For example, executor-per-query-max-cpu-threads-ratio  can limit the fraction of CPU
threads one query can take . The default is 0.9 (90%), meaning one query can use nearly all
threads if needed . In a high-concurrency environment, you might lower this ratio so that one
heavy query doesn’t starve others. Similarly, there’s a ratio for CPU memory per query .
Consider tuning these if you observe that a few large queries slow down the rest.
If using GPU, HeavyDB will also allocate GPU kernels. With multiple GPUs, queries may split work
among them (especially if sharded or if data is replicated). It’s beneficial to have as many queries
run concurrently as GPUs available (so each GPU is busy). You might need to experiment with
enabling/disabling the dynamic watchdog (which kills long-running queries) and the allow-
cpu-retry  setting. By default, if a query would exhaust GPU memory, HeavyDB can retry it on
CPU . In concurrency, you might actually prefer some queries go straight to CPU if GPU is full,• 
20
• 
24
• 
• 
• 
25
• 
7
• 
26
27
28
• 
29
9
to avoid failing or evicting cached data. The parameter gpu-input-mem-limit  default 0.9
(90%) means if a query’s data would exceed 90% of GPU memory, it triggers a CPU execution .
You can adjust this threshold to balance GPU usage. For example, in heavy concurrency, if
memory is a bottleneck, you might set it lower to be conservative (so large scans go to CPU to
keep GPU free for smaller interactive queries).
Query Profiling:  To understand bottlenecks, use EXPLAIN  (HeavyDB supports explaining the
query plan) and monitor resource usage. HeavyDB’s system tables such as memory_summary
and memory_details  let you see how much CPU/GPU memory is used during queries .
There are also log-based system tables ( query_log  or similar) that record queries and their
execution times. By examining these, you can identify if certain queries are slower and why (e.g.
did they fall back to CPU? did they scan a huge number of rows?). HeavyDB’s log will often note if
a query was run on CPU or GPU and if any timeouts occurred.
In summary, write queries that filter aggressively, exploit data ordering, and let HeavyDB do the
heavy lifting on GPU . Use analytic SQL features to avoid doing work in the application layer . And in a
concurrent scenario, tune the system to share resources fairly among queries. Each backtest query
should aim to touch as little data as necessary (both in breadth – by instrument/exchange – and length
– by time window) to get its result. With 10M rows per instrument, a well-filtered scan and aggregate on
GPU can finish in milliseconds; the challenge is ensuring 1000 such queries also perform well together ,
which is where partitioning and resource management come in (discussed more below).
4. Data Ingestion and Updating
Efficient data loading and update procedures are critical when you ingest millions of rows daily (EOD
updates)  and  still  need  the  system  online  for  queries.  HeavyDB  is  designed  for  high-speed  bulk
ingestion, but you should follow best practices to minimize downtime and avoid bottlenecks during the
load process:
Bulk Load in Batches:  For initial data load (historical data) and daily EOD inserts, use batch
ingestion. HeavyDB supports COPY FROM  command to load CSV/Parquet files directly. Prepare
your daily data as files and load them in one transaction per table if possible. According to
Heavy.AI guidelines, it’s best to  load data in batches rather than one row at a time .
Inserting one row at a time (like via many individual INSERT statements or a row-by-row API
feed)  incurs  huge  overhead  due  to  transaction  commits  and  is  extremely  slow.  Instead,
accumulate new data (per instrument or per day) into a file and do a single COPY operation. If
using Python, you can also batch insert via the pymapd/pyheavy client by sending columnar data
(Arrow) in chunks.
Parallelize Ingestion:  If you opted for separate tables per instrument, you can potentially load
them in parallel (since each table’s load is independent). E.g., spawn multiple COPY commands
concurrently (perhaps limited by disk and CPU I/O capacity) – one per instrument file. HeavyDB
by default will try to use all available CPU cores for parsing and inserting data (it has a parameter
num-reader-threads  which by default is all threads ). If you saturate CPU or I/O, you may
adjust this or stagger loads. If using a single large table, you might be loading one file that
contains data for all instruments for the day (assuming you merge them). In that case, one COPY
can utilize multiple threads internally, but you cannot run multiple COPYs to the same table
concurrently (table-level write lock). You might choose to break the daily load by instrument: e.g.,
have 70 files (one per instrument's new data) and load sequentially or in small groups. However ,
loading a single combined file might be simpler – HeavyDB’s bulk insert is quite fast (millions of
rows per second in many cases), so even 700 million new rows (if that were one day, which is
likely far above actual daily ticks) could be ingested in minutes on a decent machine.29
• 
3031
• 
32
• 
33
10
Minimize Disruption to Queries:  One concern is how to update at EOD without interrupting
running queries. HeavyDB handles reads and writes fairly gracefully: an ongoing COPY FROM
will commit the new data at the end of the operation, at which point it becomes visible to
queries. During the copy, the table is essentially being written but readers might still be able to
query the already committed data (depending on transaction isolation – HeavyDB likely uses
lock-based isolation). To be safe, you can schedule a brief maintenance window for the EOD
update or at least inform processes not to query that instrument’s latest day until load complete.
In practice, the load might be so fast that it’s done in seconds or a minute, which might be
acceptable downtime. If absolutely no downtime is wanted, one strategy is:
Load new data into a staging table  (same schema as main). This avoids locking the main table
until the final step.
Once loaded, use INSERT INTO main_table SELECT * FROM staging_table;  or do a 
UNION via a view. Or swap tables by renaming if you loaded a full new version.
However , HeavyDB doesn’t have a trivial partition switch like some DBs. A simpler approach: use
an append-only strategy  and ensure queries that run during load either tolerate missing the latest
data until next run or are run after .
Also note: if using max_rows  table property (like a rolling buffer), adding rows beyond the limit
will  drop  old  fragments  automatically .  This  doesn’t  sound  like  your  use  case  (likely  you
accumulate data). If it is (like only keep last N days), then heavy will automatically drop the oldest
fragment when limit exceeded.
Batch Size and File Recommendations:  If using CSV, using multiple smaller files can actually
improve import parallelism. A Heavy.AI doc suggests for compressed files, use <1M rows per file
for parallel processing . You might not compress the data (to save CPU on load) since disk is
not the bottleneck if you have fast SSDs. If you do compress (gzip), consider splitting into parts
so  HeavyDB  can  decompress  in  parallel.  Also,  ensure  no  extraneous  formatting  issues
(consistent delimiter , proper quoting, etc.) to avoid slowing the parser .
End-of-Day (EOD) Load Process:  Typically, you would:
Gather the day’s data for each instrument (from whatever source) after market close.
Transform/clean it if needed (ensure it matches schema, correct types).
Possibly sort the data by timestamp (and by instrument if multi-instrument file) before loading –
HeavyDB’s import can sort on the fly if sort_column  is specified and you increase the import 
BUFFER_SIZE  to allow bigger sorting window . By default, HeavyDB sorts incoming data in
8MB chunks; setting BUFFER_SIZE=128MB  in COPY FROM can improve the sort quality for
large imports .
Execute COPY FROM '/path/to/data_2025-06-02.csv' INTO tick_data WITH 
(header='true', buffer_size='128MB');  (with proper options).
After  import,  verify  row  counts  (perhaps  via  SELECT count(*)  or  using  HeavyDB’s
\memory_summary  to see if memory usage increased as expected).
Updates and Deletes:  If data might be corrected or revised , you need a strategy for updates.
HeavyDB does allow UPDATE and DELETE statements as of recent versions, but they work by
marking rows as deleted (and optionally vacuuming later) rather than in-place alteration. If you
only append new data daily and never modify historical data, you avoid this complexity. If a
correction is needed (say an exchange recalc hits), you have two options:• 
• 
• 
• 
• 
34
• 
35
• 
• 
• 
• 
36
36
• 
• 
• 
11
Issue a DELETE FROM tick_data WHERE instrument='XYZ' AND ts=...  for the specific
entries, then INSERT the corrected rows. This works, but note that heavy deletions can fragment
the data . HeavyDB’s doc says when >10% of rows in a fragment are deleted, it may trigger an
automatic vacuum . Vacuuming will reclaim space but at some cost. Keep an eye on the
percentage of deletes – if minimal, it’s fine.
The more brute-force method: keep data for each day or partition in separate table or use a
partitioned approach. For instance, maintain a table per month or year . If a chunk needs reload,
drop that month’s table and recreate it. This is more complex but avoids piecemeal deletes. With
10M rows per instrument, if that’s say 1 year of data for active symbols, per-month tables could
be ~<1M rows each, easy to reload entirely if needed.
Given daily EOD updates, ideally your pipeline produces clean data so that retroactive updates
are rare. If needed, schedule them also in off hours.
Data  Versioning:  If  you  want  the  ability  to  run  backtests  on  prior  snapshots  of  data  (for
example, comparing results with last week’s dataset vs today’s dataset), you might consider a
simple versioning approach like keeping old tables or using copy-on-write. HeavyDB doesn’t
have built-in versioning, but you could:
At EOD load, before inserting new data, take a backup (e.g., dump the DB or copy the table). Not
very practical given size.
Or maintain a separate historical database instance for archival.
Simpler: ensure that your loading doesn’t compromise running tests. If a backtest is in progress
during an update, it will either use the old data (if the query was already compiled/executing) or
might see partial new data if it queries mid-load (depending on transaction isolation). It’s best to
avoid overlapping loads and queries on the same data. Possibly pause new queries or queue
them during the few minutes of data load to keep things consistent.
Ingestion Performance Tuning:  A few config parameters can affect loading speed:
num-reader-threads  (default all cores) : if you find loading saturates the machine and
you need to keep some CPU free for queries, you might reduce this. Otherwise, keep it high for
fastest ingest.
Disk throughput: use fast SSD or NVMe drives. HeavyDB recommends RAID10 SSDs for best
ingest and query speed . Ensure the data files are on the same server (remote loading adds
network overhead).
If you ingest via a client (not using COPY), use the Arrow flight interface or bulk import API to
avoid overhead of row-by-row insertion.
Consider disabling HeavyDB’s dynamic watchdog during bulk loads if it’s a long operation
(watchdog is mainly for queries, though).
Monitor memory: Bulk import will allocate CPU memory (and possibly GPU memory if dictionary
encoding has to sync dictionaries across GPUs). Make sure you have enough free memory to
ingest (the entire file plus some overhead). If memory is an issue, import in smaller chunks or
one instrument at a time.
Continuous Ingestion vs Batch:  Since data is updated daily, a batch nightly load is fine. If you
had real-time streaming (not in this scenario), HeavyDB can handle Kafka or stream ingestion
but at some cost. Keep in mind heavy batch loads are efficient; streaming many small batches is
less so. So even if you had the option, prefer to accumulate intraday and bulk load once.• 
37
• 
• 
• 
• 
• 
• 
• 
• 33
• 
38
• 
• 
• 
• 
12
Overall, design your ingestion pipeline to be as automated and quick as possible : generate sorted
daily files, use COPY FROM with batch settings, and have proper monitoring (so you know the load
succeeded and data counts match). After loading, you can run a quick query to update any metadata or
precompute aggregates (as mentioned in Query Optimization). For example, after loading the ticks, you
might run a query to refresh the minute-bars table or update certain caches. This could be scripted as
part of EOD processing.
Finally, since the question emphasizes not interrupting queries: If 24/7 uptime is needed, consider a
strategy like rolling updates on a cluster (if using HeavyDB Enterprise, maybe you can load data on one
node and then others catch up). For open source single-server , a brief pause or scheduling loads during
off-peak hours is usually acceptable for daily updates.
5. HeavyDB-Specific Features and Best Practices
HeavyDB provides several features that can be harnessed to maximize performance in our use case.
Understanding these engine-specific capabilities  will help you squeeze out maximum throughput for
backtesting queries:
GPU Acceleration:  The hallmark of HeavyDB is its ability to use GPUs for query processing.
Ensure your system is configured to take advantage of this:
Use a machine with one or more NVIDIA GPUs  (HeavyDB supports NVIDIA GPU architectures).
For production, enterprise-grade GPUs (Tesla series like V100, A100) are recommended . They
offer large memory (16-80GB) and high memory bandwidth, which is crucial for scanning large
datasets.
GPU Memory (L1 cache):  HeavyDB caches frequently used data in GPU memory for ultra-fast
access . The first time a query touches a portion of the data, it may load it from disk or CPU
memory to GPU. Subsequent queries on the same data can be much faster . Leverage this by
running common queries or a warming script at system start (HeavyDB even allows a “query list”
on startup to pre-load certain data into GPU) . For example, you could have a startup
query for each instrument like SELECT COUNT(volume) FROM tick_data WHERE 
instrument='XYZ' AND ts > now() - interval '7 days';  to load recent data into GPU
memory (the actual result doesn’t matter , the access is what loads the data).
Monitor GPU memory usage via memory_summary  system table or \gpu_memory  command.
Ensure your GPU has enough memory to hold at least the working set of data for active queries.
If not, data will spill to CPU (L2) which is slower . If you notice frequent GPU <-> CPU
swapping, it may indicate queries are touching more data than fits in GPU. In a multi-GPU setup,
adding more GPUs (with sharding) effectively adds more aggregate GPU memory.
If running out of GPU memory, consider increasing  gpu-buffer-mem-bytes  (by default it
uses all GPU memory available) , or upgrade GPUs. Also consider limiting concurrency or
query complexity to what fits in memory.
Heavy Connect (Arrow / Client Interfaces):  When running thousands of backtests, you likely
have an application layer controlling them. If that layer retrieves large result sets from HeavyDB,
use the fastest data transfer method:
HeavyDB’s Python connector (pymapd) can use Arrow to fetch results in columnar format quickly.
This minimizes overhead in transferring millions of rows. If you find yourself pulling tick data out
to simulate in Python, try to offload as much as possible to HeavyDB SQL. But if you must pull
data (say to run a custom algo), fetch only needed columns and perhaps compress them (Arrow• 
• 
39
• 
40
4142
• 
43
• 
44
• 
• 
13
is binary, efficient). Avoid ODBC/JDBC for bulk data retrieval if possible, since Arrow Flight or
native connector is faster .
HeavyDB supports a result set recycler  (data recycler) which can cache certain query results or
intermediate  join  hash  tables .  This  means  if  you  run  identical  or  similar  queries
repeatedly,  HeavyDB  may  reuse  previous  computation  (like  a  cached  join  structure  or  a
previously compiled GPU code). Ensure the config  enable-data-recycler  is true (it is by
default) . For example, if many backtests query the same join of two tables, the hash table
from the first join might be cached and reused . This feature is mostly automatic, just keep it
enabled.
QUALIFY  and  Advanced  SQL:  As  discussed,  the  QUALIFY  clause  (filtering  after  window
functions) can simplify certain ranking queries. It is a relatively new addition to SQL engines;
confirm HeavyDB’s support (it’s likely supported given HeavyDB’s aim to be ANSI SQL compliant
and support analytics). Use it to reduce query complexity where appropriate.
Common Patterns:  For backtesting, you might need last value in a group, or top N, etc. Instead
of subqueries, use window functions (e.g. LAST_VALUE(price) OVER (PARTITION BY 
instrument ORDER BY ts ROWS BETWEEN UNBOUNDED PRECEDING AND UNBOUNDED 
FOLLOWING)  to get last price per instrument). HeavyDB’s window functions include 
ROW_NUMBER, RANK, SUM() OVER ...  and even advanced ones like LAG/LEAD  (per
release notes). These are GPU-accelerated and will outperform doing such calculations in Python
after fetching data.
Iterative or Conditional Logic:  SQL is not great for certain simulations (like step-by-step order
matching). If you have to implement such logic outside the database, try to reduce the data
before extraction (filter in SQL as much as possible). HeavyDB might also allow user-defined
functions (UDFs) or table functions in C++/Python that run on the server for custom logic, but
that’s advanced. For example, a table function could simulate a strategy within the database for
each instrument. If your concurrency is huge, it might be complex to do inside DB though.
Resource Management (CPU & GPU):
HeavyDB has a dynamic watchdog  that terminates queries running too long (default ~100
seconds)  to prevent runaway queries from hanging the system. For backtesting queries, if
you anticipate some could run longer (maybe a very large analysis), you can increase this
timeout or disable the watchdog for specific queries. Alternatively, break the work into smaller
queries. 
You can adjust how HeavyDB balances between queries: as mentioned, limit per-query thread
usage to ensure fair scheduling . Also, if using a multi-tenant setup or sharing hardware,
consider running HeavyDB in CPU-only mode for some tasks (there’s a cpu-only  flag ). But
in our scenario, we want GPU on.
Memory : The config cpu-buffer-mem-bytes  controls how much host RAM HeavyDB will use
(by default 80% of available) . If you have other processes, you might lower this to avoid
swapping. Similarly, ensure OS HugePages are configured (for better memory management).
If queries often overflow GPU, you might prefer them to fail fast instead of silently retry on CPU
(especially if accuracy or performance on CPU is not acceptable). The allow-cpu-retry  (if
disabled, a query that can't fit on GPU and hits the limit will error rather than run on CPU) could
be toggled. By default, HeavyDB will retry on CPU unless watchdog is on and it can't (then fail)
. Decide based on whether you want a slower correct result or an error (maybe better to get
result albeit slower).• 
4546
47
46
• 
• 
4849
• 
• 
• 
50
• 
51
52
• 
53
• 
54
14
Concurrency Limits : If truly thousands of queries are fired at once, you may exceed practical
concurrency (context switching overhead, GPU kernel queue backlog). It might be useful to
queue or throttle at the application level (limit maybe 100 queries at a time or such). HeavyDB
doesn’t have an internal query queue beyond using all threads, so it will attempt to run as many
as thrown at it. Monitor system load (CPU utilization, GPU utilization). If you see diminishing
returns or thrashing, implement a simple concurrency limit externally.
128M Row Limit (Large Projections):  Historically, HeavyDB had a limitation on the number of
rows that a single GPU kernel could project when the watchdog is enabled (it was 32 million,
raised to 128 million per GPU in recent versions) . This means if you try to select more than
128M rows per GPU  in a single query result, the watchdog might kill it, or the engine will split it.
In our context, if you ever had a query that tries to  return  all tick data of many instruments
(hundreds of millions of rows) at once, you might hit this. But typically backtests aggregate or
process data rather than dump it raw. If you do need to retrieve a massive result set, consider
slicing it (e.g. fetch in chunks by time or instrument) instead of one giant query. Also ensure
you’re on a version that extended this limit. With 4 GPUs, the effective limit could be 4 * 128M =
512M output rows without hitting the single-GPU limit. If you anticipate more, you could disable
the dynamic watchdog or request the result in a windowed manner .
The 128M figure also relates to intermediate results in query execution. For example, a join or
group by  might have to build hash tables or intermediate buffers. HeavyDB likely handles large
inputs by partitioning, but if you see errors related to "Projection limit" or "too many rows",
these are the areas to check. The release notes  indicate improvements for large result handling
, so upgrading to the latest HeavyDB is beneficial.
Monitoring and Profiling Tools:  It is important to continuously monitor the performance as you
implement these optimizations:
Use HeavyDB’s system tables  for insight. For instance, query 
information_schema.memory_summary  to see memory use across CPU/GPU . High 
used_page_count  on GPU indicates lots of data cached – which is good until it nears capacity.
The memory_details  table even shows which table’s chunks are in GPU memory , so
you can see if your hot instruments are indeed cached.
HeavyDB also has query log tables  (like omnisci_server_queries  in older OmniSci versions
or equivalent in HeavyDB) that record each query’s text, execution time, and whether it ran on
GPU. By analyzing these logs (you might have to enable them in heavy.conf or via startup
parameter), you can identify slow queries or those that fallback to CPU. This is extremely useful
for pinpointing bottlenecks.
The HeavyDB server logs ( heavydb.INFO  etc.) will contain messages about memory allocation
failures, or if the dynamic watchdog killed a query. Set the logging level to info or debug during
testing to catch these events.
OS Monitoring:  Don’t forget to monitor at the system level: CPU usage, GPU usage (use 
nvidia-smi  to see GPU utilization and memory usage per process in real-time), disk I/O, etc. If
GPU is underutilized and CPU is maxed out, maybe some queries fell back to CPU – investigate
why (possibly data too large for GPU memory or some feature not supported on GPU). If GPU is
100% and CPU is fairly low, that’s typically ideal for heavy scan queries.
HeavyDB’s EXPLAIN  command can show you the query plan (including whether it’s using
certain optimizations like columnar output). After writing complex SQL, do an EXPLAIN 
SELECT ...  to ensure it looks sane (e.g., it’s doing a indexed join vs loop join, etc.). The docs
also mention table reordering  (the optimizer will reorder join tables to favor hash joins) . If• 
• 
55
• 
55
• 
• 
3031
5657
• 
• 
• 
• 
58
15
you have multiple joins, make sure the largest table is last (fact after dimension) or let the
optimizer handle it (it's on by default).
Using HeavyDB’s features wisely can turn your backtesting system from just okay to blazing fast . The
combination of GPU acceleration, a well-chosen schema, and careful query design means you can query
billions of rows in milliseconds  under the right conditions. For financial tick data, this means you
can run complex analytics (like calculating indicators over millions of ticks) faster than the data could
even be scanned on a traditional disk-based system.
6. Configuration and Tuning
Finally, after schema and query optimizations, we must ensure the HeavyDB server configuration  and
hardware are tuned for high performance and concurrency. Here are key parameters and tuning tips:
Server Hardware Considerations:  Ensure the server has sufficient RAM and fast disks  relative
to your data size. A rule of thumb from HeavyDB is to have 4–8x CPU RAM per GPU memory .
For example, if you have 4 GPUs each with 16GB (total 64GB GPU), have 256–512GB of system
RAM. This is because if data doesn’t fit in GPU, it spills to CPU memory, and you want that to be
ample and avoid swapping to disk. Also, use NVMe or SSD for the data storage – mechanical
disks will be a serious bottleneck when scanning large fragments. HeavyDB suggests SSDs and
even RAID configurations for reliability and throughput . If using cloud, provision sufficient
IOPS.
HeavyDB  Configuration  Parameters:  The  heavy.conf  (or  command-line  flags)  has
numerous settings. Some notable ones for performance:
cuda-block-size  and cuda-grid-size : These control GPU kernel launch parameters
(threads per block, blocks per grid) . Default is 0 = auto (use all threads and blocks). Typically
leave these at default unless an expert suggests change; auto usually maximizes GPU usage.
dynamic-watchdog  settings: As mentioned, dynamic-watchdog-time-limit  (default
100000 ms = 100s) . If your queries legitimately take longer (maybe some huge backtest),
raise this or disable the watchdog ( --enable-watchdog=false  if needed) to prevent
premature termination.
gpu-buffer-mem-bytes : If for some reason you want to limit GPU memory HeavyDB uses
(maybe to leave room for another process), you can set this. Default 0 = use all available . In a
dedicated server , keep it at 0 to leverage all VRAM.
cpu-buffer-mem-bytes : As discussed, default 0 = 80% of system RAM . If you run other
services, you might lower this to avoid out-of-memory. But ideally HeavyDB has as much
memory as it needs. Check memory consumption; if OS is swapping, that’s bad – either reduce
this setting or add RAM.
Thread and Concurrency Settings:  Newer HeavyDB versions have an internal Executor
Resource Manager . The key settings:
num-executors  or similar (not directly in config, HeavyDB uses one executor per
available CPU core by default).
executor-per-query-max-cpu-threads-ratio  (default 0.9) : as mentioned,
consider lowering to maybe 0.5 or 0.7 if you want to run many queries truly concurrently
without one eating all threads.
There’s also max-concurrent-queries  implicit by hardware, but not a direct config. If
you have e.g. 64 cores, theoretically ~64 heavy queries at once (if each took a core) is59
• 
60
61
• 
• 
62
• 
50
• 
44
• 53
• 
◦ 
◦ 26
◦ 
16
okay, but more and you context switch. It might be okay to have more queries than cores
if many are waiting on GPU; but if all are CPU-bound, things slow.
calcite-max-mem  (JVM memory for query parser) default 1024 MB . If you have
many simultaneous query compilations or very complex queries, you might bump this if
you see Calcite OOM errors.
calcite-service-timeout  default 5000 ms . If you have hundreds of active tables
or frequent DDL, sometimes planning could exceed 5s. You might have seen timeouts if
so. With many concurrent queries, maybe increase it modestly (10s) to avoid failures
under heavy load.
enable-columnar-output  (default true) : This allows HeavyDB to produce result sets in
columnar form. It’s usually faster for large results especially when interfacing with Arrow. Keep it
on (unless you find some query regresses, which is rare).
hashtable-cache-* : If you do many repeated joins, consider the 
hashtable-cache-total-bytes  (default 4GB) . If you have memory to spare and heavy
join reuse, you could increase this so more join hash tables can be cached for longer .
max-session-duration  or idle session: If you have an application that keeps sessions open,
ensure  idle-session-duration  (default 60 min)  is high enough or set to 0 (infinite) if
you don’t want sessions to drop. Or manage reconnections gracefully.
128M Row Handling:  We addressed this above, but to reiterate in config terms: The increase
from 32M to 128M per GPU projection was in HeavyDB 6.0+ . If running an older version and
hitting that, upgrade. If you still anticipate queries hitting limits (like trying to select all 700
million rows in one go), consider disabling the watchdog (which enforces that limit). You can also
chunk your query with  LIMIT/OFFSET  or by splitting by instrument to avoid one monster
result. But practically, in backtesting you rarely need to  output  that many rows; you’d output
summaries or P/L which are small.
Monitoring  Tools:  Use  HeavyDB’s  built-in  stats  as  covered,  but  also  consider  external
monitoring:
nvidia-smi can be queried programmatically to log GPU usage.
Linux perf or htop to see CPU usage and if threads are CPU-bound.
If using HeavyDB Enterprise or Cloud, it may come with admin dashboards.
You might create a simple script to query memory_summary  periodically to see if you approach
memory limits. Also check  storage_details  system table to track how large tables are on
disk and fragmentation.
Testing and Benchmarking:  Before fully deploying, run benchmarks with a subset of data and
simulated  concurrent  queries.  HeavyDB  has  a  benchmarking  script  (for  SSB,  etc.)  and  has
published comparisons showing it’s extremely fast . But your specific patterns matter . Test
simple scans, then more complex backtest queries, measure times, adjust config if needed.
Especially test worst-case scenarios: e.g. all backtests query the biggest instrument for the entire
year – can the system handle that concurrently? If not, impose some query limits or encourage
users to be more selective in time or instrument per query.
Query Queueing (if needed):  While not a built-in HeavyDB feature, you might implement a
lightweight  queue  outside  HeavyDB  if  you  truly  have  more  simultaneous  queries  than  the
hardware can efficiently handle. This could be as simple as a Python semaphore limiting number
of threads that call the DB, or using a job scheduler . The goal is to avoid thrashing – e.g., if each◦ 63
◦ 64
• 65
• 
46
• 
66
• 
55
• 
• 
• 
• 
• 
• 
67
• 
17
query would take 1s alone but 1000 at once might each take 30s due to contention, you might
actually finish faster by running 100 at a time in batches.
Vacuum and Maintenance:  If you do deletes/updates, consider running the HeavyDB vacuum
(either automatically via vacuum DELAYED  setting or manual VACUUM command if provided)
to  reclaim  space  and  tidy  fragmentation  when  needed .  For  append-only  workloads,
fragmentation should not be an issue except when data is dropped or updated.
In summary,  tune HeavyDB’s settings to match your workload and hardware . The defaults are
reasonable  for  general  use,  but  high-concurrency,  high-volume  scenarios  benefit  from  careful
adjustments (especially around resource limits). Keep the system well-resourced (CPU, GPU, RAM, disk)
and monitor it so you can preempt any performance issues by scaling up or tweaking configs.
Summary of Best Practices
To  conclude,  here  is  a  consolidated  list  of  best  practices  for  optimizing  HeavyDB  in  a  financial
backtesting context:
Schema & Data Layout:  Favor a denormalized schema for tick data – one main fact table (or one
per instrument) with dictionary-encoded dimensions (instrument, exchange, etc.) . Sort the
data by time (and cluster by instrument if possible) to enable fragment skipping on common
filters . Use appropriate fragment sizes (use HeavyDB default ~32M rows)  to balance
parallelism and overhead.
Data Types & Encoding:  Use the smallest numeric types and fixed encodings that accommodate
your data . Prefer FLOAT over DOUBLE when feasible for speed (but not at the expense of
required accuracy). Utilize  DECIMAL  for exact financial calculations if needed. Always declare
text  columns  with  ENCODING DICT  for  storage  and  performance  benefits .  Share
dictionaries across tables for consistent encoding if you have multiple tables .
Query Optimization:  Always include selective  WHERE filters (instrument, date range, etc.) to
minimize  scanned  data.  Design  queries  to  use  HeavyDB’s  strengths:  do  aggregations  and
analytics in SQL (GROUP BY, window functions) rather than post-processing. Avoid inefficient
constructs  that  negate  fragment  skipping  (like  functions  on  filtered  columns).  Use  WITH  
(sort_column=...)  when creating tables to physically order data for faster filtering .
Since indexes can’t be created, rely on sorting and partitioning to speed up lookups . Pre-
aggregate data (e.g. minute bars, daily summaries) when appropriate to avoid scanning raw
ticks for every query. 
Concurrent Execution:  HeavyDB will parallelize scans and utilize all CPU cores and GPUs. Exploit
this by keeping data in large contiguous chunks (fragments) it can split among threads. If using
multiple GPUs, consider sharding tables by instrument to distribute load . Tune executor-
per-query-max-cpu-threads-ratio  and  similar  settings  to  prevent  a  single  query  from
monopolizing  resources  in  a  high-concurrency  scenario .  In  extreme  concurrency  cases,
implement external query throttling to avoid overloading the server .
Data Ingestion:  Perform bulk inserts in batch transactions . Utilize multi-threaded  COPY 
FROM and split files for parallel loading where possible. Load during off-peak times or stage data
to minimize impact on running queries. Keep the database append-only for simplicity; if updates• 
68
• 
6
2 7
• 
6912
6
8
• 
2236
5
• 
3
51
• 32
18
are needed, handle them in bulk and vacuum periodically. Ensure new data is sorted per the
table’s sort key before or during load (increase  BUFFER_SIZE  for the insert to maintain sort
order) . Verify data integrity after loads (row counts, basic queries) as part of your pipeline.
Hardware & Config:  Use powerful GPUs with ample memory and high memory bandwidth,
paired with sufficient CPU and RAM to feed them . Fast SSD storage is essential for quick
scans and loads. Adjust HeavyDB configuration: allow it to use most of the system’s memory
(default  80%) ,  and  all  GPU  memory  (default)  for  caching  hot  data.  Increase  watchdog
timeout if legitimate queries approach 100s runtime . Monitor for queries falling back to CPU
due to gpu-input-mem-limit  and adjust if necessary (to either avoid slow CPU execution
or proactively run some queries on CPU by choice). Keep the data recycler on to benefit from
cache of frequent query results and join hash tables .
Monitoring & Maintenance:  Continuously monitor query performance and resource usage. Use
HeavyDB’s system tables (memory summary, etc.) to watch CPU/GPU memory utilization .
Analyze HeavyDB’s query logs for slow queries or errors. Maintain your database by vacuuming
deleted rows (if any) and updating statistics if needed (HeavyDB doesn’t require manual stats, it’s
fully columnar). Revisit your design if usage patterns change – e.g., if new strategies query
across many instruments at once, you might need to adjust the schema or add precomputed
results.
By following these guidelines and leveraging HeavyDB’s GPU-powered engine, you can achieve  high-
throughput, low-latency query performance  on large-scale intraday financial data. The result is a
backtesting system that can run complex simulations across millions of data points with both speed and
accuracy, enabling faster research iterations and more robust strategy evaluation. Keep both the  big
picture optimizations  (schema, partitioning) and the low-level details  (data types, config parameters)
in mind, and adjust as needed based on profiling and testing results. HeavyDB is built for exactly this
kind of heavy analytical workload, and with the best practices above, you can make the most of it.
HeavyDB | Open Source Analytical Database Using GPUs
https://www.heavy.ai/product/heavydb
Index on table – HEAVY.AI Support Portal
https://support.heavy.ai/hc/en-us/community/posts/11659059937047-Index-on-table
Tables | HEAVY.AI Docs
https://docs.heavy.ai/sql/data-definition-ddl/tables
When should I use dictionary encoding? – HEAVY.AI Support Portal
https://support.heavy.ai/hc/en-us/articles/360047908053-When-should-I-use-dictionary-encoding
Optimizing | HEAVY.AI Docs
https://docs.heavy.ai/troubleshooting-and-special-topics/optimizing-performance
Datatypes | HEAVY.AI Docs
https://docs.heavy.ai/sql/data-definition-ddl/datatypes-and-fixed-encoding
Configuration Parameters
for HeavyDB | HEAVY.AI Docs
https://docs.heavy.ai/installation-and-configuration/config-parameters/configuration-parameters-for-heavydb36
• 
60
53
50
29
47 46
• 
30 31
5
10
110 59 67
2 523 37
3 4 720 21 34 68
6 8 9
11 12 22 36 38 41 42 69
13 14 15 16 17 18 19
24 26 27 28 29 33 44 45 46 47 50 51 52 53 54 58 62 63 64 65 66
19
Is there a way to set the number of CPU cores (or threads) being ...
https://github.com/heavyai/heavydb/issues/718
System Tables | HEAVY.AI Docs
https://docs.heavy.ai/sql/data-definition-ddl/system-tables
What are some best practices for inserting data? – HEAVY.AI Support Portal
https://support.heavy.ai/hc/en-us/articles/11128877526935-What-are-some-best-practices-for-inserting-data
Best Practices | HEAVY.AI Docs
https://docs.heavy.ai/heavyconnect/best-practices
Hardware Reference | HEAVY.AI Docs
https://docs.heavy.ai/installation-and-configuration/system-requirements/hardware
Release 6.x | HEAVY.AI Docs
https://docs.heavy.ai/troubleshooting-and-special-topics/archived-release-notes/release-6.x25
30 31 56 57
32
35
39 40 43 60 61
48 49 55
20

